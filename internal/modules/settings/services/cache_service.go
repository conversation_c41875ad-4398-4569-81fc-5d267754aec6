package services

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/tranthanhloi/wn-api-v3/pkg/cache"
)

// cacheService implements the CacheService interface
type cacheService struct {
	cache  cache.JsonCache
	config *CacheConfig
	stats  *CacheStats
	mutex  sync.RWMutex
}

// CacheConfig holds cache configuration
type CacheConfig struct {
	DefaultTTL   time.Duration `json:"default_ttl"`
	MaxKeyLength int           `json:"max_key_length"`
	KeyPrefix    string        `json:"key_prefix"`

	// TTL per scope
	SystemTTL time.Duration `json:"system_ttl"`
	TenantTTL time.Duration `json:"tenant_ttl"`
	ModuleTTL time.Duration `json:"module_ttl"`
	UserTTL   time.Duration `json:"user_ttl"`

	// Cache warming settings
	WarmupBatchSize int           `json:"warmup_batch_size"`
	WarmupTimeout   time.Duration `json:"warmup_timeout"`

	// Invalidation settings
	InvalidationDelay time.Duration `json:"invalidation_delay"`

	// Compression settings
	EnableCompression bool `json:"enable_compression"`
	CompressionLevel  int  `json:"compression_level"`
}

// DefaultCacheConfig returns default cache configuration
func DefaultCacheConfig() *CacheConfig {
	return &CacheConfig{
		DefaultTTL:        10 * time.Minute,
		MaxKeyLength:      250,
		KeyPrefix:         "settings",
		SystemTTL:         DefaultSystemTTL,
		TenantTTL:         DefaultTenantTTL,
		ModuleTTL:         DefaultModuleTTL,
		UserTTL:           DefaultUserTTL,
		WarmupBatchSize:   50,
		WarmupTimeout:     30 * time.Second,
		InvalidationDelay: 100 * time.Millisecond,
		EnableCompression: true,
		CompressionLevel:  6,
	}
}

// NewCacheService creates a new cache service
func NewCacheService(cacheClient cache.JsonCache, config *CacheConfig) CacheService {
	if config == nil {
		config = DefaultCacheConfig()
	}

	return &cacheService{
		cache:  cacheClient,
		config: config,
		stats: &CacheStats{
			Hits:        0,
			Misses:      0,
			Size:        0,
			MemoryUsage: 0,
			Uptime:      0,
		},
		mutex: sync.RWMutex{},
	}
}

// Get retrieves a value from cache
func (c *cacheService) Get(ctx context.Context, key string, dest interface{}) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Validate key
	if err := c.validateKey(key); err != nil {
		return fmt.Errorf("invalid cache key: %w", err)
	}

	// Get from cache
	err := c.cache.GetJSON(ctx, key, dest)
	if err != nil {
		c.stats.Misses++
		return fmt.Errorf("cache miss for key %s: %w", key, err)
	}

	c.stats.Hits++
	return nil
}

// Set stores a value in cache
func (c *cacheService) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Validate key
	if err := c.validateKey(key); err != nil {
		return fmt.Errorf("invalid cache key: %w", err)
	}

	// Use default TTL if not specified
	if ttl == 0 {
		ttl = c.config.DefaultTTL
	}

	// Store in cache
	err := c.cache.SetJSON(ctx, key, value, ttl)
	if err != nil {
		return fmt.Errorf("failed to cache value for key %s: %w", key, err)
	}

	c.stats.Size++
	return nil
}

// Delete removes a value from cache
func (c *cacheService) Delete(ctx context.Context, key string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Validate key
	if err := c.validateKey(key); err != nil {
		return fmt.Errorf("invalid cache key: %w", err)
	}

	// Delete from cache
	err := c.cache.Delete(ctx, key)
	if err != nil {
		return fmt.Errorf("failed to delete cache key %s: %w", key, err)
	}

	c.stats.Size--
	return nil
}

// GetMulti retrieves multiple values from cache
func (c *cacheService) GetMulti(ctx context.Context, keys []string) (map[string]interface{}, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Validate keys
	for _, key := range keys {
		if err := c.validateKey(key); err != nil {
			return nil, fmt.Errorf("invalid cache key %s: %w", key, err)
		}
	}

	// Get from cache
	result := c.cache.GetMultiJSON(ctx, keys, make(map[string]interface{}))
	if result == nil {
		c.stats.Misses += int64(len(keys))
		return nil, fmt.Errorf("failed to get multiple cache keys")
	}

	// TODO: Implement proper multi-get statistics
	c.stats.Hits += 0
	c.stats.Misses += int64(len(keys))

	return make(map[string]interface{}), nil
}

// SetMulti stores multiple values in cache
func (c *cacheService) SetMulti(ctx context.Context, items map[string]interface{}, ttl time.Duration) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Validate keys
	for key := range items {
		if err := c.validateKey(key); err != nil {
			return fmt.Errorf("invalid cache key %s: %w", key, err)
		}
	}

	// Use default TTL if not specified
	if ttl == 0 {
		ttl = c.config.DefaultTTL
	}

	// Store in cache
	err := c.cache.SetMultiJSON(ctx, items, ttl)
	if err != nil {
		return fmt.Errorf("failed to cache multiple values: %w", err)
	}

	c.stats.Size += int64(len(items))
	return nil
}

// DeleteMulti removes multiple values from cache
func (c *cacheService) DeleteMulti(ctx context.Context, keys []string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Validate keys
	for _, key := range keys {
		if err := c.validateKey(key); err != nil {
			return fmt.Errorf("invalid cache key %s: %w", key, err)
		}
	}

	// Delete from cache
	err := c.cache.DeleteMulti(ctx, keys)
	if err != nil {
		return fmt.Errorf("failed to delete multiple cache keys: %w", err)
	}

	c.stats.Size -= int64(len(keys))
	return nil
}

// DeletePattern removes values matching a pattern
func (c *cacheService) DeletePattern(ctx context.Context, pattern string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Delete from cache
	err := c.cache.DeletePattern(ctx, pattern)
	if err != nil {
		return fmt.Errorf("failed to delete cache pattern %s: %w", pattern, err)
	}

	// We can't easily track how many keys were deleted, so we don't update stats
	return nil
}

// WarmCache preloads cache with settings
func (c *cacheService) WarmCache(ctx context.Context, settingCtx *SettingContext, categories []string) error {
	// Create a context with timeout
	warmupCtx, cancel := context.WithTimeout(ctx, c.config.WarmupTimeout)
	defer cancel()

	// Warm cache in batches
	for i := 0; i < len(categories); i += c.config.WarmupBatchSize {
		end := i + c.config.WarmupBatchSize
		if end > len(categories) {
			end = len(categories)
		}

		batch := categories[i:end]
		if err := c.warmCacheBatch(warmupCtx, settingCtx, batch); err != nil {
			return fmt.Errorf("failed to warm cache batch: %w", err)
		}
	}

	return nil
}

// InvalidateSettingCache invalidates cache for a specific setting
func (c *cacheService) InvalidateSettingCache(ctx context.Context, settingCtx *SettingContext, category, key string) error {
	// Generate cache key
	cacheKey := c.GenerateKey(settingCtx, category, key)

	// Delete with delay to handle race conditions
	if c.config.InvalidationDelay > 0 {
		go func() {
			time.Sleep(c.config.InvalidationDelay)
			c.Delete(context.Background(), cacheKey)
		}()
	} else {
		return c.Delete(ctx, cacheKey)
	}

	return nil
}

// InvalidateContextCache invalidates all cache for a context
func (c *cacheService) InvalidateContextCache(ctx context.Context, settingCtx *SettingContext) error {
	// Generate pattern for all keys in this context
	pattern := c.generateContextPattern(settingCtx)

	// Delete pattern with delay
	if c.config.InvalidationDelay > 0 {
		go func() {
			time.Sleep(c.config.InvalidationDelay)
			c.DeletePattern(context.Background(), pattern)
		}()
	} else {
		return c.DeletePattern(ctx, pattern)
	}

	return nil
}

// GenerateKey generates a cache key for a setting
func (c *cacheService) GenerateKey(settingCtx *SettingContext, category, key string) string {
	var parts []string

	// Add prefix
	parts = append(parts, c.config.KeyPrefix)

	// Add context parts
	if settingCtx.TenantID != nil {
		parts = append(parts, fmt.Sprintf("t%d", *settingCtx.TenantID))
	}
	if settingCtx.ModuleID != nil {
		parts = append(parts, fmt.Sprintf("m%d", *settingCtx.ModuleID))
	}
	if settingCtx.UserID != nil {
		parts = append(parts, fmt.Sprintf("u%d", *settingCtx.UserID))
	}

	// Add category and key
	parts = append(parts, category, key)

	// Join with separator
	cacheKey := strings.Join(parts, ":")

	// Truncate if too long
	if len(cacheKey) > c.config.MaxKeyLength {
		cacheKey = cacheKey[:c.config.MaxKeyLength]
	}

	return cacheKey
}

// GeneratePatternKey generates a cache key pattern for a category
func (c *cacheService) GeneratePatternKey(settingCtx *SettingContext, category string) string {
	var parts []string

	// Add prefix
	parts = append(parts, c.config.KeyPrefix)

	// Add context parts
	if settingCtx.TenantID != nil {
		parts = append(parts, fmt.Sprintf("t%d", *settingCtx.TenantID))
	}
	if settingCtx.ModuleID != nil {
		parts = append(parts, fmt.Sprintf("m%d", *settingCtx.ModuleID))
	}
	if settingCtx.UserID != nil {
		parts = append(parts, fmt.Sprintf("u%d", *settingCtx.UserID))
	}

	// Add category and wildcard
	parts = append(parts, category, "*")

	// Join with separator
	return strings.Join(parts, ":")
}

// GetStats returns cache statistics
func (c *cacheService) GetStats(ctx context.Context) (*CacheStats, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	// Calculate hit rate
	totalRequests := c.stats.Hits + c.stats.Misses
	var hitRate float64
	if totalRequests > 0 {
		hitRate = float64(c.stats.Hits) / float64(totalRequests)
	}

	// Create copy of stats
	stats := &CacheStats{
		Hits:        c.stats.Hits,
		Misses:      c.stats.Misses,
		HitRate:     hitRate,
		Size:        c.stats.Size,
		MemoryUsage: c.stats.MemoryUsage,
		Uptime:      c.stats.Uptime,
	}

	return stats, nil
}

// Helper methods

func (c *cacheService) validateKey(key string) error {
	if key == "" {
		return fmt.Errorf("key cannot be empty")
	}

	if len(key) > c.config.MaxKeyLength {
		return fmt.Errorf("key too long: %d > %d", len(key), c.config.MaxKeyLength)
	}

	// Check for invalid characters
	if strings.ContainsAny(key, " \t\n\r") {
		return fmt.Errorf("key contains invalid characters")
	}

	return nil
}

func (c *cacheService) warmCacheBatch(ctx context.Context, settingCtx *SettingContext, categories []string) error {
	// This would typically load settings from database and populate cache
	// For now, we'll just return nil as this is a placeholder
	return nil
}

func (c *cacheService) generateContextPattern(settingCtx *SettingContext) string {
	var parts []string

	// Add prefix
	parts = append(parts, c.config.KeyPrefix)

	// Add context parts
	if settingCtx.TenantID != nil {
		parts = append(parts, fmt.Sprintf("t%d", *settingCtx.TenantID))
	}
	if settingCtx.ModuleID != nil {
		parts = append(parts, fmt.Sprintf("m%d", *settingCtx.ModuleID))
	}
	if settingCtx.UserID != nil {
		parts = append(parts, fmt.Sprintf("u%d", *settingCtx.UserID))
	}

	// Add wildcard
	parts = append(parts, "*")

	// Join with separator
	return strings.Join(parts, ":")
}

// LayeredCacheService provides L1 (memory) and L2 (Redis) caching
type LayeredCacheService struct {
	l1Cache CacheService
	l2Cache CacheService
	config  *LayeredCacheConfig
}

// LayeredCacheConfig holds layered cache configuration
type LayeredCacheConfig struct {
	L1TTL         time.Duration `json:"l1_ttl"`
	L2TTL         time.Duration `json:"l2_ttl"`
	L1MaxSize     int           `json:"l1_max_size"`
	WriteThrough  bool          `json:"write_through"`
	ReadThrough   bool          `json:"read_through"`
	SyncInterval  time.Duration `json:"sync_interval"`
	EnableWarmup  bool          `json:"enable_warmup"`
	WarmupPercent float64       `json:"warmup_percent"`
}

// NewLayeredCacheService creates a layered cache service
func NewLayeredCacheService(l1Cache, l2Cache CacheService, config *LayeredCacheConfig) *LayeredCacheService {
	if config == nil {
		config = &LayeredCacheConfig{
			L1TTL:         5 * time.Minute,
			L2TTL:         30 * time.Minute,
			L1MaxSize:     1000,
			WriteThrough:  true,
			ReadThrough:   true,
			SyncInterval:  time.Minute,
			EnableWarmup:  true,
			WarmupPercent: 0.1,
		}
	}

	service := &LayeredCacheService{
		l1Cache: l1Cache,
		l2Cache: l2Cache,
		config:  config,
	}

	// Start sync goroutine
	go service.startSync()

	return service
}

// Get retrieves from L1 first, then L2
func (l *LayeredCacheService) Get(ctx context.Context, key string, dest interface{}) error {
	// Try L1 first
	if err := l.l1Cache.Get(ctx, key, dest); err == nil {
		return nil
	}

	// Try L2
	if l.config.ReadThrough {
		if err := l.l2Cache.Get(ctx, key, dest); err == nil {
			// Store in L1
			l.l1Cache.Set(ctx, key, dest, l.config.L1TTL)
			return nil
		}
	}

	return fmt.Errorf("cache miss for key: %s", key)
}

// Set stores in both L1 and L2
func (l *LayeredCacheService) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	// Store in L1
	if err := l.l1Cache.Set(ctx, key, value, l.config.L1TTL); err != nil {
		return fmt.Errorf("failed to set L1 cache: %w", err)
	}

	// Store in L2 if write-through is enabled
	if l.config.WriteThrough {
		if err := l.l2Cache.Set(ctx, key, value, l.config.L2TTL); err != nil {
			return fmt.Errorf("failed to set L2 cache: %w", err)
		}
	}

	return nil
}

// Delete removes from both L1 and L2
func (l *LayeredCacheService) Delete(ctx context.Context, key string) error {
	// Delete from L1
	l.l1Cache.Delete(ctx, key)

	// Delete from L2
	return l.l2Cache.Delete(ctx, key)
}

// GetMulti retrieves multiple values
func (l *LayeredCacheService) GetMulti(ctx context.Context, keys []string) (map[string]interface{}, error) {
	result := make(map[string]interface{})
	missingKeys := make([]string, 0)

	// Try L1 first
	l1Result, _ := l.l1Cache.GetMulti(ctx, keys)
	for key, value := range l1Result {
		result[key] = value
	}

	// Find missing keys
	for _, key := range keys {
		if _, exists := result[key]; !exists {
			missingKeys = append(missingKeys, key)
		}
	}

	// Try L2 for missing keys
	if len(missingKeys) > 0 && l.config.ReadThrough {
		l2Result, _ := l.l2Cache.GetMulti(ctx, missingKeys)
		for key, value := range l2Result {
			result[key] = value
			// Store in L1
			l.l1Cache.Set(ctx, key, value, l.config.L1TTL)
		}
	}

	return result, nil
}

// SetMulti stores multiple values
func (l *LayeredCacheService) SetMulti(ctx context.Context, items map[string]interface{}, ttl time.Duration) error {
	// Store in L1
	if err := l.l1Cache.SetMulti(ctx, items, l.config.L1TTL); err != nil {
		return fmt.Errorf("failed to set L1 cache: %w", err)
	}

	// Store in L2 if write-through is enabled
	if l.config.WriteThrough {
		if err := l.l2Cache.SetMulti(ctx, items, l.config.L2TTL); err != nil {
			return fmt.Errorf("failed to set L2 cache: %w", err)
		}
	}

	return nil
}

// DeleteMulti removes multiple values
func (l *LayeredCacheService) DeleteMulti(ctx context.Context, keys []string) error {
	// Delete from L1
	l.l1Cache.DeleteMulti(ctx, keys)

	// Delete from L2
	return l.l2Cache.DeleteMulti(ctx, keys)
}

// DeletePattern removes values matching a pattern
func (l *LayeredCacheService) DeletePattern(ctx context.Context, pattern string) error {
	// Delete from L1
	l.l1Cache.DeletePattern(ctx, pattern)

	// Delete from L2
	return l.l2Cache.DeletePattern(ctx, pattern)
}

// WarmCache preloads cache
func (l *LayeredCacheService) WarmCache(ctx context.Context, settingCtx *SettingContext, categories []string) error {
	// Warm L2 cache first
	if err := l.l2Cache.WarmCache(ctx, settingCtx, categories); err != nil {
		return fmt.Errorf("failed to warm L2 cache: %w", err)
	}

	// Warm L1 cache with subset of data
	if l.config.EnableWarmup {
		warmupCount := int(float64(len(categories)) * l.config.WarmupPercent)
		if warmupCount > 0 {
			warmupCategories := categories[:warmupCount]
			if err := l.l1Cache.WarmCache(ctx, settingCtx, warmupCategories); err != nil {
				return fmt.Errorf("failed to warm L1 cache: %w", err)
			}
		}
	}

	return nil
}

// InvalidateSettingCache invalidates cache for a specific setting
func (l *LayeredCacheService) InvalidateSettingCache(ctx context.Context, settingCtx *SettingContext, category, key string) error {
	// Invalidate L1
	if err := l.l1Cache.InvalidateSettingCache(ctx, settingCtx, category, key); err != nil {
		return fmt.Errorf("failed to invalidate L1 cache: %w", err)
	}

	// Invalidate L2
	return l.l2Cache.InvalidateSettingCache(ctx, settingCtx, category, key)
}

// InvalidateContextCache invalidates all cache for a context
func (l *LayeredCacheService) InvalidateContextCache(ctx context.Context, settingCtx *SettingContext) error {
	// Invalidate L1
	if err := l.l1Cache.InvalidateContextCache(ctx, settingCtx); err != nil {
		return fmt.Errorf("failed to invalidate L1 cache: %w", err)
	}

	// Invalidate L2
	return l.l2Cache.InvalidateContextCache(ctx, settingCtx)
}

// GenerateKey generates a cache key
func (l *LayeredCacheService) GenerateKey(settingCtx *SettingContext, category, key string) string {
	return l.l1Cache.GenerateKey(settingCtx, category, key)
}

// GeneratePatternKey generates a cache key pattern
func (l *LayeredCacheService) GeneratePatternKey(settingCtx *SettingContext, category string) string {
	return l.l1Cache.GeneratePatternKey(settingCtx, category)
}

// GetStats returns combined cache statistics
func (l *LayeredCacheService) GetStats(ctx context.Context) (*CacheStats, error) {
	l1Stats, err := l.l1Cache.GetStats(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get L1 stats: %w", err)
	}

	l2Stats, err := l.l2Cache.GetStats(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get L2 stats: %w", err)
	}

	// Combine stats
	return &CacheStats{
		Hits:        l1Stats.Hits + l2Stats.Hits,
		Misses:      l1Stats.Misses + l2Stats.Misses,
		HitRate:     (l1Stats.HitRate + l2Stats.HitRate) / 2,
		Size:        l1Stats.Size + l2Stats.Size,
		MemoryUsage: l1Stats.MemoryUsage + l2Stats.MemoryUsage,
		Uptime:      l1Stats.Uptime,
	}, nil
}

// startSync starts the sync goroutine
func (l *LayeredCacheService) startSync() {
	if l.config.SyncInterval <= 0 {
		return
	}

	ticker := time.NewTicker(l.config.SyncInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			// Sync L1 and L2 caches
			// This would typically sync hot data from L2 to L1
			// For now, we'll just log that sync is running
		}
	}
}
