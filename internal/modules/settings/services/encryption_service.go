package services

import (
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"sync"
	"time"
)

// Encryption<PERSON>ey represents an encryption key with metadata
type EncryptionKey struct {
	ID        string    `json:"id"`
	Key       []byte    `json:"key"`
	Algorithm string    `json:"algorithm"`
	CreatedAt time.Time `json:"created_at"`
	ExpiresAt time.Time `json:"expires_at"`
	IsActive  bool      `json:"is_active"`
}

// encryptionService implements the EncryptionService interface
type encryptionService struct {
	keys       map[string]*EncryptionKey
	currentKey *EncryptionKey
	mutex      sync.RWMutex
}

// NewEncryptionService creates a new encryption service
func NewEncryptionService() (EncryptionService, error) {
	service := &encryptionService{
		keys:  make(map[string]*EncryptionKey),
		mutex: sync.RWMutex{},
	}

	// Generate initial encryption key
	if err := service.generateInitialKey(); err != nil {
		return nil, fmt.Errorf("failed to generate initial encryption key: %w", err)
	}

	return service, nil
}

// Encrypt encrypts plaintext data
func (e *encryptionService) Encrypt(ctx context.Context, plaintext []byte) ([]byte, error) {
	e.mutex.RLock()
	currentKey := e.currentKey
	e.mutex.RUnlock()

	if currentKey == nil {
		return nil, fmt.Errorf("no encryption key available")
	}

	// Create AES cipher
	block, err := aes.NewCipher(currentKey.Key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// Generate nonce
	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return nil, fmt.Errorf("failed to generate nonce: %w", err)
	}

	// Encrypt data
	ciphertext := gcm.Seal(nonce, nonce, plaintext, nil)

	// Prepend key ID and version for future key rotation
	result := fmt.Sprintf("v1:%s:%s", currentKey.ID, base64.StdEncoding.EncodeToString(ciphertext))

	return []byte(result), nil
}

// Decrypt decrypts ciphertext data
func (e *encryptionService) Decrypt(ctx context.Context, ciphertext []byte) ([]byte, error) {
	// Parse the encrypted data format
	parts := strings.Split(string(ciphertext), ":")
	if len(parts) != 3 {
		return nil, fmt.Errorf("invalid encrypted data format")
	}

	version := parts[0]
	keyID := parts[1]
	encryptedData := parts[2]

	// Check version
	if version != "v1" {
		return nil, fmt.Errorf("unsupported encryption version: %s", version)
	}

	// Get the key
	e.mutex.RLock()
	key, exists := e.keys[keyID]
	e.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("encryption key not found: %s", keyID)
	}

	// Decode base64
	encryptedBytes, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decode encrypted data: %w", err)
	}

	// Create AES cipher
	block, err := aes.NewCipher(key.Key)
	if err != nil {
		return nil, fmt.Errorf("failed to create cipher: %w", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("failed to create GCM: %w", err)
	}

	// Extract nonce
	nonceSize := gcm.NonceSize()
	if len(encryptedBytes) < nonceSize {
		return nil, fmt.Errorf("encrypted data too short")
	}

	nonce, cipherData := encryptedBytes[:nonceSize], encryptedBytes[nonceSize:]

	// Decrypt
	plaintext, err := gcm.Open(nil, nonce, cipherData, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to decrypt data: %w", err)
	}

	return plaintext, nil
}

// EncryptJSON encrypts a JSON value
func (e *encryptionService) EncryptJSON(ctx context.Context, value interface{}) ([]byte, error) {
	// Marshal to JSON
	jsonData, err := json.Marshal(value)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal JSON: %w", err)
	}

	// Encrypt JSON data
	return e.Encrypt(ctx, jsonData)
}

// DecryptJSON decrypts a JSON value
func (e *encryptionService) DecryptJSON(ctx context.Context, ciphertext []byte, dest interface{}) error {
	// Decrypt data
	plaintext, err := e.Decrypt(ctx, ciphertext)
	if err != nil {
		return fmt.Errorf("failed to decrypt data: %w", err)
	}

	// Unmarshal JSON
	if err := json.Unmarshal(plaintext, dest); err != nil {
		return fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	return nil
}

// IsEncrypted checks if data is encrypted
func (e *encryptionService) IsEncrypted(data []byte) bool {
	// Check if data starts with our encryption format
	dataStr := string(data)
	return strings.HasPrefix(dataStr, "v1:")
}

// RotateKeys rotates encryption keys
func (e *encryptionService) RotateKeys(ctx context.Context) error {
	e.mutex.Lock()
	defer e.mutex.Unlock()

	// Generate new key
	newKey, err := e.generateKey()
	if err != nil {
		return fmt.Errorf("failed to generate new key: %w", err)
	}

	// Mark old key as inactive
	if e.currentKey != nil {
		e.currentKey.IsActive = false
	}

	// Set new key as current
	e.currentKey = newKey
	e.keys[newKey.ID] = newKey

	return nil
}

// GetCurrentKeyID returns the current key ID
func (e *encryptionService) GetCurrentKeyID(ctx context.Context) (string, error) {
	e.mutex.RLock()
	defer e.mutex.RUnlock()

	if e.currentKey == nil {
		return "", fmt.Errorf("no current encryption key")
	}

	return e.currentKey.ID, nil
}

// Helper methods

func (e *encryptionService) generateInitialKey() error {
	key, err := e.generateKey()
	if err != nil {
		return err
	}

	e.currentKey = key
	e.keys[key.ID] = key

	return nil
}

func (e *encryptionService) generateKey() (*EncryptionKey, error) {
	// Generate 32-byte AES key
	keyBytes := make([]byte, 32)
	if _, err := rand.Read(keyBytes); err != nil {
		return nil, fmt.Errorf("failed to generate key bytes: %w", err)
	}

	// Generate key ID
	keyID := e.generateKeyID(keyBytes)

	key := &EncryptionKey{
		ID:        keyID,
		Key:       keyBytes,
		Algorithm: "AES-256-GCM",
		CreatedAt: time.Now(),
		ExpiresAt: time.Now().Add(365 * 24 * time.Hour), // 1 year
		IsActive:  true,
	}

	return key, nil
}

func (e *encryptionService) generateKeyID(keyBytes []byte) string {
	// Generate a unique key ID based on key content and timestamp
	hash := sha256.Sum256(append(keyBytes, []byte(time.Now().Format(time.RFC3339))...))
	return fmt.Sprintf("key_%x", hash[:8])
}

// Advanced encryption service with key management
type advancedEncryptionService struct {
	*encryptionService
	keyRotationInterval time.Duration
	maxKeyAge           time.Duration
}

// NewAdvancedEncryptionService creates an advanced encryption service with key rotation
func NewAdvancedEncryptionService(keyRotationInterval, maxKeyAge time.Duration) (EncryptionService, error) {
	base, err := NewEncryptionService()
	if err != nil {
		return nil, err
	}

	service := &advancedEncryptionService{
		encryptionService:   base.(*encryptionService),
		keyRotationInterval: keyRotationInterval,
		maxKeyAge:           maxKeyAge,
	}

	// Start key rotation goroutine
	go service.startKeyRotation()

	return service, nil
}

func (e *advancedEncryptionService) startKeyRotation() {
	ticker := time.NewTicker(e.keyRotationInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			e.rotateKeysIfNeeded()
		}
	}
}

func (e *advancedEncryptionService) rotateKeysIfNeeded() {
	e.mutex.RLock()
	currentKey := e.currentKey
	e.mutex.RUnlock()

	if currentKey == nil {
		return
	}

	// Check if current key is too old
	if time.Since(currentKey.CreatedAt) > e.maxKeyAge {
		if err := e.RotateKeys(context.Background()); err != nil {
			// Log error in production
			fmt.Printf("Failed to rotate keys: %v\n", err)
		}
	}
}

// EncryptionManager manages multiple encryption services
type EncryptionManager struct {
	services map[string]EncryptionService
	mutex    sync.RWMutex
}

// NewEncryptionManager creates a new encryption manager
func NewEncryptionManager() *EncryptionManager {
	return &EncryptionManager{
		services: make(map[string]EncryptionService),
		mutex:    sync.RWMutex{},
	}
}

// RegisterService registers an encryption service
func (m *EncryptionManager) RegisterService(name string, service EncryptionService) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.services[name] = service
}

// GetService returns an encryption service by name
func (m *EncryptionManager) GetService(name string) (EncryptionService, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()
	service, exists := m.services[name]
	return service, exists
}

// GetDefaultService returns the default encryption service
func (m *EncryptionManager) GetDefaultService() (EncryptionService, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	if service, exists := m.services["default"]; exists {
		return service, nil
	}

	// Return any available service
	for _, service := range m.services {
		return service, nil
	}

	return nil, fmt.Errorf("no encryption service available")
}

// MultiKeyEncryptionService supports multiple encryption keys
type MultiKeyEncryptionService struct {
	services map[string]EncryptionService
	mutex    sync.RWMutex
}

// NewMultiKeyEncryptionService creates a service that supports multiple encryption keys
func NewMultiKeyEncryptionService() *MultiKeyEncryptionService {
	return &MultiKeyEncryptionService{
		services: make(map[string]EncryptionService),
		mutex:    sync.RWMutex{},
	}
}

// AddKeyService adds an encryption service for a specific key
func (m *MultiKeyEncryptionService) AddKeyService(keyID string, service EncryptionService) {
	m.mutex.Lock()
	defer m.mutex.Unlock()
	m.services[keyID] = service
}

// Encrypt encrypts data using the primary key
func (m *MultiKeyEncryptionService) Encrypt(ctx context.Context, plaintext []byte) ([]byte, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	// Use the first available service (primary key)
	for _, service := range m.services {
		return service.Encrypt(ctx, plaintext)
	}

	return nil, fmt.Errorf("no encryption service available")
}

// Decrypt decrypts data using the appropriate key
func (m *MultiKeyEncryptionService) Decrypt(ctx context.Context, ciphertext []byte) ([]byte, error) {
	// Parse key ID from encrypted data
	parts := strings.Split(string(ciphertext), ":")
	if len(parts) != 3 {
		return nil, fmt.Errorf("invalid encrypted data format")
	}

	keyID := parts[1]

	m.mutex.RLock()
	service, exists := m.services[keyID]
	m.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("no decryption service for key: %s", keyID)
	}

	return service.Decrypt(ctx, ciphertext)
}

// EncryptJSON encrypts JSON data
func (m *MultiKeyEncryptionService) EncryptJSON(ctx context.Context, value interface{}) ([]byte, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	for _, service := range m.services {
		return service.EncryptJSON(ctx, value)
	}

	return nil, fmt.Errorf("no encryption service available")
}

// DecryptJSON decrypts JSON data
func (m *MultiKeyEncryptionService) DecryptJSON(ctx context.Context, ciphertext []byte, dest interface{}) error {
	// Parse key ID from encrypted data
	parts := strings.Split(string(ciphertext), ":")
	if len(parts) != 3 {
		return fmt.Errorf("invalid encrypted data format")
	}

	keyID := parts[1]

	m.mutex.RLock()
	service, exists := m.services[keyID]
	m.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("no decryption service for key: %s", keyID)
	}

	return service.DecryptJSON(ctx, ciphertext, dest)
}

// IsEncrypted checks if data is encrypted
func (m *MultiKeyEncryptionService) IsEncrypted(data []byte) bool {
	dataStr := string(data)
	return strings.HasPrefix(dataStr, "v1:")
}

// RotateKeys rotates all encryption keys
func (m *MultiKeyEncryptionService) RotateKeys(ctx context.Context) error {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	for _, service := range m.services {
		if err := service.RotateKeys(ctx); err != nil {
			return fmt.Errorf("failed to rotate keys: %w", err)
		}
	}

	return nil
}

// GetCurrentKeyID returns the current key ID
func (m *MultiKeyEncryptionService) GetCurrentKeyID(ctx context.Context) (string, error) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	for _, service := range m.services {
		return service.GetCurrentKeyID(ctx)
	}

	return "", fmt.Errorf("no encryption service available")
}
