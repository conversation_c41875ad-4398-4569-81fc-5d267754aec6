package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
)

// SubscriptionRepository defines the interface for subscription data operations
type SubscriptionRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, subscription *models.Subscription) (*models.Subscription, error)
	GetByID(ctx context.Context, id uint) (*models.Subscription, error)
	Update(ctx context.Context, subscription *models.Subscription) error
	Delete(ctx context.Context, id uint) error

	// Subscription-specific operations
	GetActiveByTenantID(ctx context.Context, tenantID uint) (*models.Subscription, error)
	GetByTenantID(ctx context.Context, tenantID uint, filters *models.SubscriptionFilter) ([]*models.Subscription, error)
	GetExpiredTrials(ctx context.Context) ([]*models.Subscription, error)
	GetSubscriptionsForRenewal(ctx context.Context) ([]*models.Subscription, error)

	// Payment failure operations
	CreatePaymentFailure(ctx context.Context, failure *models.PaymentFailure) (*models.PaymentFailure, error)
	GetPaymentFailuresBySubscription(ctx context.Context, subscriptionID uint) ([]*models.PaymentFailure, error)
	UpdatePaymentFailure(ctx context.Context, failure *models.PaymentFailure) error

	// Plan transition operations
	CreatePlanTransition(ctx context.Context, transition *models.PlanTransition) (*models.PlanTransition, error)
	GetPlanTransitionsBySubscription(ctx context.Context, subscriptionID uint) ([]*models.PlanTransition, error)
	GetPendingTransitions(ctx context.Context) ([]*models.PlanTransition, error)
	UpdatePlanTransition(ctx context.Context, transition *models.PlanTransition) error

	// Filtering and pagination
	List(ctx context.Context, filters *models.SubscriptionFilter) ([]*models.Subscription, int64, error)
}
