package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// MySQLTenantFeatureRepository implements TenantFeatureRepository using MySQL
type MySQLTenantFeatureRepository struct {
	db *gorm.DB
}

// NewMySQLTenantFeatureRepository creates a new MySQL tenant feature repository
func NewMySQLTenantFeatureRepository(db *gorm.DB) TenantFeatureRepository {
	return &MySQLTenantFeatureRepository{db: db}
}

// Create creates a new feature for a tenant
func (r *MySQLTenantFeatureRepository) Create(ctx context.Context, feature *models.TenantFeature) error {
	return r.db.WithContext(ctx).Create(feature).Error
}

// GetByKey retrieves a feature by tenant ID and feature key
func (r *MySQLTenantFeatureRepository) GetByKey(ctx context.Context, tenantID uint, featureKey string) (*models.TenantFeature, error) {
	var feature models.TenantFeature
	err := r.db.WithContext(ctx).
		Preload("FeatureCatalog").
		Where("tenant_id = ? AND feature_key = ?", tenantID, featureKey).
		First(&feature).Error

	if err != nil {
		return nil, err
	}
	return &feature, nil
}

// Update updates a feature
func (r *MySQLTenantFeatureRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	// Remove fields that shouldn't be updated directly
	delete(updates, "id")
	delete(updates, "tenant_id")
	delete(updates, "feature_key")
	delete(updates, "created_at")

	// Update enabled/disabled timestamps based on enabled status
	if enabled, ok := updates["enabled"].(bool); ok {
		now := time.Now()
		if enabled {
			updates["enabled_at"] = now
			updates["disabled_at"] = nil
		} else {
			updates["disabled_at"] = now
		}
	}

	return r.db.WithContext(ctx).
		Model(&models.TenantFeature{}).
		Where("id = ?", id).
		Updates(updates).Error
}

// Upsert creates or updates a feature
func (r *MySQLTenantFeatureRepository) Upsert(ctx context.Context, feature *models.TenantFeature) error {
	// Set enabled/disabled timestamps
	now := time.Now()
	if feature.Enabled {
		feature.EnabledAt = &now
		feature.DisabledAt = nil
	} else {
		feature.DisabledAt = &now
	}

	return r.db.WithContext(ctx).
		Clauses(clause.OnConflict{
			Columns: []clause.Column{{Name: "tenant_id"}, {Name: "feature_key"}},
			DoUpdates: clause.AssignmentColumns([]string{
				"feature_name", "description", "enabled", "configuration",
				"rollout_percentage", "rollout_groups", "available_from", "available_until",
				"requires_features", "conflicts_with", "enabled_at", "enabled_by",
				"disabled_at", "disabled_by", "updated_at",
			}),
		}).
		Create(feature).Error
}

// Delete deletes a feature
func (r *MySQLTenantFeatureRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.TenantFeature{}, id).Error
}

// DeleteByKey deletes a feature by tenant ID and feature key
func (r *MySQLTenantFeatureRepository) DeleteByKey(ctx context.Context, tenantID uint, featureKey string) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND feature_key = ?", tenantID, featureKey).
		Delete(&models.TenantFeature{}).Error
}

// List retrieves features based on filter
func (r *MySQLTenantFeatureRepository) List(ctx context.Context, filter models.FeatureFilter) ([]*models.TenantFeature, error) {
	query := r.db.WithContext(ctx).Model(&models.TenantFeature{}).
		Preload("FeatureCatalog")

	// Apply filters
	if filter.TenantID > 0 {
		query = query.Where("tenant_id = ?", filter.TenantID)
	}

	if filter.Enabled != nil {
		query = query.Where("enabled = ?", *filter.Enabled)
	}

	if filter.Search != "" {
		search := "%" + filter.Search + "%"
		query = query.Where("feature_key LIKE ? OR feature_name LIKE ? OR description LIKE ?",
			search, search, search)
	}

	if len(filter.Categories) > 0 {
		// Join with feature catalog to filter by category
		query = query.Joins("JOIN feature_catalog ON tenant_features.feature_key = feature_catalog.feature_key").
			Where("feature_catalog.category IN ?", filter.Categories)
	}

	// Order by feature name
	query = query.Order("feature_name ASC")

	// Fetch records
	var features []*models.TenantFeature
	if err := query.Find(&features).Error; err != nil {
		return nil, err
	}

	return features, nil
}

// GetEnabledFeatures retrieves all enabled features for a tenant
func (r *MySQLTenantFeatureRepository) GetEnabledFeatures(ctx context.Context, tenantID uint) ([]*models.TenantFeature, error) {
	var features []*models.TenantFeature

	now := time.Now()
	err := r.db.WithContext(ctx).
		Preload("FeatureCatalog").
		Where("tenant_id = ? AND enabled = ?", tenantID, true).
		Where("(available_from IS NULL OR available_from <= ?)", now).
		Where("(available_until IS NULL OR available_until >= ?)", now).
		Find(&features).Error

	return features, err
}

// BulkToggle enables/disables multiple features
func (r *MySQLTenantFeatureRepository) BulkToggle(ctx context.Context, tenantID uint, featureKeys []string, enabled bool) error {
	if len(featureKeys) == 0 {
		return nil
	}

	now := time.Now()
	updates := map[string]interface{}{
		"enabled":    enabled,
		"updated_at": now,
	}

	if enabled {
		updates["enabled_at"] = now
		updates["disabled_at"] = nil
	} else {
		updates["disabled_at"] = now
	}

	return r.db.WithContext(ctx).
		Model(&models.TenantFeature{}).
		Where("tenant_id = ? AND feature_key IN ?", tenantID, featureKeys).
		Updates(updates).Error
}
