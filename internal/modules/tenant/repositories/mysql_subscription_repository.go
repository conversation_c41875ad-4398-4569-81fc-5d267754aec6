package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"gorm.io/gorm"
)

// MySQLSubscriptionRepository implements SubscriptionRepository using MySQL/GORM
type MySQLSubscriptionRepository struct {
	db *gorm.DB
}

// NewMySQLSubscriptionRepository creates a new MySQL subscription repository
func NewMySQLSubscriptionRepository(db *gorm.DB) SubscriptionRepository {
	return &MySQLSubscriptionRepository{db: db}
}

// Create creates a new subscription
func (r *MySQLSubscriptionRepository) Create(ctx context.Context, subscription *models.Subscription) (*models.Subscription, error) {
	if err := r.db.WithContext(ctx).Create(subscription).Error; err != nil {
		return nil, err
	}
	return subscription, nil
}

// GetByID retrieves a subscription by ID
func (r *MySQLSubscriptionRepository) GetByID(ctx context.Context, id uint) (*models.Subscription, error) {
	var subscription models.Subscription
	err := r.db.WithContext(ctx).
		Preload("Tenant").
		Preload("Plan").
		First(&subscription, id).Error
	if err != nil {
		return nil, err
	}
	return &subscription, nil
}

// Update updates a subscription
func (r *MySQLSubscriptionRepository) Update(ctx context.Context, subscription *models.Subscription) error {
	return r.db.WithContext(ctx).Save(subscription).Error
}

// Delete soft deletes a subscription by setting status to canceled
func (r *MySQLSubscriptionRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.Subscription{}).
		Where("id = ?", id).
		Update("status", models.SubscriptionStatusCanceled).Error
}

// GetActiveByTenantID retrieves the active subscription for a tenant
func (r *MySQLSubscriptionRepository) GetActiveByTenantID(ctx context.Context, tenantID uint) (*models.Subscription, error) {
	var subscription models.Subscription
	err := r.db.WithContext(ctx).
		Preload("Tenant").
		Preload("Plan").
		Where("tenant_id = ? AND status IN (?)", tenantID, []models.SubscriptionStatus{
			models.SubscriptionStatusActive,
			models.SubscriptionStatusTrialing,
			models.SubscriptionStatusPastDue,
		}).
		First(&subscription).Error
	if err != nil {
		return nil, err
	}
	return &subscription, nil
}

// GetByTenantID retrieves subscriptions for a tenant with filters
func (r *MySQLSubscriptionRepository) GetByTenantID(ctx context.Context, tenantID uint, filters *models.SubscriptionFilter) ([]*models.Subscription, error) {
	query := r.db.WithContext(ctx).
		Preload("Tenant").
		Preload("Plan").
		Where("tenant_id = ?", tenantID)

	if filters != nil {
		if filters.Status != "" {
			query = query.Where("status = ?", filters.Status)
		}
		if filters.BillingCycle != "" {
			query = query.Where("billing_cycle = ?", filters.BillingCycle)
		}
	}

	var subscriptions []*models.Subscription
	err := query.Find(&subscriptions).Error
	return subscriptions, err
}

// GetExpiredTrials retrieves subscriptions with expired trials
func (r *MySQLSubscriptionRepository) GetExpiredTrials(ctx context.Context) ([]*models.Subscription, error) {
	var subscriptions []*models.Subscription
	err := r.db.WithContext(ctx).
		Preload("Tenant").
		Preload("Plan").
		Where("status = ? AND trial_ends_at IS NOT NULL AND trial_ends_at <= ?",
			models.SubscriptionStatusTrialing, time.Now()).
		Find(&subscriptions).Error
	return subscriptions, err
}

// GetSubscriptionsForRenewal retrieves subscriptions that need renewal
func (r *MySQLSubscriptionRepository) GetSubscriptionsForRenewal(ctx context.Context) ([]*models.Subscription, error) {
	var subscriptions []*models.Subscription
	err := r.db.WithContext(ctx).
		Preload("Tenant").
		Preload("Plan").
		Where("status = ? AND next_billing_date IS NOT NULL AND next_billing_date <= ?",
			models.SubscriptionStatusActive, time.Now()).
		Find(&subscriptions).Error
	return subscriptions, err
}

// CreatePaymentFailure creates a new payment failure record
func (r *MySQLSubscriptionRepository) CreatePaymentFailure(ctx context.Context, failure *models.PaymentFailure) (*models.PaymentFailure, error) {
	if err := r.db.WithContext(ctx).Create(failure).Error; err != nil {
		return nil, err
	}
	return failure, nil
}

// GetPaymentFailuresBySubscription retrieves payment failures for a subscription
func (r *MySQLSubscriptionRepository) GetPaymentFailuresBySubscription(ctx context.Context, subscriptionID uint) ([]*models.PaymentFailure, error) {
	var failures []*models.PaymentFailure
	err := r.db.WithContext(ctx).
		Where("subscription_id = ?", subscriptionID).
		Order("created_at DESC").
		Find(&failures).Error
	return failures, err
}

// UpdatePaymentFailure updates a payment failure record
func (r *MySQLSubscriptionRepository) UpdatePaymentFailure(ctx context.Context, failure *models.PaymentFailure) error {
	return r.db.WithContext(ctx).Save(failure).Error
}

// CreatePlanTransition creates a new plan transition record
func (r *MySQLSubscriptionRepository) CreatePlanTransition(ctx context.Context, transition *models.PlanTransition) (*models.PlanTransition, error) {
	if err := r.db.WithContext(ctx).Create(transition).Error; err != nil {
		return nil, err
	}
	return transition, nil
}

// GetPlanTransitionsBySubscription retrieves plan transitions for a subscription
func (r *MySQLSubscriptionRepository) GetPlanTransitionsBySubscription(ctx context.Context, subscriptionID uint) ([]*models.PlanTransition, error) {
	var transitions []*models.PlanTransition
	err := r.db.WithContext(ctx).
		Preload("FromPlan").
		Preload("ToPlan").
		Where("subscription_id = ?", subscriptionID).
		Order("created_at DESC").
		Find(&transitions).Error
	return transitions, err
}

// GetPendingTransitions retrieves plan transitions that need to be processed
func (r *MySQLSubscriptionRepository) GetPendingTransitions(ctx context.Context) ([]*models.PlanTransition, error) {
	var transitions []*models.PlanTransition
	err := r.db.WithContext(ctx).
		Preload("FromPlan").
		Preload("ToPlan").
		Where("processed_at IS NULL AND effective_date <= ?", time.Now()).
		Find(&transitions).Error
	return transitions, err
}

// UpdatePlanTransition updates a plan transition record
func (r *MySQLSubscriptionRepository) UpdatePlanTransition(ctx context.Context, transition *models.PlanTransition) error {
	return r.db.WithContext(ctx).Save(transition).Error
}

// List retrieves subscriptions with filtering and pagination
func (r *MySQLSubscriptionRepository) List(ctx context.Context, filters *models.SubscriptionFilter) ([]*models.Subscription, int64, error) {
	query := r.db.WithContext(ctx).
		Preload("Tenant").
		Preload("Plan").
		Model(&models.Subscription{})

	// Apply filters
	if filters != nil {
		if filters.TenantID > 0 {
			query = query.Where("tenant_id = ?", filters.TenantID)
		}
		if filters.PlanID > 0 {
			query = query.Where("plan_id = ?", filters.PlanID)
		}
		if filters.Status != "" {
			query = query.Where("status = ?", filters.Status)
		}
		if filters.BillingCycle != "" {
			query = query.Where("billing_cycle = ?", filters.BillingCycle)
		}
	}

	// Get total count
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and sorting
	if filters != nil {
		if filters.PageSize > 0 {
			offset := 0
			if filters.Page > 1 {
				offset = (filters.Page - 1) * filters.PageSize
			}
			query = query.Offset(offset).Limit(filters.PageSize)
		}

		// Apply sorting
		orderBy := "created_at DESC"
		if filters.SortBy != "" {
			orderBy = filters.SortBy
			if filters.SortOrder != "" {
				orderBy += " " + filters.SortOrder
			}
		}
		query = query.Order(orderBy)
	}

	var subscriptions []*models.Subscription
	err := query.Find(&subscriptions).Error
	return subscriptions, total, err
}
