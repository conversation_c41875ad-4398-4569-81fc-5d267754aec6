package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
)

// TenantRepository defines the interface for tenant data operations
type TenantRepository interface {
	// Create creates a new tenant
	Create(ctx context.Context, tenant *models.Tenant) error

	// GetByID retrieves a tenant by ID
	GetByID(ctx context.Context, id uint) (*models.Tenant, error)

	// GetBySlug retrieves a tenant by slug
	GetBySlug(ctx context.Context, slug string) (*models.Tenant, error)

	// GetByDomain retrieves a tenant by domain
	GetByDomain(ctx context.Context, domain string) (*models.Tenant, error)

	// GetByOwnerEmail retrieves tenants by owner email
	GetByOwnerEmail(ctx context.Context, email string) ([]*models.Tenant, error)

	// Update updates a tenant
	Update(ctx context.Context, id uint, updates map[string]interface{}) error

	// Delete soft deletes a tenant (sets status to deleted)
	Delete(ctx context.Context, id uint) error

	// List retrieves tenants based on filter
	List(ctx context.Context, filter models.TenantFilter) ([]*models.Tenant, int64, error)

	// CheckSlugExists checks if a slug already exists
	CheckSlugExists(ctx context.Context, slug string, excludeID uint) (bool, error)

	// CheckDomainExists checks if a domain already exists
	CheckDomainExists(ctx context.Context, domain string, excludeID uint) (bool, error)
}

// TenantPlanRepository defines the interface for tenant plan data operations
type TenantPlanRepository interface {
	// Create creates a new plan
	Create(ctx context.Context, plan *models.TenantPlan) error

	// GetByID retrieves a plan by ID
	GetByID(ctx context.Context, id uint) (*models.TenantPlan, error)

	// GetBySlug retrieves a plan by slug
	GetBySlug(ctx context.Context, slug string) (*models.TenantPlan, error)

	// Update updates a plan
	Update(ctx context.Context, id uint, updates map[string]interface{}) error

	// Delete soft deletes a plan (sets status to inactive)
	Delete(ctx context.Context, id uint) error

	// List retrieves plans based on filter
	List(ctx context.Context, filter models.PlanFilter) ([]*models.TenantPlan, int64, error)

	// GetActivePlans retrieves all active plans
	GetActivePlans(ctx context.Context) ([]*models.TenantPlan, error)

	// CheckSlugExists checks if a slug already exists
	CheckSlugExists(ctx context.Context, slug string, excludeID uint) (bool, error)
}

// TenantSettingRepository defines the interface for tenant setting data operations
type TenantSettingRepository interface {
	// Create creates a new setting
	Create(ctx context.Context, setting *models.TenantSetting) error

	// GetByKey retrieves a setting by tenant ID, category and key
	GetByKey(ctx context.Context, tenantID uint, category, key string) (*models.TenantSetting, error)

	// Update updates a setting
	Update(ctx context.Context, id uint, updates map[string]interface{}) error

	// Upsert creates or updates a setting
	Upsert(ctx context.Context, setting *models.TenantSetting) error

	// Delete deletes a setting
	Delete(ctx context.Context, id uint) error

	// DeleteByKey deletes a setting by tenant ID, category and key
	DeleteByKey(ctx context.Context, tenantID uint, category, key string) error

	// List retrieves settings based on filter
	List(ctx context.Context, filter models.SettingFilter) ([]*models.TenantSetting, error)

	// BulkUpsert creates or updates multiple settings
	BulkUpsert(ctx context.Context, tenantID uint, settings []models.SettingUpdate) error

	// GetByCategory retrieves all settings for a tenant in a category
	GetByCategory(ctx context.Context, tenantID uint, category string) ([]*models.TenantSetting, error)
}

// TenantFeatureRepository defines the interface for tenant feature data operations
type TenantFeatureRepository interface {
	// Create creates a new feature for a tenant
	Create(ctx context.Context, feature *models.TenantFeature) error

	// GetByKey retrieves a feature by tenant ID and feature key
	GetByKey(ctx context.Context, tenantID uint, featureKey string) (*models.TenantFeature, error)

	// Update updates a feature
	Update(ctx context.Context, id uint, updates map[string]interface{}) error

	// Upsert creates or updates a feature
	Upsert(ctx context.Context, feature *models.TenantFeature) error

	// Delete deletes a feature
	Delete(ctx context.Context, id uint) error

	// DeleteByKey deletes a feature by tenant ID and feature key
	DeleteByKey(ctx context.Context, tenantID uint, featureKey string) error

	// List retrieves features based on filter
	List(ctx context.Context, filter models.FeatureFilter) ([]*models.TenantFeature, error)

	// GetEnabledFeatures retrieves all enabled features for a tenant
	GetEnabledFeatures(ctx context.Context, tenantID uint) ([]*models.TenantFeature, error)

	// BulkToggle enables/disables multiple features
	BulkToggle(ctx context.Context, tenantID uint, featureKeys []string, enabled bool) error
}

// FeatureCatalogRepository defines the interface for feature catalog data operations
type FeatureCatalogRepository interface {
	// Create creates a new feature in catalog
	Create(ctx context.Context, feature *models.FeatureCatalog) error

	// GetByID retrieves a feature by ID
	GetByID(ctx context.Context, id uint) (*models.FeatureCatalog, error)

	// GetByKey retrieves a feature by key
	GetByKey(ctx context.Context, featureKey string) (*models.FeatureCatalog, error)

	// Update updates a feature
	Update(ctx context.Context, id uint, updates map[string]interface{}) error

	// Delete soft deletes a feature (sets status to deprecated)
	Delete(ctx context.Context, id uint) error

	// List retrieves features based on filter
	List(ctx context.Context, filter models.FeatureCatalogFilter) ([]*models.FeatureCatalog, int64, error)

	// GetActiveFeatures retrieves all active features
	GetActiveFeatures(ctx context.Context) ([]*models.FeatureCatalog, error)

	// GetFeaturesForPlan retrieves features available for a specific plan
	GetFeaturesForPlan(ctx context.Context, planID uint) ([]*models.FeatureCatalog, error)

	// CheckKeyExists checks if a feature key already exists
	CheckKeyExists(ctx context.Context, featureKey string, excludeID uint) (bool, error)
}
