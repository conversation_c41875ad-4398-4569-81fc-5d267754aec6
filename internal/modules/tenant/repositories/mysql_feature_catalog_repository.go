package repositories

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"gorm.io/gorm"
)

// MySQLFeatureCatalogRepository implements FeatureCatalogRepository using MySQL
type MySQLFeatureCatalogRepository struct {
	db *gorm.DB
}

// NewMySQLFeatureCatalogRepository creates a new MySQL feature catalog repository
func NewMySQLFeatureCatalogRepository(db *gorm.DB) FeatureCatalogRepository {
	return &MySQLFeatureCatalogRepository{db: db}
}

// Create creates a new feature in catalog
func (r *MySQLFeatureCatalogRepository) Create(ctx context.Context, feature *models.FeatureCatalog) error {
	return r.db.WithContext(ctx).Create(feature).Error
}

// GetByID retrieves a feature by ID
func (r *MySQLFeatureCatalogRepository) GetByID(ctx context.Context, id uint) (*models.FeatureCatalog, error) {
	var feature models.FeatureCatalog
	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		First(&feature).Error

	if err != nil {
		return nil, err
	}

	// Load adoption statistics
	r.loadFeatureStats(ctx, &feature)

	return &feature, nil
}

// GetByKey retrieves a feature by key
func (r *MySQLFeatureCatalogRepository) GetByKey(ctx context.Context, featureKey string) (*models.FeatureCatalog, error) {
	var feature models.FeatureCatalog
	err := r.db.WithContext(ctx).
		Where("feature_key = ?", featureKey).
		First(&feature).Error

	if err != nil {
		return nil, err
	}

	// Load adoption statistics
	r.loadFeatureStats(ctx, &feature)

	return &feature, nil
}

// Update updates a feature
func (r *MySQLFeatureCatalogRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	// Remove fields that shouldn't be updated directly
	delete(updates, "id")
	delete(updates, "feature_key") // Feature key shouldn't be changed
	delete(updates, "created_at")

	// Handle plan arrays
	if requiredPlans, ok := updates["required_plans"].([]uint); ok {
		plansJSON, _ := json.Marshal(requiredPlans)
		updates["required_plans"] = plansJSON
	}

	if excludedPlans, ok := updates["excluded_plans"].([]uint); ok {
		plansJSON, _ := json.Marshal(excludedPlans)
		updates["excluded_plans"] = plansJSON
	}

	return r.db.WithContext(ctx).
		Model(&models.FeatureCatalog{}).
		Where("id = ?", id).
		Updates(updates).Error
}

// Delete soft deletes a feature (sets status to deprecated)
func (r *MySQLFeatureCatalogRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.FeatureCatalog{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"status":        models.FeatureStatusDeprecated,
			"is_deprecated": true,
		}).Error
}

// List retrieves features based on filter
func (r *MySQLFeatureCatalogRepository) List(ctx context.Context, filter models.FeatureCatalogFilter) ([]*models.FeatureCatalog, int64, error) {
	query := r.db.WithContext(ctx).Model(&models.FeatureCatalog{})

	// Apply filters
	if filter.Category != "" {
		query = query.Where("category = ?", filter.Category)
	}

	if len(filter.Categories) > 0 {
		query = query.Where("category IN ?", filter.Categories)
	}

	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}

	if filter.IsBeta != nil {
		query = query.Where("is_beta = ?", *filter.IsBeta)
	}

	if filter.IsExperimental != nil {
		query = query.Where("is_experimental = ?", *filter.IsExperimental)
	}

	if filter.IsDeprecated != nil {
		query = query.Where("is_deprecated = ?", *filter.IsDeprecated)
	}

	if filter.Search != "" {
		search := "%" + filter.Search + "%"
		query = query.Where("feature_key LIKE ? OR feature_name LIKE ? OR description LIKE ?",
			search, search, search)
	}

	// Filter by plan availability
	if filter.PlanID > 0 {
		// Features that are either not plan-restricted or available for the specific plan
		query = query.Where(`
			(required_plans IS NULL OR JSON_CONTAINS(required_plans, ?, '$')) 
			AND (excluded_plans IS NULL OR NOT JSON_CONTAINS(excluded_plans, ?, '$'))
		`, filter.PlanID, filter.PlanID)
	}

	// Count total records
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	sortBy := filter.SortBy
	if sortBy == "" {
		sortBy = "feature_name"
	}
	sortOrder := filter.SortOrder
	if sortOrder == "" {
		sortOrder = "asc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if filter.Page <= 0 {
		filter.Page = 1
	}
	if filter.PageSize <= 0 {
		filter.PageSize = 20
	}
	offset := (filter.Page - 1) * filter.PageSize
	query = query.Offset(offset).Limit(filter.PageSize)

	// Fetch records
	var features []*models.FeatureCatalog
	if err := query.Find(&features).Error; err != nil {
		return nil, 0, err
	}

	// Load adoption statistics for each feature
	for i := range features {
		r.loadFeatureStats(ctx, features[i])
	}

	return features, total, nil
}

// GetActiveFeatures retrieves all active features
func (r *MySQLFeatureCatalogRepository) GetActiveFeatures(ctx context.Context) ([]*models.FeatureCatalog, error) {
	var features []*models.FeatureCatalog
	err := r.db.WithContext(ctx).
		Where("status = ? AND is_deprecated = ?", models.FeatureStatusActive, false).
		Order("category ASC, feature_name ASC").
		Find(&features).Error

	// Load adoption statistics for each feature
	for i := range features {
		r.loadFeatureStats(ctx, features[i])
	}

	return features, err
}

// GetFeaturesForPlan retrieves features available for a specific plan
func (r *MySQLFeatureCatalogRepository) GetFeaturesForPlan(ctx context.Context, planID uint) ([]*models.FeatureCatalog, error) {
	var features []*models.FeatureCatalog

	// Get features that are either not plan-restricted or available for the specific plan
	err := r.db.WithContext(ctx).
		Where("status = ? AND is_deprecated = ?", models.FeatureStatusActive, false).
		Where(`
			(required_plans IS NULL OR JSON_CONTAINS(required_plans, ?, '$')) 
			AND (excluded_plans IS NULL OR NOT JSON_CONTAINS(excluded_plans, ?, '$'))
		`, planID, planID).
		Order("category ASC, feature_name ASC").
		Find(&features).Error

	return features, err
}

// CheckKeyExists checks if a feature key already exists
func (r *MySQLFeatureCatalogRepository) CheckKeyExists(ctx context.Context, featureKey string, excludeID uint) (bool, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.FeatureCatalog{}).
		Where("feature_key = ?", featureKey)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	err := query.Count(&count).Error
	return count > 0, err
}

// loadFeatureStats loads adoption statistics for a feature
func (r *MySQLFeatureCatalogRepository) loadFeatureStats(ctx context.Context, feature *models.FeatureCatalog) {
	// Count enabled tenants
	var enabledCount int64
	r.db.WithContext(ctx).
		Model(&models.TenantFeature{}).
		Where("feature_key = ? AND enabled = ?", feature.FeatureKey, true).
		Count(&enabledCount)

	// Count total tenants
	var totalTenants int64
	r.db.WithContext(ctx).
		Model(&models.Tenant{}).
		Where("status != ?", models.TenantStatusDeleted).
		Count(&totalTenants)

	feature.EnabledTenantsCount = int(enabledCount)
	if totalTenants > 0 {
		feature.AdoptionRate = float64(enabledCount) / float64(totalTenants) * 100
	}
}
