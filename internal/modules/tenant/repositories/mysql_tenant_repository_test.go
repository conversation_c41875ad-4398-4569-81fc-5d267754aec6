package repositories

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupTestDB(t *testing.T) *gorm.DB {
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// Auto migrate the schema
	err = db.AutoMigrate(&models.Tenant{}, &models.TenantPlan{})
	require.NoError(t, err)

	return db
}

func TestMySQLTenantRepository_Create(t *testing.T) {
	db := setupTestDB(t)
	repo := NewMySQLTenantRepository(db)
	ctx := context.Background()

	tenant := &models.Tenant{
		Name:         "Test Company",
		Domain:       "test.example.com",
		Slug:         "test-company",
		PlanID:       1,
		Status:       models.TenantStatusActive,
		OwnerEmail:   "<EMAIL>",
		BillingEmail: "<EMAIL>",
		Settings:     models.JSONMap{},
	}

	err := repo.Create(ctx, tenant)
	assert.NoError(t, err)
	assert.NotZero(t, tenant.ID)
}

func TestMySQLTenantRepository_GetByID(t *testing.T) {
	db := setupTestDB(t)
	repo := NewMySQLTenantRepository(db)
	ctx := context.Background()

	// Create a tenant first
	tenant := &models.Tenant{
		Name:         "Test Company",
		Domain:       "test.example.com",
		Slug:         "test-company",
		PlanID:       1,
		Status:       models.TenantStatusActive,
		OwnerEmail:   "<EMAIL>",
		BillingEmail: "<EMAIL>",
		Settings:     models.JSONMap{},
	}
	err := repo.Create(ctx, tenant)
	require.NoError(t, err)

	// Test GetByID
	retrieved, err := repo.GetByID(ctx, tenant.ID)
	assert.NoError(t, err)
	assert.Equal(t, tenant.Name, retrieved.Name)
	assert.Equal(t, tenant.Domain, retrieved.Domain)
}

func TestMySQLTenantRepository_CheckDomainExists(t *testing.T) {
	db := setupTestDB(t)
	repo := NewMySQLTenantRepository(db)
	ctx := context.Background()

	// Test domain that doesn't exist
	exists, err := repo.CheckDomainExists(ctx, "nonexistent.example.com", 0)
	assert.NoError(t, err)
	assert.False(t, exists)

	// Create a tenant
	tenant := &models.Tenant{
		Name:         "Test Company",
		Domain:       "test.example.com",
		Slug:         "test-company",
		PlanID:       1,
		Status:       models.TenantStatusActive,
		OwnerEmail:   "<EMAIL>",
		BillingEmail: "<EMAIL>",
		Settings:     models.JSONMap{},
	}
	err = repo.Create(ctx, tenant)
	require.NoError(t, err)

	// Test domain that exists
	exists, err = repo.CheckDomainExists(ctx, "test.example.com", 0)
	assert.NoError(t, err)
	assert.True(t, exists)

	// Test excluding the same tenant ID
	exists, err = repo.CheckDomainExists(ctx, "test.example.com", tenant.ID)
	assert.NoError(t, err)
	assert.False(t, exists)
}
