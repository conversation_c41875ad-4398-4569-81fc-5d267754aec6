package repositories

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// MySQLTenantSettingRepository implements TenantSettingRepository using MySQL
type MySQLTenantSettingRepository struct {
	db *gorm.DB
}

// NewMySQLTenantSettingRepository creates a new MySQL tenant setting repository
func NewMySQLTenantSettingRepository(db *gorm.DB) TenantSettingRepository {
	return &MySQLTenantSettingRepository{db: db}
}

// Create creates a new setting
func (r *MySQLTenantSettingRepository) Create(ctx context.Context, setting *models.TenantSetting) error {
	return r.db.WithContext(ctx).Create(setting).Error
}

// GetByKey retrieves a setting by tenant ID, category and key
func (r *MySQLTenantSettingRepository) GetByKey(ctx context.Context, tenantID uint, category, key string) (*models.TenantSetting, error) {
	var setting models.TenantSetting
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND category = ? AND `key` = ?", tenantID, category, key).
		First(&setting).Error

	if err != nil {
		return nil, err
	}
	return &setting, nil
}

// Update updates a setting
func (r *MySQLTenantSettingRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	// Remove fields that shouldn't be updated directly
	delete(updates, "id")
	delete(updates, "tenant_id")
	delete(updates, "category")
	delete(updates, "key")
	delete(updates, "created_at")

	return r.db.WithContext(ctx).
		Model(&models.TenantSetting{}).
		Where("id = ?", id).
		Updates(updates).Error
}

// Upsert creates or updates a setting
func (r *MySQLTenantSettingRepository) Upsert(ctx context.Context, setting *models.TenantSetting) error {
	// Value is already json.RawMessage, no conversion needed

	return r.db.WithContext(ctx).
		Clauses(clause.OnConflict{
			Columns:   []clause.Column{{Name: "tenant_id"}, {Name: "category"}, {Name: "key"}},
			DoUpdates: clause.AssignmentColumns([]string{"value", "description", "validation_rules", "default_value", "is_encrypted", "is_public", "updated_by", "updated_at"}),
		}).
		Create(setting).Error
}

// Delete deletes a setting
func (r *MySQLTenantSettingRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.TenantSetting{}, id).Error
}

// DeleteByKey deletes a setting by tenant ID, category and key
func (r *MySQLTenantSettingRepository) DeleteByKey(ctx context.Context, tenantID uint, category, key string) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND category = ? AND `key` = ?", tenantID, category, key).
		Delete(&models.TenantSetting{}).Error
}

// List retrieves settings based on filter
func (r *MySQLTenantSettingRepository) List(ctx context.Context, filter models.SettingFilter) ([]*models.TenantSetting, error) {
	query := r.db.WithContext(ctx).Model(&models.TenantSetting{})

	// Apply filters
	if filter.TenantID > 0 {
		query = query.Where("tenant_id = ?", filter.TenantID)
	}

	if filter.Category != "" {
		query = query.Where("category = ?", filter.Category)
	}

	if len(filter.Categories) > 0 {
		query = query.Where("category IN ?", filter.Categories)
	}

	if filter.IsPublic != nil {
		query = query.Where("is_public = ?", *filter.IsPublic)
	}

	if filter.Search != "" {
		search := "%" + filter.Search + "%"
		query = query.Where("`key` LIKE ? OR description LIKE ?", search, search)
	}

	// Order by category and key
	query = query.Order("category ASC, `key` ASC")

	// Fetch records
	var settings []*models.TenantSetting
	if err := query.Find(&settings).Error; err != nil {
		return nil, err
	}

	return settings, nil
}

// BulkUpsert creates or updates multiple settings
func (r *MySQLTenantSettingRepository) BulkUpsert(ctx context.Context, tenantID uint, settings []models.SettingUpdate) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, update := range settings {
			// Convert value to JSON
			valueBytes, err := json.Marshal(update.Value)
			if err != nil {
				return fmt.Errorf("failed to marshal value for %s.%s: %w", update.Category, update.Key, err)
			}

			setting := models.TenantSetting{
				TenantID: tenantID,
				Category: update.Category,
				Key:      update.Key,
				Value:    json.RawMessage(valueBytes),
			}

			if err := tx.Clauses(clause.OnConflict{
				Columns:   []clause.Column{{Name: "tenant_id"}, {Name: "category"}, {Name: "key"}},
				DoUpdates: clause.AssignmentColumns([]string{"value", "updated_at"}),
			}).Create(&setting).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// GetByCategory retrieves all settings for a tenant in a category
func (r *MySQLTenantSettingRepository) GetByCategory(ctx context.Context, tenantID uint, category string) ([]*models.TenantSetting, error) {
	var settings []*models.TenantSetting
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND category = ?", tenantID, category).
		Order("`key` ASC").
		Find(&settings).Error

	return settings, err
}
