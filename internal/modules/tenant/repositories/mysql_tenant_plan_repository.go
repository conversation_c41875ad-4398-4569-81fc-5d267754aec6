package repositories

import (
	"context"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"gorm.io/gorm"
)

// MySQLTenantPlanRepository implements TenantPlanRepository using MySQL
type MySQLTenantPlanRepository struct {
	db *gorm.DB
}

// NewMySQLTenantPlanRepository creates a new MySQL tenant plan repository
func NewMySQLTenantPlanRepository(db *gorm.DB) TenantPlanRepository {
	return &MySQLTenantPlanRepository{db: db}
}

// Create creates a new plan
func (r *MySQLTenantPlanRepository) Create(ctx context.Context, plan *models.TenantPlan) error {
	return r.db.WithContext(ctx).Create(plan).Error
}

// GetByID retrieves a plan by ID
func (r *MySQLTenantPlanRepository) GetByID(ctx context.Context, id uint) (*models.TenantPlan, error) {
	var plan models.TenantPlan
	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		First(&plan).Error

	if err != nil {
		return nil, err
	}
	return &plan, nil
}

// GetBySlug retrieves a plan by slug
func (r *MySQLTenantPlanRepository) GetBySlug(ctx context.Context, slug string) (*models.TenantPlan, error) {
	var plan models.TenantPlan
	err := r.db.WithContext(ctx).
		Where("slug = ?", slug).
		First(&plan).Error

	if err != nil {
		return nil, err
	}
	return &plan, nil
}

// Update updates a plan
func (r *MySQLTenantPlanRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	// Remove fields that shouldn't be updated directly
	delete(updates, "id")
	delete(updates, "created_at")
	delete(updates, "slug") // Slug shouldn't be changed after creation

	return r.db.WithContext(ctx).
		Model(&models.TenantPlan{}).
		Where("id = ?", id).
		Updates(updates).Error
}

// Delete soft deletes a plan (sets status to inactive)
func (r *MySQLTenantPlanRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TenantPlan{}).
		Where("id = ?", id).
		Update("status", models.PlanStatusInactive).Error
}

// List retrieves plans based on filter
func (r *MySQLTenantPlanRepository) List(ctx context.Context, filter models.PlanFilter) ([]*models.TenantPlan, int64, error) {
	query := r.db.WithContext(ctx).Model(&models.TenantPlan{})

	// Apply filters
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}

	if filter.IsVisible != nil {
		query = query.Where("is_visible = ?", *filter.IsVisible)
	}

	if filter.IsFeatured != nil {
		query = query.Where("is_featured = ?", *filter.IsFeatured)
	}

	if filter.MinPrice > 0 {
		query = query.Where("monthly_price >= ?", filter.MinPrice)
	}

	if filter.MaxPrice > 0 {
		query = query.Where("monthly_price <= ?", filter.MaxPrice)
	}

	// Count total records
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	sortBy := filter.SortBy
	if sortBy == "" {
		sortBy = "display_order"
	}
	sortOrder := filter.SortOrder
	if sortOrder == "" {
		sortOrder = "asc"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if filter.Page <= 0 {
		filter.Page = 1
	}
	if filter.PageSize <= 0 {
		filter.PageSize = 20
	}
	offset := (filter.Page - 1) * filter.PageSize
	query = query.Offset(offset).Limit(filter.PageSize)

	// Fetch records
	var plans []*models.TenantPlan
	if err := query.Find(&plans).Error; err != nil {
		return nil, 0, err
	}

	return plans, total, nil
}

// GetActivePlans retrieves all active plans
func (r *MySQLTenantPlanRepository) GetActivePlans(ctx context.Context) ([]*models.TenantPlan, error) {
	var plans []*models.TenantPlan
	err := r.db.WithContext(ctx).
		Where("status = ? AND is_visible = ?", models.PlanStatusActive, true).
		Order("display_order ASC").
		Find(&plans).Error

	return plans, err
}

// CheckSlugExists checks if a slug already exists
func (r *MySQLTenantPlanRepository) CheckSlugExists(ctx context.Context, slug string, excludeID uint) (bool, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.TenantPlan{}).
		Where("slug = ?", slug)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	err := query.Count(&count).Error
	return count > 0, err
}
