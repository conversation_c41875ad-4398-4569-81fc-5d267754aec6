package tenant

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func setupIntegrationTest(t *testing.T) (*gin.Engine, *gorm.DB) {
	// Setup in-memory database
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(t, err)

	// Auto migrate the schema
	err = db.AutoMigrate(
		&models.Tenant{},
		&models.TenantPlan{},
		&models.TenantSetting{},
		&models.TenantFeature{},
		&models.FeatureCatalog{},
	)
	require.NoError(t, err)

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	router := gin.New()

	// Initialize validator
	err = validator.Initialize()
	require.NoError(t, err)
	v := validator.GetDefaultValidator()

	// Register routes
	api := router.Group("/api/cms/v1")
	RegisterRoutes(api, db, v)

	return router, db
}

func createTestPlan(t *testing.T, db *gorm.DB) *models.TenantPlan {
	plan := &models.TenantPlan{
		Name:         "Basic Plan",
		Slug:         "basic",
		Description:  "Basic plan for testing",
		MonthlyPrice: 9.99,
		YearlyPrice:  99.99,
		MaxUsers:     10,
		MaxWebsites:  1,
		Status:       models.PlanStatusActive,
		IsVisible:    true,
	}

	err := db.Create(plan).Error
	require.NoError(t, err)

	return plan
}

func TestTenantWorkflow_Integration(t *testing.T) {
	router, db := setupIntegrationTest(t)

	// Step 1: Create a plan first (required for tenant creation)
	plan := createTestPlan(t, db)

	// Step 2: Create a tenant
	createReq := handlers.CreateTenantRequest{
		Name:         "Test Company",
		Domain:       "test.example.com",
		PlanID:       plan.ID,
		ContactEmail: "<EMAIL>",
		CompanyName:  "Test Company Inc",
	}

	jsonBody, _ := json.Marshal(createReq)
	req, _ := http.NewRequest("POST", "/api/cms/v1/tenants", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusCreated, w.Code)

	var createResp map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &createResp)
	require.NoError(t, err)
	assert.True(t, createResp["success"].(bool))

	tenantData := createResp["data"].(map[string]interface{})["tenant"].(map[string]interface{})
	tenantID := uint(tenantData["id"].(float64))

	// Step 3: Get the created tenant
	req, _ = http.NewRequest("GET", fmt.Sprintf("/api/cms/v1/tenants/%d", tenantID), nil)
	w = httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var getResp map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &getResp)
	require.NoError(t, err)
	assert.True(t, getResp["success"].(bool))

	retrievedTenant := getResp["data"].(map[string]interface{})["tenant"].(map[string]interface{})
	assert.Equal(t, "Test Company", retrievedTenant["name"])
	assert.Equal(t, "test.example.com", retrievedTenant["domain"])

	// Step 4: Update tenant
	updateReq := handlers.UpdateTenantRequest{
		Name:        "Updated Company Name",
		CompanyName: stringPtr("Updated Company Inc"),
	}

	jsonBody, _ = json.Marshal(updateReq)
	req, _ = http.NewRequest("PUT", fmt.Sprintf("/api/cms/v1/tenants/%d", tenantID), bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var updateResp map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &updateResp)
	require.NoError(t, err)
	assert.True(t, updateResp["success"].(bool))

	// Step 5: List tenants
	req, _ = http.NewRequest("GET", "/api/cms/v1/tenants", nil)
	w = httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var listResp map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &listResp)
	require.NoError(t, err)
	assert.True(t, listResp["success"].(bool))

	tenants := listResp["data"].(map[string]interface{})["tenants"].([]interface{})
	assert.Len(t, tenants, 1)

	// Step 6: Update tenant status
	statusReq := map[string]string{
		"status": string(models.TenantStatusSuspended),
	}

	jsonBody, _ = json.Marshal(statusReq)
	req, _ = http.NewRequest("PATCH", fmt.Sprintf("/api/cms/v1/tenants/%d/status", tenantID), bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	w = httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	// Step 7: Delete tenant (soft delete)
	req, _ = http.NewRequest("DELETE", fmt.Sprintf("/api/cms/v1/tenants/%d", tenantID), nil)
	w = httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func stringPtr(s string) *string {
	return &s
}

func TestPlanWorkflow_Integration(t *testing.T) {
	router, _ := setupIntegrationTest(t)

	// Step 1: Create a plan
	createReq := handlers.CreatePlanRequest{
		Name:          "Premium Plan",
		Slug:          "premium",
		Description:   "Premium plan with advanced features",
		Price:         29.99,
		BillingPeriod: "monthly",
		MaxUsers:      100,
		MaxProjects:   10,
		MaxStorage:    10000000000, // 10GB
		MaxAPICalls:   100000,
		Features:      []string{"advanced_analytics", "priority_support"},
	}

	jsonBody, _ := json.Marshal(createReq)
	req, _ := http.NewRequest("POST", "/api/cms/v1/tenant-plans", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusCreated, w.Code)

	var createResp map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &createResp)
	require.NoError(t, err)
	assert.True(t, createResp["success"].(bool))

	// Step 2: Get active plans
	req, _ = http.NewRequest("GET", "/api/cms/v1/tenant-plans/active", nil)
	w = httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var activeResp map[string]interface{}
	err = json.Unmarshal(w.Body.Bytes(), &activeResp)
	require.NoError(t, err)
	assert.True(t, activeResp["success"].(bool))

	plans := activeResp["data"].(map[string]interface{})["plans"].([]interface{})
	assert.Len(t, plans, 1)
}
