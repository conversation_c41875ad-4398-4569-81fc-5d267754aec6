package handlers

import (
	"errors"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"gorm.io/gorm"
)

type SettingsHandler struct {
	settingsService services.TenantSettingService
	validator       validator.Validator
}

func NewSettingsHandler(settingsService services.TenantSettingService, validator validator.Validator) *SettingsHandler {
	return &SettingsHandler{
		settingsService: settingsService,
		validator:       validator,
	}
}

// GetSetting handles GET /api/v1/tenants/{tenantId}/settings/{category}/{key}
func (h *SettingsHandler) GetSetting(c *gin.Context) {
	tenantIDStr := c.Param("tenantId")
	category := c.Param("category")
	key := c.Param("key")

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	// Use default category if not provided
	if category == "" {
		category = "general"
	}

	setting, err := h.settingsService.Get(c.Request.Context(), uint(tenantID), category, key)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Setting not found")
			return
		}
		response.InternalError(c.Writer, "Failed to get setting", err.Error())
		return
	}

	response.Success(c.Writer, setting)
}

// SetSetting handles POST /api/v1/tenants/{tenantId}/settings
func (h *SettingsHandler) SetSetting(c *gin.Context) {
	tenantIDStr := c.Param("tenantId")

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	var req SetSettingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Use default category if not provided
	category := req.Category
	if category == "" {
		category = "general"
	}

	err = h.settingsService.Set(c.Request.Context(), uint(tenantID), category, req.Key, req.Value)
	if err != nil {
		response.InternalError(c.Writer, "Failed to set setting", err.Error())
		return
	}

	response.Success(c.Writer, map[string]string{"status": "created"})
}

// DeleteSetting handles DELETE /api/v1/tenants/{tenantId}/settings/{category}/{key}
func (h *SettingsHandler) DeleteSetting(c *gin.Context) {
	tenantIDStr := c.Param("tenantId")
	category := c.Param("category")
	key := c.Param("key")

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	// Use default category if not provided
	if category == "" {
		category = "general"
	}

	err = h.settingsService.Delete(c.Request.Context(), uint(tenantID), category, key)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Setting not found")
			return
		}
		response.InternalError(c.Writer, "Failed to delete setting", err.Error())
		return
	}

	c.Writer.WriteHeader(http.StatusNoContent)
}

// GetSettingsByCategory handles GET /api/v1/tenants/{tenantId}/settings/category/{category}
func (h *SettingsHandler) GetSettingsByCategory(c *gin.Context) {
	tenantIDStr := c.Param("tenantId")
	category := c.Param("category")

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	settings, err := h.settingsService.GetByCategory(c.Request.Context(), uint(tenantID), category)
	if err != nil {
		response.InternalError(c.Writer, "Failed to get settings", err.Error())
		return
	}

	response.Success(c.Writer, settings)
}

// BulkUpdateSettings handles PUT /api/v1/tenants/{tenantId}/settings/bulk
func (h *SettingsHandler) BulkUpdateSettings(c *gin.Context) {
	tenantIDStr := c.Param("tenantId")

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	var req BulkUpdateSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Convert request to service types
	updates := make([]services.SettingUpdate, len(req.Settings))
	for i, s := range req.Settings {
		updates[i] = services.SettingUpdate{
			Category: s.Category,
			Key:      s.Key,
			Value:    s.Value,
		}
	}

	err = h.settingsService.BulkUpdate(c.Request.Context(), uint(tenantID), updates)
	if err != nil {
		response.InternalError(c.Writer, "Failed to update settings", err.Error())
		return
	}

	response.Success(c.Writer, map[string]string{"message": "Settings updated successfully"})
}

// GetPublicSettings handles GET /api/v1/tenants/{tenantId}/settings/public
func (h *SettingsHandler) GetPublicSettings(c *gin.Context) {
	tenantIDStr := c.Param("tenantId")

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	settings, err := h.settingsService.GetPublicSettings(c.Request.Context(), uint(tenantID))
	if err != nil {
		response.InternalError(c.Writer, "Failed to get public settings", err.Error())
		return
	}

	response.Success(c.Writer, settings)
}

// ValidateSetting handles POST /api/v1/tenants/{tenantId}/settings/validate
func (h *SettingsHandler) ValidateSetting(c *gin.Context) {
	var req ValidateSettingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Use default category since it's not provided in request
	category := "general"

	err := h.settingsService.ValidateSetting(c.Request.Context(), category, req.Key, req.Value)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid setting value", err.Error())
		return
	}

	response.Success(c.Writer, map[string]string{"message": "Setting is valid"})
}

// Request DTOs
type SetSettingRequest struct {
	Key      string      `json:"key" validate:"required,min=1,max=100"`
	Value    interface{} `json:"value" validate:"required"`
	Category string      `json:"category" validate:"omitempty,max=50"`
	IsPublic bool        `json:"is_public"`
}

type BulkUpdateSettingsRequest struct {
	Settings []SettingUpdateRequest `json:"settings" validate:"required,min=1,dive"`
}

type SettingUpdateRequest struct {
	Key      string      `json:"key" validate:"required,min=1,max=100"`
	Value    interface{} `json:"value" validate:"required"`
	Category string      `json:"category" validate:"omitempty,max=50"`
	IsPublic bool        `json:"is_public"`
}

type ValidateSettingRequest struct {
	Key   string      `json:"key" validate:"required,min=1,max=100"`
	Value interface{} `json:"value" validate:"required"`
}
