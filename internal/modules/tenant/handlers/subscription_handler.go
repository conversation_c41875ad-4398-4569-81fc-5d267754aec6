package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// SubscriptionHandler handles subscription-related HTTP requests
type SubscriptionHandler struct {
	subscriptionService *services.SubscriptionService
	validator           validator.Validator
}

// NewSubscriptionHandler creates a new subscription handler
func NewSubscriptionHandler(subscriptionService *services.SubscriptionService, validator validator.Validator) *SubscriptionHandler {
	return &SubscriptionHandler{
		subscriptionService: subscriptionService,
		validator:           validator,
	}
}

// CreateSubscription creates a new subscription
func (h *SubscriptionHandler) CreateSubscription(c *gin.Context) {
	var req models.CreateSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	subscription, err := h.subscriptionService.CreateSubscription(c.Request.Context(), &req)
	if err != nil {
		response.InternalError(c.Writer, "Failed to create subscription", err.Error())
		return
	}

	response.Created(c.Writer, subscription)
}

// GetSubscriptionByTenant gets the active subscription for a tenant
func (h *SubscriptionHandler) GetSubscriptionByTenant(c *gin.Context) {
	tenantIDStr := c.Param("tenantId")
	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	subscription, err := h.subscriptionService.GetSubscriptionByTenantID(c.Request.Context(), uint(tenantID))
	if err != nil {
		if err == services.ErrSubscriptionNotFound {
			response.NotFound(c.Writer, "Subscription not found")
			return
		}
		response.InternalError(c.Writer, "Failed to get subscription", err.Error())
		return
	}

	response.Success(c.Writer, subscription)
}

// UpgradePlan upgrades a subscription to a higher plan
func (h *SubscriptionHandler) UpgradePlan(c *gin.Context) {
	subscriptionIDStr := c.Param("id")
	subscriptionID, err := strconv.ParseUint(subscriptionIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid subscription ID", err.Error())
		return
	}

	var req models.UpgradePlanRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	transition, err := h.subscriptionService.UpgradePlan(c.Request.Context(), uint(subscriptionID), req.NewPlanID)
	if err != nil {
		if err == services.ErrSubscriptionNotActive {
			response.BadRequest(c.Writer, "Subscription is not active", "subscription_not_active")
			return
		}
		if err == services.ErrInvalidPlanTransition {
			response.BadRequest(c.Writer, "Invalid plan upgrade", "invalid_plan_transition")
			return
		}
		response.InternalError(c.Writer, "Failed to upgrade plan", err.Error())
		return
	}

	response.Success(c.Writer, transition)
}

// DowngradePlan downgrades a subscription to a lower plan
func (h *SubscriptionHandler) DowngradePlan(c *gin.Context) {
	subscriptionIDStr := c.Param("id")
	subscriptionID, err := strconv.ParseUint(subscriptionIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid subscription ID", err.Error())
		return
	}

	var req models.DowngradePlanRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	transition, err := h.subscriptionService.DowngradePlan(c.Request.Context(), uint(subscriptionID), req.NewPlanID)
	if err != nil {
		if err == services.ErrSubscriptionNotActive {
			response.BadRequest(c.Writer, "Subscription is not active", "subscription_not_active")
			return
		}
		if err == services.ErrInvalidPlanTransition {
			response.BadRequest(c.Writer, "Invalid plan downgrade", "invalid_plan_transition")
			return
		}
		response.InternalError(c.Writer, "Failed to downgrade plan", err.Error())
		return
	}

	response.Success(c.Writer, transition)
}

// CancelSubscription cancels a subscription
func (h *SubscriptionHandler) CancelSubscription(c *gin.Context) {
	subscriptionIDStr := c.Param("id")
	subscriptionID, err := strconv.ParseUint(subscriptionIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid subscription ID", err.Error())
		return
	}

	var req models.CancelSubscriptionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	err = h.subscriptionService.CancelSubscription(c.Request.Context(), uint(subscriptionID), req.CancelAtPeriodEnd)
	if err != nil {
		if err == services.ErrSubscriptionCanceled {
			response.BadRequest(c.Writer, "Subscription is already canceled", "subscription_already_canceled")
			return
		}
		response.InternalError(c.Writer, "Failed to cancel subscription", err.Error())
		return
	}

	message := "Subscription canceled successfully"
	if req.CancelAtPeriodEnd {
		message = "Subscription will be canceled at the end of the billing period"
	}

	response.Success(c.Writer, gin.H{
		"message":                message,
		"canceled_at_period_end": req.CancelAtPeriodEnd,
	})
}

// ProcessPaymentFailure records a payment failure
func (h *SubscriptionHandler) ProcessPaymentFailure(c *gin.Context) {
	subscriptionIDStr := c.Param("id")
	subscriptionID, err := strconv.ParseUint(subscriptionIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid subscription ID", err.Error())
		return
	}

	var req models.PaymentFailureRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	err = h.subscriptionService.ProcessPaymentFailure(c.Request.Context(), uint(subscriptionID), req.FailureReason, req.Amount)
	if err != nil {
		response.InternalError(c.Writer, "Failed to process payment failure", err.Error())
		return
	}

	response.Success(c.Writer, gin.H{"message": "Payment failure recorded successfully"})
}

// ProcessTrialExpiration processes trial expiration for a subscription
func (h *SubscriptionHandler) ProcessTrialExpiration(c *gin.Context) {
	subscriptionIDStr := c.Param("id")
	subscriptionID, err := strconv.ParseUint(subscriptionIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid subscription ID", err.Error())
		return
	}

	err = h.subscriptionService.ProcessTrialExpiration(c.Request.Context(), uint(subscriptionID))
	if err != nil {
		if err == services.ErrTrialAlreadyExpired {
			response.BadRequest(c.Writer, "Trial has not expired yet", "trial_not_expired")
			return
		}
		response.InternalError(c.Writer, "Failed to process trial expiration", err.Error())
		return
	}

	response.Success(c.Writer, gin.H{"message": "Trial expiration processed successfully"})
}

// GetExpiredTrials gets all subscriptions with expired trials
func (h *SubscriptionHandler) GetExpiredTrials(c *gin.Context) {
	subscriptions, err := h.subscriptionService.GetExpiredTrials(c.Request.Context())
	if err != nil {
		response.InternalError(c.Writer, "Failed to get expired trials", err.Error())
		return
	}

	response.Success(c.Writer, gin.H{
		"subscriptions": subscriptions,
		"count":         len(subscriptions),
	})
}

// ProcessPendingTransitions processes all pending plan transitions
func (h *SubscriptionHandler) ProcessPendingTransitions(c *gin.Context) {
	err := h.subscriptionService.ProcessPendingTransitions(c.Request.Context())
	if err != nil {
		response.InternalError(c.Writer, "Failed to process pending transitions", err.Error())
		return
	}

	response.Success(c.Writer, gin.H{"message": "Pending transitions processed successfully"})
}
