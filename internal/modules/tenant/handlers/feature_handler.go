package handlers

import (
	"errors"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"gorm.io/gorm"
)

type FeatureHandler struct {
	featureService        services.TenantFeatureService
	featureCatalogService services.FeatureCatalogService
	validator             validator.Validator
}

func NewFeatureHandler(
	featureService services.TenantFeatureService,
	featureCatalogService services.FeatureCatalogService,
	validator validator.Validator,
) *FeatureHandler {
	return &FeatureHandler{
		featureService:        featureService,
		featureCatalogService: featureCatalogService,
		validator:             validator,
	}
}

// Tenant Feature Management

// EnableFeature handles POST /api/v1/tenants/{tenantId}/features/{featureKey}/enable
func (h *FeatureHandler) EnableFeature(c *gin.Context) {
	tenantIDStr := c.Param("tenantId")
	featureKey := c.Param("featureKey")

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	var config map[string]interface{}
	// Optional configuration in request body
	_ = c.ShouldBindJSON(&config)

	err = h.featureService.Enable(c.Request.Context(), uint(tenantID), featureKey)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Tenant or feature not found")
			return
		}
		response.InternalError(c.Writer, "Failed to enable feature", err.Error())
		return
	}

	response.Success(c.Writer, map[string]string{"status": "enabled"})
}

// DisableFeature handles POST /api/v1/tenants/{tenantId}/features/{featureKey}/disable
func (h *FeatureHandler) DisableFeature(c *gin.Context) {
	tenantIDStr := c.Param("tenantId")
	featureKey := c.Param("featureKey")

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	err = h.featureService.Disable(c.Request.Context(), uint(tenantID), featureKey)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Feature not found")
			return
		}
		response.InternalError(c.Writer, "Failed to disable feature", err.Error())
		return
	}

	response.Success(c.Writer, map[string]string{"status": "disabled"})
}

// ToggleFeature handles POST /api/v1/tenants/{tenantId}/features/{featureKey}/toggle
func (h *FeatureHandler) ToggleFeature(c *gin.Context) {
	tenantIDStr := c.Param("tenantId")
	featureKey := c.Param("featureKey")

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	err = h.featureService.Toggle(c.Request.Context(), uint(tenantID), featureKey)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Feature not found")
			return
		}
		response.InternalError(c.Writer, "Failed to toggle feature", err.Error())
		return
	}

	response.Success(c.Writer, map[string]string{"status": "toggled"})
}

// IsFeatureEnabled handles GET /api/v1/tenants/{tenantId}/features/{featureKey}/enabled
func (h *FeatureHandler) IsFeatureEnabled(c *gin.Context) {
	tenantIDStr := c.Param("tenantId")
	featureKey := c.Param("featureKey")

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	// Get userID from context or use empty string for tenant-level check
	userID := "" // TODO: Extract from JWT context
	enabled, err := h.featureService.IsEnabled(c.Request.Context(), uint(tenantID), featureKey, userID)
	if err != nil {
		response.InternalError(c.Writer, "Failed to check feature status", err.Error())
		return
	}

	response.Success(c.Writer, map[string]bool{"enabled": enabled})
}

// GetEnabledFeatures handles GET /api/v1/tenants/{tenantId}/features/enabled
func (h *FeatureHandler) GetEnabledFeatures(c *gin.Context) {
	tenantIDStr := c.Param("tenantId")

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	features, err := h.featureService.GetEnabledFeatures(c.Request.Context(), uint(tenantID))
	if err != nil {
		response.InternalError(c.Writer, "Failed to get enabled features", err.Error())
		return
	}

	response.Success(c.Writer, features)
}

// BulkToggleFeatures handles POST /api/v1/tenants/{tenantId}/features/bulk-toggle
func (h *FeatureHandler) BulkToggleFeatures(c *gin.Context) {
	tenantIDStr := c.Param("tenantId")

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	var req BulkToggleFeaturesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Convert map to slice and get enabled state
	var featureKeys []string
	var enabled bool
	for key, state := range req.Features {
		featureKeys = append(featureKeys, key)
		enabled = state // Use last state for bulk operation
	}

	err = h.featureService.BulkToggle(c.Request.Context(), uint(tenantID), featureKeys, enabled)
	if err != nil {
		response.InternalError(c.Writer, "Failed to toggle features", err.Error())
		return
	}

	response.Success(c.Writer, map[string]string{"status": "updated"})
}

// UpdateFeatureConfiguration handles PUT /api/v1/tenants/{tenantId}/features/{featureKey}/config
func (h *FeatureHandler) UpdateFeatureConfiguration(c *gin.Context) {
	tenantIDStr := c.Param("tenantId")
	featureKey := c.Param("featureKey")

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	var config map[string]interface{}
	if err := c.ShouldBindJSON(&config); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	err = h.featureService.UpdateConfiguration(c.Request.Context(), uint(tenantID), featureKey, config)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Feature not found")
			return
		}
		response.InternalError(c.Writer, "Failed to update feature configuration", err.Error())
		return
	}

	response.Success(c.Writer, map[string]string{"status": "updated"})
}

// GetFeatureUsage handles GET /api/v1/tenants/{tenantId}/features/{featureKey}/usage
func (h *FeatureHandler) GetFeatureUsage(c *gin.Context) {
	tenantIDStr := c.Param("tenantId")
	featureKey := c.Param("featureKey")

	tenantID, err := strconv.ParseUint(tenantIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid tenant ID", err.Error())
		return
	}

	usage, err := h.featureService.GetFeatureUsage(c.Request.Context(), uint(tenantID), featureKey)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Feature not found")
			return
		}
		response.InternalError(c.Writer, "Failed to get feature usage", err.Error())
		return
	}

	response.Success(c.Writer, usage)
}

// Feature Catalog Management

// CreateFeatureCatalog handles POST /api/v1/feature-catalog
func (h *FeatureHandler) CreateFeatureCatalog(c *gin.Context) {
	var req CreateFeatureCatalogRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	input := services.CreateFeatureInput{
		FeatureKey:       req.Key,
		FeatureName:      req.Name,
		Description:      req.Description,
		Category:         req.Category,
		RequiredPlans:    req.PlanIDs,
		ExcludedPlans:    []uint{},
		IsBeta:           false,
		IsExperimental:   false,
		DocumentationURL: "",
	}

	feature, err := h.featureCatalogService.Create(c.Request.Context(), input)
	if err != nil {
		response.InternalError(c.Writer, "Failed to create feature", err.Error())
		return
	}

	response.Created(c.Writer, feature)
}

// GetFeatureCatalog handles GET /api/v1/feature-catalog/{key}
func (h *FeatureHandler) GetFeatureCatalog(c *gin.Context) {
	key := c.Param("key")

	feature, err := h.featureCatalogService.GetByKey(c.Request.Context(), key)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Feature not found")
			return
		}
		response.InternalError(c.Writer, "Failed to get feature", err.Error())
		return
	}

	response.Success(c.Writer, feature)
}

// UpdateFeatureCatalog handles PUT /api/v1/feature-catalog/{key}
func (h *FeatureHandler) UpdateFeatureCatalog(c *gin.Context) {
	key := c.Param("key")

	var req UpdateFeatureCatalogRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	if err := h.validator.Validate(c.Request.Context(), req); err != nil {
		response.ValidationError(c.Writer, err)
		return
	}

	// Convert strings to pointers for optional fields
	var name, desc, cat *string
	if req.Name != "" {
		name = &req.Name
	}
	if req.Description != "" {
		desc = &req.Description
	}
	if req.Category != "" {
		cat = &req.Category
	}

	input := services.UpdateFeatureInput{
		FeatureName:      name,
		Description:      desc,
		Category:         cat,
		RequiredPlans:    req.PlanIDs,
		ExcludedPlans:    []uint{},
		IsBeta:           nil,
		IsExperimental:   nil,
		DocumentationURL: nil,
		Status:           nil,
	}

	// TODO: Convert key to ID - this needs to be fixed in service interface
	// For now, assume key is numeric ID
	id, parseErr := strconv.ParseUint(key, 10, 32)
	if parseErr != nil {
		response.BadRequest(c.Writer, "Invalid feature key", parseErr.Error())
		return
	}
	feature, err := h.featureCatalogService.Update(c.Request.Context(), uint(id), input)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Feature not found")
			return
		}
		response.InternalError(c.Writer, "Failed to update feature", err.Error())
		return
	}

	response.Success(c.Writer, feature)
}

// DeprecateFeatureCatalog handles POST /api/v1/feature-catalog/{key}/deprecate
func (h *FeatureHandler) DeprecateFeatureCatalog(c *gin.Context) {
	key := c.Param("key")

	// TODO: Convert key to ID - this needs to be fixed in service interface
	// For now, assume key is numeric ID
	id, parseErr := strconv.ParseUint(key, 10, 32)
	if parseErr != nil {
		response.BadRequest(c.Writer, "Invalid feature key", parseErr.Error())
		return
	}
	err := h.featureCatalogService.Deprecate(c.Request.Context(), uint(id))
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			response.NotFound(c.Writer, "Feature not found")
			return
		}
		response.InternalError(c.Writer, "Failed to deprecate feature", err.Error())
		return
	}

	response.Success(c.Writer, map[string]string{"status": "deprecated"})
}

// ListFeatureCatalog handles GET /api/v1/feature-catalog
func (h *FeatureHandler) ListFeatureCatalog(c *gin.Context) {
	// Parse query parameters
	perPage := 20
	orderBy := "created_at"
	order := "DESC"

	// Parse per_page
	if perPageStr := c.Query("per_page"); perPageStr != "" {
		if parsed, err := strconv.Atoi(perPageStr); err == nil && parsed > 0 {
			perPage = parsed
		}
	}

	// Parse order_by
	if orderByParam := c.Query("order_by"); orderByParam != "" {
		orderBy = orderByParam
	}

	// Parse order
	if orderParam := c.Query("order"); orderParam != "" {
		order = orderParam
	}

	// Parse filters
	var category string
	var isDeprecated *bool
	if cat := c.Query("category"); cat != "" {
		category = cat
	}
	if deprecated := c.Query("is_deprecated"); deprecated != "" {
		if dep, err := strconv.ParseBool(deprecated); err == nil {
			isDeprecated = &dep
		}
	}

	// Convert params and filters to match interface
	filter := models.FeatureCatalogFilter{
		Category:     category,
		IsDeprecated: isDeprecated,
		Page:         1, // TODO: Fix pagination mapping
		PageSize:     perPage,
		SortBy:       orderBy,
		SortOrder:    order,
	}
	result, err := h.featureCatalogService.List(c.Request.Context(), filter)
	if err != nil {
		response.InternalError(c.Writer, "Failed to list features", err.Error())
		return
	}

	response.Success(c.Writer, result)
}

// GetFeaturesForPlan handles GET /api/v1/feature-catalog/plan/{planId}
func (h *FeatureHandler) GetFeaturesForPlan(c *gin.Context) {
	planIDStr := c.Param("planId")

	planID, err := strconv.ParseUint(planIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid plan ID", err.Error())
		return
	}

	features, err := h.featureCatalogService.GetFeaturesForPlan(c.Request.Context(), uint(planID))
	if err != nil {
		response.InternalError(c.Writer, "Failed to get features for plan", err.Error())
		return
	}

	response.Success(c.Writer, features)
}

// GetFeatureAdoption handles GET /api/v1/feature-catalog/{key}/adoption
func (h *FeatureHandler) GetFeatureAdoption(c *gin.Context) {
	key := c.Param("key")

	// The interface doesn't take a key parameter, it returns all adoption stats
	adoption, err := h.featureCatalogService.GetFeatureAdoption(c.Request.Context())
	if err != nil {
		response.InternalError(c.Writer, "Failed to get feature adoption", err.Error())
		return
	}

	// Filter for the specific key
	for _, stat := range adoption {
		if stat.FeatureKey == key {
			response.Success(c.Writer, stat)
			return
		}
	}

	response.NotFound(c.Writer, "Feature not found")
	return
}

// Request DTOs
type BulkToggleFeaturesRequest struct {
	Features map[string]bool `json:"features" validate:"required,min=1"`
}

type CreateFeatureCatalogRequest struct {
	Key             string                 `json:"key" validate:"required,min=3,max=50,alphanumdash"`
	Name            string                 `json:"name" validate:"required,min=3,max=100"`
	Description     string                 `json:"description" validate:"omitempty,max=500"`
	Category        string                 `json:"category" validate:"required,max=50"`
	Type            string                 `json:"type" validate:"required,oneof=boolean number string json"`
	DefaultEnabled  bool                   `json:"default_enabled"`
	RequiresPlan    bool                   `json:"requires_plan"`
	PlanIDs         []uint                 `json:"plan_ids" validate:"omitempty"`
	DefaultSettings map[string]interface{} `json:"default_settings" validate:"omitempty"`
	ValidationRules map[string]interface{} `json:"validation_rules" validate:"omitempty"`
}

type UpdateFeatureCatalogRequest struct {
	Name            string                 `json:"name" validate:"omitempty,min=3,max=100"`
	Description     string                 `json:"description" validate:"omitempty,max=500"`
	Category        string                 `json:"category" validate:"omitempty,max=50"`
	DefaultEnabled  *bool                  `json:"default_enabled" validate:"omitempty"`
	RequiresPlan    *bool                  `json:"requires_plan" validate:"omitempty"`
	PlanIDs         []uint                 `json:"plan_ids" validate:"omitempty"`
	DefaultSettings map[string]interface{} `json:"default_settings" validate:"omitempty"`
	ValidationRules map[string]interface{} `json:"validation_rules" validate:"omitempty"`
}
