package handlers

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// MockTenantService is a mock implementation of TenantService
type MockTenantService struct {
	mock.Mock
}

func (m *MockTenantService) Create(ctx context.Context, input services.CreateTenantInput) (*models.Tenant, error) {
	args := m.Called(ctx, input)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Tenant), args.Error(1)
}

func (m *MockTenantService) GetByID(ctx context.Context, id uint) (*models.Tenant, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Tenant), args.Error(1)
}

func (m *MockTenantService) GetByDomain(ctx context.Context, domain string) (*models.Tenant, error) {
	args := m.Called(ctx, domain)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Tenant), args.Error(1)
}

func (m *MockTenantService) Update(ctx context.Context, id uint, input services.UpdateTenantInput) (*models.Tenant, error) {
	args := m.Called(ctx, id, input)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Tenant), args.Error(1)
}

func (m *MockTenantService) UpdateStatus(ctx context.Context, id uint, status models.TenantStatus) error {
	args := m.Called(ctx, id, status)
	return args.Error(0)
}

func (m *MockTenantService) Delete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockTenantService) List(ctx context.Context, filter services.ListTenantFilter) (*services.TenantListResponse, error) {
	args := m.Called(ctx, filter)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*services.TenantListResponse), args.Error(1)
}

func (m *MockTenantService) AssignPlan(ctx context.Context, tenantID, planID uint) error {
	args := m.Called(ctx, tenantID, planID)
	return args.Error(0)
}

func (m *MockTenantService) CheckResourceLimit(ctx context.Context, tenantID uint, resource string) (bool, error) {
	args := m.Called(ctx, tenantID, resource)
	return args.Bool(0), args.Error(1)
}

func (m *MockTenantService) GetTenantStats(ctx context.Context, tenantID uint) (*services.TenantStats, error) {
	args := m.Called(ctx, tenantID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*services.TenantStats), args.Error(1)
}

// MockValidator is a mock implementation of Validator
type MockValidator struct {
	mock.Mock
}

func (m *MockValidator) Validate(ctx context.Context, data interface{}) error {
	args := m.Called(ctx, data)
	return args.Error(0)
}

func (m *MockValidator) ValidateField(ctx context.Context, field interface{}, tag string) error {
	args := m.Called(ctx, field, tag)
	return args.Error(0)
}

func (m *MockValidator) RegisterValidation(tag string, fn validator.ValidationFunc) error {
	args := m.Called(tag, fn)
	return args.Error(0)
}

func (m *MockValidator) RegisterTagNameFunc(fn validator.TagNameFunc) {
	m.Called(fn)
}

func (m *MockValidator) RegisterTranslation(tag string, locale string, translation string) error {
	args := m.Called(tag, locale, translation)
	return args.Error(0)
}

func (m *MockValidator) SetLocale(locale string) error {
	args := m.Called(locale)
	return args.Error(0)
}

func setupTestRouter() *gin.Engine {
	gin.SetMode(gin.TestMode)
	return gin.New()
}

func TestTenantHandler_CreateTenant(t *testing.T) {
	mockService := new(MockTenantService)
	mockValidator := new(MockValidator)
	handler := NewTenantHandler(mockService, mockValidator)

	router := setupTestRouter()
	router.POST("/tenants", handler.CreateTenant)

	// Mock validation success
	mockValidator.On("Validate", mock.Anything, mock.AnythingOfType("CreateTenantRequest")).Return(nil)

	// Mock service success
	expectedTenant := &models.Tenant{
		ID:     1,
		Name:   "Test Company",
		Domain: "test.example.com",
		Status: models.TenantStatusActive,
	}
	mockService.On("Create", mock.Anything, mock.AnythingOfType("services.CreateTenantInput")).Return(expectedTenant, nil)

	// Prepare request
	requestBody := CreateTenantRequest{
		Name:         "Test Company",
		Domain:       "test.example.com",
		PlanID:       1,
		ContactEmail: "<EMAIL>",
		CompanyName:  "Test Company",
	}
	jsonBody, _ := json.Marshal(requestBody)

	req, _ := http.NewRequest("POST", "/tenants", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusCreated, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response["success"].(bool))
	assert.NotNil(t, response["data"])

	mockService.AssertExpectations(t)
	mockValidator.AssertExpectations(t)
}

func TestTenantHandler_GetTenant(t *testing.T) {
	mockService := new(MockTenantService)
	mockValidator := new(MockValidator)
	handler := NewTenantHandler(mockService, mockValidator)

	router := setupTestRouter()
	router.GET("/tenants/:id", handler.GetTenant)

	// Mock service success
	expectedTenant := &models.Tenant{
		ID:     1,
		Name:   "Test Company",
		Domain: "test.example.com",
		Status: models.TenantStatusActive,
	}
	mockService.On("GetByID", mock.Anything, uint(1)).Return(expectedTenant, nil)

	req, _ := http.NewRequest("GET", "/tenants/1", nil)
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)

	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	assert.NoError(t, err)
	assert.True(t, response["success"].(bool))
	assert.NotNil(t, response["data"])

	mockService.AssertExpectations(t)
}

func TestTenantHandler_CreateTenant_ValidationError(t *testing.T) {
	mockService := new(MockTenantService)
	mockValidator := new(MockValidator)
	handler := NewTenantHandler(mockService, mockValidator)

	router := setupTestRouter()
	router.POST("/tenants", handler.CreateTenant)

	// Mock validation error
	mockValidator.On("Validate", mock.Anything, mock.AnythingOfType("CreateTenantRequest")).Return(assert.AnError)

	// Prepare request with invalid data
	requestBody := CreateTenantRequest{
		Name: "", // Empty name should fail validation
	}
	jsonBody, _ := json.Marshal(requestBody)

	req, _ := http.NewRequest("POST", "/tenants", bytes.NewBuffer(jsonBody))
	req.Header.Set("Content-Type", "application/json")
	w := httptest.NewRecorder()

	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusBadRequest, w.Code)
	mockValidator.AssertExpectations(t)
}
