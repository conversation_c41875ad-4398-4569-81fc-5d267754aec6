package services

import (
	"context"
	"errors"
	"fmt"
	"math"
	"strings"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/repositories"
	"gorm.io/gorm"
)

// tenantPlanService implements TenantPlanService
type tenantPlanService struct {
	planRepo   repositories.TenantPlanRepository
	tenantRepo repositories.TenantRepository
}

// NewTenantPlanService creates a new tenant plan service
func NewTenantPlanService(planRepo repositories.TenantPlanRepository, tenantRepo repositories.TenantRepository) TenantPlanService {
	return &tenantPlanService{
		planRepo:   planRepo,
		tenantRepo: tenantRepo,
	}
}

// Create creates a new plan
func (s *tenantPlanService) Create(ctx context.Context, input CreatePlanInput) (*models.TenantPlan, error) {
	// Validate slug is unique
	exists, err := s.planRepo.CheckSlugExists(ctx, input.Slug, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to check slug: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("slug already exists")
	}

	// Create plan
	plan := &models.TenantPlan{
		Name:              input.Name,
		Slug:              strings.ToLower(strings.TrimSpace(input.Slug)),
		Description:       input.Description,
		MonthlyPrice:      input.MonthlyPrice,
		YearlyPrice:       input.YearlyPrice,
		MaxUsers:          input.MaxUsers,
		MaxWebsites:       input.MaxProjects,                            // Map MaxProjects to MaxWebsites
		MaxStorageGB:      int(input.MaxStorage / (1024 * 1024 * 1024)), // Convert bytes to GB
		MaxAPICallsPerDay: input.MaxAPICallsPerDay,
		Features: models.JSONMap(map[string]interface{}{
			"included": input.Features,
		}),
		Status:       models.PlanStatusActive,
		IsFeatured:   input.IsFeatured,
		IsVisible:    input.IsVisible,
		DisplayOrder: input.DisplayOrder,
	}

	// Set yearly price based on monthly if not provided
	if plan.YearlyPrice == 0 && plan.MonthlyPrice > 0 {
		// Default to 20% discount for yearly billing
		plan.YearlyPrice = plan.MonthlyPrice * 12 * 0.8
	}

	if err := s.planRepo.Create(ctx, plan); err != nil {
		return nil, fmt.Errorf("failed to create plan: %w", err)
	}

	return plan, nil
}

// GetByID retrieves a plan by ID
func (s *tenantPlanService) GetByID(ctx context.Context, id uint) (*models.TenantPlan, error) {
	plan, err := s.planRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("plan not found")
		}
		return nil, fmt.Errorf("failed to get plan: %w", err)
	}

	return plan, nil
}

// GetBySlug retrieves a plan by slug
func (s *tenantPlanService) GetBySlug(ctx context.Context, slug string) (*models.TenantPlan, error) {
	slug = strings.ToLower(strings.TrimSpace(slug))

	plan, err := s.planRepo.GetBySlug(ctx, slug)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("plan not found")
		}
		return nil, fmt.Errorf("failed to get plan: %w", err)
	}

	return plan, nil
}

// Update updates plan information
func (s *tenantPlanService) Update(ctx context.Context, id uint, input UpdatePlanInput) (*models.TenantPlan, error) {
	// Get existing plan
	plan, err := s.planRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("plan not found")
		}
		return nil, fmt.Errorf("failed to get plan: %w", err)
	}

	updates := make(map[string]interface{})

	// Update basic fields
	if input.Name != nil {
		updates["name"] = *input.Name
	}

	if input.Description != nil {
		updates["description"] = *input.Description
	}

	if input.MonthlyPrice != nil {
		updates["monthly_price"] = *input.MonthlyPrice
	}

	if input.YearlyPrice != nil {
		updates["yearly_price"] = *input.YearlyPrice
	}

	if input.TrialDays != nil {
		updates["trial_days"] = *input.TrialDays
	}

	// Update resource limits
	if input.MaxUsers != nil {
		updates["max_users"] = *input.MaxUsers
	}

	if input.MaxProjects != nil {
		updates["max_projects"] = *input.MaxProjects
	}

	if input.MaxStorage != nil {
		updates["max_storage"] = *input.MaxStorage
	}

	if input.MaxAPICallsPerDay != nil {
		updates["max_api_calls_per_day"] = *input.MaxAPICallsPerDay
	}

	// Update features
	if input.Features != nil {
		features := make(models.JSONMap)
		// Copy existing features
		for k, v := range plan.Features {
			features[k] = v
		}
		// Update included features
		features["included"] = input.Features
		updates["features"] = features
	}

	// Update visibility settings
	if input.IsFeatured != nil {
		updates["is_featured"] = *input.IsFeatured
	}

	if input.IsVisible != nil {
		updates["is_visible"] = *input.IsVisible
	}

	if input.DisplayOrder != nil {
		updates["display_order"] = *input.DisplayOrder
	}

	if input.Status != nil {
		// Validate status change
		if *input.Status == models.PlanStatusInactive {
			// Check if any active tenants are using this plan
			count, err := s.GetPlanTenantCount(ctx, id)
			if err != nil {
				return nil, fmt.Errorf("failed to check tenant count: %w", err)
			}
			if count > 0 {
				return nil, fmt.Errorf("cannot deactivate plan with active tenants")
			}
		}
		updates["status"] = *input.Status
	}

	// Update plan
	if err := s.planRepo.Update(ctx, id, updates); err != nil {
		return nil, fmt.Errorf("failed to update plan: %w", err)
	}

	// Get updated plan
	return s.planRepo.GetByID(ctx, id)
}

// Delete soft deletes a plan
func (s *tenantPlanService) Delete(ctx context.Context, id uint) error {
	// Check if any tenants are using this plan
	count, err := s.GetPlanTenantCount(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to check tenant count: %w", err)
	}
	if count > 0 {
		return fmt.Errorf("cannot delete plan with active tenants")
	}

	return s.planRepo.Delete(ctx, id)
}

// List retrieves plans with pagination
func (s *tenantPlanService) List(ctx context.Context, filter models.PlanFilter) (*PlanListResponse, error) {
	// Get plans
	plans, total, err := s.planRepo.List(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to list plans: %w", err)
	}

	// Calculate total pages
	pageSize := filter.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}
	totalPages := int(math.Ceil(float64(total) / float64(pageSize)))
	if totalPages == 0 && total > 0 {
		totalPages = 1
	}

	page := filter.Page
	if page <= 0 {
		page = 1
	}

	return &PlanListResponse{
		Plans:      plans,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// GetActivePlans retrieves all active visible plans
func (s *tenantPlanService) GetActivePlans(ctx context.Context) ([]*models.TenantPlan, error) {
	plans, err := s.planRepo.GetActivePlans(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get active plans: %w", err)
	}

	return plans, nil
}

// GetPlanTenantCount gets count of tenants on a plan
func (s *tenantPlanService) GetPlanTenantCount(ctx context.Context, planID uint) (int64, error) {
	// Get count of active tenants using this plan
	filter := models.TenantFilter{
		PlanID:   planID,
		PageSize: 1, // We only need the count
	}

	_, count, err := s.tenantRepo.List(ctx, filter)
	if err != nil {
		return 0, fmt.Errorf("failed to count tenants: %w", err)
	}

	return count, nil
}
