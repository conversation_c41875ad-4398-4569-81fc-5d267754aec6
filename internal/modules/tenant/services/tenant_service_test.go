package services

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"gorm.io/gorm"
)

// MockTenantRepository is a mock implementation of TenantRepository
type MockTenantRepository struct {
	mock.Mock
}

func (m *MockTenantRepository) Create(ctx context.Context, tenant *models.Tenant) error {
	args := m.Called(ctx, tenant)
	return args.Error(0)
}

func (m *MockTenantRepository) GetByID(ctx context.Context, id uint) (*models.Tenant, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.Tenant), args.Error(1)
}

func (m *MockTenantRepository) GetByDomain(ctx context.Context, domain string) (*models.Tenant, error) {
	args := m.Called(ctx, domain)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Tenant), args.Error(1)
}

func (m *MockTenantRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	args := m.Called(ctx, id, updates)
	return args.Error(0)
}

func (m *MockTenantRepository) Delete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockTenantRepository) List(ctx context.Context, filter models.TenantFilter) ([]*models.Tenant, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]*models.Tenant), args.Get(1).(int64), args.Error(2)
}

func (m *MockTenantRepository) CheckDomainExists(ctx context.Context, domain string, excludeID uint) (bool, error) {
	args := m.Called(ctx, domain, excludeID)
	return args.Bool(0), args.Error(1)
}

func (m *MockTenantRepository) CheckSlugExists(ctx context.Context, slug string, excludeID uint) (bool, error) {
	args := m.Called(ctx, slug, excludeID)
	return args.Bool(0), args.Error(1)
}

func (m *MockTenantRepository) GetByOwnerEmail(ctx context.Context, email string) ([]*models.Tenant, error) {
	args := m.Called(ctx, email)
	return args.Get(0).([]*models.Tenant), args.Error(1)
}

func (m *MockTenantRepository) GetBySlug(ctx context.Context, slug string) (*models.Tenant, error) {
	args := m.Called(ctx, slug)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.Tenant), args.Error(1)
}

// MockTenantPlanRepository is a mock implementation of TenantPlanRepository
type MockTenantPlanRepository struct {
	mock.Mock
}

func (m *MockTenantPlanRepository) Create(ctx context.Context, plan *models.TenantPlan) error {
	args := m.Called(ctx, plan)
	return args.Error(0)
}

func (m *MockTenantPlanRepository) GetByID(ctx context.Context, id uint) (*models.TenantPlan, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.TenantPlan), args.Error(1)
}

func (m *MockTenantPlanRepository) GetBySlug(ctx context.Context, slug string) (*models.TenantPlan, error) {
	args := m.Called(ctx, slug)
	return args.Get(0).(*models.TenantPlan), args.Error(1)
}

func (m *MockTenantPlanRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	args := m.Called(ctx, id, updates)
	return args.Error(0)
}

func (m *MockTenantPlanRepository) Delete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockTenantPlanRepository) List(ctx context.Context, filter models.PlanFilter) ([]*models.TenantPlan, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]*models.TenantPlan), args.Get(1).(int64), args.Error(2)
}

func (m *MockTenantPlanRepository) GetActivePlans(ctx context.Context) ([]*models.TenantPlan, error) {
	args := m.Called(ctx)
	return args.Get(0).([]*models.TenantPlan), args.Error(1)
}

func (m *MockTenantPlanRepository) CheckSlugExists(ctx context.Context, slug string, excludeID uint) (bool, error) {
	args := m.Called(ctx, slug, excludeID)
	return args.Bool(0), args.Error(1)
}

func TestTenantService_Create(t *testing.T) {
	mockTenantRepo := new(MockTenantRepository)
	mockPlanRepo := new(MockTenantPlanRepository)
	service := NewTenantService(mockTenantRepo, mockPlanRepo)
	ctx := context.Background()

	// Mock plan exists and is active
	plan := &models.TenantPlan{
		ID:     1,
		Name:   "Basic Plan",
		Status: models.PlanStatusActive,
	}
	mockPlanRepo.On("GetByID", ctx, uint(1)).Return(plan, nil)

	// Mock domain doesn't exist
	mockTenantRepo.On("CheckDomainExists", ctx, "test.example.com", uint(0)).Return(false, nil)

	// Mock create success
	mockTenantRepo.On("Create", ctx, mock.AnythingOfType("*models.Tenant")).Return(nil)

	input := CreateTenantInput{
		Name:       "Test Company",
		Domain:     "test.example.com",
		PlanID:     1,
		AdminEmail: "<EMAIL>",
		AdminName:  "Admin User",
	}

	tenant, err := service.Create(ctx, input)
	assert.NoError(t, err)
	assert.NotNil(t, tenant)
	assert.Equal(t, "Test Company", tenant.Name)
	assert.Equal(t, "test.example.com", tenant.Domain)
	assert.Equal(t, models.TenantStatusActive, tenant.Status)

	mockTenantRepo.AssertExpectations(t)
	mockPlanRepo.AssertExpectations(t)
}

func TestTenantService_Create_PlanNotFound(t *testing.T) {
	mockTenantRepo := new(MockTenantRepository)
	mockPlanRepo := new(MockTenantPlanRepository)
	service := NewTenantService(mockTenantRepo, mockPlanRepo)
	ctx := context.Background()

	// Mock plan not found
	mockPlanRepo.On("GetByID", ctx, uint(1)).Return(nil, gorm.ErrRecordNotFound)

	input := CreateTenantInput{
		Name:       "Test Company",
		Domain:     "test.example.com",
		PlanID:     1,
		AdminEmail: "<EMAIL>",
		AdminName:  "Admin User",
	}

	tenant, err := service.Create(ctx, input)
	assert.Error(t, err)
	assert.Nil(t, tenant)
	assert.Contains(t, err.Error(), "plan not found")

	mockPlanRepo.AssertExpectations(t)
}

func TestTenantService_Create_DomainExists(t *testing.T) {
	mockTenantRepo := new(MockTenantRepository)
	mockPlanRepo := new(MockTenantPlanRepository)
	service := NewTenantService(mockTenantRepo, mockPlanRepo)
	ctx := context.Background()

	// Mock plan exists and is active
	plan := &models.TenantPlan{
		ID:     1,
		Name:   "Basic Plan",
		Status: models.PlanStatusActive,
	}
	mockPlanRepo.On("GetByID", ctx, uint(1)).Return(plan, nil)

	// Mock domain already exists
	mockTenantRepo.On("CheckDomainExists", ctx, "test.example.com", uint(0)).Return(true, nil)

	input := CreateTenantInput{
		Name:       "Test Company",
		Domain:     "test.example.com",
		PlanID:     1,
		AdminEmail: "<EMAIL>",
		AdminName:  "Admin User",
	}

	tenant, err := service.Create(ctx, input)
	assert.Error(t, err)
	assert.Nil(t, tenant)
	assert.Contains(t, err.Error(), "domain already exists")

	mockTenantRepo.AssertExpectations(t)
	mockPlanRepo.AssertExpectations(t)
}
