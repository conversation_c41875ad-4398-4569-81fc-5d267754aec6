package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/repositories"
	"gorm.io/gorm"
)

var (
	ErrSubscriptionNotFound  = errors.New("subscription not found")
	ErrSubscriptionNotActive = errors.New("subscription is not active")
	ErrInvalidPlanTransition = errors.New("invalid plan transition")
	ErrTrialAlreadyExpired   = errors.New("trial period has already expired")
	ErrPaymentRequired       = errors.New("payment required to continue service")
	ErrMaxPaymentFailures    = errors.New("maximum payment failures reached")
	ErrSubscriptionCanceled  = errors.New("subscription is canceled")
)

// SubscriptionService handles subscription management business logic
type SubscriptionService struct {
	subscriptionRepo repositories.SubscriptionRepository
	tenantRepo       repositories.TenantRepository
	planRepo         repositories.TenantPlanRepository
}

// NewSubscriptionService creates a new subscription service instance
func NewSubscriptionService(
	subscriptionRepo repositories.SubscriptionRepository,
	tenantRepo repositories.TenantRepository,
	planRepo repositories.TenantPlanRepository,
) *SubscriptionService {
	return &SubscriptionService{
		subscriptionRepo: subscriptionRepo,
		tenantRepo:       tenantRepo,
		planRepo:         planRepo,
	}
}

// CreateSubscription creates a new subscription for a tenant
func (s *SubscriptionService) CreateSubscription(ctx context.Context, req *models.CreateSubscriptionRequest) (*models.Subscription, error) {
	// Validate tenant exists
	tenant, err := s.tenantRepo.GetByID(ctx, req.TenantID)
	if err != nil {
		return nil, fmt.Errorf("tenant not found: %w", err)
	}

	// Validate plan exists
	plan, err := s.planRepo.GetByID(ctx, req.PlanID)
	if err != nil {
		return nil, fmt.Errorf("plan not found: %w", err)
	}

	// Check if tenant already has an active subscription
	existing, err := s.subscriptionRepo.GetActiveByTenantID(ctx, req.TenantID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("failed to check existing subscription: %w", err)
	}
	if existing != nil {
		return nil, errors.New("tenant already has an active subscription")
	}

	// Determine price based on billing cycle
	var amount float64
	if req.BillingCycle == models.BillingCycleYearly {
		amount = plan.YearlyPrice
	} else {
		amount = plan.MonthlyPrice
	}

	// Create subscription
	subscription := &models.Subscription{
		TenantID:           req.TenantID,
		PlanID:             req.PlanID,
		Status:             models.SubscriptionStatusTrialing,
		BillingCycle:       req.BillingCycle,
		Amount:             amount,
		Currency:           "USD",
		CurrentPeriodStart: time.Now(),
		CurrentPeriodEnd:   time.Now().AddDate(0, 1, 0), // Default 1 month
	}

	// Set trial period (default 7 days trial)
	trialEnd := time.Now().AddDate(0, 0, 7)
	subscription.TrialEndsAt = &trialEnd

	// Set billing cycle dates
	if req.BillingCycle == models.BillingCycleYearly {
		subscription.CurrentPeriodEnd = time.Now().AddDate(1, 0, 0)
	}

	// Set next billing date
	nextBilling := subscription.CurrentPeriodEnd
	subscription.NextBillingDate = &nextBilling

	// Create subscription
	created, err := s.subscriptionRepo.Create(ctx, subscription)
	if err != nil {
		return nil, fmt.Errorf("failed to create subscription: %w", err)
	}

	// Update tenant status to trial if subscription is trialing
	if subscription.Status == models.SubscriptionStatusTrialing {
		updateData := map[string]interface{}{
			"status": models.TenantStatusTrial,
		}
		if err := s.tenantRepo.Update(ctx, tenant.ID, updateData); err != nil {
			// Log error but don't fail the subscription creation
			fmt.Printf("Warning: failed to update tenant status: %v", err)
		}
	}

	return created, nil
}

// GetSubscriptionByTenantID gets the active subscription for a tenant
func (s *SubscriptionService) GetSubscriptionByTenantID(ctx context.Context, tenantID uint) (*models.Subscription, error) {
	subscription, err := s.subscriptionRepo.GetActiveByTenantID(ctx, tenantID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, ErrSubscriptionNotFound
		}
		return nil, fmt.Errorf("failed to get subscription: %w", err)
	}
	return subscription, nil
}

// ProcessTrialExpiration handles trial period expiration
func (s *SubscriptionService) ProcessTrialExpiration(ctx context.Context, subscriptionID uint) error {
	subscription, err := s.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		return fmt.Errorf("failed to get subscription: %w", err)
	}

	// Check if subscription is in trial
	if subscription.Status != models.SubscriptionStatusTrialing {
		return errors.New("subscription is not in trial period")
	}

	// Check if trial has expired
	if subscription.TrialEndsAt == nil || subscription.TrialEndsAt.After(time.Now()) {
		return ErrTrialAlreadyExpired
	}

	// Update subscription status to require payment
	subscription.Status = models.SubscriptionStatusPastDue
	if err := s.subscriptionRepo.Update(ctx, subscription); err != nil {
		return fmt.Errorf("failed to update subscription status: %w", err)
	}

	// Update tenant status
	updateData := map[string]interface{}{
		"status": models.TenantStatusSuspended,
	}
	if err := s.tenantRepo.Update(ctx, subscription.TenantID, updateData); err != nil {
		return fmt.Errorf("failed to update tenant status: %w", err)
	}

	return nil
}

// UpgradePlan upgrades a subscription to a higher plan
func (s *SubscriptionService) UpgradePlan(ctx context.Context, subscriptionID uint, newPlanID uint) (*models.PlanTransition, error) {
	subscription, err := s.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get subscription: %w", err)
	}

	if !subscription.CanUpgrade() {
		return nil, ErrSubscriptionNotActive
	}

	// Get new plan
	newPlan, err := s.planRepo.GetByID(ctx, newPlanID)
	if err != nil {
		return nil, fmt.Errorf("new plan not found: %w", err)
	}

	// Get current plan
	currentPlan, err := s.planRepo.GetByID(ctx, subscription.PlanID)
	if err != nil {
		return nil, fmt.Errorf("current plan not found: %w", err)
	}

	// Get current and new prices based on billing cycle
	var currentPrice, newPrice float64
	if subscription.BillingCycle == models.BillingCycleYearly {
		currentPrice = currentPlan.YearlyPrice
		newPrice = newPlan.YearlyPrice
	} else {
		currentPrice = currentPlan.MonthlyPrice
		newPrice = newPlan.MonthlyPrice
	}

	// Validate upgrade (new plan should be higher tier)
	if newPrice <= currentPrice {
		return nil, ErrInvalidPlanTransition
	}

	// Calculate prorated amount
	daysRemaining := int(subscription.CurrentPeriodEnd.Sub(time.Now()).Hours() / 24)
	totalDays := int(subscription.CurrentPeriodEnd.Sub(subscription.CurrentPeriodStart).Hours() / 24)
	proratedAmount := calculateProratedAmount(currentPrice, newPrice, daysRemaining, totalDays)

	// Create plan transition record
	transition := &models.PlanTransition{
		TenantID:       subscription.TenantID,
		SubscriptionID: subscriptionID,
		FromPlanID:     subscription.PlanID,
		ToPlanID:       newPlanID,
		TransitionType: "upgrade",
		EffectiveDate:  time.Now(),
		ProratedAmount: proratedAmount,
		Notes:          fmt.Sprintf("Upgraded from %s to %s", currentPlan.Name, newPlan.Name),
	}

	// Create transition record
	created, err := s.subscriptionRepo.CreatePlanTransition(ctx, transition)
	if err != nil {
		return nil, fmt.Errorf("failed to create plan transition: %w", err)
	}

	// Update subscription
	subscription.PlanID = newPlanID
	subscription.Amount = newPrice
	if err := s.subscriptionRepo.Update(ctx, subscription); err != nil {
		return nil, fmt.Errorf("failed to update subscription: %w", err)
	}

	// Mark transition as processed
	now := time.Now()
	created.ProcessedAt = &now
	if err := s.subscriptionRepo.UpdatePlanTransition(ctx, created); err != nil {
		return nil, fmt.Errorf("failed to mark transition as processed: %w", err)
	}

	return created, nil
}

// DowngradePlan downgrades a subscription to a lower plan
func (s *SubscriptionService) DowngradePlan(ctx context.Context, subscriptionID uint, newPlanID uint) (*models.PlanTransition, error) {
	subscription, err := s.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get subscription: %w", err)
	}

	if !subscription.CanDowngrade() {
		return nil, ErrSubscriptionNotActive
	}

	// Get new plan
	newPlan, err := s.planRepo.GetByID(ctx, newPlanID)
	if err != nil {
		return nil, fmt.Errorf("new plan not found: %w", err)
	}

	// Get current plan
	currentPlan, err := s.planRepo.GetByID(ctx, subscription.PlanID)
	if err != nil {
		return nil, fmt.Errorf("current plan not found: %w", err)
	}

	// Get current and new prices based on billing cycle
	var currentPrice, newPrice float64
	if subscription.BillingCycle == models.BillingCycleYearly {
		currentPrice = currentPlan.YearlyPrice
		newPrice = newPlan.YearlyPrice
	} else {
		currentPrice = currentPlan.MonthlyPrice
		newPrice = newPlan.MonthlyPrice
	}

	// Validate downgrade (new plan should be lower tier)
	if newPrice >= currentPrice {
		return nil, ErrInvalidPlanTransition
	}

	// Create plan transition record (effective at period end)
	transition := &models.PlanTransition{
		TenantID:       subscription.TenantID,
		SubscriptionID: subscriptionID,
		FromPlanID:     subscription.PlanID,
		ToPlanID:       newPlanID,
		TransitionType: "downgrade",
		EffectiveDate:  subscription.CurrentPeriodEnd, // Effective at period end
		ProratedAmount: 0,                             // No refund for downgrades
		Notes:          fmt.Sprintf("Scheduled downgrade from %s to %s", currentPlan.Name, newPlan.Name),
	}

	// Create transition record
	created, err := s.subscriptionRepo.CreatePlanTransition(ctx, transition)
	if err != nil {
		return nil, fmt.Errorf("failed to create plan transition: %w", err)
	}

	return created, nil
}

// ProcessPaymentFailure records a payment failure and updates subscription status
func (s *SubscriptionService) ProcessPaymentFailure(ctx context.Context, subscriptionID uint, failureReason string, amount float64) error {
	subscription, err := s.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		return fmt.Errorf("failed to get subscription: %w", err)
	}

	// Create payment failure record
	failure := &models.PaymentFailure{
		TenantID:       subscription.TenantID,
		SubscriptionID: subscriptionID,
		Amount:         amount,
		Currency:       subscription.Currency,
		FailureReason:  failureReason,
		RetryCount:     0,
	}

	// Set next retry time (24 hours from now)
	nextRetry := time.Now().Add(24 * time.Hour)
	failure.NextRetryAt = &nextRetry

	// Create failure record
	_, err = s.subscriptionRepo.CreatePaymentFailure(ctx, failure)
	if err != nil {
		return fmt.Errorf("failed to create payment failure record: %w", err)
	}

	// Update subscription
	subscription.PaymentFailureCount++
	now := time.Now()
	subscription.LastPaymentFailureAt = &now

	// Update status based on failure count
	if subscription.PaymentFailureCount >= 3 {
		subscription.Status = models.SubscriptionStatusSuspended

		// Update tenant status
		updateData := map[string]interface{}{
			"status": models.TenantStatusSuspended,
		}
		s.tenantRepo.Update(ctx, subscription.TenantID, updateData)
	} else {
		subscription.Status = models.SubscriptionStatusPastDue
	}

	if err := s.subscriptionRepo.Update(ctx, subscription); err != nil {
		return fmt.Errorf("failed to update subscription: %w", err)
	}

	return nil
}

// CancelSubscription cancels a subscription
func (s *SubscriptionService) CancelSubscription(ctx context.Context, subscriptionID uint, cancelAtPeriodEnd bool) error {
	subscription, err := s.subscriptionRepo.GetByID(ctx, subscriptionID)
	if err != nil {
		return fmt.Errorf("failed to get subscription: %w", err)
	}

	if subscription.Status == models.SubscriptionStatusCanceled {
		return ErrSubscriptionCanceled
	}

	now := time.Now()
	subscription.CanceledAt = &now
	subscription.CancelAtPeriodEnd = cancelAtPeriodEnd

	if !cancelAtPeriodEnd {
		// Cancel immediately
		subscription.Status = models.SubscriptionStatusCanceled
		subscription.CurrentPeriodEnd = now

		// Update tenant status
		updateData := map[string]interface{}{
			"status": models.TenantStatusInactive,
		}
		s.tenantRepo.Update(ctx, subscription.TenantID, updateData)
	}

	if err := s.subscriptionRepo.Update(ctx, subscription); err != nil {
		return fmt.Errorf("failed to update subscription: %w", err)
	}

	return nil
}

// GetExpiredTrials returns subscriptions with expired trials
func (s *SubscriptionService) GetExpiredTrials(ctx context.Context) ([]*models.Subscription, error) {
	return s.subscriptionRepo.GetExpiredTrials(ctx)
}

// GetPendingTransitions returns plan transitions that need to be processed
func (s *SubscriptionService) GetPendingTransitions(ctx context.Context) ([]*models.PlanTransition, error) {
	return s.subscriptionRepo.GetPendingTransitions(ctx)
}

// ProcessPendingTransitions processes plan transitions that are now effective
func (s *SubscriptionService) ProcessPendingTransitions(ctx context.Context) error {
	transitions, err := s.GetPendingTransitions(ctx)
	if err != nil {
		return fmt.Errorf("failed to get pending transitions: %w", err)
	}

	for _, transition := range transitions {
		if transition.IsEffective() {
			// Update subscription
			subscription, err := s.subscriptionRepo.GetByID(ctx, transition.SubscriptionID)
			if err != nil {
				continue // Skip if subscription not found
			}

			subscription.PlanID = transition.ToPlanID

			// Get new plan for pricing
			newPlan, err := s.planRepo.GetByID(ctx, transition.ToPlanID)
			if err == nil {
				// Update amount based on billing cycle
				if subscription.BillingCycle == models.BillingCycleYearly {
					subscription.Amount = newPlan.YearlyPrice
				} else {
					subscription.Amount = newPlan.MonthlyPrice
				}
			}

			if err := s.subscriptionRepo.Update(ctx, subscription); err != nil {
				continue // Skip if update fails
			}

			// Mark transition as processed
			now := time.Now()
			transition.ProcessedAt = &now
			s.subscriptionRepo.UpdatePlanTransition(ctx, transition)
		}
	}

	return nil
}

// calculateProratedAmount calculates the prorated amount for plan changes
func calculateProratedAmount(currentPrice, newPrice float64, daysRemaining, totalDays int) float64 {
	if totalDays <= 0 {
		return 0
	}

	dailyDifference := (newPrice - currentPrice) / float64(totalDays)
	return dailyDifference * float64(daysRemaining)
}
