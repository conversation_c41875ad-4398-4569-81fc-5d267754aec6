package services

import (
	"context"
	"errors"
	"fmt"
	"math"
	"regexp"
	"strings"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/repositories"
	"gorm.io/gorm"
)

// tenantService implements TenantService
type tenantService struct {
	tenantRepo repositories.TenantRepository
	planRepo   repositories.TenantPlanRepository
}

// NewTenantService creates a new tenant service
func NewTenantService(tenantRepo repositories.TenantRepository, planRepo repositories.TenantPlanRepository) TenantService {
	return &tenantService{
		tenantRepo: tenantRepo,
		planRepo:   planRepo,
	}
}

// Create creates a new tenant with default settings
func (s *tenantService) Create(ctx context.Context, input CreateTenantInput) (*models.Tenant, error) {
	// Validate plan exists
	plan, err := s.planRepo.GetByID(ctx, input.PlanID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("plan not found")
		}
		return nil, fmt.Errorf("failed to get plan: %w", err)
	}

	if plan.Status != models.PlanStatusActive {
		return nil, fmt.Errorf("plan is not active")
	}

	// Check if domain already exists
	exists, err := s.tenantRepo.CheckDomainExists(ctx, input.Domain, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to check domain: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("domain already exists")
	}

	// Check custom domains
	for _, customDomain := range input.CustomDomains {
		exists, err := s.tenantRepo.CheckDomainExists(ctx, customDomain, 0)
		if err != nil {
			return nil, fmt.Errorf("failed to check custom domain: %w", err)
		}
		if exists {
			return nil, fmt.Errorf("custom domain %s already exists", customDomain)
		}
	}

	// Generate slug from name with uniqueness check
	slug, err := s.generateUniqueSlug(ctx, input.Name)
	if err != nil {
		return nil, fmt.Errorf("failed to generate unique slug: %w", err)
	}

	// Create tenant
	tenant := &models.Tenant{
		Name:         input.Name,
		Domain:       input.Domain,
		Slug:         slug,
		PlanID:       input.PlanID,
		Status:       models.TenantStatusActive,
		OwnerEmail:   input.AdminEmail,
		BillingEmail: input.AdminEmail,
		Settings: models.JSONMap(map[string]interface{}{
			"admin_email":    input.AdminEmail,
			"admin_name":     input.AdminName,
			"custom_domains": input.CustomDomains, // Store as setting instead
		}),
	}

	// Set trial expiry - TODO: Add TrialDays field to TenantPlan model
	// if plan.TrialDays > 0 {
	//     trialExpiry := time.Now().AddDate(0, 0, plan.TrialDays)
	//     tenant.TrialEndsAt = &trialExpiry
	//     tenant.Status = models.TenantStatusTrial
	// }

	// Add company info if provided
	if input.CompanyInfo != nil {
		for k, v := range input.CompanyInfo {
			tenant.Settings[k] = v
		}
	}

	// Create tenant
	if err := s.tenantRepo.Create(ctx, tenant); err != nil {
		return nil, fmt.Errorf("failed to create tenant: %w", err)
	}

	// Load plan data
	tenant.Plan = plan

	return tenant, nil
}

// GetByID retrieves a tenant by ID
func (s *tenantService) GetByID(ctx context.Context, id uint) (*models.Tenant, error) {
	tenant, err := s.tenantRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("tenant not found")
		}
		return nil, fmt.Errorf("failed to get tenant: %w", err)
	}

	return tenant, nil
}

// GetByDomain retrieves a tenant by domain
func (s *tenantService) GetByDomain(ctx context.Context, domain string) (*models.Tenant, error) {
	domain = strings.ToLower(strings.TrimSpace(domain))

	tenant, err := s.tenantRepo.GetByDomain(ctx, domain)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("tenant not found")
		}
		return nil, fmt.Errorf("failed to get tenant: %w", err)
	}

	return tenant, nil
}

// Update updates tenant information
func (s *tenantService) Update(ctx context.Context, id uint, input UpdateTenantInput) (*models.Tenant, error) {
	// Get existing tenant
	tenant, err := s.tenantRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("tenant not found")
		}
		return nil, fmt.Errorf("failed to get tenant: %w", err)
	}

	updates := make(map[string]interface{})

	// Update name
	if input.Name != nil {
		updates["name"] = *input.Name
	}

	// Update domain
	if input.Domain != nil {
		newDomain := strings.ToLower(strings.TrimSpace(*input.Domain))
		if newDomain != tenant.Domain {
			exists, err := s.tenantRepo.CheckDomainExists(ctx, newDomain, id)
			if err != nil {
				return nil, fmt.Errorf("failed to check domain: %w", err)
			}
			if exists {
				return nil, fmt.Errorf("domain already exists")
			}
			updates["domain"] = newDomain
		}
	}

	// Update custom domains
	if input.CustomDomains != nil {
		// Check all new custom domains
		for _, customDomain := range input.CustomDomains {
			exists, err := s.tenantRepo.CheckDomainExists(ctx, customDomain, id)
			if err != nil {
				return nil, fmt.Errorf("failed to check custom domain: %w", err)
			}
			if exists {
				return nil, fmt.Errorf("custom domain %s already exists", customDomain)
			}
		}
		updates["custom_domains"] = input.CustomDomains // Store as regular slice
	}

	// Update settings
	if input.Settings != nil {
		settings := make(models.JSONMap)
		// Copy existing settings
		for k, v := range tenant.Settings {
			settings[k] = v
		}
		// Apply new settings
		for k, v := range input.Settings {
			settings[k] = v
		}
		updates["settings"] = settings
	}

	// Update company info
	if input.CompanyInfo != nil {
		settings := make(models.JSONMap)
		// Copy existing settings
		for k, v := range tenant.Settings {
			settings[k] = v
		}
		// Apply company info
		for k, v := range input.CompanyInfo {
			settings[k] = v
		}
		updates["settings"] = settings
	}

	// Update tenant
	if err := s.tenantRepo.Update(ctx, id, updates); err != nil {
		return nil, fmt.Errorf("failed to update tenant: %w", err)
	}

	// Get updated tenant
	return s.tenantRepo.GetByID(ctx, id)
}

// UpdateStatus updates tenant status
func (s *tenantService) UpdateStatus(ctx context.Context, id uint, status models.TenantStatus) error {
	// Validate status transition
	tenant, err := s.tenantRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("tenant not found")
		}
		return fmt.Errorf("failed to get tenant: %w", err)
	}

	// Check valid status transitions
	if !isValidStatusTransition(tenant.Status, status) {
		return fmt.Errorf("invalid status transition from %s to %s", tenant.Status, status)
	}

	updates := map[string]interface{}{
		"status": status,
	}

	// Handle specific status changes
	switch status {
	case models.TenantStatusActive:
		// Clear trial end date if moving from trial to active
		if tenant.Status == models.TenantStatusTrial {
			updates["trial_ends_at"] = nil
		}
	case models.TenantStatusSuspended:
		updates["suspended_at"] = time.Now()
		updates["suspended_reason"] = "Manual suspension"
	}

	return s.tenantRepo.Update(ctx, id, updates)
}

// Delete soft deletes a tenant
func (s *tenantService) Delete(ctx context.Context, id uint) error {
	// Check if tenant exists
	_, err := s.tenantRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("tenant not found")
		}
		return fmt.Errorf("failed to get tenant: %w", err)
	}

	return s.tenantRepo.Delete(ctx, id)
}

// List retrieves tenants with pagination
func (s *tenantService) List(ctx context.Context, filter ListTenantFilter) (*TenantListResponse, error) {
	// Convert to repository filter
	repoFilter := models.TenantFilter{
		Search:    filter.Search,
		Status:    filter.Status,
		PlanID:    filter.PlanID,
		Page:      filter.Page,
		PageSize:  filter.PageSize,
		SortBy:    filter.SortBy,
		SortOrder: filter.SortOrder,
	}

	// Get tenants
	tenants, total, err := s.tenantRepo.List(ctx, repoFilter)
	if err != nil {
		return nil, fmt.Errorf("failed to list tenants: %w", err)
	}

	// Calculate total pages
	totalPages := int(math.Ceil(float64(total) / float64(filter.PageSize)))
	if totalPages == 0 && total > 0 {
		totalPages = 1
	}

	return &TenantListResponse{
		Tenants:    tenants,
		Total:      total,
		Page:       filter.Page,
		PageSize:   filter.PageSize,
		TotalPages: totalPages,
	}, nil
}

// AssignPlan assigns a plan to tenant
func (s *tenantService) AssignPlan(ctx context.Context, tenantID, planID uint) error {
	// Validate plan exists and is active
	plan, err := s.planRepo.GetByID(ctx, planID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("plan not found")
		}
		return fmt.Errorf("failed to get plan: %w", err)
	}

	if plan.Status != models.PlanStatusActive {
		return fmt.Errorf("plan is not active")
	}

	// Get tenant
	tenant, err := s.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("tenant not found")
		}
		return fmt.Errorf("failed to get tenant: %w", err)
	}

	// Check if downgrading - may need to validate resource usage
	if tenant.PlanID != planID && tenant.Plan != nil {
		// TODO: Implement resource usage validation when downgrading
	}

	// Update tenant plan
	updates := map[string]interface{}{
		"plan_id": planID,
	}

	// TODO: Add TrialDays field to TenantPlan model
	// If new plan has trial and tenant hasn't had trial before
	// if plan.TrialDays > 0 && tenant.TrialEndsAt == nil && tenant.Status == models.TenantStatusActive {
	//     trialEndsAt := time.Now().AddDate(0, 0, plan.TrialDays)
	//     updates["trial_ends_at"] = trialEndsAt
	//     updates["status"] = models.TenantStatusTrial
	// }

	return s.tenantRepo.Update(ctx, tenantID, updates)
}

// CheckResourceLimit checks if tenant has reached resource limit
func (s *tenantService) CheckResourceLimit(ctx context.Context, tenantID uint, resource string) (bool, error) {
	// Get tenant with plan
	tenant, err := s.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		return false, fmt.Errorf("failed to get tenant: %w", err)
	}

	if tenant.Plan == nil {
		return false, fmt.Errorf("tenant has no plan assigned")
	}

	// Check resource limits based on plan
	switch resource {
	case "users":
		if tenant.Plan.MaxUsers == -1 {
			return false, nil // Unlimited
		}
		// TODO: Get actual user count from user service
		return false, nil

	case "projects":
		if tenant.Plan.MaxWebsites == -1 {
			return false, nil // Unlimited
		}
		// TODO: Get actual project count
		return false, nil

	case "storage":
		if tenant.Plan.MaxStorageGB == -1 {
			return false, nil // Unlimited
		}
		// TODO: Get actual storage usage
		return false, nil

	case "api_calls":
		if tenant.Plan.MaxAPICallsPerDay == -1 {
			return false, nil // Unlimited
		}
		// TODO: Get actual API calls count for today
		return false, nil

	default:
		return false, fmt.Errorf("unknown resource type: %s", resource)
	}
}

// GetTenantStats retrieves tenant statistics
func (s *tenantService) GetTenantStats(ctx context.Context, tenantID uint) (*TenantStats, error) {
	// Get tenant with plan
	tenant, err := s.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get tenant: %w", err)
	}

	stats := &TenantStats{
		// TODO: Get actual counts from respective services
		UserCount:      0,
		ProjectCount:   0,
		StorageUsed:    0,
		APICallsToday:  0,
		ResourceLimits: make(map[string]interface{}),
		PlanInfo:       tenant.Plan,
	}

	// Set resource limits from plan
	if tenant.Plan != nil {
		stats.ResourceLimits["max_users"] = tenant.Plan.MaxUsers
		stats.ResourceLimits["max_projects"] = tenant.Plan.MaxWebsites
		stats.ResourceLimits["max_storage"] = tenant.Plan.MaxStorageGB
		stats.ResourceLimits["max_api_calls_per_day"] = tenant.Plan.MaxAPICallsPerDay
	}

	return stats, nil
}

// isValidStatusTransition checks if status transition is valid
func isValidStatusTransition(from, to models.TenantStatus) bool {
	validTransitions := map[models.TenantStatus][]models.TenantStatus{
		models.TenantStatusActive: {
			models.TenantStatusSuspended,
			models.TenantStatusInactive,
			models.TenantStatusDeleted,
		},
		models.TenantStatusTrial: {
			models.TenantStatusActive,
			models.TenantStatusSuspended,
			models.TenantStatusInactive,
			models.TenantStatusDeleted,
		},
		models.TenantStatusSuspended: {
			models.TenantStatusActive,
			models.TenantStatusInactive,
			models.TenantStatusDeleted,
		},
		models.TenantStatusInactive: {
			models.TenantStatusActive,
			models.TenantStatusDeleted,
		},
		models.TenantStatusDeleted: {
			// No valid transitions from deleted
		},
	}

	allowed, ok := validTransitions[from]
	if !ok {
		return false
	}

	for _, status := range allowed {
		if status == to {
			return true
		}
	}

	return false
}

// generateValidSlug creates a valid slug from name that matches ^[a-z0-9-]+$
func generateValidSlug(name string) string {
	// Convert to lowercase and trim
	slug := strings.ToLower(strings.TrimSpace(name))

	// Replace spaces with hyphens
	slug = strings.ReplaceAll(slug, " ", "-")

	// Remove all characters that are not lowercase letters, numbers, or hyphens
	re := regexp.MustCompile(`[^a-z0-9-]`)
	slug = re.ReplaceAllString(slug, "")

	// Remove multiple consecutive hyphens
	re = regexp.MustCompile(`-+`)
	slug = re.ReplaceAllString(slug, "-")

	// Remove leading and trailing hyphens
	slug = strings.Trim(slug, "-")

	// Ensure slug is not empty
	if slug == "" {
		slug = "tenant"
	}

	return slug
}

// generateUniqueSlug creates a unique slug by checking database and adding suffix if needed
func (s *tenantService) generateUniqueSlug(ctx context.Context, name string) (string, error) {
	baseSlug := generateValidSlug(name)
	slug := baseSlug

	// Check if slug exists
	exists, err := s.tenantRepo.CheckSlugExists(ctx, slug, 0)
	if err != nil {
		return "", err
	}

	// If slug doesn't exist, return it
	if !exists {
		return slug, nil
	}

	// If slug exists, try with incrementing numbers
	for i := 1; i <= 100; i++ {
		slug = fmt.Sprintf("%s-%d", baseSlug, i)
		exists, err = s.tenantRepo.CheckSlugExists(ctx, slug, 0)
		if err != nil {
			return "", err
		}
		if !exists {
			return slug, nil
		}
	}

	// If all attempts failed, return error
	return "", fmt.Errorf("could not generate unique slug after 100 attempts")
}
