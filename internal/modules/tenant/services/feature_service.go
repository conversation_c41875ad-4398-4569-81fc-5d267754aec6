package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/repositories"
	"gorm.io/gorm"
)

// tenantFeatureService implements TenantFeatureService
type tenantFeatureService struct {
	featureRepo repositories.TenantFeatureRepository
	catalogRepo repositories.FeatureCatalogRepository
	tenantRepo  repositories.TenantRepository
	planRepo    repositories.TenantPlanRepository
}

// NewTenantFeatureService creates a new tenant feature service
func NewTenantFeatureService(
	featureRepo repositories.TenantFeatureRepository,
	catalogRepo repositories.FeatureCatalogRepository,
	tenantRepo repositories.TenantRepository,
	planRepo repositories.TenantPlanRepository,
) TenantFeatureService {
	return &tenantFeatureService{
		featureRepo: featureRepo,
		catalogRepo: catalogRepo,
		tenantRepo:  tenantRepo,
		planRepo:    planRepo,
	}
}

// Enable enables a feature for tenant
func (s *tenantFeatureService) Enable(ctx context.Context, tenantID uint, featureKey string) error {
	// Validate tenant exists
	tenant, err := s.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("tenant not found")
		}
		return fmt.Errorf("failed to get tenant: %w", err)
	}

	// Check if feature exists in catalog
	catalogFeature, err := s.catalogRepo.GetByKey(ctx, featureKey)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("feature not found in catalog")
		}
		return fmt.Errorf("failed to get feature from catalog: %w", err)
	}

	// Check if feature is active
	if catalogFeature.Status != models.FeatureStatusActive {
		return fmt.Errorf("feature is not active")
	}

	// Check if tenant's plan allows this feature
	if !s.isPlanAllowed(catalogFeature, tenant.PlanID) {
		return fmt.Errorf("feature not available for tenant's plan")
	}

	// Check if feature already exists for tenant
	existingFeature, err := s.featureRepo.GetByKey(ctx, tenantID, featureKey)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("failed to check existing feature: %w", err)
	}

	if existingFeature != nil {
		// Update existing feature
		updates := map[string]interface{}{
			"enabled": true,
		}
		return s.featureRepo.Update(ctx, existingFeature.ID, updates)
	}

	// Create new feature for tenant
	now := time.Now()
	feature := &models.TenantFeature{
		TenantID:          tenantID,
		FeatureKey:        featureKey,
		FeatureName:       catalogFeature.FeatureName,
		Description:       catalogFeature.Description,
		Enabled:           true,
		EnabledAt:         &now,
		Configuration:     json.RawMessage("{}"),
		RolloutPercentage: 100, // Default to 100% rollout
	}

	return s.featureRepo.Create(ctx, feature)
}

// Disable disables a feature for tenant
func (s *tenantFeatureService) Disable(ctx context.Context, tenantID uint, featureKey string) error {
	// Get existing feature
	feature, err := s.featureRepo.GetByKey(ctx, tenantID, featureKey)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("feature not found for tenant")
		}
		return fmt.Errorf("failed to get feature: %w", err)
	}

	// Update feature
	updates := map[string]interface{}{
		"enabled": false,
	}

	return s.featureRepo.Update(ctx, feature.ID, updates)
}

// Toggle toggles a feature state
func (s *tenantFeatureService) Toggle(ctx context.Context, tenantID uint, featureKey string) error {
	// Get existing feature
	feature, err := s.featureRepo.GetByKey(ctx, tenantID, featureKey)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Feature doesn't exist, enable it
			return s.Enable(ctx, tenantID, featureKey)
		}
		return fmt.Errorf("failed to get feature: %w", err)
	}

	// Toggle enabled state
	updates := map[string]interface{}{
		"enabled": !feature.Enabled,
	}

	return s.featureRepo.Update(ctx, feature.ID, updates)
}

// IsEnabled checks if a feature is enabled for tenant and user
func (s *tenantFeatureService) IsEnabled(ctx context.Context, tenantID uint, featureKey, userID string) (bool, error) {
	// Get feature
	feature, err := s.featureRepo.GetByKey(ctx, tenantID, featureKey)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return false, nil // Feature not found means not enabled
		}
		return false, fmt.Errorf("failed to get feature: %w", err)
	}

	// Check if enabled
	if !feature.Enabled {
		return false, nil
	}

	// Check time-based availability
	now := time.Now()
	if feature.AvailableFrom != nil && now.Before(*feature.AvailableFrom) {
		return false, nil
	}
	if feature.AvailableUntil != nil && now.After(*feature.AvailableUntil) {
		return false, nil
	}

	// Check rollout
	if userID != "" {
		return feature.IsEnabledForUser(userID), nil
	}

	// If no user ID, check general rollout percentage
	return feature.RolloutPercentage > 0, nil
}

// GetEnabledFeatures gets all enabled features for tenant
func (s *tenantFeatureService) GetEnabledFeatures(ctx context.Context, tenantID uint) ([]*models.TenantFeature, error) {
	features, err := s.featureRepo.GetEnabledFeatures(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get enabled features: %w", err)
	}

	// Filter out features that require other disabled features
	filteredFeatures := make([]*models.TenantFeature, 0, len(features))
	enabledKeys := make(map[string]bool)

	// First pass: build enabled keys map
	for _, feature := range features {
		enabledKeys[feature.FeatureKey] = true
	}

	// Second pass: filter based on dependencies
	for _, feature := range features {
		if s.areDependenciesMet(feature, enabledKeys) {
			filteredFeatures = append(filteredFeatures, feature)
		}
	}

	return filteredFeatures, nil
}

// BulkToggle enables/disables multiple features
func (s *tenantFeatureService) BulkToggle(ctx context.Context, tenantID uint, featureKeys []string, enabled bool) error {
	// Validate tenant exists
	_, err := s.tenantRepo.GetByID(ctx, tenantID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("tenant not found")
		}
		return fmt.Errorf("failed to get tenant: %w", err)
	}

	if enabled {
		// Enable features one by one to validate each
		for _, key := range featureKeys {
			if err := s.Enable(ctx, tenantID, key); err != nil {
				return fmt.Errorf("failed to enable feature %s: %w", key, err)
			}
		}
		return nil
	}

	// Bulk disable is simpler
	return s.featureRepo.BulkToggle(ctx, tenantID, featureKeys, false)
}

// UpdateConfiguration updates feature configuration
func (s *tenantFeatureService) UpdateConfiguration(ctx context.Context, tenantID uint, featureKey string, config map[string]interface{}) error {
	// Get existing feature
	feature, err := s.featureRepo.GetByKey(ctx, tenantID, featureKey)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("feature not found for tenant")
		}
		return fmt.Errorf("failed to get feature: %w", err)
	}

	// Merge configurations
	newConfig := make(models.JSONMap)

	// Parse existing configuration
	var existingConfig models.JSONMap
	if feature.Configuration != nil {
		if err := json.Unmarshal(feature.Configuration, &existingConfig); err == nil {
			for k, v := range existingConfig {
				newConfig[k] = v
			}
		}
	}

	// Add new configuration
	for k, v := range config {
		newConfig[k] = v
	}

	// Update feature
	updates := map[string]interface{}{
		"configuration": newConfig,
	}

	return s.featureRepo.Update(ctx, feature.ID, updates)
}

// GetFeatureUsage gets feature usage statistics
func (s *tenantFeatureService) GetFeatureUsage(ctx context.Context, tenantID uint, featureKey string) (*FeatureUsage, error) {
	// Get feature
	feature, err := s.featureRepo.GetByKey(ctx, tenantID, featureKey)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("feature not found for tenant")
		}
		return nil, fmt.Errorf("failed to get feature: %w", err)
	}

	usage := &FeatureUsage{
		UsageStats: make(map[string]interface{}),
	}

	if feature.EnabledAt != nil {
		enabledAtStr := feature.EnabledAt.Format(time.RFC3339)
		usage.EnabledAt = &enabledAtStr
	}

	// TODO: Get actual usage statistics from analytics service
	usage.TotalUsers = 0
	usage.ActiveUsers = 0

	// Add configuration info
	usage.UsageStats["rollout_percentage"] = feature.RolloutPercentage
	usage.UsageStats["configuration"] = feature.Configuration

	return usage, nil
}

// Helper methods

func (s *tenantFeatureService) isPlanAllowed(feature *models.FeatureCatalog, planID uint) bool {
	// Check if feature has plan restrictions
	requiredPlans := feature.GetRequiredPlans()
	if len(requiredPlans) > 0 {
		// Feature requires specific plans
		found := false
		for _, reqPlanID := range requiredPlans {
			if reqPlanID == planID {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}

	// Check excluded plans
	excludedPlans := feature.GetExcludedPlans()
	for _, exclPlanID := range excludedPlans {
		if exclPlanID == planID {
			return false
		}
	}

	return true
}

func (s *tenantFeatureService) areDependenciesMet(feature *models.TenantFeature, enabledKeys map[string]bool) bool {
	// Check if all required features are enabled
	requiredFeatures := feature.GetRequiredFeatures()
	for _, reqKey := range requiredFeatures {
		if !enabledKeys[reqKey] {
			return false
		}
	}

	// Check if any conflicting features are enabled
	conflictingFeatures := feature.GetConflictingFeatures()
	for _, conflictKey := range conflictingFeatures {
		if enabledKeys[conflictKey] {
			return false
		}
	}

	return true
}
