package services

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"strings"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/repositories"
	"gorm.io/gorm"
)

// featureCatalogService implements FeatureCatalogService
type featureCatalogService struct {
	catalogRepo repositories.FeatureCatalogRepository
	featureRepo repositories.TenantFeatureRepository
	tenantRepo  repositories.TenantRepository
}

// NewFeatureCatalogService creates a new feature catalog service
func NewFeatureCatalogService(
	catalogRepo repositories.FeatureCatalogRepository,
	featureRepo repositories.TenantFeatureRepository,
	tenantRepo repositories.TenantRepository,
) FeatureCatalogService {
	return &featureCatalogService{
		catalogRepo: catalogRepo,
		featureRepo: featureRepo,
		tenantRepo:  tenantRepo,
	}
}

// Create creates a new feature in catalog
func (s *featureCatalogService) Create(ctx context.Context, input CreateFeatureInput) (*models.FeatureCatalog, error) {
	// Validate feature key is unique
	exists, err := s.catalogRepo.CheckKeyExists(ctx, input.FeatureKey, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to check feature key: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("feature key already exists")
	}

	// Convert plan arrays to JSON
	var requiredPlansJSON, excludedPlansJSON json.RawMessage
	if input.RequiredPlans != nil {
		jsonBytes, _ := json.Marshal(input.RequiredPlans)
		requiredPlansJSON = json.RawMessage(jsonBytes)
	}
	if input.ExcludedPlans != nil {
		jsonBytes, _ := json.Marshal(input.ExcludedPlans)
		excludedPlansJSON = json.RawMessage(jsonBytes)
	}

	// Create feature
	feature := &models.FeatureCatalog{
		FeatureKey:       strings.ToLower(strings.TrimSpace(input.FeatureKey)),
		FeatureName:      input.FeatureName,
		Description:      input.Description,
		Category:         input.Category,
		RequiredPlans:    requiredPlansJSON,
		ExcludedPlans:    excludedPlansJSON,
		Status:           models.FeatureStatusActive,
		IsBeta:           input.IsBeta,
		IsExperimental:   input.IsExperimental,
		IsDeprecated:     false,
		DocumentationURL: input.DocumentationURL,
	}

	if err := s.catalogRepo.Create(ctx, feature); err != nil {
		return nil, fmt.Errorf("failed to create feature: %w", err)
	}

	return feature, nil
}

// GetByKey retrieves a feature by key
func (s *featureCatalogService) GetByKey(ctx context.Context, featureKey string) (*models.FeatureCatalog, error) {
	featureKey = strings.ToLower(strings.TrimSpace(featureKey))

	feature, err := s.catalogRepo.GetByKey(ctx, featureKey)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("feature not found")
		}
		return nil, fmt.Errorf("failed to get feature: %w", err)
	}

	return feature, nil
}

// Update updates feature information
func (s *featureCatalogService) Update(ctx context.Context, id uint, input UpdateFeatureInput) (*models.FeatureCatalog, error) {
	// Check if feature exists
	_, err := s.catalogRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("feature not found")
		}
		return nil, fmt.Errorf("failed to get feature: %w", err)
	}

	updates := make(map[string]interface{})

	// Update basic fields
	if input.FeatureName != nil {
		updates["feature_name"] = *input.FeatureName
	}

	if input.Description != nil {
		updates["description"] = *input.Description
	}

	if input.Category != nil {
		updates["category"] = *input.Category
	}

	// Update plan restrictions
	if input.RequiredPlans != nil {
		updates["required_plans"] = input.RequiredPlans
	}

	if input.ExcludedPlans != nil {
		updates["excluded_plans"] = input.ExcludedPlans
	}

	// Update flags
	if input.IsBeta != nil {
		updates["is_beta"] = *input.IsBeta
	}

	if input.IsExperimental != nil {
		updates["is_experimental"] = *input.IsExperimental
	}

	if input.DocumentationURL != nil {
		updates["documentation_url"] = *input.DocumentationURL
	}

	if input.Status != nil {
		// Validate status change
		if *input.Status == models.FeatureStatusDeprecated {
			updates["is_deprecated"] = true
		}
		updates["status"] = *input.Status
	}

	// Update feature
	if err := s.catalogRepo.Update(ctx, id, updates); err != nil {
		return nil, fmt.Errorf("failed to update feature: %w", err)
	}

	// Get updated feature
	return s.catalogRepo.GetByID(ctx, id)
}

// Deprecate marks a feature as deprecated
func (s *featureCatalogService) Deprecate(ctx context.Context, id uint) error {
	updates := map[string]interface{}{
		"status":        models.FeatureStatusDeprecated,
		"is_deprecated": true,
	}

	return s.catalogRepo.Update(ctx, id, updates)
}

// List retrieves features with filters
func (s *featureCatalogService) List(ctx context.Context, filter models.FeatureCatalogFilter) (*FeatureListResponse, error) {
	// Get features
	features, total, err := s.catalogRepo.List(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to list features: %w", err)
	}

	// Calculate total pages
	pageSize := filter.PageSize
	if pageSize <= 0 {
		pageSize = 20
	}
	totalPages := int(math.Ceil(float64(total) / float64(pageSize)))
	if totalPages == 0 && total > 0 {
		totalPages = 1
	}

	page := filter.Page
	if page <= 0 {
		page = 1
	}

	return &FeatureListResponse{
		Features:   features,
		Total:      total,
		Page:       page,
		PageSize:   pageSize,
		TotalPages: totalPages,
	}, nil
}

// GetFeaturesForPlan gets features available for a plan
func (s *featureCatalogService) GetFeaturesForPlan(ctx context.Context, planID uint) ([]*models.FeatureCatalog, error) {
	features, err := s.catalogRepo.GetFeaturesForPlan(ctx, planID)
	if err != nil {
		return nil, fmt.Errorf("failed to get features for plan: %w", err)
	}

	return features, nil
}

// GetFeatureAdoption gets adoption statistics
func (s *featureCatalogService) GetFeatureAdoption(ctx context.Context) ([]*FeatureAdoptionStats, error) {
	// Get all active features
	features, err := s.catalogRepo.GetActiveFeatures(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get active features: %w", err)
	}

	// Convert to adoption stats
	stats := make([]*FeatureAdoptionStats, len(features))
	for i, feature := range features {
		stats[i] = &FeatureAdoptionStats{
			FeatureKey:          feature.FeatureKey,
			FeatureName:         feature.FeatureName,
			Category:            feature.Category,
			EnabledTenantsCount: feature.EnabledTenantsCount,
			AdoptionRate:        feature.AdoptionRate,
		}

		// Get total tenant count if not already loaded
		if stats[i].TotalTenants == 0 {
			var totalTenants int64
			s.tenantRepo.List(ctx, models.TenantFilter{PageSize: 1})
			stats[i].TotalTenants = int(totalTenants)
		}
	}

	return stats, nil
}
