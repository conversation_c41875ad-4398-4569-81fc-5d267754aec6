package models

// CreateSubscriptionRequest represents the request to create a subscription
type CreateSubscriptionRequest struct {
	TenantID     uint         `json:"tenant_id" validate:"required,min=1"`
	PlanID       uint         `json:"plan_id" validate:"required,min=1"`
	BillingCycle BillingCycle `json:"billing_cycle" validate:"required,oneof=monthly yearly"`
}

// UpgradePlanRequest represents the request to upgrade a plan
type UpgradePlanRequest struct {
	NewPlanID uint `json:"new_plan_id" validate:"required,min=1"`
}

// DowngradePlanRequest represents the request to downgrade a plan
type DowngradePlanRequest struct {
	NewPlanID uint `json:"new_plan_id" validate:"required,min=1"`
}

// CancelSubscriptionRequest represents the request to cancel a subscription
type CancelSubscriptionRequest struct {
	CancelAtPeriodEnd bool   `json:"cancel_at_period_end"`
	Reason            string `json:"reason,omitempty" validate:"omitempty,max=500"`
}

// PaymentFailureRequest represents the request to record a payment failure
type PaymentFailureRequest struct {
	Amount        float64 `json:"amount" validate:"required,min=0"`
	FailureReason string  `json:"failure_reason" validate:"required,max=1000"`
	ExternalID    string  `json:"external_id,omitempty" validate:"omitempty,max=255"`
}

// SubscriptionFilter represents filters for querying subscriptions
type SubscriptionFilter struct {
	TenantID     uint               `json:"tenant_id,omitempty"`
	PlanID       uint               `json:"plan_id,omitempty"`
	Status       SubscriptionStatus `json:"status,omitempty"`
	BillingCycle BillingCycle       `json:"billing_cycle,omitempty"`
	Page         int                `json:"page,omitempty"`
	PageSize     int                `json:"page_size,omitempty"`
	SortBy       string             `json:"sort_by,omitempty"`
	SortOrder    string             `json:"sort_order,omitempty"`
}
