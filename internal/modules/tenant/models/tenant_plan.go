package models

import (
	"database/sql/driver"
	"time"

	"gorm.io/gorm"
)

// PlanStatus represents the status of a plan
// @Enum active,inactive,deprecated
type PlanStatus string

const (
	PlanStatusActive     PlanStatus = "active"
	PlanStatusInactive   PlanStatus = "inactive"
	PlanStatusDeprecated PlanStatus = "deprecated"
)

// Scan implements sql.Scanner interface
func (s *PlanStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = PlanStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s PlanStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// TenantPlan represents a subscription plan
type TenantPlan struct {
	ID                uint       `json:"id" gorm:"primaryKey"`
	Name              string     `json:"name" gorm:"not null" validate:"required,min=2,max=100"`
	Slug              string     `json:"slug" gorm:"type:varchar(255);uniqueIndex;not null" validate:"required,min=2,max=100,slug"`
	Description       string     `json:"description,omitempty" validate:"max=500"`
	MonthlyPrice      float64    `json:"monthly_price" gorm:"type:decimal(10,2);not null;default:0" validate:"min=0"`
	YearlyPrice       float64    `json:"yearly_price" gorm:"type:decimal(10,2);not null;default:0" validate:"min=0"`
	Currency          string     `json:"currency" gorm:"type:char(3);not null;default:'USD'" validate:"required,len=3,uppercase"`
	MaxUsers          int        `json:"max_users" gorm:"not null;default:-1"` // -1 means unlimited
	MaxWebsites       int        `json:"max_websites" gorm:"not null;default:1"`
	MaxStorageGB      int        `json:"max_storage_gb" gorm:"not null;default:10"`
	MaxBandwidthGB    int        `json:"max_bandwidth_gb" gorm:"not null;default:100"`
	MaxPostsPerMonth  int        `json:"max_posts_per_month" gorm:"not null;default:-1"`
	MaxAPICallsPerDay int        `json:"max_api_calls_per_day" gorm:"not null;default:10000"`
	Features          JSONMap    `json:"features,omitempty" gorm:"type:json"`
	DisplayOrder      int        `json:"display_order" gorm:"not null;default:0"`
	IsFeatured        bool       `json:"is_featured" gorm:"not null;default:false"`
	IsVisible         bool       `json:"is_visible" gorm:"not null;default:true"`
	Status            PlanStatus `json:"status" gorm:"type:enum('active','inactive','deprecated');not null;default:'active'"`
	CreatedAt         time.Time  `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt         time.Time  `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations
	Tenants []Tenant `json:"tenants,omitempty" gorm:"foreignKey:PlanID"`
}

// TableName specifies the table name for TenantPlan
func (TenantPlan) TableName() string {
	return "tenant_plans"
}

// IsActive checks if plan is active
func (p *TenantPlan) IsActive() bool {
	return p.Status == PlanStatusActive
}

// IsUnlimited checks if a resource is unlimited
func (p *TenantPlan) IsUnlimitedUsers() bool {
	return p.MaxUsers == -1
}

// IsUnlimitedPosts checks if posts are unlimited
func (p *TenantPlan) IsUnlimitedPosts() bool {
	return p.MaxPostsPerMonth == -1
}

// BeforeCreate hook for TenantPlan
func (p *TenantPlan) BeforeCreate(tx *gorm.DB) error {
	if p.Features == nil {
		p.Features = make(JSONMap)
	}
	return nil
}

// PlanFilter represents filters for querying plans
type PlanFilter struct {
	Status     PlanStatus `json:"status,omitempty"`
	IsVisible  *bool      `json:"is_visible,omitempty"`
	IsFeatured *bool      `json:"is_featured,omitempty"`
	MinPrice   float64    `json:"min_price,omitempty"`
	MaxPrice   float64    `json:"max_price,omitempty"`
	Page       int        `json:"page,omitempty"`
	PageSize   int        `json:"page_size,omitempty"`
	SortBy     string     `json:"sort_by,omitempty"`
	SortOrder  string     `json:"sort_order,omitempty"`
}

// PlanCreateRequest represents the request to create a plan
type PlanCreateRequest struct {
	Name              string  `json:"name" validate:"required,min=2,max=100"`
	Slug              string  `json:"slug,omitempty" validate:"omitempty,min=2,max=100,slug"`
	Description       string  `json:"description,omitempty" validate:"max=500"`
	MonthlyPrice      float64 `json:"monthly_price" validate:"min=0"`
	YearlyPrice       float64 `json:"yearly_price" validate:"min=0"`
	Currency          string  `json:"currency" validate:"required,len=3,uppercase"`
	MaxUsers          int     `json:"max_users" validate:"min=-1"`
	MaxWebsites       int     `json:"max_websites" validate:"min=1"`
	MaxStorageGB      int     `json:"max_storage_gb" validate:"min=1"`
	MaxBandwidthGB    int     `json:"max_bandwidth_gb" validate:"min=1"`
	MaxPostsPerMonth  int     `json:"max_posts_per_month" validate:"min=-1"`
	MaxAPICallsPerDay int     `json:"max_api_calls_per_day" validate:"min=100"`
	Features          JSONMap `json:"features,omitempty"`
	DisplayOrder      int     `json:"display_order"`
	IsFeatured        bool    `json:"is_featured"`
	IsVisible         bool    `json:"is_visible"`
}

// PlanUpdateRequest represents the request to update a plan
type PlanUpdateRequest struct {
	Name              string     `json:"name,omitempty" validate:"omitempty,min=2,max=100"`
	Description       string     `json:"description,omitempty" validate:"max=500"`
	MonthlyPrice      float64    `json:"monthly_price,omitempty" validate:"min=0"`
	YearlyPrice       float64    `json:"yearly_price,omitempty" validate:"min=0"`
	MaxUsers          int        `json:"max_users,omitempty" validate:"min=-1"`
	MaxWebsites       int        `json:"max_websites,omitempty" validate:"min=1"`
	MaxStorageGB      int        `json:"max_storage_gb,omitempty" validate:"min=1"`
	MaxBandwidthGB    int        `json:"max_bandwidth_gb,omitempty" validate:"min=1"`
	MaxPostsPerMonth  int        `json:"max_posts_per_month,omitempty" validate:"min=-1"`
	MaxAPICallsPerDay int        `json:"max_api_calls_per_day,omitempty" validate:"min=100"`
	Features          JSONMap    `json:"features,omitempty"`
	DisplayOrder      int        `json:"display_order,omitempty"`
	IsFeatured        bool       `json:"is_featured,omitempty"`
	IsVisible         bool       `json:"is_visible,omitempty"`
	Status            PlanStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive deprecated"`
}

// ResourceUsage represents current resource usage for a tenant
type ResourceUsage struct {
	TenantID        uint      `json:"tenant_id"`
	UsersCount      int       `json:"users_count"`
	WebsitesCount   int       `json:"websites_count"`
	StorageUsedGB   float64   `json:"storage_used_gb"`
	BandwidthUsedGB float64   `json:"bandwidth_used_gb"`
	PostsThisMonth  int       `json:"posts_this_month"`
	APICallsToday   int       `json:"api_calls_today"`
	LastCalculated  time.Time `json:"last_calculated"`
}
