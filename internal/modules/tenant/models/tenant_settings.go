package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

// DataType represents the data type of a setting
// @Enum string,number,boolean,json,array
type DataType string

const (
	DataTypeString  DataType = "string"
	DataTypeNumber  DataType = "number"
	DataTypeBoolean DataType = "boolean"
	DataTypeJSON    DataType = "json"
	DataTypeArray   DataType = "array"
)

// Scan implements sql.Scanner interface
func (d *DataType) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*d = DataType(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (d DataType) Value() (driver.Value, error) {
	return string(d), nil
}

// TenantSetting represents a configuration setting for a tenant
type TenantSetting struct {
	ID              uint            `json:"id" gorm:"primaryKey"`
	TenantID        uint            `json:"tenant_id" gorm:"not null;index:idx_tenant_category_key,unique"`
	Category        string          `json:"category" gorm:"type:varchar(50);not null;index:idx_tenant_category_key,unique" validate:"required,max=50"`
	Key             string          `json:"key" gorm:"type:varchar(100);not null;index:idx_tenant_category_key,unique" validate:"required,max=100"`
	Value           json.RawMessage `json:"value" gorm:"type:json;not null" swaggertype:"object"`
	Description     string          `json:"description,omitempty" validate:"max=500"`
	DataType        DataType        `json:"data_type" gorm:"type:enum('string','number','boolean','json','array');not null;default:'string'"`
	ValidationRules json.RawMessage `json:"validation_rules,omitempty" gorm:"type:json" swaggertype:"object"`
	DefaultValue    json.RawMessage `json:"default_value,omitempty" gorm:"type:json" swaggertype:"object"`
	IsEncrypted     bool            `json:"is_encrypted" gorm:"not null;default:false"`
	IsPublic        bool            `json:"is_public" gorm:"not null;default:false"`
	UpdatedBy       uint            `json:"updated_by,omitempty"`
	CreatedAt       time.Time       `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt       time.Time       `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations
	Tenant *Tenant `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
}

// TableName specifies the table name for TenantSetting
func (TenantSetting) TableName() string {
	return "tenant_settings"
}

// GetValueAsString returns the value as string
func (s *TenantSetting) GetValueAsString() (string, error) {
	var value string
	err := json.Unmarshal(s.Value, &value)
	return value, err
}

// GetValueAsInt returns the value as int
func (s *TenantSetting) GetValueAsInt() (int, error) {
	var value int
	err := json.Unmarshal(s.Value, &value)
	return value, err
}

// GetValueAsBool returns the value as bool
func (s *TenantSetting) GetValueAsBool() (bool, error) {
	var value bool
	err := json.Unmarshal(s.Value, &value)
	return value, err
}

// GetValueAsJSON returns the value as a map
func (s *TenantSetting) GetValueAsJSON() (map[string]interface{}, error) {
	var value map[string]interface{}
	err := json.Unmarshal(s.Value, &value)
	return value, err
}

// SetValue sets the value with proper JSON encoding
func (s *TenantSetting) SetValue(value interface{}) error {
	bytes, err := json.Marshal(value)
	if err != nil {
		return err
	}
	s.Value = bytes
	return nil
}

// SettingFilter represents filters for querying settings
type SettingFilter struct {
	TenantID   uint     `json:"tenant_id,omitempty"`
	Category   string   `json:"category,omitempty"`
	Categories []string `json:"categories,omitempty"`
	IsPublic   *bool    `json:"is_public,omitempty"`
	Search     string   `json:"search,omitempty"`
}

// SettingCreateRequest represents the request to create a setting
type SettingCreateRequest struct {
	Category        string          `json:"category" validate:"required,max=50"`
	Key             string          `json:"key" validate:"required,max=100"`
	Value           interface{}     `json:"value" validate:"required"`
	Description     string          `json:"description,omitempty" validate:"max=500"`
	DataType        DataType        `json:"data_type" validate:"required,oneof=string number boolean json array"`
	ValidationRules json.RawMessage `json:"validation_rules,omitempty"`
	DefaultValue    interface{}     `json:"default_value,omitempty"`
	IsEncrypted     bool            `json:"is_encrypted"`
	IsPublic        bool            `json:"is_public"`
}

// SettingUpdateRequest represents the request to update a setting
type SettingUpdateRequest struct {
	Value           interface{}     `json:"value,omitempty"`
	Description     string          `json:"description,omitempty" validate:"max=500"`
	ValidationRules json.RawMessage `json:"validation_rules,omitempty"`
	DefaultValue    interface{}     `json:"default_value,omitempty"`
	IsPublic        bool            `json:"is_public,omitempty"`
}

// BulkSettingUpdateRequest represents the request to update multiple settings
type BulkSettingUpdateRequest struct {
	Settings []SettingUpdate `json:"settings" validate:"required,dive"`
}

// SettingUpdate represents a single setting update in bulk operation
type SettingUpdate struct {
	Category string      `json:"category" validate:"required"`
	Key      string      `json:"key" validate:"required"`
	Value    interface{} `json:"value" validate:"required"`
}

// SettingCategory represents a group of related settings
type SettingCategory struct {
	Name        string `json:"name"`
	DisplayName string `json:"display_name"`
	Description string `json:"description"`
	Icon        string `json:"icon,omitempty"`
	Order       int    `json:"order"`
}

// PredefinedCategories returns the list of predefined setting categories
func PredefinedCategories() []SettingCategory {
	return []SettingCategory{
		{Name: "general", DisplayName: "General Settings", Description: "Basic configuration", Order: 1},
		{Name: "branding", DisplayName: "Branding", Description: "Logo, colors, and appearance", Order: 2},
		{Name: "email", DisplayName: "Email Settings", Description: "Email configuration and templates", Order: 3},
		{Name: "security", DisplayName: "Security", Description: "Security and authentication settings", Order: 4},
		{Name: "api", DisplayName: "API Settings", Description: "API keys and integration settings", Order: 5},
		{Name: "notifications", DisplayName: "Notifications", Description: "Notification preferences", Order: 6},
		{Name: "billing", DisplayName: "Billing", Description: "Billing and payment settings", Order: 7},
		{Name: "advanced", DisplayName: "Advanced", Description: "Advanced configuration options", Order: 8},
	}
}
