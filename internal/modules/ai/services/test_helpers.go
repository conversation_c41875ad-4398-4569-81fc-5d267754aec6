package services

import (
	"context"
	"fmt"
	"strings"

	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// MockLogger is a mock implementation of utils.Logger for testing
type MockLogger struct {
	logs []string
}

func NewMockLogger() *MockLogger {
	return &MockLogger{
		logs: make([]string, 0),
	}
}

// Basic logging methods
func (m *MockLogger) Debug(args ...interface{}) {
	m.logs = append(m.logs, "DEBUG: "+fmt.Sprint(args...))
}

func (m *MockLogger) Info(args ...interface{}) {
	m.logs = append(m.logs, "INFO: "+fmt.Sprint(args...))
}

func (m *MockLogger) Warn(args ...interface{}) {
	m.logs = append(m.logs, "WARN: "+fmt.Sprint(args...))
}

func (m *<PERSON>ckLogger) Error(args ...interface{}) {
	m.logs = append(m.logs, "ERROR: "+fmt.Sprint(args...))
}

func (m *MockLogger) Fatal(args ...interface{}) {
	m.logs = append(m.logs, "FATAL: "+fmt.Sprint(args...))
}

func (m *MockLogger) Panic(args ...interface{}) {
	m.logs = append(m.logs, "PANIC: "+fmt.Sprint(args...))
}

// Formatted logging methods
func (m *MockLogger) Debugf(format string, args ...interface{}) {
	m.logs = append(m.logs, "DEBUG: "+fmt.Sprintf(format, args...))
}

func (m *MockLogger) Infof(format string, args ...interface{}) {
	m.logs = append(m.logs, "INFO: "+fmt.Sprintf(format, args...))
}

func (m *MockLogger) Warnf(format string, args ...interface{}) {
	m.logs = append(m.logs, "WARN: "+fmt.Sprintf(format, args...))
}

func (m *MockLogger) Errorf(format string, args ...interface{}) {
	m.logs = append(m.logs, "ERROR: "+fmt.Sprintf(format, args...))
}

func (m *MockLogger) Fatalf(format string, args ...interface{}) {
	m.logs = append(m.logs, "FATAL: "+fmt.Sprintf(format, args...))
}

func (m *MockLogger) Panicf(format string, args ...interface{}) {
	m.logs = append(m.logs, "PANIC: "+fmt.Sprintf(format, args...))
}

// With fields
func (m *MockLogger) WithField(key string, value interface{}) utils.Logger {
	return m
}

func (m *MockLogger) WithFields(fields utils.Fields) utils.Logger {
	return m
}

func (m *MockLogger) WithError(err error) utils.Logger {
	return m
}

func (m *MockLogger) WithContext(ctx context.Context) utils.Logger {
	return m
}

// Get underlying logger
func (m *MockLogger) GetLogger() interface{} {
	return m
}

// Test helper methods
func (m *MockLogger) GetLogs() []string {
	return m.logs
}

func (m *MockLogger) Reset() {
	m.logs = make([]string, 0)
}

func (m *MockLogger) ContainsLog(substring string) bool {
	for _, log := range m.logs {
		if strings.Contains(log, substring) {
			return true
		}
	}
	return false
}
