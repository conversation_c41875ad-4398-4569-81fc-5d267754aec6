package repositories

import (
	"context"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
)

// AIChatSessionRepository defines the interface for AI chat session operations
type AIChatSessionRepository interface {
	// Session operations
	CreateSession(ctx context.Context, session *models.AIChatSession) error
	GetSessionByID(ctx context.Context, id uint) (*models.AIChatSession, error)
	UpdateSession(ctx context.Context, session *models.AIChatSession) error
	DeleteSession(ctx context.Context, id uint) error
	ArchiveSession(ctx context.Context, id uint) error
	ListSessions(ctx context.Context, filter models.ChatSessionFilter) ([]models.AIChatSession, int64, error)
	GetSessionsByTenant(ctx context.Context, tenantID uint, filter models.ChatSessionFilter) ([]models.AIChatSession, int64, error)
	GetSessionsByUser(ctx context.Context, userID uint, filter models.ChatSessionFilter) ([]models.AIChatSession, int64, error)
	GetSessionsByWebsite(ctx context.Context, websiteID uint, filter models.ChatSessionFilter) ([]models.AIChatSession, int64, error)

	// Session statistics
	GetSessionStats(ctx context.Context, tenantID uint, websiteID *uint) (*models.MessageStats, error)
	GetActiveSessionsCount(ctx context.Context, tenantID uint, websiteID *uint) (int64, error)

	// Session context operations
	UpdateSessionContext(ctx context.Context, sessionID uint, context models.JSONMap) error
	GetSessionContext(ctx context.Context, sessionID uint) (models.JSONMap, error)

	// Session timeout operations
	GetExpiredSessions(ctx context.Context, olderThan int64) ([]models.AIChatSession, error)
	CleanupExpiredSessions(ctx context.Context, olderThan int64) error

	// Bulk operations
	BulkArchiveSessions(ctx context.Context, sessionIDs []uint) error
	BulkDeleteSessions(ctx context.Context, sessionIDs []uint) error
}

// AIChatMessageRepository defines the interface for AI chat message operations
type AIChatMessageRepository interface {
	// Message operations
	CreateMessage(ctx context.Context, message *models.AIChatMessage) error
	GetMessageByID(ctx context.Context, id uint) (*models.AIChatMessage, error)
	UpdateMessage(ctx context.Context, message *models.AIChatMessage) error
	DeleteMessage(ctx context.Context, id uint) error
	ListMessages(ctx context.Context, filter models.ChatMessageFilter) ([]models.AIChatMessage, int64, error)
	GetMessagesBySession(ctx context.Context, sessionID uint, filter models.ChatMessageFilter) ([]models.AIChatMessage, int64, error)
	GetMessagesByUser(ctx context.Context, userID uint, filter models.ChatMessageFilter) ([]models.AIChatMessage, int64, error)

	// Message statistics
	GetMessageStats(ctx context.Context, sessionID uint) (*models.MessageStats, error)
	GetMessageStatsForTenant(ctx context.Context, tenantID uint, websiteID *uint) (*models.MessageStats, error)
	GetTokenUsageStats(ctx context.Context, sessionID uint) (int64, error)

	// Message search
	SearchMessages(ctx context.Context, request models.SearchChatMessagesRequest) ([]models.AIChatMessage, int64, error)
	GetConversationHistory(ctx context.Context, sessionID uint, limit int) ([]models.AIChatMessage, error)

	// Bulk operations
	BulkCreateMessages(ctx context.Context, messages []models.AIChatMessage) error
	BulkDeleteMessages(ctx context.Context, messageIDs []uint) error
	DeleteMessagesBySession(ctx context.Context, sessionID uint) error

	// Export operations
	ExportMessages(ctx context.Context, request models.ExportChatMessagesRequest) ([]models.AIChatMessage, error)

	// Context window operations
	GetContextWindowMessages(ctx context.Context, sessionID uint, maxTokens int) ([]models.AIChatMessage, error)
	GetLatestMessages(ctx context.Context, sessionID uint, limit int) ([]models.AIChatMessage, error)
}

// AIChatRepository combines both session and message repositories
type AIChatRepository interface {
	AIChatSessionRepository
	AIChatMessageRepository
}
