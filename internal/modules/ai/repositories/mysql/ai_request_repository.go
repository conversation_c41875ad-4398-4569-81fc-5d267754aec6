package mysql

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// aiRequestRepository implements the AIRequestRepository interface for MySQL
type aiRequestRepository struct {
	db *gorm.DB
}

// NewAIRequestRepository creates a new AI request repository with MySQL backend
func NewAIRequestRepository(db *gorm.DB) repositories.AIRequestRepository {
	return &aiRequestRepository{db: db}
}

// Create creates a new AI request in the database
func (r *aiRequestRepository) Create(ctx context.Context, request *models.AIRequest) error {
	return r.db.WithContext(ctx).Create(request).Error
}

// GetByID retrieves an AI request by its ID
func (r *aiRequestRepository) GetByID(ctx context.Context, id uint) (*models.AIRequest, error) {
	var request models.AIRequest
	if err := r.db.WithContext(ctx).First(&request, id).Error; err != nil {
		return nil, err
	}
	return &request, nil
}

// GetByIDWithRelations retrieves an AI request by its ID with related data
func (r *aiRequestRepository) GetByIDWithRelations(ctx context.Context, id uint) (*models.AIRequest, error) {
	var request models.AIRequest
	if err := r.db.WithContext(ctx).
		Preload("Tenant").
		Preload("Website").
		Preload("User").
		Preload("Model").
		First(&request, id).Error; err != nil {
		return nil, err
	}
	return &request, nil
}

// Update updates an AI request in the database
func (r *aiRequestRepository) Update(ctx context.Context, request *models.AIRequest) error {
	return r.db.WithContext(ctx).Save(request).Error
}

// Delete deletes an AI request from the database
func (r *aiRequestRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.AIRequest{}, id).Error
}

// List retrieves AI requests with filtering and pagination
func (r *aiRequestRepository) List(ctx context.Context, filter models.AIRequestFilter, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	var requests []models.AIRequest

	query := r.db.WithContext(ctx).Model(&models.AIRequest{})
	query = r.applyFilter(query, filter)

	if pag != nil {
		// Apply cursor pagination
		if pag.Cursor != "" {
			query = query.Where("id < ?", pag.Cursor)
		}

		query = query.Order("id DESC").Limit(pag.Limit + 1)
	}

	if err := query.Find(&requests).Error; err != nil {
		return nil, nil, err
	}

	// Handle pagination response
	if pag != nil {
		return r.handlePaginationResponse(requests, pag)
	}

	return requests, nil, nil
}

// ListWithRelations retrieves AI requests with related data, filtering, and pagination
func (r *aiRequestRepository) ListWithRelations(ctx context.Context, filter models.AIRequestFilter, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	var requests []models.AIRequest

	query := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Preload("Tenant").
		Preload("Website").
		Preload("User").
		Preload("Model")

	query = r.applyFilter(query, filter)

	if pag != nil {
		// Apply cursor pagination
		if pag.Cursor != "" {
			query = query.Where("id < ?", pag.Cursor)
		}

		query = query.Order("id DESC").Limit(pag.Limit + 1)
	}

	if err := query.Find(&requests).Error; err != nil {
		return nil, nil, err
	}

	// Handle pagination response
	if pag != nil {
		return r.handlePaginationResponse(requests, pag)
	}

	return requests, nil, nil
}

// GetByStatus retrieves AI requests by status for a tenant
func (r *aiRequestRepository) GetByStatus(ctx context.Context, tenantID uint, status models.AIRequestStatus, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	filter := models.AIRequestFilter{
		TenantID: &tenantID,
		Status:   &status,
	}
	return r.List(ctx, filter, pag)
}

// UpdateStatus updates the status of an AI request
func (r *aiRequestRepository) UpdateStatus(ctx context.Context, id uint, status models.AIRequestStatus) error {
	return r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("id = ?", id).
		Update("status", status).Error
}

// BulkUpdateStatus updates the status of multiple AI requests
func (r *aiRequestRepository) BulkUpdateStatus(ctx context.Context, ids []uint, status models.AIRequestStatus) error {
	return r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("id IN ?", ids).
		Update("status", status).Error
}

// GetByTenant retrieves AI requests for a specific tenant
func (r *aiRequestRepository) GetByTenant(ctx context.Context, tenantID uint, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	filter := models.AIRequestFilter{TenantID: &tenantID}
	return r.List(ctx, filter, pag)
}

// GetByTenantAndType retrieves AI requests for a tenant filtered by type
func (r *aiRequestRepository) GetByTenantAndType(ctx context.Context, tenantID uint, requestType models.AIRequestType, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	filter := models.AIRequestFilter{
		TenantID:    &tenantID,
		RequestType: &requestType,
	}
	return r.List(ctx, filter, pag)
}

// GetByTenantAndStatus retrieves AI requests for a tenant filtered by status
func (r *aiRequestRepository) GetByTenantAndStatus(ctx context.Context, tenantID uint, status models.AIRequestStatus, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	filter := models.AIRequestFilter{
		TenantID: &tenantID,
		Status:   &status,
	}
	return r.List(ctx, filter, pag)
}

// GetByWebsite retrieves AI requests for a specific website
func (r *aiRequestRepository) GetByWebsite(ctx context.Context, websiteID uint, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	filter := models.AIRequestFilter{WebsiteID: &websiteID}
	return r.List(ctx, filter, pag)
}

// GetByWebsiteAndType retrieves AI requests for a website filtered by type
func (r *aiRequestRepository) GetByWebsiteAndType(ctx context.Context, websiteID uint, requestType models.AIRequestType, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	filter := models.AIRequestFilter{
		WebsiteID:   &websiteID,
		RequestType: &requestType,
	}
	return r.List(ctx, filter, pag)
}

// GetByWebsiteAndStatus retrieves AI requests for a website filtered by status
func (r *aiRequestRepository) GetByWebsiteAndStatus(ctx context.Context, websiteID uint, status models.AIRequestStatus, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	filter := models.AIRequestFilter{
		WebsiteID: &websiteID,
		Status:    &status,
	}
	return r.List(ctx, filter, pag)
}

// GetByUser retrieves AI requests for a specific user
func (r *aiRequestRepository) GetByUser(ctx context.Context, userID uint, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	filter := models.AIRequestFilter{UserID: &userID}
	return r.List(ctx, filter, pag)
}

// GetByUserAndType retrieves AI requests for a user filtered by type
func (r *aiRequestRepository) GetByUserAndType(ctx context.Context, userID uint, requestType models.AIRequestType, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	filter := models.AIRequestFilter{
		UserID:      &userID,
		RequestType: &requestType,
	}
	return r.List(ctx, filter, pag)
}

// GetByUserAndStatus retrieves AI requests for a user filtered by status
func (r *aiRequestRepository) GetByUserAndStatus(ctx context.Context, userID uint, status models.AIRequestStatus, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	filter := models.AIRequestFilter{
		UserID: &userID,
		Status: &status,
	}
	return r.List(ctx, filter, pag)
}

// GetByModel retrieves AI requests for a specific model
func (r *aiRequestRepository) GetByModel(ctx context.Context, modelID uint, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	filter := models.AIRequestFilter{ModelID: &modelID}
	return r.List(ctx, filter, pag)
}

// GetByModelAndType retrieves AI requests for a model filtered by type
func (r *aiRequestRepository) GetByModelAndType(ctx context.Context, modelID uint, requestType models.AIRequestType, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	filter := models.AIRequestFilter{
		ModelID:     &modelID,
		RequestType: &requestType,
	}
	return r.List(ctx, filter, pag)
}

// GetByModelAndStatus retrieves AI requests for a model filtered by status
func (r *aiRequestRepository) GetByModelAndStatus(ctx context.Context, modelID uint, status models.AIRequestStatus, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	filter := models.AIRequestFilter{
		ModelID: &modelID,
		Status:  &status,
	}
	return r.List(ctx, filter, pag)
}

// GetByTimeRange retrieves AI requests within a time range
func (r *aiRequestRepository) GetByTimeRange(ctx context.Context, tenantID uint, startTime, endTime time.Time, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	filter := models.AIRequestFilter{
		TenantID:      &tenantID,
		CreatedAfter:  &startTime,
		CreatedBefore: &endTime,
	}
	return r.List(ctx, filter, pag)
}

// GetRecentRequests retrieves recent AI requests within the specified number of hours
func (r *aiRequestRepository) GetRecentRequests(ctx context.Context, tenantID uint, hours int, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	startTime := time.Now().Add(-time.Duration(hours) * time.Hour)
	filter := models.AIRequestFilter{
		TenantID:     &tenantID,
		CreatedAfter: &startTime,
	}
	return r.List(ctx, filter, pag)
}

// GetAnalytics generates analytics for AI requests
func (r *aiRequestRepository) GetAnalytics(ctx context.Context, tenantID uint, startTime, endTime time.Time) (*models.AIRequestAnalytics, error) {
	analytics := &models.AIRequestAnalytics{
		RequestsByType:   make(map[string]int64),
		RequestsByStatus: make(map[string]int64),
		RequestsByHour:   make(map[string]int64),
		RequestsByDay:    make(map[string]int64),
	}

	// Base query
	query := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("tenant_id = ? AND created_at BETWEEN ? AND ?", tenantID, startTime, endTime)

	// Total requests
	query.Count(&analytics.TotalRequests)

	// Requests by status
	var statusResults []struct {
		Status string
		Count  int64
	}
	query.Select("status, COUNT(*) as count").
		Group("status").
		Scan(&statusResults)

	for _, result := range statusResults {
		analytics.RequestsByStatus[result.Status] = result.Count
		switch result.Status {
		case "completed":
			analytics.CompletedRequests = result.Count
		case "failed":
			analytics.FailedRequests = result.Count
		case "timeout":
			analytics.TimeoutRequests = result.Count
		}
	}

	// Requests by type
	var typeResults []struct {
		RequestType string
		Count       int64
	}
	query.Select("request_type, COUNT(*) as count").
		Group("request_type").
		Scan(&typeResults)

	for _, result := range typeResults {
		analytics.RequestsByType[result.RequestType] = result.Count
	}

	// Aggregate metrics
	var aggregates struct {
		TotalTokens       int64
		TotalCost         int64
		AvgProcessingTime float64
	}
	query.Select("SUM(tokens_used) as total_tokens, SUM(cost_cents) as total_cost, AVG(processing_time_ms) as avg_processing_time").
		Scan(&aggregates)

	analytics.TotalTokensUsed = aggregates.TotalTokens
	analytics.TotalCostCents = aggregates.TotalCost
	analytics.TotalCostDollars = float64(aggregates.TotalCost) / 100.0
	analytics.AverageProcessingTime = aggregates.AvgProcessingTime

	// Requests by hour
	var hourResults []struct {
		Hour  string
		Count int64
	}
	query.Select("DATE_FORMAT(created_at, '%Y-%m-%d %H:00:00') as hour, COUNT(*) as count").
		Group("hour").
		Order("hour").
		Scan(&hourResults)

	for _, result := range hourResults {
		analytics.RequestsByHour[result.Hour] = result.Count
	}

	// Requests by day
	var dayResults []struct {
		Day   string
		Count int64
	}
	query.Select("DATE(created_at) as day, COUNT(*) as count").
		Group("day").
		Order("day").
		Scan(&dayResults)

	for _, result := range dayResults {
		analytics.RequestsByDay[result.Day] = result.Count
	}

	// Top users
	var userResults []struct {
		UserID       uint
		Username     string
		RequestCount int64
		TokensUsed   int64
		CostCents    int64
	}
	r.db.WithContext(ctx).
		Table("ai_requests r").
		Select("r.user_id, u.username, COUNT(*) as request_count, SUM(r.tokens_used) as tokens_used, SUM(r.cost_cents) as cost_cents").
		Joins("LEFT JOIN users u ON r.user_id = u.id").
		Where("r.tenant_id = ? AND r.created_at BETWEEN ? AND ? AND r.user_id IS NOT NULL", tenantID, startTime, endTime).
		Group("r.user_id, u.username").
		Order("request_count DESC").
		Limit(10).
		Scan(&userResults)

	for _, result := range userResults {
		analytics.TopUsers = append(analytics.TopUsers, models.UserUsageStats{
			UserID:       result.UserID,
			Username:     result.Username,
			RequestCount: result.RequestCount,
			TokensUsed:   result.TokensUsed,
			CostCents:    result.CostCents,
			CostDollars:  float64(result.CostCents) / 100.0,
		})
	}

	// Top models
	var modelResults []struct {
		ModelID      uint
		ModelName    string
		Provider     string
		RequestCount int64
		TokensUsed   int64
		CostCents    int64
	}
	r.db.WithContext(ctx).
		Table("ai_requests r").
		Select("r.model_id, m.name as model_name, m.provider, COUNT(*) as request_count, SUM(r.tokens_used) as tokens_used, SUM(r.cost_cents) as cost_cents").
		Joins("LEFT JOIN ai_models m ON r.model_id = m.id").
		Where("r.tenant_id = ? AND r.created_at BETWEEN ? AND ?", tenantID, startTime, endTime).
		Group("r.model_id, m.name, m.provider").
		Order("request_count DESC").
		Limit(10).
		Scan(&modelResults)

	for _, result := range modelResults {
		analytics.TopModels = append(analytics.TopModels, models.ModelUsageStats{
			ModelID:      result.ModelID,
			ModelName:    result.ModelName,
			Provider:     result.Provider,
			RequestCount: result.RequestCount,
			TokensUsed:   result.TokensUsed,
			CostCents:    result.CostCents,
			CostDollars:  float64(result.CostCents) / 100.0,
		})
	}

	return analytics, nil
}

// GetAnalyticsByWebsite generates analytics for AI requests by website
func (r *aiRequestRepository) GetAnalyticsByWebsite(ctx context.Context, websiteID uint, startTime, endTime time.Time) (*models.AIRequestAnalytics, error) {
	// Similar to GetAnalytics but filtered by website_id
	analytics := &models.AIRequestAnalytics{
		RequestsByType:   make(map[string]int64),
		RequestsByStatus: make(map[string]int64),
		RequestsByHour:   make(map[string]int64),
		RequestsByDay:    make(map[string]int64),
	}

	// TODO: Implement website-specific analytics
	// Implementation similar to GetAnalytics but with website_id filter
	// ... (similar code as above but with website_id filter)

	return analytics, nil
}

// GetAnalyticsByUser generates analytics for AI requests by user
func (r *aiRequestRepository) GetAnalyticsByUser(ctx context.Context, userID uint, startTime, endTime time.Time) (*models.AIRequestAnalytics, error) {
	// Similar to GetAnalytics but filtered by user_id
	analytics := &models.AIRequestAnalytics{
		RequestsByType:   make(map[string]int64),
		RequestsByStatus: make(map[string]int64),
		RequestsByHour:   make(map[string]int64),
		RequestsByDay:    make(map[string]int64),
	}

	// TODO: Implement user-specific analytics
	// Implementation similar to GetAnalytics but with user_id filter
	// ... (similar code as above but with user_id filter)

	return analytics, nil
}

// GetAnalyticsByModel generates analytics for AI requests by model
func (r *aiRequestRepository) GetAnalyticsByModel(ctx context.Context, modelID uint, startTime, endTime time.Time) (*models.AIRequestAnalytics, error) {
	// Similar to GetAnalytics but filtered by model_id
	analytics := &models.AIRequestAnalytics{
		RequestsByType:   make(map[string]int64),
		RequestsByStatus: make(map[string]int64),
		RequestsByHour:   make(map[string]int64),
		RequestsByDay:    make(map[string]int64),
	}

	// TODO: Implement model-specific analytics
	// Implementation similar to GetAnalytics but with model_id filter
	// ... (similar code as above but with model_id filter)

	return analytics, nil
}

// Count returns the total number of AI requests matching the filter
func (r *aiRequestRepository) Count(ctx context.Context, filter models.AIRequestFilter) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.AIRequest{})
	query = r.applyFilter(query, filter)

	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}

	return count, nil
}

// CountByTenant returns the number of AI requests for a tenant
func (r *aiRequestRepository) CountByTenant(ctx context.Context, tenantID uint) (int64, error) {
	filter := models.AIRequestFilter{TenantID: &tenantID}
	return r.Count(ctx, filter)
}

// CountByWebsite returns the number of AI requests for a website
func (r *aiRequestRepository) CountByWebsite(ctx context.Context, websiteID uint) (int64, error) {
	filter := models.AIRequestFilter{WebsiteID: &websiteID}
	return r.Count(ctx, filter)
}

// CountByUser returns the number of AI requests for a user
func (r *aiRequestRepository) CountByUser(ctx context.Context, userID uint) (int64, error) {
	filter := models.AIRequestFilter{UserID: &userID}
	return r.Count(ctx, filter)
}

// CountByModel returns the number of AI requests for a model
func (r *aiRequestRepository) CountByModel(ctx context.Context, modelID uint) (int64, error) {
	filter := models.AIRequestFilter{ModelID: &modelID}
	return r.Count(ctx, filter)
}

// CountByStatus returns the number of AI requests with a specific status
func (r *aiRequestRepository) CountByStatus(ctx context.Context, tenantID uint, status models.AIRequestStatus) (int64, error) {
	filter := models.AIRequestFilter{TenantID: &tenantID, Status: &status}
	return r.Count(ctx, filter)
}

// CountByType returns the number of AI requests with a specific type
func (r *aiRequestRepository) CountByType(ctx context.Context, tenantID uint, requestType models.AIRequestType) (int64, error) {
	filter := models.AIRequestFilter{TenantID: &tenantID, RequestType: &requestType}
	return r.Count(ctx, filter)
}

// CountByTimeRange returns the number of AI requests within a time range
func (r *aiRequestRepository) CountByTimeRange(ctx context.Context, tenantID uint, startTime, endTime time.Time) (int64, error) {
	filter := models.AIRequestFilter{
		TenantID:      &tenantID,
		CreatedAfter:  &startTime,
		CreatedBefore: &endTime,
	}
	return r.Count(ctx, filter)
}

// GetTokenUsage returns total token usage for a tenant within a time range
func (r *aiRequestRepository) GetTokenUsage(ctx context.Context, tenantID uint, startTime, endTime time.Time) (int64, error) {
	var totalTokens int64
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("tenant_id = ? AND created_at BETWEEN ? AND ?", tenantID, startTime, endTime).
		Select("COALESCE(SUM(tokens_used), 0)").
		Scan(&totalTokens).Error

	return totalTokens, err
}

// GetTokenUsageByWebsite returns total token usage for a website within a time range
func (r *aiRequestRepository) GetTokenUsageByWebsite(ctx context.Context, websiteID uint, startTime, endTime time.Time) (int64, error) {
	var totalTokens int64
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("website_id = ? AND created_at BETWEEN ? AND ?", websiteID, startTime, endTime).
		Select("COALESCE(SUM(tokens_used), 0)").
		Scan(&totalTokens).Error

	return totalTokens, err
}

// GetTokenUsageByUser returns total token usage for a user within a time range
func (r *aiRequestRepository) GetTokenUsageByUser(ctx context.Context, userID uint, startTime, endTime time.Time) (int64, error) {
	var totalTokens int64
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("user_id = ? AND created_at BETWEEN ? AND ?", userID, startTime, endTime).
		Select("COALESCE(SUM(tokens_used), 0)").
		Scan(&totalTokens).Error

	return totalTokens, err
}

// GetTokenUsageByModel returns total token usage for a model within a time range
func (r *aiRequestRepository) GetTokenUsageByModel(ctx context.Context, modelID uint, startTime, endTime time.Time) (int64, error) {
	var totalTokens int64
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("model_id = ? AND created_at BETWEEN ? AND ?", modelID, startTime, endTime).
		Select("COALESCE(SUM(tokens_used), 0)").
		Scan(&totalTokens).Error

	return totalTokens, err
}

// GetCostUsage returns total cost usage for a tenant within a time range
func (r *aiRequestRepository) GetCostUsage(ctx context.Context, tenantID uint, startTime, endTime time.Time) (int64, error) {
	var totalCost int64
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("tenant_id = ? AND created_at BETWEEN ? AND ?", tenantID, startTime, endTime).
		Select("COALESCE(SUM(cost_cents), 0)").
		Scan(&totalCost).Error

	return totalCost, err
}

// GetCostUsageByWebsite returns total cost usage for a website within a time range
func (r *aiRequestRepository) GetCostUsageByWebsite(ctx context.Context, websiteID uint, startTime, endTime time.Time) (int64, error) {
	var totalCost int64
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("website_id = ? AND created_at BETWEEN ? AND ?", websiteID, startTime, endTime).
		Select("COALESCE(SUM(cost_cents), 0)").
		Scan(&totalCost).Error

	return totalCost, err
}

// GetCostUsageByUser returns total cost usage for a user within a time range
func (r *aiRequestRepository) GetCostUsageByUser(ctx context.Context, userID uint, startTime, endTime time.Time) (int64, error) {
	var totalCost int64
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("user_id = ? AND created_at BETWEEN ? AND ?", userID, startTime, endTime).
		Select("COALESCE(SUM(cost_cents), 0)").
		Scan(&totalCost).Error

	return totalCost, err
}

// GetCostUsageByModel returns total cost usage for a model within a time range
func (r *aiRequestRepository) GetCostUsageByModel(ctx context.Context, modelID uint, startTime, endTime time.Time) (int64, error) {
	var totalCost int64
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("model_id = ? AND created_at BETWEEN ? AND ?", modelID, startTime, endTime).
		Select("COALESCE(SUM(cost_cents), 0)").
		Scan(&totalCost).Error

	return totalCost, err
}

// GetAverageProcessingTime returns average processing time for a tenant within a time range
func (r *aiRequestRepository) GetAverageProcessingTime(ctx context.Context, tenantID uint, startTime, endTime time.Time) (float64, error) {
	var avgTime float64
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("tenant_id = ? AND created_at BETWEEN ? AND ? AND status = ?", tenantID, startTime, endTime, models.AIRequestStatusCompleted).
		Select("COALESCE(AVG(processing_time_ms), 0)").
		Scan(&avgTime).Error

	return avgTime, err
}

// GetAverageProcessingTimeByWebsite returns average processing time for a website within a time range
func (r *aiRequestRepository) GetAverageProcessingTimeByWebsite(ctx context.Context, websiteID uint, startTime, endTime time.Time) (float64, error) {
	var avgTime float64
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("website_id = ? AND created_at BETWEEN ? AND ? AND status = ?", websiteID, startTime, endTime, models.AIRequestStatusCompleted).
		Select("COALESCE(AVG(processing_time_ms), 0)").
		Scan(&avgTime).Error

	return avgTime, err
}

// GetAverageProcessingTimeByUser returns average processing time for a user within a time range
func (r *aiRequestRepository) GetAverageProcessingTimeByUser(ctx context.Context, userID uint, startTime, endTime time.Time) (float64, error) {
	var avgTime float64
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("user_id = ? AND created_at BETWEEN ? AND ? AND status = ?", userID, startTime, endTime, models.AIRequestStatusCompleted).
		Select("COALESCE(AVG(processing_time_ms), 0)").
		Scan(&avgTime).Error

	return avgTime, err
}

// GetAverageProcessingTimeByModel returns average processing time for a model within a time range
func (r *aiRequestRepository) GetAverageProcessingTimeByModel(ctx context.Context, modelID uint, startTime, endTime time.Time) (float64, error) {
	var avgTime float64
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("model_id = ? AND created_at BETWEEN ? AND ? AND status = ?", modelID, startTime, endTime, models.AIRequestStatusCompleted).
		Select("COALESCE(AVG(processing_time_ms), 0)").
		Scan(&avgTime).Error

	return avgTime, err
}

// GetErrorRate returns error rate for a tenant within a time range
func (r *aiRequestRepository) GetErrorRate(ctx context.Context, tenantID uint, startTime, endTime time.Time) (float64, error) {
	var total, errors int64

	// Get total requests
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("tenant_id = ? AND created_at BETWEEN ? AND ?", tenantID, startTime, endTime).
		Count(&total).Error
	if err != nil {
		return 0, err
	}

	if total == 0 {
		return 0, nil
	}

	// Get error requests
	err = r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("tenant_id = ? AND created_at BETWEEN ? AND ? AND status IN ?", tenantID, startTime, endTime, []models.AIRequestStatus{models.AIRequestStatusFailed, models.AIRequestStatusTimeout}).
		Count(&errors).Error
	if err != nil {
		return 0, err
	}

	return float64(errors) / float64(total), nil
}

// GetErrorRateByWebsite returns error rate for a website within a time range
func (r *aiRequestRepository) GetErrorRateByWebsite(ctx context.Context, websiteID uint, startTime, endTime time.Time) (float64, error) {
	var total, errors int64

	// Get total requests
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("website_id = ? AND created_at BETWEEN ? AND ?", websiteID, startTime, endTime).
		Count(&total).Error
	if err != nil {
		return 0, err
	}

	if total == 0 {
		return 0, nil
	}

	// Get error requests
	err = r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("website_id = ? AND created_at BETWEEN ? AND ? AND status IN ?", websiteID, startTime, endTime, []models.AIRequestStatus{models.AIRequestStatusFailed, models.AIRequestStatusTimeout}).
		Count(&errors).Error
	if err != nil {
		return 0, err
	}

	return float64(errors) / float64(total), nil
}

// GetErrorRateByUser returns error rate for a user within a time range
func (r *aiRequestRepository) GetErrorRateByUser(ctx context.Context, userID uint, startTime, endTime time.Time) (float64, error) {
	var total, errors int64

	// Get total requests
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("user_id = ? AND created_at BETWEEN ? AND ?", userID, startTime, endTime).
		Count(&total).Error
	if err != nil {
		return 0, err
	}

	if total == 0 {
		return 0, nil
	}

	// Get error requests
	err = r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("user_id = ? AND created_at BETWEEN ? AND ? AND status IN ?", userID, startTime, endTime, []models.AIRequestStatus{models.AIRequestStatusFailed, models.AIRequestStatusTimeout}).
		Count(&errors).Error
	if err != nil {
		return 0, err
	}

	return float64(errors) / float64(total), nil
}

// GetErrorRateByModel returns error rate for a model within a time range
func (r *aiRequestRepository) GetErrorRateByModel(ctx context.Context, modelID uint, startTime, endTime time.Time) (float64, error) {
	var total, errors int64

	// Get total requests
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("model_id = ? AND created_at BETWEEN ? AND ?", modelID, startTime, endTime).
		Count(&total).Error
	if err != nil {
		return 0, err
	}

	if total == 0 {
		return 0, nil
	}

	// Get error requests
	err = r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("model_id = ? AND created_at BETWEEN ? AND ? AND status IN ?", modelID, startTime, endTime, []models.AIRequestStatus{models.AIRequestStatusFailed, models.AIRequestStatusTimeout}).
		Count(&errors).Error
	if err != nil {
		return 0, err
	}

	return float64(errors) / float64(total), nil
}

// BulkCreate creates multiple AI requests in a single transaction
func (r *aiRequestRepository) BulkCreate(ctx context.Context, requests []models.AIRequest) error {
	return r.db.WithContext(ctx).CreateInBatches(requests, 100).Error
}

// BulkUpdate updates multiple AI requests in a single transaction
func (r *aiRequestRepository) BulkUpdate(ctx context.Context, requests []models.AIRequest) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, request := range requests {
			if err := tx.Save(&request).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// BulkDelete deletes multiple AI requests by IDs
func (r *aiRequestRepository) BulkDelete(ctx context.Context, ids []uint) error {
	return r.db.WithContext(ctx).Delete(&models.AIRequest{}, ids).Error
}

// DeleteOldRequests deletes old AI requests for a tenant
func (r *aiRequestRepository) DeleteOldRequests(ctx context.Context, tenantID uint, olderThan time.Time) (int64, error) {
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND created_at < ?", tenantID, olderThan).
		Delete(&models.AIRequest{})

	return result.RowsAffected, result.Error
}

// DeleteRequestsByStatus deletes AI requests by status for a tenant
func (r *aiRequestRepository) DeleteRequestsByStatus(ctx context.Context, tenantID uint, status models.AIRequestStatus, olderThan time.Time) (int64, error) {
	result := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ? AND created_at < ?", tenantID, status, olderThan).
		Delete(&models.AIRequest{})

	return result.RowsAffected, result.Error
}

// Search searches AI requests by query
func (r *aiRequestRepository) Search(ctx context.Context, tenantID uint, query string, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	var requests []models.AIRequest

	db := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("tenant_id = ? AND (prompt_text LIKE ? OR response_text LIKE ?)", tenantID, "%"+query+"%", "%"+query+"%")

	if pag != nil {
		if pag.Cursor != "" {
			db = db.Where("id < ?", pag.Cursor)
		}

		db = db.Order("id DESC").Limit(pag.Limit + 1)
	}

	if err := db.Find(&requests).Error; err != nil {
		return nil, nil, err
	}

	if pag != nil {
		return r.handlePaginationResponse(requests, pag)
	}

	return requests, nil, nil
}

// SearchByPrompt searches AI requests by prompt text
func (r *aiRequestRepository) SearchByPrompt(ctx context.Context, tenantID uint, prompt string, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	var requests []models.AIRequest

	db := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("tenant_id = ? AND prompt_text LIKE ?", tenantID, "%"+prompt+"%")

	if pag != nil {
		if pag.Cursor != "" {
			db = db.Where("id < ?", pag.Cursor)
		}

		db = db.Order("id DESC").Limit(pag.Limit + 1)
	}

	if err := db.Find(&requests).Error; err != nil {
		return nil, nil, err
	}

	if pag != nil {
		return r.handlePaginationResponse(requests, pag)
	}

	return requests, nil, nil
}

// SearchByResponse searches AI requests by response text
func (r *aiRequestRepository) SearchByResponse(ctx context.Context, tenantID uint, response string, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	var requests []models.AIRequest

	db := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("tenant_id = ? AND response_text LIKE ?", tenantID, "%"+response+"%")

	if pag != nil {
		if pag.Cursor != "" {
			db = db.Where("id < ?", pag.Cursor)
		}

		db = db.Order("id DESC").Limit(pag.Limit + 1)
	}

	if err := db.Find(&requests).Error; err != nil {
		return nil, nil, err
	}

	if pag != nil {
		return r.handlePaginationResponse(requests, pag)
	}

	return requests, nil, nil
}

// RequestExists checks if a request exists for a tenant
func (r *aiRequestRepository) RequestExists(ctx context.Context, tenantID uint, id uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Count(&count).Error

	return count > 0, err
}

// UserHasAccess checks if a user has access to a request
func (r *aiRequestRepository) UserHasAccess(ctx context.Context, userID uint, requestID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("user_id = ? AND id = ?", userID, requestID).
		Count(&count).Error

	return count > 0, err
}

// TenantHasAccess checks if a tenant has access to a request
func (r *aiRequestRepository) TenantHasAccess(ctx context.Context, tenantID uint, requestID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("tenant_id = ? AND id = ?", tenantID, requestID).
		Count(&count).Error

	return count > 0, err
}

// WebsiteHasAccess checks if a website has access to a request
func (r *aiRequestRepository) WebsiteHasAccess(ctx context.Context, websiteID uint, requestID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("website_id = ? AND id = ?", websiteID, requestID).
		Count(&count).Error

	return count > 0, err
}

// GetPendingRequests retrieves pending AI requests for processing
func (r *aiRequestRepository) GetPendingRequests(ctx context.Context, limit int) ([]models.AIRequest, error) {
	var requests []models.AIRequest
	err := r.db.WithContext(ctx).
		Where("status = ?", models.AIRequestStatusPending).
		Order("created_at ASC").
		Limit(limit).
		Find(&requests).Error

	return requests, err
}

// GetTimeoutRequests retrieves requests that have timed out
func (r *aiRequestRepository) GetTimeoutRequests(ctx context.Context, timeoutMinutes int) ([]models.AIRequest, error) {
	var requests []models.AIRequest
	timeoutTime := time.Now().Add(-time.Duration(timeoutMinutes) * time.Minute)

	err := r.db.WithContext(ctx).
		Where("status = ? AND created_at < ?", models.AIRequestStatusPending, timeoutTime).
		Find(&requests).Error

	return requests, err
}

// GetRetryableRequests retrieves requests that can be retried
func (r *aiRequestRepository) GetRetryableRequests(ctx context.Context, maxRetries int) ([]models.AIRequest, error) {
	var requests []models.AIRequest
	err := r.db.WithContext(ctx).
		Where("status = ? AND JSON_EXTRACT(metadata, '$.retry_count') < ?", models.AIRequestStatusFailed, maxRetries).
		Find(&requests).Error

	return requests, err
}

// GetRequestCountInTimeWindow returns the number of requests in a time window
func (r *aiRequestRepository) GetRequestCountInTimeWindow(ctx context.Context, tenantID uint, websiteID *uint, userID *uint, windowStart time.Time) (int64, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("tenant_id = ? AND created_at >= ?", tenantID, windowStart)

	if websiteID != nil {
		query = query.Where("website_id = ?", *websiteID)
	}

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	err := query.Count(&count).Error
	return count, err
}

// GetTokenUsageInTimeWindow returns token usage in a time window
func (r *aiRequestRepository) GetTokenUsageInTimeWindow(ctx context.Context, tenantID uint, websiteID *uint, userID *uint, windowStart time.Time) (int64, error) {
	var tokens int64
	query := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("tenant_id = ? AND created_at >= ?", tenantID, windowStart)

	if websiteID != nil {
		query = query.Where("website_id = ?", *websiteID)
	}

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	err := query.Select("COALESCE(SUM(tokens_used), 0)").Scan(&tokens).Error
	return tokens, err
}

// GetCostUsageInTimeWindow returns cost usage in a time window
func (r *aiRequestRepository) GetCostUsageInTimeWindow(ctx context.Context, tenantID uint, websiteID *uint, userID *uint, windowStart time.Time) (int64, error) {
	var cost int64
	query := r.db.WithContext(ctx).Model(&models.AIRequest{}).
		Where("tenant_id = ? AND created_at >= ?", tenantID, windowStart)

	if websiteID != nil {
		query = query.Where("website_id = ?", *websiteID)
	}

	if userID != nil {
		query = query.Where("user_id = ?", *userID)
	}

	err := query.Select("COALESCE(SUM(cost_cents), 0)").Scan(&cost).Error
	return cost, err
}

// GetRequestsBatch retrieves a batch of requests for processing
func (r *aiRequestRepository) GetRequestsBatch(ctx context.Context, tenantID uint, status models.AIRequestStatus, batchSize int, offset int) ([]models.AIRequest, error) {
	var requests []models.AIRequest
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Order("created_at ASC").
		Limit(batchSize).
		Offset(offset).
		Find(&requests).Error

	return requests, err
}

// ProcessBatch processes requests in batches
func (r *aiRequestRepository) ProcessBatch(ctx context.Context, processor func([]models.AIRequest) error, batchSize int, tenantID uint, status models.AIRequestStatus) error {
	offset := 0

	for {
		batch, err := r.GetRequestsBatch(ctx, tenantID, status, batchSize, offset)
		if err != nil {
			return err
		}

		if len(batch) == 0 {
			break
		}

		if err := processor(batch); err != nil {
			return err
		}

		offset += batchSize
	}

	return nil
}

// Helper methods

// applyFilter applies filters to the query
func (r *aiRequestRepository) applyFilter(query *gorm.DB, filter models.AIRequestFilter) *gorm.DB {
	if filter.TenantID != nil {
		query = query.Where("tenant_id = ?", *filter.TenantID)
	}

	if filter.WebsiteID != nil {
		query = query.Where("website_id = ?", *filter.WebsiteID)
	}

	if filter.UserID != nil {
		query = query.Where("user_id = ?", *filter.UserID)
	}

	if filter.ModelID != nil {
		query = query.Where("model_id = ?", *filter.ModelID)
	}

	if filter.RequestType != nil {
		query = query.Where("request_type = ?", *filter.RequestType)
	}

	if filter.Status != nil {
		query = query.Where("status = ?", *filter.Status)
	}

	if filter.CreatedAfter != nil {
		query = query.Where("created_at >= ?", *filter.CreatedAfter)
	}

	if filter.CreatedBefore != nil {
		query = query.Where("created_at <= ?", *filter.CreatedBefore)
	}

	if filter.MinTokens != nil {
		query = query.Where("tokens_used >= ?", *filter.MinTokens)
	}

	if filter.MaxTokens != nil {
		query = query.Where("tokens_used <= ?", *filter.MaxTokens)
	}

	if filter.MinCost != nil {
		query = query.Where("cost_cents >= ?", *filter.MinCost)
	}

	if filter.MaxCost != nil {
		query = query.Where("cost_cents <= ?", *filter.MaxCost)
	}

	if filter.MinProcessingTime != nil {
		query = query.Where("processing_time_ms >= ?", *filter.MinProcessingTime)
	}

	if filter.MaxProcessingTime != nil {
		query = query.Where("processing_time_ms <= ?", *filter.MaxProcessingTime)
	}

	return query
}

// handlePaginationResponse handles pagination response
func (r *aiRequestRepository) handlePaginationResponse(requests []models.AIRequest, pag *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error) {
	hasNext := len(requests) > pag.Limit

	if hasNext {
		requests = requests[:pag.Limit]
	}

	var nextCursor string
	if hasNext && len(requests) > 0 {
		nextCursor = fmt.Sprintf("%d", requests[len(requests)-1].ID)
	}

	response := &pagination.CursorResponse{
		HasNext:    hasNext,
		NextCursor: nextCursor,
		Count:      len(requests),
		HasMore:    hasNext,
		Limit:      pag.Limit,
	}

	return requests, response, nil
}
