package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// AIRequestRepository defines the interface for AI request data operations
type AIRequestRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, request *models.AIRequest) error
	GetByID(ctx context.Context, id uint) (*models.AIRequest, error)
	GetByIDWithRelations(ctx context.Context, id uint) (*models.AIRequest, error)
	Update(ctx context.Context, request *models.AIRequest) error
	Delete(ctx context.Context, id uint) error

	// List operations with pagination
	List(ctx context.Context, filter models.AIRequestFilter, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)
	ListWithRelations(ctx context.Context, filter models.AIRequestFilter, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)

	// Status operations
	GetByStatus(ctx context.Context, tenantID uint, status models.AIRequestStatus, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)
	UpdateStatus(ctx context.Context, id uint, status models.AIRequestStatus) error
	BulkUpdateStatus(ctx context.Context, ids []uint, status models.AIRequestStatus) error

	// Tenant-scoped operations
	GetByTenant(ctx context.Context, tenantID uint, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)
	GetByTenantAndType(ctx context.Context, tenantID uint, requestType models.AIRequestType, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)
	GetByTenantAndStatus(ctx context.Context, tenantID uint, status models.AIRequestStatus, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)

	// Website-scoped operations
	GetByWebsite(ctx context.Context, websiteID uint, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)
	GetByWebsiteAndType(ctx context.Context, websiteID uint, requestType models.AIRequestType, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)
	GetByWebsiteAndStatus(ctx context.Context, websiteID uint, status models.AIRequestStatus, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)

	// User-scoped operations
	GetByUser(ctx context.Context, userID uint, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)
	GetByUserAndType(ctx context.Context, userID uint, requestType models.AIRequestType, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)
	GetByUserAndStatus(ctx context.Context, userID uint, status models.AIRequestStatus, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)

	// Model-scoped operations
	GetByModel(ctx context.Context, modelID uint, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)
	GetByModelAndType(ctx context.Context, modelID uint, requestType models.AIRequestType, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)
	GetByModelAndStatus(ctx context.Context, modelID uint, status models.AIRequestStatus, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)

	// Time-based operations
	GetByTimeRange(ctx context.Context, tenantID uint, startTime, endTime time.Time, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)
	GetRecentRequests(ctx context.Context, tenantID uint, hours int, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)

	// Analytics and statistics
	GetAnalytics(ctx context.Context, tenantID uint, startTime, endTime time.Time) (*models.AIRequestAnalytics, error)
	GetAnalyticsByWebsite(ctx context.Context, websiteID uint, startTime, endTime time.Time) (*models.AIRequestAnalytics, error)
	GetAnalyticsByUser(ctx context.Context, userID uint, startTime, endTime time.Time) (*models.AIRequestAnalytics, error)
	GetAnalyticsByModel(ctx context.Context, modelID uint, startTime, endTime time.Time) (*models.AIRequestAnalytics, error)

	// Count operations
	Count(ctx context.Context, filter models.AIRequestFilter) (int64, error)
	CountByTenant(ctx context.Context, tenantID uint) (int64, error)
	CountByWebsite(ctx context.Context, websiteID uint) (int64, error)
	CountByUser(ctx context.Context, userID uint) (int64, error)
	CountByModel(ctx context.Context, modelID uint) (int64, error)
	CountByStatus(ctx context.Context, tenantID uint, status models.AIRequestStatus) (int64, error)
	CountByType(ctx context.Context, tenantID uint, requestType models.AIRequestType) (int64, error)
	CountByTimeRange(ctx context.Context, tenantID uint, startTime, endTime time.Time) (int64, error)

	// Usage tracking
	GetTokenUsage(ctx context.Context, tenantID uint, startTime, endTime time.Time) (int64, error)
	GetTokenUsageByWebsite(ctx context.Context, websiteID uint, startTime, endTime time.Time) (int64, error)
	GetTokenUsageByUser(ctx context.Context, userID uint, startTime, endTime time.Time) (int64, error)
	GetTokenUsageByModel(ctx context.Context, modelID uint, startTime, endTime time.Time) (int64, error)

	// Cost tracking
	GetCostUsage(ctx context.Context, tenantID uint, startTime, endTime time.Time) (int64, error)
	GetCostUsageByWebsite(ctx context.Context, websiteID uint, startTime, endTime time.Time) (int64, error)
	GetCostUsageByUser(ctx context.Context, userID uint, startTime, endTime time.Time) (int64, error)
	GetCostUsageByModel(ctx context.Context, modelID uint, startTime, endTime time.Time) (int64, error)

	// Performance tracking
	GetAverageProcessingTime(ctx context.Context, tenantID uint, startTime, endTime time.Time) (float64, error)
	GetAverageProcessingTimeByWebsite(ctx context.Context, websiteID uint, startTime, endTime time.Time) (float64, error)
	GetAverageProcessingTimeByUser(ctx context.Context, userID uint, startTime, endTime time.Time) (float64, error)
	GetAverageProcessingTimeByModel(ctx context.Context, modelID uint, startTime, endTime time.Time) (float64, error)

	// Error tracking
	GetErrorRate(ctx context.Context, tenantID uint, startTime, endTime time.Time) (float64, error)
	GetErrorRateByWebsite(ctx context.Context, websiteID uint, startTime, endTime time.Time) (float64, error)
	GetErrorRateByUser(ctx context.Context, userID uint, startTime, endTime time.Time) (float64, error)
	GetErrorRateByModel(ctx context.Context, modelID uint, startTime, endTime time.Time) (float64, error)

	// Bulk operations
	BulkCreate(ctx context.Context, requests []models.AIRequest) error
	BulkUpdate(ctx context.Context, requests []models.AIRequest) error
	BulkDelete(ctx context.Context, ids []uint) error

	// Cleanup operations
	DeleteOldRequests(ctx context.Context, tenantID uint, olderThan time.Time) (int64, error)
	DeleteRequestsByStatus(ctx context.Context, tenantID uint, status models.AIRequestStatus, olderThan time.Time) (int64, error)

	// Search operations
	Search(ctx context.Context, tenantID uint, query string, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)
	SearchByPrompt(ctx context.Context, tenantID uint, prompt string, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)
	SearchByResponse(ctx context.Context, tenantID uint, response string, pagination *pagination.CursorPagination) ([]models.AIRequest, *pagination.CursorResponse, error)

	// Validation operations
	RequestExists(ctx context.Context, tenantID uint, id uint) (bool, error)
	UserHasAccess(ctx context.Context, userID uint, requestID uint) (bool, error)
	TenantHasAccess(ctx context.Context, tenantID uint, requestID uint) (bool, error)
	WebsiteHasAccess(ctx context.Context, websiteID uint, requestID uint) (bool, error)

	// Queue operations for async processing
	GetPendingRequests(ctx context.Context, limit int) ([]models.AIRequest, error)
	GetTimeoutRequests(ctx context.Context, timeoutMinutes int) ([]models.AIRequest, error)
	GetRetryableRequests(ctx context.Context, maxRetries int) ([]models.AIRequest, error)

	// Rate limiting helpers
	GetRequestCountInTimeWindow(ctx context.Context, tenantID uint, websiteID *uint, userID *uint, windowStart time.Time) (int64, error)
	GetTokenUsageInTimeWindow(ctx context.Context, tenantID uint, websiteID *uint, userID *uint, windowStart time.Time) (int64, error)
	GetCostUsageInTimeWindow(ctx context.Context, tenantID uint, websiteID *uint, userID *uint, windowStart time.Time) (int64, error)

	// Batch processing helpers
	GetRequestsBatch(ctx context.Context, tenantID uint, status models.AIRequestStatus, batchSize int, offset int) ([]models.AIRequest, error)
	ProcessBatch(ctx context.Context, processor func([]models.AIRequest) error, batchSize int, tenantID uint, status models.AIRequestStatus) error
}

// AIRequestSearchOptions represents search options for AI request queries
type AIRequestSearchOptions struct {
	Filter           models.AIRequestFilter       `json:"filter"`
	Pagination       *pagination.CursorPagination `json:"pagination"`
	SortBy           string                       `json:"sort_by"`
	SortOrder        string                       `json:"sort_order"`
	IncludeRelations bool                         `json:"include_relations"`
	IncludeMetadata  bool                         `json:"include_metadata"`
}

// AIRequestBatchOptions represents options for batch processing
type AIRequestBatchOptions struct {
	TenantID    uint                   `json:"tenant_id"`
	Status      models.AIRequestStatus `json:"status"`
	BatchSize   int                    `json:"batch_size"`
	MaxBatches  int                    `json:"max_batches"`
	Parallel    bool                   `json:"parallel"`
	RetryFailed bool                   `json:"retry_failed"`
}

// AIRequestCleanupOptions represents options for cleanup operations
type AIRequestCleanupOptions struct {
	TenantID   uint                    `json:"tenant_id"`
	OlderThan  time.Time               `json:"older_than"`
	Status     *models.AIRequestStatus `json:"status,omitempty"`
	DryRun     bool                    `json:"dry_run"`
	BatchSize  int                     `json:"batch_size"`
	MaxDeletes int                     `json:"max_deletes"`
}

// AIRequestRateLimitInfo represents rate limiting information
type AIRequestRateLimitInfo struct {
	TenantID          uint      `json:"tenant_id"`
	WebsiteID         *uint     `json:"website_id,omitempty"`
	UserID            *uint     `json:"user_id,omitempty"`
	WindowStart       time.Time `json:"window_start"`
	WindowEnd         time.Time `json:"window_end"`
	RequestCount      int64     `json:"request_count"`
	TokenUsage        int64     `json:"token_usage"`
	CostUsage         int64     `json:"cost_usage"`
	RemainingRequests int64     `json:"remaining_requests"`
	RemainingTokens   int64     `json:"remaining_tokens"`
	RemainingCost     int64     `json:"remaining_cost"`
	ResetTime         time.Time `json:"reset_time"`
}

// AIRequestBatchResult represents the result of a batch operation
type AIRequestBatchResult struct {
	TotalProcessed int64         `json:"total_processed"`
	TotalSucceeded int64         `json:"total_succeeded"`
	TotalFailed    int64         `json:"total_failed"`
	Errors         []BatchError  `json:"errors,omitempty"`
	ProcessingTime time.Duration `json:"processing_time"`
}

// BatchError represents an error that occurred during batch processing
type BatchError struct {
	RequestID uint   `json:"request_id"`
	Error     string `json:"error"`
}

// AIRequestPerformanceMetrics represents performance metrics for AI requests
type AIRequestPerformanceMetrics struct {
	TenantID              uint      `json:"tenant_id"`
	WebsiteID             *uint     `json:"website_id,omitempty"`
	StartTime             time.Time `json:"start_time"`
	EndTime               time.Time `json:"end_time"`
	TotalRequests         int64     `json:"total_requests"`
	AverageProcessingTime float64   `json:"average_processing_time_ms"`
	MedianProcessingTime  float64   `json:"median_processing_time_ms"`
	P95ProcessingTime     float64   `json:"p95_processing_time_ms"`
	P99ProcessingTime     float64   `json:"p99_processing_time_ms"`
	MaxProcessingTime     float64   `json:"max_processing_time_ms"`
	MinProcessingTime     float64   `json:"min_processing_time_ms"`
	SuccessRate           float64   `json:"success_rate"`
	ErrorRate             float64   `json:"error_rate"`
	TimeoutRate           float64   `json:"timeout_rate"`
	RequestsPerSecond     float64   `json:"requests_per_second"`
	TokensPerSecond       float64   `json:"tokens_per_second"`
	CostPerSecond         float64   `json:"cost_per_second"`
}
