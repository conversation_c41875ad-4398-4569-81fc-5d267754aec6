package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/ai/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// AIRequestHandler handles HTTP requests for AI requests
type AIRequestHandler struct {
	service services.AIRequestService
	logger  utils.Logger
}

// NewAIRequestHandler creates a new AI request handler
func NewAIRequestHandler(service services.AIRequestService, logger utils.Logger) *AIRequestHandler {
	return &AIRequestHandler{
		service: service,
		logger:  logger,
	}
}

// CreateRequest handles creating a new AI request
func (h *AIRequestHandler) CreateRequest(c *gin.Context) {
	var input dto.CreateAIRequestRequest

	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind JSON for create request")
		response.ValidationError(c.Writer, err)
		return
	}

	// Get tenant ID from context (would be set by authentication middleware)
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	input.TenantID = tenantID.(uint)

	// Get user ID from context if available
	if userID, exists := c.Get("user_id"); exists {
		uid := userID.(uint)
		input.UserID = &uid
	}

	// Get website ID from context if available
	if websiteID, exists := c.Get("website_id"); exists {
		wid := websiteID.(uint)
		input.WebsiteID = &wid
	}

	// Add client info to metadata
	if input.Metadata == nil {
		input.Metadata = make(models.JSONMap)
	}
	input.Metadata["client_info"] = map[string]interface{}{
		"user_agent": c.GetHeader("User-Agent"),
		"ip_address": c.ClientIP(),
		"timestamp":  time.Now().UTC(),
	}

	// Create request
	request, err := h.service.CreateRequest(c.Request.Context(), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to create AI request")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	// Convert to response
	var resp dto.AIRequestResponse
	resp.FromAIRequest(request)

	response.Created(c.Writer, resp)
}

// GetRequest handles retrieving an AI request by ID
func (h *AIRequestHandler) GetRequest(c *gin.Context) {
	// Get request ID from URL parameter
	requestID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, err.Error())
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	// Get request
	request, err := h.service.GetRequestWithRelations(c.Request.Context(), tenantID.(uint), uint(requestID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get AI request")
		response.NotFound(c.Writer, err.Error())
		return
	}

	// Convert to response
	var resp dto.AIRequestResponse
	resp.FromAIRequest(request)

	response.Success(c.Writer, resp)
}

// UpdateRequest handles updating an AI request
func (h *AIRequestHandler) UpdateRequest(c *gin.Context) {
	// Get request ID from URL parameter
	requestID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, err.Error())
		return
	}

	var input dto.UpdateAIRequestRequest
	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind JSON for update request")
		response.ValidationError(c.Writer, err)
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	// Update request
	request, err := h.service.UpdateRequest(c.Request.Context(), tenantID.(uint), uint(requestID), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to update AI request")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	// Convert to response
	var resp dto.AIRequestResponse
	resp.FromAIRequest(request)

	response.Success(c.Writer, resp)
}

// DeleteRequest handles deleting an AI request
func (h *AIRequestHandler) DeleteRequest(c *gin.Context) {
	// Get request ID from URL parameter
	requestID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, err.Error())
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	// Delete request
	err = h.service.DeleteRequest(c.Request.Context(), tenantID.(uint), uint(requestID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to delete AI request")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	response.Success(c.Writer, nil)
}

// ListRequests handles listing AI requests with filtering and pagination
func (h *AIRequestHandler) ListRequests(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	// Parse query parameters
	filter := dto.AIRequestFilter{
		TenantID: &[]uint{tenantID.(uint)}[0],
	}

	// Parse optional filters
	if websiteID := c.Query("website_id"); websiteID != "" {
		if wid, err := strconv.ParseUint(websiteID, 10, 32); err == nil {
			uid := uint(wid)
			filter.WebsiteID = &uid
		}
	}

	if userID := c.Query("user_id"); userID != "" {
		if uid, err := strconv.ParseUint(userID, 10, 32); err == nil {
			uid := uint(uid)
			filter.UserID = &uid
		}
	}

	if modelID := c.Query("model_id"); modelID != "" {
		if mid, err := strconv.ParseUint(modelID, 10, 32); err == nil {
			uid := uint(mid)
			filter.ModelID = &uid
		}
	}

	if requestType := c.Query("request_type"); requestType != "" {
		rt := models.AIRequestType(requestType)
		filter.RequestType = &rt
	}

	if status := c.Query("status"); status != "" {
		s := models.AIRequestStatus(status)
		filter.Status = &s
	}

	// Parse time filters
	if createdAfter := c.Query("created_after"); createdAfter != "" {
		if t, err := time.Parse(time.RFC3339, createdAfter); err == nil {
			filter.CreatedAfter = &t
		}
	}

	if createdBefore := c.Query("created_before"); createdBefore != "" {
		if t, err := time.Parse(time.RFC3339, createdBefore); err == nil {
			filter.CreatedBefore = &t
		}
	}

	if searchQuery := c.Query("search_query"); searchQuery != "" {
		filter.SearchQuery = searchQuery
	}

	// Parse pagination
	pag := &pagination.CursorPagination{
		Limit:  20, // Default limit
		Cursor: c.Query("cursor"),
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil && l > 0 && l <= 100 {
			pag.Limit = l
		}
	}

	filter.Pagination = pag

	// Convert filter to service input
	serviceInput := services.ListAIRequestsInput{
		TenantID:          *filter.TenantID,
		WebsiteID:         filter.WebsiteID,
		UserID:            filter.UserID,
		ModelID:           filter.ModelID,
		RequestType:       filter.RequestType,
		Status:            filter.Status,
		CreatedAfter:      filter.CreatedAfter,
		CreatedBefore:     filter.CreatedBefore,
		MinTokens:         filter.MinTokens,
		MaxTokens:         filter.MaxTokens,
		MinCost:           filter.MinCost,
		MaxCost:           filter.MaxCost,
		MinProcessingTime: filter.MinProcessingTime,
		MaxProcessingTime: filter.MaxProcessingTime,
		Pagination:        filter.Pagination,
	}

	// List requests
	requests, paginationResp, err := h.service.ListRequests(c.Request.Context(), serviceInput)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list AI requests")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	// Convert to DTO response
	resp := dto.AIRequestListResponse{
		Requests:   make([]dto.AIRequestResponse, len(requests)),
		Pagination: paginationResp,
		Total:      int64(len(requests)),
	}

	for i, req := range requests {
		resp.Requests[i].FromAIRequest(&req)
	}

	response.Success(c.Writer, resp)
}

// ProcessRequest handles processing an AI request
func (h *AIRequestHandler) ProcessRequest(c *gin.Context) {
	// Get request ID from URL parameter
	requestID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, err.Error())
		return
	}

	// Process request
	request, err := h.service.ProcessRequest(c.Request.Context(), uint(requestID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to process AI request")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	// Convert to response
	var resp dto.AIRequestResponse
	resp.FromAIRequest(request)

	response.Success(c.Writer, resp)
}

// RetryRequest handles retrying a failed AI request
func (h *AIRequestHandler) RetryRequest(c *gin.Context) {
	// Get request ID from URL parameter
	requestID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, err.Error())
		return
	}

	// Retry request
	request, err := h.service.RetryFailedRequest(c.Request.Context(), uint(requestID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to retry AI request")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	// Convert to response
	var resp dto.AIRequestResponse
	resp.FromAIRequest(request)

	response.Success(c.Writer, resp)
}

// CancelRequest handles canceling a pending AI request
func (h *AIRequestHandler) CancelRequest(c *gin.Context) {
	// Get request ID from URL parameter
	requestID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, err.Error())
		return
	}

	// Cancel request
	err = h.service.CancelRequest(c.Request.Context(), uint(requestID))
	if err != nil {
		h.logger.WithError(err).Error("Failed to cancel AI request")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	response.Success(c.Writer, nil)
}

// ProcessPendingRequests handles processing multiple pending AI requests
func (h *AIRequestHandler) ProcessPendingRequests(c *gin.Context) {
	// Parse limit parameter
	limit := 10 // Default limit
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	// Process pending requests
	results, err := h.service.ProcessPendingRequests(c.Request.Context(), limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process pending AI requests")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	response.Success(c.Writer, results)
}

// GetAnalytics handles retrieving AI request analytics
func (h *AIRequestHandler) GetAnalytics(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	// Parse query parameters
	input := services.AnalyticsInput{
		TenantID: tenantID.(uint),
		Scope:    c.DefaultQuery("scope", "tenant"),
	}

	// Parse time range
	if startTime := c.Query("start_time"); startTime != "" {
		if t, err := time.Parse(time.RFC3339, startTime); err == nil {
			input.StartTime = t
		} else {
			response.BadRequest(c.Writer, err.Error())
			return
		}
	} else {
		input.StartTime = time.Now().Add(-24 * time.Hour) // Default to last 24 hours
	}

	if endTime := c.Query("end_time"); endTime != "" {
		if t, err := time.Parse(time.RFC3339, endTime); err == nil {
			input.EndTime = t
		} else {
			response.BadRequest(c.Writer, err.Error())
			return
		}
	} else {
		input.EndTime = time.Now()
	}

	// Parse optional scope filters
	if websiteID := c.Query("website_id"); websiteID != "" {
		if wid, err := strconv.ParseUint(websiteID, 10, 32); err == nil {
			uid := uint(wid)
			input.WebsiteID = &uid
		}
	}

	if userID := c.Query("user_id"); userID != "" {
		if uid, err := strconv.ParseUint(userID, 10, 32); err == nil {
			uid := uint(uid)
			input.UserID = &uid
		}
	}

	if modelID := c.Query("model_id"); modelID != "" {
		if mid, err := strconv.ParseUint(modelID, 10, 32); err == nil {
			uid := uint(mid)
			input.ModelID = &uid
		}
	}

	// Get analytics
	analytics, err := h.service.GetAnalytics(c.Request.Context(), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get AI request analytics")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	response.Success(c.Writer, analytics)
}

// GetUsageStats handles retrieving usage statistics
func (h *AIRequestHandler) GetUsageStats(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	// Parse query parameters
	input := services.UsageStatsInput{
		TenantID: tenantID.(uint),
	}

	// Parse time range
	if startTime := c.Query("start_time"); startTime != "" {
		if t, err := time.Parse(time.RFC3339, startTime); err == nil {
			input.StartTime = t
		} else {
			response.BadRequest(c.Writer, err.Error())
			return
		}
	} else {
		input.StartTime = time.Now().Add(-24 * time.Hour)
	}

	if endTime := c.Query("end_time"); endTime != "" {
		if t, err := time.Parse(time.RFC3339, endTime); err == nil {
			input.EndTime = t
		} else {
			response.BadRequest(c.Writer, err.Error())
			return
		}
	} else {
		input.EndTime = time.Now()
	}

	// Get usage stats
	stats, err := h.service.GetUsageStats(c.Request.Context(), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get usage stats")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	response.Success(c.Writer, stats)
}

// GetPerformanceMetrics handles retrieving performance metrics
func (h *AIRequestHandler) GetPerformanceMetrics(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	// Parse query parameters
	input := services.PerformanceMetricsInput{
		TenantID: tenantID.(uint),
	}

	// Parse time range
	if startTime := c.Query("start_time"); startTime != "" {
		if t, err := time.Parse(time.RFC3339, startTime); err == nil {
			input.StartTime = t
		} else {
			response.BadRequest(c.Writer, err.Error())
			return
		}
	} else {
		input.StartTime = time.Now().Add(-24 * time.Hour)
	}

	if endTime := c.Query("end_time"); endTime != "" {
		if t, err := time.Parse(time.RFC3339, endTime); err == nil {
			input.EndTime = t
		} else {
			response.BadRequest(c.Writer, err.Error())
			return
		}
	} else {
		input.EndTime = time.Now()
	}

	// Get performance metrics
	metrics, err := h.service.GetPerformanceMetrics(c.Request.Context(), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get performance metrics")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	response.Success(c.Writer, metrics)
}

// SearchRequests handles searching AI requests
func (h *AIRequestHandler) SearchRequests(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	// Parse query parameters
	query := c.Query("q")
	if query == "" {
		response.BadRequest(c.Writer, "Query parameter is required")
		return
	}

	input := services.SearchAIRequestsInput{
		TenantID: tenantID.(uint),
		Query:    query,
	}

	// Parse pagination
	pagination := &pagination.CursorPagination{
		Limit:  20, // Default limit
		Cursor: c.Query("cursor"),
	}

	if limit := c.Query("limit"); limit != "" {
		if l, err := strconv.Atoi(limit); err == nil && l > 0 && l <= 100 {
			pagination.Limit = l
		}
	}

	input.Pagination = pagination

	// Search requests
	requests, paginationResp, err := h.service.SearchRequests(c.Request.Context(), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search AI requests")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	// Convert to response
	resp := make([]dto.AIRequestResponse, len(requests))
	for i, req := range requests {
		resp[i].FromAIRequest(&req)
	}

	response.CursorPaginated(c.Writer, resp, *paginationResp)
}

// BulkCreateRequests handles bulk creating AI requests
func (h *AIRequestHandler) BulkCreateRequests(c *gin.Context) {
	var inputs []services.CreateAIRequestInput

	if err := c.ShouldBindJSON(&inputs); err != nil {
		h.logger.WithError(err).Error("Failed to bind JSON for bulk create requests")
		response.ValidationError(c.Writer, err)
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	// Set tenant ID for all inputs
	for i := range inputs {
		inputs[i].TenantID = tenantID.(uint)
	}

	// Bulk create requests
	requests, errors := h.service.BulkCreateRequests(c.Request.Context(), inputs)

	// Check for errors
	hasErrors := false
	for _, err := range errors {
		if err != nil {
			hasErrors = true
			break
		}
	}

	// Convert to response
	resp := make([]dto.AIRequestResponse, len(requests))
	for i, req := range requests {
		resp[i].FromAIRequest(&req)
	}

	result := map[string]interface{}{
		"requests": resp,
		"errors":   errors,
	}

	if hasErrors {
		c.JSON(http.StatusMultiStatus, gin.H{
			"success": true,
			"message": "Bulk operation completed with some errors",
			"data":    result,
		})
	} else {
		response.Success(c.Writer, result)
	}
}

// BulkUpdateStatus handles bulk updating AI request status
func (h *AIRequestHandler) BulkUpdateStatus(c *gin.Context) {
	var input struct {
		RequestIDs []uint                 `json:"request_ids" binding:"required"`
		Status     models.AIRequestStatus `json:"status" binding:"required"`
	}

	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind JSON for bulk update status")
		response.ValidationError(c.Writer, err)
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	// Bulk update status
	err := h.service.BulkUpdateStatus(c.Request.Context(), tenantID.(uint), input.RequestIDs, input.Status)
	if err != nil {
		h.logger.WithError(err).Error("Failed to bulk update AI request status")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	response.Success(c.Writer, nil)
}

// BulkProcessRequests handles bulk processing AI requests
func (h *AIRequestHandler) BulkProcessRequests(c *gin.Context) {
	var input struct {
		RequestIDs []uint `json:"request_ids" binding:"required"`
	}

	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind JSON for bulk process requests")
		response.ValidationError(c.Writer, err)
		return
	}

	// Bulk process requests
	results, err := h.service.BulkProcessRequests(c.Request.Context(), input.RequestIDs)
	if err != nil {
		h.logger.WithError(err).Error("Failed to bulk process AI requests")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	response.Success(c.Writer, results)
}

// GetRateLimitInfo handles retrieving rate limit information
func (h *AIRequestHandler) GetRateLimitInfo(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	input := services.RateLimitInput{
		TenantID: tenantID.(uint),
	}

	// Get website ID from context if available
	if websiteID, exists := c.Get("website_id"); exists {
		wid := websiteID.(uint)
		input.WebsiteID = &wid
	}

	// Get user ID from context if available
	if userID, exists := c.Get("user_id"); exists {
		uid := userID.(uint)
		input.UserID = &uid
	}

	// Get rate limit info
	info, err := h.service.GetRateLimitInfo(c.Request.Context(), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get rate limit info")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	response.Success(c.Writer, info)
}

// QueueRequest handles queuing an AI request
func (h *AIRequestHandler) QueueRequest(c *gin.Context) {
	var input services.QueueRequestInput

	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind JSON for queue request")
		response.ValidationError(c.Writer, err)
		return
	}

	// Queue request
	request, err := h.service.QueueRequest(c.Request.Context(), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to queue AI request")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	// Convert to response
	var resp dto.AIRequestResponse
	if request != nil {
		resp.FromAIRequest(request)
	}

	response.Success(c.Writer, resp)
}

// GetQueueStatus handles retrieving queue status
func (h *AIRequestHandler) GetQueueStatus(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	// Get queue status
	status, err := h.service.GetQueueStatus(c.Request.Context(), tenantID.(uint))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get queue status")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	response.Success(c.Writer, status)
}

// GetCostBreakdown handles retrieving cost breakdown
func (h *AIRequestHandler) GetCostBreakdown(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	// Parse query parameters
	input := services.CostBreakdownInput{
		TenantID: tenantID.(uint),
	}

	// Parse time range
	if startTime := c.Query("start_time"); startTime != "" {
		if t, err := time.Parse(time.RFC3339, startTime); err == nil {
			input.StartTime = t
		} else {
			response.BadRequest(c.Writer, err.Error())
			return
		}
	} else {
		input.StartTime = time.Now().Add(-24 * time.Hour)
	}

	if endTime := c.Query("end_time"); endTime != "" {
		if t, err := time.Parse(time.RFC3339, endTime); err == nil {
			input.EndTime = t
		} else {
			response.BadRequest(c.Writer, err.Error())
			return
		}
	} else {
		input.EndTime = time.Now()
	}

	// Get cost breakdown
	breakdown, err := h.service.GetCostBreakdown(c.Request.Context(), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get cost breakdown")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	response.Success(c.Writer, breakdown)
}

// ExportRequests handles exporting AI requests
func (h *AIRequestHandler) ExportRequests(c *gin.Context) {
	var input services.ExportInput

	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind JSON for export requests")
		response.ValidationError(c.Writer, err)
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	input.TenantID = tenantID.(uint)

	// Export requests
	result, err := h.service.ExportRequests(c.Request.Context(), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to export AI requests")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	response.Success(c.Writer, result)
}

// CleanupOldRequests handles cleaning up old AI requests
func (h *AIRequestHandler) CleanupOldRequests(c *gin.Context) {
	var input services.CleanupInput

	if err := c.ShouldBindJSON(&input); err != nil {
		h.logger.WithError(err).Error("Failed to bind JSON for cleanup requests")
		response.ValidationError(c.Writer, err)
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		// This should not happen if middleware is properly configured
		response.InternalServerError(c.Writer, "Tenant context not found", "MISSING_TENANT_CONTEXT")
		return
	}

	input.TenantID = tenantID.(uint)

	// Cleanup old requests
	result, err := h.service.CleanupOldRequests(c.Request.Context(), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to cleanup old AI requests")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	response.Success(c.Writer, result)
}

// GetHealthStatus handles retrieving health status
func (h *AIRequestHandler) GetHealthStatus(c *gin.Context) {
	// Get health status
	status, err := h.service.GetHealthStatus(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get health status")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	response.Success(c.Writer, status)
}

// GetSystemMetrics handles retrieving system metrics
func (h *AIRequestHandler) GetSystemMetrics(c *gin.Context) {
	// Get system metrics
	metrics, err := h.service.GetSystemMetrics(c.Request.Context())
	if err != nil {
		h.logger.WithError(err).Error("Failed to get system metrics")
		response.InternalServerError(c.Writer, err.Error())
		return
	}

	response.Success(c.Writer, metrics)
}
