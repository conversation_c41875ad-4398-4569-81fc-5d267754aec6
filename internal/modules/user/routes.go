package user

import (
	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"gorm.io/gorm"
)

// RegisterRoutes registers all user-related routes
func RegisterRoutes(
	router *gin.RouterGroup,
	db *gorm.DB,
	validator validator.Validator,
	logger utils.Logger,
) {
	// Initialize repositories
	userRepo := mysql.NewUserRepository(db, logger)
	userProfileRepo := mysql.NewUserProfileRepository(db, logger)
	userPreferencesRepo := mysql.NewUserPreferencesRepository(db, logger)
	userSocialLinksRepo := mysql.NewUserSocialLinksRepository(db, logger)
	tenantMembershipRepo := mysql.NewTenantMembershipRepository(db, logger)
	userInvitationRepo := mysql.NewUserInvitationRepository(db, logger)

	// Initialize services
	userService := services.NewUserService(userRepo, userProfileRepo, userPreferencesRepo, userSocialLinksRepo, logger)
	userProfileService := services.NewUserProfileService(userProfileRepo, userRepo, logger)
	userPreferencesService := services.NewUserPreferencesService(userPreferencesRepo, userRepo, logger)
	userSocialLinksService := services.NewUserSocialLinksService(userSocialLinksRepo, userRepo, logger)
	userSearchService := services.NewUserSearchService(userRepo, userProfileRepo, logger)
	userAnalyticsService := services.NewUserAnalyticsService(userRepo, userProfileRepo, logger)
	// TODO: UserVerificationService needs emailVerificationService and notificationService from auth module
	// This creates a circular dependency that needs to be resolved
	// For now, passing nil to allow compilation
	userVerificationService := services.NewUserVerificationService(userRepo, nil, nil, logger)
	tenantMembershipService := services.NewTenantMembershipService(tenantMembershipRepo, logger)
	userInvitationService := services.NewUserInvitationService(userInvitationRepo, tenantMembershipRepo, userRepo, logger)

	// Initialize handlers
	userHandler := handlers.NewUserHandler(userService, validator, logger)
	userProfileHandler := handlers.NewUserProfileHandler(userProfileService, validator, logger)
	userPreferencesHandler := handlers.NewUserPreferencesHandler(userPreferencesService, validator, logger)
	userSocialLinksHandler := handlers.NewUserSocialLinksHandler(userSocialLinksService, validator, logger)
	userSearchHandler := handlers.NewUserSearchHandler(userSearchService, validator, logger)
	userAnalyticsHandler := handlers.NewUserAnalyticsHandler(userAnalyticsService, validator, logger)
	userVerificationHandler := handlers.NewUserVerificationHandler(userVerificationService, validator, logger)
	tenantMembershipHandler := handlers.NewTenantMembershipHandler(tenantMembershipService, validator, logger)
	userInvitationHandler := handlers.NewUserInvitationHandler(userInvitationService, validator, logger)

	// User routes
	userRoutes := router.Group("/users")
	{
		userRoutes.POST("", userHandler.CreateUser)
		userRoutes.GET("", userHandler.ListUsers)
		userRoutes.GET("/search", userHandler.SearchUsers)
		userRoutes.GET("/stats", userHandler.GetUserStats)
		userRoutes.PUT("/bulk/status", userHandler.BulkUpdateUserStatus)

		userRoutes.GET("/:id", userHandler.GetUser)
		userRoutes.PUT("/:id", userHandler.UpdateUser)
		userRoutes.DELETE("/:id", userHandler.DeleteUser)
		userRoutes.PUT("/:id/status", userHandler.UpdateUserStatus)
		userRoutes.PUT("/:id/password", userHandler.UpdateUserPassword)
		userRoutes.POST("/:id/verify-email", userHandler.VerifyUserEmail)
		userRoutes.POST("/:id/verify-phone", userHandler.VerifyUserPhone)
		userRoutes.POST("/:id/two-factor/enable", userHandler.EnableTwoFactor)
		userRoutes.POST("/:id/two-factor/disable", userHandler.DisableTwoFactor)

		// Tenant membership routes for users
		userRoutes.GET("/:id/memberships", tenantMembershipHandler.GetUserMemberships)
	}

	// User Profile routes
	userProfileRoutes := router.Group("/user-profiles")
	{
		userProfileRoutes.POST("", userProfileHandler.CreateUserProfile)
		userProfileRoutes.GET("/low-completion", userProfileHandler.GetProfilesWithLowCompletion)
		userProfileRoutes.GET("/search/skills", userProfileHandler.SearchProfilesBySkills)
		userProfileRoutes.GET("/search/location", userProfileHandler.SearchProfilesByLocation)
		userProfileRoutes.GET("/stats/completion", userProfileHandler.GetCompletionStats)
		userProfileRoutes.POST("/validate", userProfileHandler.ValidateProfile)

		userProfileRoutes.GET("/user/:user_id", userProfileHandler.GetUserProfile)
		userProfileRoutes.PUT("/user/:user_id", userProfileHandler.UpdateUserProfile)
		userProfileRoutes.PUT("/user/:user_id/completion", userProfileHandler.UpdateCompletionStatus)
		userProfileRoutes.PUT("/user/:user_id/custom-fields", userProfileHandler.UpdateCustomFields)
	}

	// User Preferences routes
	userPreferencesRoutes := router.Group("/user-preferences")
	{
		userPreferencesRoutes.POST("", userPreferencesHandler.CreateUserPreferences)
		userPreferencesRoutes.GET("/defaults", userPreferencesHandler.GetDefaultPreferences)
		userPreferencesRoutes.POST("/validate", userPreferencesHandler.ValidatePreferences)
		userPreferencesRoutes.GET("/stats", userPreferencesHandler.GetPreferencesStats)

		userPreferencesRoutes.GET("/user/:user_id", userPreferencesHandler.GetUserPreferences)
		userPreferencesRoutes.PUT("/user/:user_id", userPreferencesHandler.UpdateUserPreferences)
		userPreferencesRoutes.PUT("/user/:user_id/notifications", userPreferencesHandler.UpdateNotificationPreferences)
		userPreferencesRoutes.PUT("/user/:user_id/privacy", userPreferencesHandler.UpdatePrivacyPreferences)
		userPreferencesRoutes.PUT("/user/:user_id/ui", userPreferencesHandler.UpdateUIPreferences)
		userPreferencesRoutes.GET("/user/:user_id/export", userPreferencesHandler.ExportPreferences)
		userPreferencesRoutes.POST("/user/:user_id/import", userPreferencesHandler.ImportPreferences)
	}

	// User Social Links routes
	userSocialLinksRoutes := router.Group("/user-social-links")
	{
		userSocialLinksRoutes.POST("", userSocialLinksHandler.CreateSocialLink)
		userSocialLinksRoutes.POST("/validate", userSocialLinksHandler.ValidateSocialLink)
		userSocialLinksRoutes.GET("/stats", userSocialLinksHandler.GetPlatformStats)
		userSocialLinksRoutes.PUT("/bulk/verify", userSocialLinksHandler.BulkUpdateVerificationStatus)

		userSocialLinksRoutes.GET("/:id", userSocialLinksHandler.GetSocialLinkByPlatform)
		userSocialLinksRoutes.PUT("/:id", userSocialLinksHandler.UpdateSocialLink)
		userSocialLinksRoutes.DELETE("/:id", userSocialLinksHandler.DeleteSocialLink)
		userSocialLinksRoutes.POST("/:id/verify", userSocialLinksHandler.VerifySocialLink)
		userSocialLinksRoutes.POST("/:id/unverify", userSocialLinksHandler.UnverifySocialLink)

		userSocialLinksRoutes.GET("/user/:user_id", userSocialLinksHandler.GetUserSocialLinks)
		userSocialLinksRoutes.GET("/user/:user_id/public", userSocialLinksHandler.GetPublicSocialLinks)
		userSocialLinksRoutes.POST("/user/:user_id/reorder", userSocialLinksHandler.ReorderSocialLinks)
		userSocialLinksRoutes.POST("/user/:user_id/bulk", userSocialLinksHandler.BulkCreateSocialLinks)
		userSocialLinksRoutes.GET("/user/:user_id/platform/:platform", userSocialLinksHandler.GetSocialLinkByPlatform)
	}

	// User Search routes
	userSearchRoutes := router.Group("/user-search")
	{
		userSearchRoutes.GET("", userSearchHandler.SearchUsers)
		userSearchRoutes.GET("/skills", userSearchHandler.SearchUsersBySkills)
		userSearchRoutes.GET("/location", userSearchHandler.SearchUsersByLocation)
		userSearchRoutes.GET("/company", userSearchHandler.SearchUsersByCompany)
		userSearchRoutes.GET("/suggestions", userSearchHandler.GetSearchSuggestions)
		userSearchRoutes.GET("/popular-skills", userSearchHandler.GetPopularSkills)
		userSearchRoutes.GET("/popular-locations", userSearchHandler.GetPopularLocations)
	}

	// User Analytics routes
	userAnalyticsRoutes := router.Group("/user-analytics")
	{
		userAnalyticsRoutes.GET("/stats", userAnalyticsHandler.GetUserStats)
		userAnalyticsRoutes.GET("/registrations", userAnalyticsHandler.GetRegistrationStats)
		userAnalyticsRoutes.GET("/engagement", userAnalyticsHandler.GetEngagementStats)
		userAnalyticsRoutes.GET("/retention", userAnalyticsHandler.GetRetentionStats)
		userAnalyticsRoutes.GET("/demographics", userAnalyticsHandler.GetDemographicStats)
		userAnalyticsRoutes.GET("/activity/:user_id", userAnalyticsHandler.GetUserActivity)
	}

	// User Verification routes
	userVerificationRoutes := router.Group("/user-verification")
	{
		userVerificationRoutes.POST("/:user_id/email/send", userVerificationHandler.SendEmailVerification)
		userVerificationRoutes.POST("/:user_id/email/verify", userVerificationHandler.VerifyEmail)
		userVerificationRoutes.POST("/:user_id/phone/send", userVerificationHandler.SendPhoneVerification)
		userVerificationRoutes.POST("/:user_id/phone/verify", userVerificationHandler.VerifyPhone)
		userVerificationRoutes.POST("/:user_id/resend", userVerificationHandler.ResendVerification)
		userVerificationRoutes.GET("/:user_id/status", userVerificationHandler.CheckVerificationStatus)
	}

	// User Invitation routes
	invitationRoutes := router.Group("/user-invitations")
	{
		// Basic CRUD operations
		invitationRoutes.POST("", userInvitationHandler.CreateInvitation)
		invitationRoutes.GET("/:id", userInvitationHandler.GetInvitation)
		invitationRoutes.PUT("/:id", userInvitationHandler.UpdateInvitation)
		invitationRoutes.DELETE("/:id", userInvitationHandler.DeleteInvitation)
		invitationRoutes.GET("", userInvitationHandler.ListInvitations)

		// Token-based operations
		invitationRoutes.GET("/token/:token", userInvitationHandler.GetInvitationByToken)

		// Invitation actions
		invitationRoutes.POST("/accept", userInvitationHandler.AcceptInvitation)
		invitationRoutes.POST("/reject", userInvitationHandler.RejectInvitation)
		invitationRoutes.POST("/:id/revoke", userInvitationHandler.RevokeInvitation)
		invitationRoutes.POST("/:id/resend", userInvitationHandler.ResendInvitation)
	}

	// Tenant Membership routes
	tenantRoutes := router.Group("/tenants")
	{
		tenantRoutes.GET("/:id/members", tenantMembershipHandler.GetTenantMembers)
		tenantRoutes.POST("/:id/members", tenantMembershipHandler.AddTenantMember)
		tenantRoutes.GET("/:id/members/stats", tenantMembershipHandler.GetMembershipStats)

		tenantRoutes.GET("/:id/members/:userId", tenantMembershipHandler.GetMembership)
		tenantRoutes.PUT("/:id/members/:userId/role", tenantMembershipHandler.UpdateMemberRole)
		tenantRoutes.PUT("/:id/members/:userId/status", tenantMembershipHandler.UpdateMembershipStatus)
		tenantRoutes.DELETE("/:id/members/:userId", tenantMembershipHandler.RemoveTenantMember)
	}
}
