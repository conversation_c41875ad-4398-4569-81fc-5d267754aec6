package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// UserSearchHandler handles user search-related HTTP requests
type UserSearchHandler struct {
	searchService services.UserSearchService
	validator     validator.Validator
	logger        utils.Logger
}

// NewUserSearchHandler creates a new user search handler
func NewUserSearchHandler(
	searchService services.UserSearchService,
	validator validator.Validator,
	logger utils.Logger,
) *UserSearchHandler {
	return &UserSearchHandler{
		searchService: searchService,
		validator:     validator,
		logger:        logger,
	}
}

// SearchUsers searches users with advanced filtering
// @Summary Search users with advanced filtering
// @Description Search users by query with advanced filtering options
// @Tags user-search
// @Accept json
// @Produce json
// @Param query query string true "Search query"
// @Param skills query []string false "Skills to search for" collectionFormat(multi)
// @Param location query string false "Location to search for"
// @Param company query string false "Company to search for"
// @Param role query string false "Role to filter by"
// @Param status query string false "Status to filter by"
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Limit for pagination" default(20)
// @Success 200 {object} services.UserSearchResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-search [get]
func (h *UserSearchHandler) SearchUsers(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpresponse.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Get query parameter
	query := c.Query("query")
	if query == "" {
		h.logger.Error("Search query is required")
		httpresponse.BadRequest(c.Writer, "Search query is required")
		return
	}

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		httpresponse.BadRequest(c.Writer, "Invalid limit parameter")
		return
	}

	// Create pagination
	pagination := &pagination.CursorPagination{
		Cursor: cursor,
		Limit:  limit,
	}

	// Build search input
	input := services.UserSearchInput{
		TenantID:   tenantID.(uint),
		Query:      query,
		Pagination: pagination,
	}

	// Parse optional filters
	if skills := c.QueryArray("skills"); len(skills) > 0 {
		input.Skills = skills
	}

	if location := c.Query("location"); location != "" {
		input.Location = location
	}

	if company := c.Query("company"); company != "" {
		input.Company = company
	}

	if role := c.Query("role"); role != "" {
		userRole := models.UserRole(role)
		input.Role = &userRole
	}

	if status := c.Query("status"); status != "" {
		userStatus := models.UserStatus(status)
		input.Status = &userStatus
	}

	// Validate input
	if err := h.validator.Validate(c.Request.Context(), input); err != nil {
		h.logger.WithError(err).Error("Failed to validate search input")
		httpresponse.ValidationError(c.Writer, err)
		return
	}

	// Search users
	response, err := h.searchService.Search(c.Request.Context(), input)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search users")
		httpresponse.InternalError(c.Writer, "Failed to search users")
		return
	}

	httpresponse.OK(c.Writer, response)
}

// SearchUsersBySkills searches users by skills
// @Summary Search users by skills
// @Description Search users by specific skills
// @Tags user-search
// @Accept json
// @Produce json
// @Param skills query []string true "Skills to search for" collectionFormat(multi)
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Limit for pagination" default(20)
// @Success 200 {object} services.UserSearchResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-search/skills [get]
func (h *UserSearchHandler) SearchUsersBySkills(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpresponse.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Parse skills parameter
	skills := c.QueryArray("skills")
	if len(skills) == 0 {
		h.logger.Error("Skills parameter is required")
		httpresponse.BadRequest(c.Writer, "Skills parameter is required")
		return
	}

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		httpresponse.BadRequest(c.Writer, "Invalid limit parameter")
		return
	}

	// Create pagination
	pagination := &pagination.CursorPagination{
		Cursor: cursor,
		Limit:  limit,
	}

	// Search users by skills
	response, err := h.searchService.SearchBySkills(c.Request.Context(), tenantID.(uint), skills, pagination)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search users by skills")
		httpresponse.InternalError(c.Writer, "Failed to search users by skills")
		return
	}

	httpresponse.OK(c.Writer, response)
}

// SearchUsersByLocation searches users by location
// @Summary Search users by location
// @Description Search users by location
// @Tags user-search
// @Accept json
// @Produce json
// @Param location query string true "Location to search for"
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Limit for pagination" default(20)
// @Success 200 {object} services.UserSearchResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-search/location [get]
func (h *UserSearchHandler) SearchUsersByLocation(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpresponse.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Parse location parameter
	location := c.Query("location")
	if location == "" {
		h.logger.Error("Location parameter is required")
		httpresponse.BadRequest(c.Writer, "Location parameter is required")
		return
	}

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		httpresponse.BadRequest(c.Writer, "Invalid limit parameter")
		return
	}

	// Create pagination
	pagination := &pagination.CursorPagination{
		Cursor: cursor,
		Limit:  limit,
	}

	// Search users by location
	response, err := h.searchService.SearchByLocation(c.Request.Context(), tenantID.(uint), location, pagination)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search users by location")
		httpresponse.InternalError(c.Writer, "Failed to search users by location")
		return
	}

	httpresponse.OK(c.Writer, response)
}

// SearchUsersByCompany searches users by company
// @Summary Search users by company
// @Description Search users by company
// @Tags user-search
// @Accept json
// @Produce json
// @Param company query string true "Company to search for"
// @Param cursor query string false "Cursor for pagination"
// @Param limit query int false "Limit for pagination" default(20)
// @Success 200 {object} services.UserSearchResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-search/company [get]
func (h *UserSearchHandler) SearchUsersByCompany(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpresponse.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Parse company parameter
	company := c.Query("company")
	if company == "" {
		h.logger.Error("Company parameter is required")
		httpresponse.BadRequest(c.Writer, "Company parameter is required")
		return
	}

	// Parse pagination parameters
	cursor := c.Query("cursor")
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		httpresponse.BadRequest(c.Writer, "Invalid limit parameter")
		return
	}

	// Create pagination
	pagination := &pagination.CursorPagination{
		Cursor: cursor,
		Limit:  limit,
	}

	// Search users by company
	response, err := h.searchService.SearchByCompany(c.Request.Context(), tenantID.(uint), company, pagination)
	if err != nil {
		h.logger.WithError(err).Error("Failed to search users by company")
		httpresponse.InternalError(c.Writer, "Failed to search users by company")
		return
	}

	httpresponse.OK(c.Writer, response)
}

// GetSearchSuggestions retrieves search suggestions
// @Summary Get search suggestions
// @Description Get search suggestions based on partial query
// @Tags user-search
// @Accept json
// @Produce json
// @Param query query string true "Partial search query"
// @Param limit query int false "Limit for suggestions" default(10)
// @Success 200 {array} string
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-search/suggestions [get]
func (h *UserSearchHandler) GetSearchSuggestions(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpresponse.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Parse query parameter
	query := c.Query("query")
	if query == "" {
		h.logger.Error("Query parameter is required")
		httpresponse.BadRequest(c.Writer, "Query parameter is required")
		return
	}

	// Parse limit parameter
	limitStr := c.DefaultQuery("limit", "10")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		httpresponse.BadRequest(c.Writer, "Invalid limit parameter")
		return
	}

	// Get search suggestions
	suggestions, err := h.searchService.GetSearchSuggestions(c.Request.Context(), tenantID.(uint), query, limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get search suggestions")
		httpresponse.InternalError(c.Writer, "Failed to get search suggestions")
		return
	}

	httpresponse.OK(c.Writer, suggestions)
}

// GetPopularSkills retrieves popular skills
// @Summary Get popular skills
// @Description Get popular skills used by users
// @Tags user-search
// @Accept json
// @Produce json
// @Param limit query int false "Limit for skills" default(20)
// @Success 200 {array} string
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-search/popular-skills [get]
func (h *UserSearchHandler) GetPopularSkills(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpresponse.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Parse limit parameter
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		httpresponse.BadRequest(c.Writer, "Invalid limit parameter")
		return
	}

	// Get popular skills
	skills, err := h.searchService.GetPopularSkills(c.Request.Context(), tenantID.(uint), limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get popular skills")
		httpresponse.InternalError(c.Writer, "Failed to get popular skills")
		return
	}

	httpresponse.OK(c.Writer, skills)
}

// GetPopularLocations retrieves popular locations
// @Summary Get popular locations
// @Description Get popular locations used by users
// @Tags user-search
// @Accept json
// @Produce json
// @Param limit query int false "Limit for locations" default(20)
// @Success 200 {array} string
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /user-search/popular-locations [get]
func (h *UserSearchHandler) GetPopularLocations(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		h.logger.Error("Tenant ID not found in context")
		httpresponse.BadRequest(c.Writer, "Tenant ID is required")
		return
	}

	// Parse limit parameter
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		h.logger.WithError(err).Error("Invalid limit parameter")
		httpresponse.BadRequest(c.Writer, "Invalid limit parameter")
		return
	}

	// Get popular locations
	locations, err := h.searchService.GetPopularLocations(c.Request.Context(), tenantID.(uint), limit)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get popular locations")
		httpresponse.InternalError(c.Writer, "Failed to get popular locations")
		return
	}

	httpresponse.OK(c.Writer, locations)
}
