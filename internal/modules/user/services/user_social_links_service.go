package services

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// userSocialLinksService implements UserSocialLinksService interface
type userSocialLinksService struct {
	socialLinksRepo repositories.UserSocialLinksRepository
	userRepo        repositories.UserRepository
	logger          utils.Logger
}

// NewUserSocialLinksService creates a new user social links service
func NewUserSocialLinksService(
	socialLinksRepo repositories.UserSocialLinksRepository,
	userRepo repositories.UserRepository,
	logger utils.Logger,
) UserSocialLinksService {
	return &userSocialLinksService{
		socialLinksRepo: socialLinksRepo,
		userRepo:        userRepo,
		logger:          logger,
	}
}

// <PERSON><PERSON> creates a new social link
func (s *userSocialLinksService) Create(ctx context.Context, input CreateUserSocialLinkInput) (*models.UserSocialLink, error) {
	// Validate input
	if err := s.ValidateLink(ctx, input); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Check if user exists
	_, err := s.userRepo.GetByID(ctx, input.UserID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Check if platform already exists for user
	exists, err := s.socialLinksRepo.PlatformExists(ctx, input.UserID, input.Platform)
	if err != nil {
		return nil, fmt.Errorf("failed to check platform existence: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("platform %s already exists for user", input.Platform)
	}

	// Create social link model
	socialLink := &models.UserSocialLink{
		UserID:       input.UserID,
		Platform:     input.Platform,
		Username:     input.Username,
		URL:          input.URL,
		DisplayOrder: input.DisplayOrder,
		IsPublic:     input.IsPublic,
		IsVerified:   false, // Default to false
	}

	// Set profile data
	if len(input.ProfileData) > 0 {
		if err := socialLink.SetProfileData(input.ProfileData); err != nil {
			return nil, fmt.Errorf("failed to set profile data: %w", err)
		}
	}

	// Set default display order if not provided
	if socialLink.DisplayOrder == 0 {
		maxOrder, err := s.socialLinksRepo.GetMaxDisplayOrder(ctx, input.UserID)
		if err != nil {
			return nil, fmt.Errorf("failed to get max display order: %w", err)
		}
		socialLink.DisplayOrder = maxOrder + 1
	}

	// Create social link in database
	if err := s.socialLinksRepo.Create(ctx, socialLink); err != nil {
		s.logger.WithError(err).Error("Failed to create social link")
		return nil, fmt.Errorf("failed to create social link: %w", err)
	}

	return socialLink, nil
}

// GetByUserID retrieves social links by user ID
func (s *userSocialLinksService) GetByUserID(ctx context.Context, userID uint) ([]models.UserSocialLink, error) {
	links, err := s.socialLinksRepo.GetByUserID(ctx, userID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get social links")
		return nil, fmt.Errorf("failed to get social links: %w", err)
	}
	return links, nil
}

// GetByUserIDAndPlatform retrieves social link by user ID and platform
func (s *userSocialLinksService) GetByUserIDAndPlatform(ctx context.Context, userID uint, platform models.SocialPlatform) (*models.UserSocialLink, error) {
	link, err := s.socialLinksRepo.GetByUserIDAndPlatform(ctx, userID, platform)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("social link not found")
		}
		return nil, fmt.Errorf("failed to get social link: %w", err)
	}
	return link, nil
}

// Update updates social link
func (s *userSocialLinksService) Update(ctx context.Context, id uint, input UpdateUserSocialLinkInput) (*models.UserSocialLink, error) {
	// Validate input
	if err := s.validateUpdateInput(ctx, input); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Get existing social link
	link, err := s.socialLinksRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("social link not found")
		}
		return nil, fmt.Errorf("failed to get social link: %w", err)
	}

	// Update fields
	if input.Username != nil {
		link.Username = *input.Username
	}
	if input.URL != nil {
		link.URL = *input.URL
	}
	if input.DisplayOrder != nil {
		link.DisplayOrder = *input.DisplayOrder
	}
	if input.IsPublic != nil {
		link.IsPublic = *input.IsPublic
	}

	// Update profile data
	if len(input.ProfileData) > 0 {
		if err := link.SetProfileData(input.ProfileData); err != nil {
			return nil, fmt.Errorf("failed to set profile data: %w", err)
		}
	}

	// Update social link in database
	if err := s.socialLinksRepo.Update(ctx, link); err != nil {
		s.logger.WithError(err).Error("Failed to update social link")
		return nil, fmt.Errorf("failed to update social link: %w", err)
	}

	return link, nil
}

// Delete deletes social link
func (s *userSocialLinksService) Delete(ctx context.Context, id uint) error {
	// Check if social link exists
	link, err := s.socialLinksRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("social link not found")
		}
		return fmt.Errorf("failed to get social link: %w", err)
	}

	// Delete social link
	if err := s.socialLinksRepo.Delete(ctx, link.ID); err != nil {
		s.logger.WithError(err).Error("Failed to delete social link")
		return fmt.Errorf("failed to delete social link: %w", err)
	}

	return nil
}

// VerifyLink verifies social link
func (s *userSocialLinksService) VerifyLink(ctx context.Context, id uint) error {
	// Get social link
	link, err := s.socialLinksRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("social link not found")
		}
		return fmt.Errorf("failed to get social link: %w", err)
	}

	// Verify link
	if err := s.socialLinksRepo.UpdateVerificationStatus(ctx, link.ID, true); err != nil {
		s.logger.WithError(err).Error("Failed to verify social link")
		return fmt.Errorf("failed to verify social link: %w", err)
	}

	return nil
}

// UnverifyLink unverifies social link
func (s *userSocialLinksService) UnverifyLink(ctx context.Context, id uint) error {
	// Get social link
	link, err := s.socialLinksRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("social link not found")
		}
		return fmt.Errorf("failed to get social link: %w", err)
	}

	// Unverify link
	if err := s.socialLinksRepo.UpdateVerificationStatus(ctx, link.ID, false); err != nil {
		s.logger.WithError(err).Error("Failed to unverify social link")
		return fmt.Errorf("failed to unverify social link: %w", err)
	}

	return nil
}

// ReorderLinks reorders social links for user
func (s *userSocialLinksService) ReorderLinks(ctx context.Context, userID uint, linkOrders []models.UserSocialLinkOrder) error {
	// Validate input
	if err := s.validateReorderInput(ctx, userID, linkOrders); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	// Reorder links
	if err := s.socialLinksRepo.ReorderUserLinks(ctx, userID, linkOrders); err != nil {
		s.logger.WithError(err).Error("Failed to reorder social links")
		return fmt.Errorf("failed to reorder social links: %w", err)
	}

	return nil
}

// GetPublicLinks retrieves public social links
func (s *userSocialLinksService) GetPublicLinks(ctx context.Context, userID uint) ([]models.UserSocialLink, error) {
	links, err := s.socialLinksRepo.GetPublicLinksByUser(ctx, userID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get public social links")
		return nil, fmt.Errorf("failed to get public social links: %w", err)
	}
	return links, nil
}

// ValidateLink validates social link data
func (s *userSocialLinksService) ValidateLink(ctx context.Context, input CreateUserSocialLinkInput) error {
	// Validate platform
	if err := s.validatePlatform(input.Platform); err != nil {
		return fmt.Errorf("invalid platform: %w", err)
	}

	// Validate username
	if err := s.validateUsername(input.Username); err != nil {
		return fmt.Errorf("invalid username: %w", err)
	}

	// Validate URL
	if err := s.validateURL(input.URL, input.Platform); err != nil {
		return fmt.Errorf("invalid URL: %w", err)
	}

	// Validate profile data
	if len(input.ProfileData) > 0 {
		if err := s.validateProfileData(input.ProfileData); err != nil {
			return fmt.Errorf("invalid profile data: %w", err)
		}
	}

	return nil
}

// GetPlatformStats retrieves platform statistics
func (s *userSocialLinksService) GetPlatformStats(ctx context.Context, tenantID uint) (*SocialPlatformStats, error) {
	stats, err := s.socialLinksRepo.GetPlatformStats(ctx, tenantID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get platform stats")
		return nil, fmt.Errorf("failed to get platform stats: %w", err)
	}

	return &SocialPlatformStats{
		TotalLinks:          stats.TotalLinks,
		VerifiedLinks:       stats.VerifiedLinks,
		UnverifiedLinks:     stats.UnverifiedLinks,
		PublicLinks:         stats.PublicLinks,
		PrivateLinks:        stats.PrivateLinks,
		PlatformCounts:      stats.PlatformCounts,
		VerificationRates:   stats.VerificationRates,
		PublicRates:         stats.PublicRates,
		UsersWithLinks:      stats.UsersWithLinks,
		AverageLinksPerUser: stats.AverageLinksPerUser,
	}, nil
}

// BulkCreate creates multiple social links
func (s *userSocialLinksService) BulkCreate(ctx context.Context, userID uint, links []CreateUserSocialLinkInput) error {
	// Check if user exists
	_, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Validate all links
	for i, link := range links {
		if err := s.ValidateLink(ctx, link); err != nil {
			return fmt.Errorf("validation failed for link %d: %w", i, err)
		}
	}

	// Create social links
	socialLinks := make([]models.UserSocialLink, 0, len(links))
	for _, link := range links {
		socialLink := models.UserSocialLink{
			UserID:       userID,
			Platform:     link.Platform,
			Username:     link.Username,
			URL:          link.URL,
			DisplayOrder: link.DisplayOrder,
			IsPublic:     link.IsPublic,
			IsVerified:   false,
		}

		// Set profile data
		if len(link.ProfileData) > 0 {
			if err := socialLink.SetProfileData(link.ProfileData); err != nil {
				return fmt.Errorf("failed to set profile data: %w", err)
			}
		}

		socialLinks = append(socialLinks, socialLink)
	}

	// Bulk create social links
	if err := s.socialLinksRepo.BulkCreate(ctx, socialLinks); err != nil {
		s.logger.WithError(err).Error("Failed to bulk create social links")
		return fmt.Errorf("failed to bulk create social links: %w", err)
	}

	return nil
}

// BulkUpdateVerificationStatus updates verification status for multiple links
func (s *userSocialLinksService) BulkUpdateVerificationStatus(ctx context.Context, ids []uint, verified bool) error {
	if err := s.socialLinksRepo.BulkUpdateVerificationStatus(ctx, ids, verified); err != nil {
		s.logger.WithError(err).Error("Failed to bulk update verification status")
		return fmt.Errorf("failed to bulk update verification status: %w", err)
	}
	return nil
}

// Helper methods

// validateUpdateInput validates update social link input
func (s *userSocialLinksService) validateUpdateInput(ctx context.Context, input UpdateUserSocialLinkInput) error {
	// Validate username
	if input.Username != nil {
		if err := s.validateUsername(*input.Username); err != nil {
			return fmt.Errorf("invalid username: %w", err)
		}
	}

	// Validate URL
	if input.URL != nil {
		if err := s.validateSimpleURL(*input.URL); err != nil {
			return fmt.Errorf("invalid URL: %w", err)
		}
	}

	// Validate profile data
	if len(input.ProfileData) > 0 {
		if err := s.validateProfileData(input.ProfileData); err != nil {
			return fmt.Errorf("invalid profile data: %w", err)
		}
	}

	return nil
}

// validateReorderInput validates reorder input
func (s *userSocialLinksService) validateReorderInput(ctx context.Context, userID uint, linkOrders []models.UserSocialLinkOrder) error {
	if len(linkOrders) == 0 {
		return fmt.Errorf("link orders cannot be empty")
	}

	// Check for duplicate IDs
	seenIDs := make(map[uint]bool)
	for _, order := range linkOrders {
		if seenIDs[order.ID] {
			return fmt.Errorf("duplicate link ID: %d", order.ID)
		}
		seenIDs[order.ID] = true
	}

	// Verify all links belong to the user
	for _, order := range linkOrders {
		link, err := s.socialLinksRepo.GetByID(ctx, order.ID)
		if err != nil {
			return fmt.Errorf("link not found: %d", order.ID)
		}
		if link.UserID != userID {
			return fmt.Errorf("link %d does not belong to user %d", order.ID, userID)
		}
	}

	return nil
}

// validatePlatform validates social platform
func (s *userSocialLinksService) validatePlatform(platform models.SocialPlatform) error {
	validPlatforms := map[models.SocialPlatform]bool{
		models.PlatformTwitter:       true,
		models.PlatformLinkedIn:      true,
		models.PlatformGitHub:        true,
		models.PlatformFacebook:      true,
		models.PlatformInstagram:     true,
		models.PlatformYouTube:       true,
		models.PlatformTikTok:        true,
		models.PlatformSnapchat:      true,
		models.PlatformDiscord:       true,
		models.PlatformTwitch:        true,
		models.PlatformReddit:        true,
		models.PlatformPinterest:     true,
		models.PlatformMedium:        true,
		models.PlatformDev:           true,
		models.PlatformStackOverflow: true,
		models.PlatformBehance:       true,
		models.PlatformDribbble:      true,
		models.PlatformWebsite:       true,
		models.PlatformOther:         true,
	}

	if !validPlatforms[platform] {
		return fmt.Errorf("invalid platform: %s", platform)
	}

	return nil
}

// validateUsername validates username
func (s *userSocialLinksService) validateUsername(username string) error {
	if len(strings.TrimSpace(username)) == 0 {
		return fmt.Errorf("username cannot be empty")
	}

	if len(username) > 255 {
		return fmt.Errorf("username must be less than 255 characters")
	}

	return nil
}

// validateURL validates URL for specific platform
func (s *userSocialLinksService) validateURL(url string, platform models.SocialPlatform) error {
	if len(strings.TrimSpace(url)) == 0 {
		return fmt.Errorf("URL cannot be empty")
	}

	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		return fmt.Errorf("URL must start with http:// or https://")
	}

	// Platform-specific URL validation
	switch platform {
	case models.PlatformTwitter:
		if !strings.Contains(url, "twitter.com") && !strings.Contains(url, "x.com") {
			return fmt.Errorf("invalid Twitter URL")
		}
	case models.PlatformLinkedIn:
		if !strings.Contains(url, "linkedin.com") {
			return fmt.Errorf("invalid LinkedIn URL")
		}
	case models.PlatformGitHub:
		if !strings.Contains(url, "github.com") {
			return fmt.Errorf("invalid GitHub URL")
		}
	case models.PlatformFacebook:
		if !strings.Contains(url, "facebook.com") {
			return fmt.Errorf("invalid Facebook URL")
		}
	case models.PlatformInstagram:
		if !strings.Contains(url, "instagram.com") {
			return fmt.Errorf("invalid Instagram URL")
		}
	case models.PlatformYouTube:
		if !strings.Contains(url, "youtube.com") && !strings.Contains(url, "youtu.be") {
			return fmt.Errorf("invalid YouTube URL")
		}
	}

	return nil
}

// validateSimpleURL validates URL without platform checking
func (s *userSocialLinksService) validateSimpleURL(url string) error {
	if len(strings.TrimSpace(url)) == 0 {
		return fmt.Errorf("URL cannot be empty")
	}

	if !strings.HasPrefix(url, "http://") && !strings.HasPrefix(url, "https://") {
		return fmt.Errorf("URL must start with http:// or https://")
	}

	return nil
}

// validateProfileData validates profile data
func (s *userSocialLinksService) validateProfileData(profileData map[string]interface{}) error {
	if len(profileData) > 10 {
		return fmt.Errorf("too many profile data fields (max 10)")
	}

	for key, value := range profileData {
		if len(key) > 50 {
			return fmt.Errorf("profile data key '%s' is too long (max 50 characters)", key)
		}

		// Validate value type and length
		switch v := value.(type) {
		case string:
			if len(v) > 500 {
				return fmt.Errorf("profile data value for '%s' is too long (max 500 characters)", key)
			}
		case []interface{}:
			if len(v) > 50 {
				return fmt.Errorf("profile data array for '%s' is too long (max 50 items)", key)
			}
		}
	}

	return nil
}
