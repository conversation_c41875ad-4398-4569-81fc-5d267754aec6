package services

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// UserService defines operations for user management
type UserService interface {
	// C<PERSON> creates a new user with tenant context
	Create(ctx context.Context, input CreateUserInput) (*models.User, error)

	// GetByID retrieves a user by ID
	GetByID(ctx context.Context, id uint) (*models.User, error)

	// GetByEmail retrieves a user by email and tenant ID
	GetByEmail(ctx context.Context, email string, tenantID uint) (*models.User, error)

	// GetByUsername retrieves a user by username and tenant ID
	GetByUsername(ctx context.Context, username string, tenantID uint) (*models.User, error)

	// Update updates user information
	Update(ctx context.Context, id uint, input UpdateUserInput) (*models.User, error)

	// UpdateStatus updates user status
	UpdateStatus(ctx context.Context, id uint, status models.UserStatus) error

	// Delete soft deletes a user
	Delete(ctx context.Context, id uint) error

	// List retrieves users with pagination and filtering
	List(ctx context.Context, filter ListUserFilter) (*UserListResponse, error)

	// Search searches users by query
	Search(ctx context.Context, tenantID uint, query string, pagination *pagination.CursorPagination) (*UserSearchResponse, error)

	// UpdatePassword updates user password
	UpdatePassword(ctx context.Context, userID uint, newPassword string) error

	// VerifyEmail verifies user email
	VerifyEmail(ctx context.Context, userID uint) error

	// VerifyPhone verifies user phone
	VerifyPhone(ctx context.Context, userID uint) error

	// EnableTwoFactor enables two-factor authentication
	EnableTwoFactor(ctx context.Context, userID uint) (*TwoFactorSetup, error)

	// DisableTwoFactor disables two-factor authentication
	DisableTwoFactor(ctx context.Context, userID uint) error

	// GetUserStats retrieves user statistics
	GetUserStats(ctx context.Context, tenantID uint) (*UserStats, error)

	// BulkUpdateStatus updates status for multiple users
	BulkUpdateStatus(ctx context.Context, userIDs []uint, status models.UserStatus) error

	// TransferToTenant transfers user to another tenant
	TransferToTenant(ctx context.Context, userID uint, newTenantID uint) error

	// GetRecentlyActive retrieves recently active users
	GetRecentlyActive(ctx context.Context, tenantID uint, limit int) ([]models.User, error)
}

// UserProfileService defines operations for user profile management
type UserProfileService interface {
	// Create creates a new user profile
	Create(ctx context.Context, input CreateUserProfileInput) (*models.UserProfile, error)

	// GetByUserID retrieves user profile by user ID
	GetByUserID(ctx context.Context, userID uint) (*models.UserProfile, error)

	// Update updates user profile
	Update(ctx context.Context, userID uint, input UpdateUserProfileInput) (*models.UserProfile, error)

	// UpdateCompletionStatus updates profile completion status
	UpdateCompletionStatus(ctx context.Context, userID uint) error

	// GetProfilesWithLowCompletion retrieves profiles with low completion
	GetProfilesWithLowCompletion(ctx context.Context, tenantID uint, threshold uint8, pagination *pagination.CursorPagination) (*UserProfileListResponse, error)

	// SearchBySkills searches profiles by skills
	SearchBySkills(ctx context.Context, tenantID uint, skills []string, pagination *pagination.CursorPagination) (*UserProfileListResponse, error)

	// SearchByLocation searches profiles by location
	SearchByLocation(ctx context.Context, tenantID uint, location string, pagination *pagination.CursorPagination) (*UserProfileListResponse, error)

	// GetCompletionStats retrieves profile completion statistics
	GetCompletionStats(ctx context.Context, tenantID uint) (*ProfileCompletionStats, error)

	// ValidateProfile validates profile data
	ValidateProfile(ctx context.Context, input UpdateUserProfileInput) error

	// UpdateCustomFields updates custom fields
	UpdateCustomFields(ctx context.Context, userID uint, customFields map[string]interface{}) error
}

// UserPreferencesService defines operations for user preferences management
type UserPreferencesService interface {
	// Create creates user preferences with defaults
	Create(ctx context.Context, input CreateUserPreferencesInput) (*models.UserPreferences, error)

	// GetByUserID retrieves user preferences by user ID
	GetByUserID(ctx context.Context, userID uint) (*models.UserPreferences, error)

	// Update updates user preferences
	Update(ctx context.Context, userID uint, input UpdateUserPreferencesInput) (*models.UserPreferences, error)

	// UpdateNotificationPreferences updates notification preferences
	UpdateNotificationPreferences(ctx context.Context, userID uint, preferences NotificationPreferencesInput) error

	// UpdatePrivacyPreferences updates privacy preferences
	UpdatePrivacyPreferences(ctx context.Context, userID uint, preferences PrivacyPreferencesInput) error

	// UpdateUIPreferences updates UI preferences
	UpdateUIPreferences(ctx context.Context, userID uint, preferences UIPreferencesInput) error

	// GetDefaultPreferences retrieves default preferences for tenant
	GetDefaultPreferences(ctx context.Context, tenantID uint) (*models.UserPreferences, error)

	// ValidatePreferences validates preferences data
	ValidatePreferences(ctx context.Context, input UpdateUserPreferencesInput) error

	// ExportPreferences exports user preferences
	ExportPreferences(ctx context.Context, userID uint) (map[string]interface{}, error)

	// ImportPreferences imports user preferences
	ImportPreferences(ctx context.Context, userID uint, preferences map[string]interface{}) error

	// GetPreferencesStats retrieves preferences statistics
	GetPreferencesStats(ctx context.Context, tenantID uint) (*PreferencesStats, error)
}

// UserSocialLinksService defines operations for user social links management
type UserSocialLinksService interface {
	// Create creates a new social link
	Create(ctx context.Context, input CreateUserSocialLinkInput) (*models.UserSocialLink, error)

	// GetByUserID retrieves social links by user ID
	GetByUserID(ctx context.Context, userID uint) ([]models.UserSocialLink, error)

	// GetByUserIDAndPlatform retrieves social link by user ID and platform
	GetByUserIDAndPlatform(ctx context.Context, userID uint, platform models.SocialPlatform) (*models.UserSocialLink, error)

	// Update updates social link
	Update(ctx context.Context, id uint, input UpdateUserSocialLinkInput) (*models.UserSocialLink, error)

	// Delete deletes social link
	Delete(ctx context.Context, id uint) error

	// VerifyLink verifies social link
	VerifyLink(ctx context.Context, id uint) error

	// UnverifyLink unverifies social link
	UnverifyLink(ctx context.Context, id uint) error

	// ReorderLinks reorders social links for user
	ReorderLinks(ctx context.Context, userID uint, linkOrders []models.UserSocialLinkOrder) error

	// GetPublicLinks retrieves public social links
	GetPublicLinks(ctx context.Context, userID uint) ([]models.UserSocialLink, error)

	// ValidateLink validates social link data
	ValidateLink(ctx context.Context, input CreateUserSocialLinkInput) error

	// GetPlatformStats retrieves platform statistics
	GetPlatformStats(ctx context.Context, tenantID uint) (*SocialPlatformStats, error)

	// BulkCreate creates multiple social links
	BulkCreate(ctx context.Context, userID uint, links []CreateUserSocialLinkInput) error

	// BulkUpdateVerificationStatus updates verification status for multiple links
	BulkUpdateVerificationStatus(ctx context.Context, ids []uint, verified bool) error
}

// UserSearchService defines operations for user search functionality
type UserSearchService interface {
	// Search searches users by query with advanced filtering
	Search(ctx context.Context, input UserSearchInput) (*UserSearchResponse, error)

	// SearchBySkills searches users by skills
	SearchBySkills(ctx context.Context, tenantID uint, skills []string, pagination *pagination.CursorPagination) (*UserSearchResponse, error)

	// SearchByLocation searches users by location
	SearchByLocation(ctx context.Context, tenantID uint, location string, pagination *pagination.CursorPagination) (*UserSearchResponse, error)

	// SearchByCompany searches users by company
	SearchByCompany(ctx context.Context, tenantID uint, company string, pagination *pagination.CursorPagination) (*UserSearchResponse, error)

	// GetSearchSuggestions retrieves search suggestions
	GetSearchSuggestions(ctx context.Context, tenantID uint, query string, limit int) ([]string, error)

	// GetPopularSkills retrieves popular skills
	GetPopularSkills(ctx context.Context, tenantID uint, limit int) ([]string, error)

	// GetPopularLocations retrieves popular locations
	GetPopularLocations(ctx context.Context, tenantID uint, limit int) ([]string, error)
}

// UserVerificationService defines operations for user verification
type UserVerificationService interface {
	// SendEmailVerification sends email verification
	SendEmailVerification(ctx context.Context, userID uint) error

	// VerifyEmail verifies email with token
	VerifyEmail(ctx context.Context, userID uint, token string) error

	// SendPhoneVerification sends phone verification
	SendPhoneVerification(ctx context.Context, userID uint) error

	// VerifyPhone verifies phone with code
	VerifyPhone(ctx context.Context, userID uint, code string) error

	// ResendVerification resends verification
	ResendVerification(ctx context.Context, userID uint, verificationType string) error

	// CheckVerificationStatus checks verification status
	CheckVerificationStatus(ctx context.Context, userID uint) (*VerificationStatus, error)
}

// UserAnalyticsService defines operations for user analytics
type UserAnalyticsService interface {
	// GetUserStats retrieves user statistics
	GetUserStats(ctx context.Context, tenantID uint) (*UserStats, error)

	// GetUserActivity retrieves user activity
	GetUserActivity(ctx context.Context, userID uint, days int) (*UserActivity, error)

	// GetRegistrationStats retrieves registration statistics
	GetRegistrationStats(ctx context.Context, tenantID uint, period string) (*RegistrationStats, error)

	// GetEngagementStats retrieves engagement statistics
	GetEngagementStats(ctx context.Context, tenantID uint, period string) (*EngagementStats, error)

	// GetRetentionStats retrieves retention statistics
	GetRetentionStats(ctx context.Context, tenantID uint, period string) (*RetentionStats, error)

	// GetDemographicStats retrieves demographic statistics
	GetDemographicStats(ctx context.Context, tenantID uint) (*DemographicStats, error)
}

// TenantMembershipService defines operations for tenant membership management
type TenantMembershipService interface {
	// GetUserMemberships retrieves all memberships for a user
	GetUserMemberships(ctx context.Context, userID uint) ([]models.TenantMembership, error)

	// GetTenantMembers retrieves all members for a tenant
	GetTenantMembers(ctx context.Context, tenantID uint, filter *TenantMembershipFilter) (*TenantMembershipListResponse, error)

	// AddTenantMember adds a user to a tenant
	AddTenantMember(ctx context.Context, input AddTenantMemberInput) (*models.TenantMembership, error)

	// UpdateMemberRole updates a member's role in a tenant
	UpdateMemberRole(ctx context.Context, tenantID, userID uint, input UpdateMemberRoleInput) (*models.TenantMembership, error)

	// RemoveTenantMember removes a user from a tenant
	RemoveTenantMember(ctx context.Context, tenantID, userID uint) error

	// GetMembership retrieves a specific membership
	GetMembership(ctx context.Context, tenantID, userID uint) (*models.TenantMembership, error)

	// UpdateMembershipStatus updates membership status
	UpdateMembershipStatus(ctx context.Context, tenantID, userID uint, status models.TenantMembershipStatus) error

	// GetMembershipStats retrieves membership statistics
	GetMembershipStats(ctx context.Context, tenantID uint) (*TenantMembershipStats, error)

	// GetActiveMemberships retrieves active memberships for a user
	GetActiveMemberships(ctx context.Context, userID uint) ([]models.TenantMembership, error)
}

// UserInvitationService defines operations for user invitation management
type UserInvitationService interface {
	// CreateInvitation creates a new user invitation
	CreateInvitation(ctx context.Context, inviterID uint, input CreateInvitationInput) (*models.UserInvitationResponse, error)

	// GetInvitationByID retrieves an invitation by ID
	GetInvitationByID(ctx context.Context, id uint) (*models.UserInvitationResponse, error)

	// GetInvitationByToken retrieves an invitation by token
	GetInvitationByToken(ctx context.Context, token string) (*models.UserInvitationResponse, error)

	// ListInvitations retrieves invitations with filtering and pagination
	ListInvitations(ctx context.Context, filter InvitationListFilter) (*InvitationListResponse, error)

	// UpdateInvitation updates an existing invitation
	UpdateInvitation(ctx context.Context, id uint, input UpdateInvitationInput) (*models.UserInvitationResponse, error)

	// AcceptInvitation accepts an invitation
	AcceptInvitation(ctx context.Context, userID uint, input AcceptInvitationInput) (*AcceptInvitationResponse, error)

	// RejectInvitation rejects an invitation
	RejectInvitation(ctx context.Context, input RejectInvitationInput) (*models.UserInvitationResponse, error)

	// RevokeInvitation revokes an invitation
	RevokeInvitation(ctx context.Context, id uint) (*models.UserInvitationResponse, error)

	// ResendInvitation resends an invitation
	ResendInvitation(ctx context.Context, id uint) (*models.UserInvitationResponse, error)

	// DeleteInvitation deletes an invitation
	DeleteInvitation(ctx context.Context, id uint) error

	// CleanupExpiredInvitations cleans up expired invitations
	CleanupExpiredInvitations(ctx context.Context, tenantID uint) error
}
