package services

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// userProfileService implements UserProfileService interface
type userProfileService struct {
	profileRepo repositories.UserProfileRepository
	userRepo    repositories.UserRepository
	logger      utils.Logger
}

// NewUserProfileService creates a new user profile service
func NewUserProfileService(
	profileRepo repositories.UserProfileRepository,
	userRepo repositories.UserRepository,
	logger utils.Logger,
) UserProfileService {
	return &userProfileService{
		profileRepo: profileRepo,
		userRepo:    userRepo,
		logger:      logger,
	}
}

// <PERSON>reate creates a new user profile
func (s *userProfileService) Create(ctx context.Context, input CreateUserProfileInput) (*models.UserProfile, error) {
	// Validate input
	if err := s.validateCreateInput(ctx, input); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Check if user exists
	_, err := s.userRepo.GetByID(ctx, input.UserID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Check if profile already exists
	exists, err := s.profileRepo.ProfileExists(ctx, input.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to check profile existence: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("profile already exists for user")
	}

	// Create profile model
	profile := &models.UserProfile{
		UserID:         input.UserID,
		Bio:            &input.Bio,
		Title:          &input.Title,
		Company:        &input.Company,
		Location:       &input.Location,
		Website:        &input.Website,
		BirthDate:      input.BirthDate,
		Gender:         (*models.Gender)(&input.Gender),
		AddressLine1:   &input.Address,
		City:           &input.City,
		State:          &input.State,
		Country:        &input.Country,
		PostalCode:     &input.PostalCode,
		JobTitle:       &input.JobTitle,
		Department:     &input.Department,
		DisplayProfile: input.DisplayProfile,
		AllowContact:   input.AllowContact,
		ShowEmail:      input.ShowEmail,
		ShowPhone:      input.ShowPhone,
	}

	// Set skills and interests
	if len(input.Skills) > 0 {
		if err := profile.SetSkills(input.Skills); err != nil {
			return nil, fmt.Errorf("failed to set skills: %w", err)
		}
	}

	if len(input.Interests) > 0 {
		if err := profile.SetInterests(input.Interests); err != nil {
			return nil, fmt.Errorf("failed to set interests: %w", err)
		}
	}

	// Set custom fields
	if len(input.CustomFields) > 0 {
		if err := profile.SetCustomFields(input.CustomFields); err != nil {
			return nil, fmt.Errorf("failed to set custom fields: %w", err)
		}
	}

	// Create profile in database
	if err := s.profileRepo.Create(ctx, profile); err != nil {
		s.logger.WithError(err).Error("Failed to create user profile")
		return nil, fmt.Errorf("failed to create user profile: %w", err)
	}

	// Update completion status
	if err := s.profileRepo.UpdateCompletionStatus(ctx, input.UserID); err != nil {
		s.logger.WithError(err).Error("Failed to update completion status")
		// Don't fail profile creation if completion update fails
	}

	return profile, nil
}

// GetByUserID retrieves user profile by user ID
func (s *userProfileService) GetByUserID(ctx context.Context, userID uint) (*models.UserProfile, error) {
	profile, err := s.profileRepo.GetByUserID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("profile not found")
		}
		return nil, fmt.Errorf("failed to get profile: %w", err)
	}
	return profile, nil
}

// Update updates user profile
func (s *userProfileService) Update(ctx context.Context, userID uint, input UpdateUserProfileInput) (*models.UserProfile, error) {
	// Validate input
	if err := s.validateUpdateInput(ctx, input); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Get existing profile
	profile, err := s.profileRepo.GetByUserID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("profile not found")
		}
		return nil, fmt.Errorf("failed to get profile: %w", err)
	}

	// Update fields
	if input.Bio != nil {
		profile.Bio = input.Bio
	}
	if input.Title != nil {
		profile.Title = input.Title
	}
	if input.Company != nil {
		profile.Company = input.Company
	}
	if input.Location != nil {
		profile.Location = input.Location
	}
	if input.Website != nil {
		profile.Website = input.Website
	}
	if input.BirthDate != nil {
		profile.BirthDate = input.BirthDate
	}
	if input.Gender != nil {
		gender := models.Gender(*input.Gender)
		profile.Gender = &gender
	}
	if input.Address != nil {
		profile.AddressLine1 = input.Address
	}
	if input.City != nil {
		profile.City = input.City
	}
	if input.State != nil {
		profile.State = input.State
	}
	if input.Country != nil {
		profile.Country = input.Country
	}
	if input.PostalCode != nil {
		profile.PostalCode = input.PostalCode
	}
	if input.JobTitle != nil {
		profile.JobTitle = input.JobTitle
	}
	if input.Department != nil {
		profile.Department = input.Department
	}
	if input.DisplayProfile != nil {
		profile.DisplayProfile = *input.DisplayProfile
	}
	if input.AllowContact != nil {
		profile.AllowContact = *input.AllowContact
	}
	if input.ShowEmail != nil {
		profile.ShowEmail = *input.ShowEmail
	}
	if input.ShowPhone != nil {
		profile.ShowPhone = *input.ShowPhone
	}

	// Update skills
	if len(input.Skills) > 0 {
		if err := profile.SetSkills(input.Skills); err != nil {
			return nil, fmt.Errorf("failed to set skills: %w", err)
		}
	}

	// Update interests
	if len(input.Interests) > 0 {
		if err := profile.SetInterests(input.Interests); err != nil {
			return nil, fmt.Errorf("failed to set interests: %w", err)
		}
	}

	// Update custom fields
	if len(input.CustomFields) > 0 {
		if err := profile.SetCustomFields(input.CustomFields); err != nil {
			return nil, fmt.Errorf("failed to set custom fields: %w", err)
		}
	}

	// Update profile in database
	if err := s.profileRepo.Update(ctx, profile); err != nil {
		s.logger.WithError(err).Error("Failed to update user profile")
		return nil, fmt.Errorf("failed to update user profile: %w", err)
	}

	// Update completion status
	if err := s.profileRepo.UpdateCompletionStatus(ctx, userID); err != nil {
		s.logger.WithError(err).Error("Failed to update completion status")
		// Don't fail profile update if completion update fails
	}

	return profile, nil
}

// UpdateCompletionStatus updates profile completion status
func (s *userProfileService) UpdateCompletionStatus(ctx context.Context, userID uint) error {
	if err := s.profileRepo.UpdateCompletionStatus(ctx, userID); err != nil {
		s.logger.WithError(err).Error("Failed to update completion status")
		return fmt.Errorf("failed to update completion status: %w", err)
	}
	return nil
}

// GetProfilesWithLowCompletion retrieves profiles with low completion
func (s *userProfileService) GetProfilesWithLowCompletion(ctx context.Context, tenantID uint, threshold uint8, pag *pagination.CursorPagination) (*UserProfileListResponse, error) {
	// Set default pagination if not provided
	if pag == nil {
		pag = &pagination.CursorPagination{
			Cursor: "",
			Limit:  20,
		}
	}

	// Get profiles from repository
	profiles, paginationResponse, err := s.profileRepo.GetProfilesWithLowCompletion(ctx, tenantID, threshold, pag)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get profiles with low completion")
		return nil, fmt.Errorf("failed to get profiles with low completion: %w", err)
	}

	// Get total count
	total, err := s.profileRepo.CountByTenant(ctx, tenantID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count profiles")
		return nil, fmt.Errorf("failed to count profiles: %w", err)
	}

	return &UserProfileListResponse{
		Profiles:   profiles,
		Pagination: paginationResponse,
		Total:      total,
	}, nil
}

// SearchBySkills searches profiles by skills
func (s *userProfileService) SearchBySkills(ctx context.Context, tenantID uint, skills []string, pag *pagination.CursorPagination) (*UserProfileListResponse, error) {
	// Set default pagination if not provided
	if pag == nil {
		pag = &pagination.CursorPagination{
			Cursor: "",
			Limit:  20,
		}
	}

	// Validate skills
	if len(skills) == 0 {
		return nil, fmt.Errorf("skills cannot be empty")
	}

	// Normalize skills
	normalizedSkills := s.normalizeSkills(skills)

	// Search profiles from repository
	profiles, paginationResponse, err := s.profileRepo.SearchBySkills(ctx, tenantID, normalizedSkills, pag)
	if err != nil {
		s.logger.WithError(err).Error("Failed to search profiles by skills")
		return nil, fmt.Errorf("failed to search profiles by skills: %w", err)
	}

	// Get total count
	total, err := s.profileRepo.CountByTenant(ctx, tenantID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count profiles")
		return nil, fmt.Errorf("failed to count profiles: %w", err)
	}

	return &UserProfileListResponse{
		Profiles:   profiles,
		Pagination: paginationResponse,
		Total:      total,
	}, nil
}

// SearchByLocation searches profiles by location
func (s *userProfileService) SearchByLocation(ctx context.Context, tenantID uint, location string, pag *pagination.CursorPagination) (*UserProfileListResponse, error) {
	// Set default pagination if not provided
	if pag == nil {
		pag = &pagination.CursorPagination{
			Cursor: "",
			Limit:  20,
		}
	}

	// Validate location
	if location == "" {
		return nil, fmt.Errorf("location cannot be empty")
	}

	// Normalize location
	normalizedLocation := s.normalizeLocation(location)

	// Search profiles from repository
	profiles, paginationResponse, err := s.profileRepo.SearchByLocation(ctx, tenantID, normalizedLocation, pag)
	if err != nil {
		s.logger.WithError(err).Error("Failed to search profiles by location")
		return nil, fmt.Errorf("failed to search profiles by location: %w", err)
	}

	// Get total count
	total, err := s.profileRepo.CountByTenant(ctx, tenantID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count profiles")
		return nil, fmt.Errorf("failed to count profiles: %w", err)
	}

	return &UserProfileListResponse{
		Profiles:   profiles,
		Pagination: paginationResponse,
		Total:      total,
	}, nil
}

// GetCompletionStats retrieves profile completion statistics
func (s *userProfileService) GetCompletionStats(ctx context.Context, tenantID uint) (*ProfileCompletionStats, error) {
	stats, err := s.profileRepo.GetCompletionStats(ctx, tenantID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get completion stats")
		return nil, fmt.Errorf("failed to get completion stats: %w", err)
	}

	return &ProfileCompletionStats{
		TotalProfiles:        stats.TotalProfiles,
		CompletedProfiles:    stats.CompletedProfiles,
		IncompleteProfiles:   stats.IncompleteProfiles,
		AverageCompletion:    stats.AverageCompletion,
		CompletionRate:       stats.CompletionRate,
		ProfilesWithBio:      stats.ProfilesWithBio,
		ProfilesWithAvatar:   stats.ProfilesWithAvatar,
		ProfilesWithLocation: stats.ProfilesWithLocation,
		ProfilesWithCompany:  stats.ProfilesWithCompany,
		ProfilesWithSkills:   stats.ProfilesWithSkills,
	}, nil
}

// ValidateProfile validates profile data
func (s *userProfileService) ValidateProfile(ctx context.Context, input UpdateUserProfileInput) error {
	// Validate bio length
	if input.Bio != nil && len(*input.Bio) > 500 {
		return fmt.Errorf("bio must be less than 500 characters")
	}

	// Validate title length
	if input.Title != nil && len(*input.Title) > 100 {
		return fmt.Errorf("title must be less than 100 characters")
	}

	// Validate company length
	if input.Company != nil && len(*input.Company) > 100 {
		return fmt.Errorf("company must be less than 100 characters")
	}

	// Validate location length
	if input.Location != nil && len(*input.Location) > 100 {
		return fmt.Errorf("location must be less than 100 characters")
	}

	// Validate website URL
	if input.Website != nil && *input.Website != "" {
		if !s.isValidURL(*input.Website) {
			return fmt.Errorf("invalid website URL")
		}
	}

	// Validate gender
	if input.Gender != nil {
		validGenders := map[string]bool{
			"male":              true,
			"female":            true,
			"other":             true,
			"prefer_not_to_say": true,
		}
		if !validGenders[*input.Gender] {
			return fmt.Errorf("invalid gender")
		}
	}

	// Validate skills
	if len(input.Skills) > 0 {
		if err := s.validateSkills(input.Skills); err != nil {
			return fmt.Errorf("invalid skills: %w", err)
		}
	}

	// Validate interests
	if len(input.Interests) > 0 {
		if err := s.validateInterests(input.Interests); err != nil {
			return fmt.Errorf("invalid interests: %w", err)
		}
	}

	return nil
}

// UpdateCustomFields updates custom fields
func (s *userProfileService) UpdateCustomFields(ctx context.Context, userID uint, customFields map[string]interface{}) error {
	// Validate custom fields
	if err := s.validateCustomFields(customFields); err != nil {
		return fmt.Errorf("invalid custom fields: %w", err)
	}

	// Update custom fields in repository
	if err := s.profileRepo.UpdateCustomFields(ctx, userID, customFields); err != nil {
		s.logger.WithError(err).Error("Failed to update custom fields")
		return fmt.Errorf("failed to update custom fields: %w", err)
	}

	return nil
}

// Helper methods

// validateCreateInput validates create profile input
func (s *userProfileService) validateCreateInput(ctx context.Context, input CreateUserProfileInput) error {
	// Validate bio length
	if len(input.Bio) > 500 {
		return fmt.Errorf("bio must be less than 500 characters")
	}

	// Validate title length
	if len(input.Title) > 100 {
		return fmt.Errorf("title must be less than 100 characters")
	}

	// Validate company length
	if len(input.Company) > 100 {
		return fmt.Errorf("company must be less than 100 characters")
	}

	// Validate location length
	if len(input.Location) > 100 {
		return fmt.Errorf("location must be less than 100 characters")
	}

	// Validate website URL
	if input.Website != "" && !s.isValidURL(input.Website) {
		return fmt.Errorf("invalid website URL")
	}

	// Validate gender
	if input.Gender != "" {
		validGenders := map[string]bool{
			"male":              true,
			"female":            true,
			"other":             true,
			"prefer_not_to_say": true,
		}
		if !validGenders[input.Gender] {
			return fmt.Errorf("invalid gender")
		}
	}

	// Validate skills
	if len(input.Skills) > 0 {
		if err := s.validateSkills(input.Skills); err != nil {
			return fmt.Errorf("invalid skills: %w", err)
		}
	}

	// Validate interests
	if len(input.Interests) > 0 {
		if err := s.validateInterests(input.Interests); err != nil {
			return fmt.Errorf("invalid interests: %w", err)
		}
	}

	// Validate custom fields
	if len(input.CustomFields) > 0 {
		if err := s.validateCustomFields(input.CustomFields); err != nil {
			return fmt.Errorf("invalid custom fields: %w", err)
		}
	}

	return nil
}

// validateUpdateInput validates update profile input
func (s *userProfileService) validateUpdateInput(ctx context.Context, input UpdateUserProfileInput) error {
	return s.ValidateProfile(ctx, input)
}

// validateSkills validates skills array
func (s *userProfileService) validateSkills(skills []string) error {
	if len(skills) > 50 {
		return fmt.Errorf("too many skills (max 50)")
	}

	for _, skill := range skills {
		if len(skill) > 50 {
			return fmt.Errorf("skill '%s' is too long (max 50 characters)", skill)
		}
		if len(strings.TrimSpace(skill)) == 0 {
			return fmt.Errorf("skill cannot be empty")
		}
	}

	return nil
}

// validateInterests validates interests array
func (s *userProfileService) validateInterests(interests []string) error {
	if len(interests) > 50 {
		return fmt.Errorf("too many interests (max 50)")
	}

	for _, interest := range interests {
		if len(interest) > 50 {
			return fmt.Errorf("interest '%s' is too long (max 50 characters)", interest)
		}
		if len(strings.TrimSpace(interest)) == 0 {
			return fmt.Errorf("interest cannot be empty")
		}
	}

	return nil
}

// validateCustomFields validates custom fields
func (s *userProfileService) validateCustomFields(customFields map[string]interface{}) error {
	if len(customFields) > 20 {
		return fmt.Errorf("too many custom fields (max 20)")
	}

	for key, value := range customFields {
		if len(key) > 50 {
			return fmt.Errorf("custom field key '%s' is too long (max 50 characters)", key)
		}
		if len(strings.TrimSpace(key)) == 0 {
			return fmt.Errorf("custom field key cannot be empty")
		}

		// Validate value type and length
		switch v := value.(type) {
		case string:
			if len(v) > 500 {
				return fmt.Errorf("custom field value for '%s' is too long (max 500 characters)", key)
			}
		case []interface{}:
			if len(v) > 100 {
				return fmt.Errorf("custom field array for '%s' is too long (max 100 items)", key)
			}
		}
	}

	return nil
}

// normalizeSkills normalizes skills array
func (s *userProfileService) normalizeSkills(skills []string) []string {
	var normalized []string
	for _, skill := range skills {
		trimmed := strings.TrimSpace(skill)
		if trimmed != "" {
			normalized = append(normalized, strings.ToLower(trimmed))
		}
	}
	return normalized
}

// normalizeLocation normalizes location string
func (s *userProfileService) normalizeLocation(location string) string {
	return strings.TrimSpace(location)
}

// isValidURL checks if URL is valid
func (s *userProfileService) isValidURL(url string) bool {
	// Simple URL validation - in production, use proper URL validation
	return strings.HasPrefix(url, "http://") || strings.HasPrefix(url, "https://")
}
