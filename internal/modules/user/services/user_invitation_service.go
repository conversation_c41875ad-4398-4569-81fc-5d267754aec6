package services

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"errors"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// userInvitationService implements UserInvitationService interface
type userInvitationService struct {
	invitationRepo repositories.UserInvitationRepository
	membershipRepo repositories.TenantMembershipRepository
	userRepo       repositories.UserRepository
	logger         utils.Logger
}

// NewUserInvitationService creates a new user invitation service
func NewUserInvitationService(
	invitationRepo repositories.UserInvitationRepository,
	membershipRepo repositories.TenantMembershipRepository,
	userRepo repositories.UserRepository,
	logger utils.Logger,
) UserInvitationService {
	return &userInvitationService{
		invitationRepo: invitationRepo,
		membershipRepo: membershipRepo,
		userRepo:       userRepo,
		logger:         logger,
	}
}

// CreateInvitation creates a new user invitation
func (s *userInvitationService) CreateInvitation(ctx context.Context, inviterID uint, input CreateInvitationInput) (*models.UserInvitationResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"inviter_id": inviterID,
		"tenant_id":  input.TenantID,
		"email":      input.Email,
	}).Info("Creating user invitation")

	// Validate input
	if err := s.validateCreateInput(ctx, input); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Check if user with email already has pending invitation for this tenant
	hasPending, err := s.invitationRepo.EmailHasPendingInvitation(ctx, input.TenantID, input.Email)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing invitation: %w", err)
	}
	if hasPending {
		return nil, errors.New("user already has a pending invitation for this tenant")
	}

	// Check if user is already a member of this tenant
	existingUser, err := s.userRepo.GetByEmail(ctx, input.Email)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("failed to check existing user: %w", err)
	}

	if existingUser != nil {
		// Check if user is already a member
		membership, err := s.membershipRepo.GetByUserAndTenant(ctx, existingUser.ID, input.TenantID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("failed to check existing membership: %w", err)
		}
		if membership != nil && membership.Status != models.TenantMembershipStatusDeleted {
			return nil, errors.New("user is already a member of this tenant")
		}
	}

	// Generate unique token
	token, err := s.generateToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate token: %w", err)
	}

	// Set default expiration if not provided (7 days)
	expiresIn := input.ExpiresIn
	if expiresIn == 0 {
		expiresIn = 168 // 7 days in hours
	}

	// Create invitation
	invitation := &models.UserInvitation{
		TenantID:  input.TenantID,
		WebsiteID: input.WebsiteID,
		Token:     token,
		Email:     input.Email,
		RoleID:    input.RoleID,
		Message:   input.Message,
		Status:    models.UserInvitationStatusPending,
		InvitedBy: inviterID,
		ExpiresAt: time.Now().Add(time.Duration(expiresIn) * time.Hour),
	}

	if err := s.invitationRepo.Create(ctx, invitation); err != nil {
		return nil, fmt.Errorf("failed to create invitation: %w", err)
	}

	// Get invitation with relationships for response
	invitationWithRels, err := s.invitationRepo.GetByID(ctx, invitation.ID)
	if err != nil {
		return nil, fmt.Errorf("failed to get created invitation: %w", err)
	}

	response := &models.UserInvitationResponse{}
	response.FromUserInvitation(invitationWithRels)

	s.logger.WithField("invitation_id", invitation.ID).Info("User invitation created successfully")
	return response, nil
}

// GetInvitationByID retrieves an invitation by ID
func (s *userInvitationService) GetInvitationByID(ctx context.Context, id uint) (*models.UserInvitationResponse, error) {
	s.logger.WithField("invitation_id", id).Info("Getting invitation by ID")

	invitation, err := s.invitationRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invitation not found")
		}
		return nil, fmt.Errorf("failed to get invitation: %w", err)
	}

	response := &models.UserInvitationResponse{}
	response.FromUserInvitation(invitation)

	return response, nil
}

// GetInvitationByToken retrieves an invitation by token
func (s *userInvitationService) GetInvitationByToken(ctx context.Context, token string) (*models.UserInvitationResponse, error) {
	s.logger.WithField("token", token[:8]+"...").Info("Getting invitation by token")

	invitation, err := s.invitationRepo.GetByToken(ctx, token)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invitation not found")
		}
		return nil, fmt.Errorf("failed to get invitation: %w", err)
	}

	response := &models.UserInvitationResponse{}
	response.FromUserInvitation(invitation)

	return response, nil
}

// ListInvitations retrieves invitations with filtering and pagination
func (s *userInvitationService) ListInvitations(ctx context.Context, filter InvitationListFilter) (*InvitationListResponse, error) {
	s.logger.WithField("tenant_id", filter.TenantID).Info("Listing invitations")

	// Set default pagination if not provided
	if filter.Pagination == nil {
		filter.Pagination = &pagination.CursorPagination{
			Limit: 20,
		}
	}

	// Get invitations based on filter
	invitations, paginationResponse, err := s.getInvitationsByFilter(ctx, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to get invitations: %w", err)
	}

	// Count total invitations
	totalCount, err := s.invitationRepo.CountByTenant(ctx, filter.TenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to count invitations: %w", err)
	}

	// Convert to response format
	responses := make([]models.UserInvitationResponse, len(invitations))
	for i, invitation := range invitations {
		responses[i].FromUserInvitation(&invitation)
	}

	return &InvitationListResponse{
		Invitations: responses,
		Pagination:  paginationResponse,
		Total:       totalCount,
	}, nil
}

// UpdateInvitation updates an existing invitation
func (s *userInvitationService) UpdateInvitation(ctx context.Context, id uint, input UpdateInvitationInput) (*models.UserInvitationResponse, error) {
	s.logger.WithField("invitation_id", id).Info("Updating invitation")

	// Get existing invitation
	invitation, err := s.invitationRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invitation not found")
		}
		return nil, fmt.Errorf("failed to get invitation: %w", err)
	}

	// Check if invitation can be updated
	if invitation.Status != models.UserInvitationStatusPending {
		return nil, errors.New("only pending invitations can be updated")
	}

	// Update fields
	if input.RoleID != nil {
		invitation.RoleID = input.RoleID
	}
	if input.Message != nil {
		invitation.Message = input.Message
	}
	if input.ExpiresAt != nil {
		invitation.ExpiresAt = *input.ExpiresAt
	}

	if err := s.invitationRepo.Update(ctx, invitation); err != nil {
		return nil, fmt.Errorf("failed to update invitation: %w", err)
	}

	response := &models.UserInvitationResponse{}
	response.FromUserInvitation(invitation)

	s.logger.WithField("invitation_id", id).Info("Invitation updated successfully")
	return response, nil
}

// AcceptInvitation accepts an invitation
func (s *userInvitationService) AcceptInvitation(ctx context.Context, userID uint, input AcceptInvitationInput) (*AcceptInvitationResponse, error) {
	s.logger.WithFields(map[string]interface{}{
		"user_id": userID,
		"token":   input.Token[:8] + "...",
	}).Info("Accepting invitation")

	// Get invitation by token
	invitation, err := s.invitationRepo.GetByToken(ctx, input.Token)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invitation not found")
		}
		return nil, fmt.Errorf("failed to get invitation: %w", err)
	}

	// Validate invitation
	if !invitation.IsPending() {
		if invitation.IsExpired() {
			return nil, errors.New("invitation has expired")
		}
		return nil, errors.New("invitation is not pending")
	}

	// Get user
	user, err := s.userRepo.GetByID(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Check if invitation email matches user email
	if strings.ToLower(invitation.Email) != strings.ToLower(user.Email) {
		return nil, errors.New("invitation email does not match user email")
	}

	// Check if user is already a member
	existingMembership, err := s.membershipRepo.GetByUserAndTenant(ctx, userID, invitation.TenantID)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, fmt.Errorf("failed to check existing membership: %w", err)
	}

	if existingMembership != nil && existingMembership.Status != models.TenantMembershipStatusDeleted {
		return nil, errors.New("user is already a member of this tenant")
	}

	// Accept invitation
	invitation.Accept(userID)
	if err := s.invitationRepo.Update(ctx, invitation); err != nil {
		return nil, fmt.Errorf("failed to update invitation: %w", err)
	}

	// Create tenant membership
	now := time.Now()
	membership := &models.TenantMembership{
		UserID:               userID,
		TenantID:             invitation.TenantID,
		Status:               models.TenantMembershipStatusActive,
		InvitedBy:            &invitation.InvitedBy,
		InvitationToken:      &invitation.Token,
		InvitationAcceptedAt: &now,
		JoinedAt:             now,
	}

	if err := s.membershipRepo.Create(ctx, membership); err != nil {
		return nil, fmt.Errorf("failed to create membership: %w", err)
	}

	// Prepare response
	invitationResponse := &models.UserInvitationResponse{}
	invitationResponse.FromUserInvitation(invitation)

	response := &AcceptInvitationResponse{
		Invitation: invitationResponse,
		Membership: membership,
	}

	s.logger.WithFields(map[string]interface{}{
		"invitation_id": invitation.ID,
		"membership_id": membership.ID,
	}).Info("Invitation accepted successfully")

	return response, nil
}

// RejectInvitation rejects an invitation
func (s *userInvitationService) RejectInvitation(ctx context.Context, input RejectInvitationInput) (*models.UserInvitationResponse, error) {
	s.logger.WithField("token", input.Token[:8]+"...").Info("Rejecting invitation")

	// Get invitation by token
	invitation, err := s.invitationRepo.GetByToken(ctx, input.Token)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invitation not found")
		}
		return nil, fmt.Errorf("failed to get invitation: %w", err)
	}

	// Validate invitation
	if !invitation.IsPending() {
		if invitation.IsExpired() {
			return nil, errors.New("invitation has expired")
		}
		return nil, errors.New("invitation is not pending")
	}

	// Reject invitation
	invitation.Reject()
	if err := s.invitationRepo.Update(ctx, invitation); err != nil {
		return nil, fmt.Errorf("failed to update invitation: %w", err)
	}

	response := &models.UserInvitationResponse{}
	response.FromUserInvitation(invitation)

	s.logger.WithField("invitation_id", invitation.ID).Info("Invitation rejected successfully")
	return response, nil
}

// RevokeInvitation revokes an invitation
func (s *userInvitationService) RevokeInvitation(ctx context.Context, id uint) (*models.UserInvitationResponse, error) {
	s.logger.WithField("invitation_id", id).Info("Revoking invitation")

	// Get invitation
	invitation, err := s.invitationRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invitation not found")
		}
		return nil, fmt.Errorf("failed to get invitation: %w", err)
	}

	// Check if invitation can be revoked
	if invitation.Status != models.UserInvitationStatusPending {
		return nil, errors.New("only pending invitations can be revoked")
	}

	// Revoke invitation
	invitation.Revoke()
	if err := s.invitationRepo.Update(ctx, invitation); err != nil {
		return nil, fmt.Errorf("failed to update invitation: %w", err)
	}

	response := &models.UserInvitationResponse{}
	response.FromUserInvitation(invitation)

	s.logger.WithField("invitation_id", id).Info("Invitation revoked successfully")
	return response, nil
}

// ResendInvitation resends an invitation
func (s *userInvitationService) ResendInvitation(ctx context.Context, id uint) (*models.UserInvitationResponse, error) {
	s.logger.WithField("invitation_id", id).Info("Resending invitation")

	// Get invitation
	invitation, err := s.invitationRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("invitation not found")
		}
		return nil, fmt.Errorf("failed to get invitation: %w", err)
	}

	// Check if invitation can be resent
	if invitation.Status != models.UserInvitationStatusPending {
		return nil, errors.New("only pending invitations can be resent")
	}

	// Generate new token
	newToken, err := s.generateToken()
	if err != nil {
		return nil, fmt.Errorf("failed to generate new token: %w", err)
	}

	// Update invitation with new token and expiration
	invitation.Token = newToken
	invitation.ExpiresAt = time.Now().Add(168 * time.Hour) // 7 days

	if err := s.invitationRepo.Update(ctx, invitation); err != nil {
		return nil, fmt.Errorf("failed to update invitation: %w", err)
	}

	response := &models.UserInvitationResponse{}
	response.FromUserInvitation(invitation)

	s.logger.WithField("invitation_id", id).Info("Invitation resent successfully")
	return response, nil
}

// DeleteInvitation deletes an invitation
func (s *userInvitationService) DeleteInvitation(ctx context.Context, id uint) error {
	s.logger.WithField("invitation_id", id).Info("Deleting invitation")

	// Check if invitation exists
	_, err := s.invitationRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("invitation not found")
		}
		return fmt.Errorf("failed to get invitation: %w", err)
	}

	if err := s.invitationRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete invitation: %w", err)
	}

	s.logger.WithField("invitation_id", id).Info("Invitation deleted successfully")
	return nil
}

// CleanupExpiredInvitations cleans up expired invitations
func (s *userInvitationService) CleanupExpiredInvitations(ctx context.Context, tenantID uint) error {
	s.logger.WithField("tenant_id", tenantID).Info("Cleaning up expired invitations")

	if err := s.invitationRepo.DeleteExpiredInvitations(ctx, tenantID); err != nil {
		return fmt.Errorf("failed to cleanup expired invitations: %w", err)
	}

	s.logger.WithField("tenant_id", tenantID).Info("Expired invitations cleaned up successfully")
	return nil
}

// validateCreateInput validates the create invitation input
func (s *userInvitationService) validateCreateInput(ctx context.Context, input CreateInvitationInput) error {
	if input.TenantID == 0 {
		return errors.New("tenant ID is required")
	}

	if input.Email == "" {
		return errors.New("email is required")
	}

	if input.ExpiresIn < 0 || input.ExpiresIn > 8760 {
		return errors.New("expires_in must be between 0 and 8760 hours")
	}

	return nil
}

// generateToken generates a unique invitation token
func (s *userInvitationService) generateToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// getInvitationsByFilter gets invitations based on filter
func (s *userInvitationService) getInvitationsByFilter(ctx context.Context, filter InvitationListFilter) ([]models.UserInvitation, *pagination.CursorResponse, error) {
	// This is a simplified implementation - in production you'd want to build
	// proper filter conditions and use repository filter methods
	return s.invitationRepo.GetByTenantID(ctx, filter.TenantID, filter.Pagination)
}
