package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// userService implements UserService interface
type userService struct {
	userRepo        repositories.UserRepository
	profileRepo     repositories.UserProfileRepository
	preferencesRepo repositories.UserPreferencesRepository
	socialLinksRepo repositories.UserSocialLinksRepository
	logger          utils.Logger
}

// NewUserService creates a new user service
func NewUserService(
	userRepo repositories.UserRepository,
	profileRepo repositories.UserProfileRepository,
	preferencesRepo repositories.UserPreferencesRepository,
	socialLinksRepo repositories.UserSocialLinksRepository,
	logger utils.Logger,
) UserService {
	return &userService{
		userRepo:        userRepo,
		profileRepo:     profileRepo,
		preferencesRepo: preferencesRepo,
		socialLinksRepo: socialLinksRepo,
		logger:          logger,
	}
}

// Create creates a new user with tenant context
func (s *userService) Create(ctx context.Context, input CreateUserInput) (*models.User, error) {
	// Validate input
	if err := s.validateCreateInput(ctx, input); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Hash password
	hashedPassword, err := s.hashPassword(input.Password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create user model
	user := &models.User{
		Email:        input.Email,
		PasswordHash: hashedPassword,
		Status:       input.Status,
		Language:     input.Language,
		Timezone:     input.Timezone,
	}

	// Set optional fields
	if input.Username != "" {
		user.Username = &input.Username
	}
	if input.FirstName != "" {
		user.FirstName = &input.FirstName
	}
	if input.LastName != "" {
		user.LastName = &input.LastName
	}
	if input.DisplayName != "" {
		user.DisplayName = &input.DisplayName
	}
	if input.Phone != "" {
		user.Phone = &input.Phone
	}
	if input.AvatarURL != "" {
		user.AvatarURL = &input.AvatarURL
	}

	// Set default values
	if user.Status == "" {
		user.Status = models.UserStatusPendingVerification
	}
	if user.Language == "" {
		user.Language = "en"
	}
	if user.Timezone == "" {
		user.Timezone = "UTC"
	}

	// Create user in database
	if err := s.userRepo.Create(ctx, user); err != nil {
		s.logger.WithError(err).Error("Failed to create user")
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	// Create default profile
	if err := s.createDefaultProfile(ctx, user.ID); err != nil {
		s.logger.WithError(err).Error("Failed to create default profile")
		// Don't fail user creation if profile creation fails
	}

	// Create default preferences
	if err := s.createDefaultPreferences(ctx, user.ID); err != nil {
		s.logger.WithError(err).Error("Failed to create default preferences")
		// Don't fail user creation if preferences creation fails
	}

	// TODO: Send welcome email if requested
	if input.SendWelcomeEmail {
		// This would integrate with email service
		s.logger.Info("Welcome email requested for user", "user_id", user.ID)
	}

	return user, nil
}

// GetByID retrieves a user by ID
func (s *userService) GetByID(ctx context.Context, id uint) (*models.User, error) {
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return user, nil
}

// GetByEmail retrieves a user by email (global lookup)
func (s *userService) GetByEmail(ctx context.Context, email string, tenantID uint) (*models.User, error) {
	user, err := s.userRepo.GetByEmail(ctx, email)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return user, nil
}

// GetByUsername retrieves a user by username (global lookup)
func (s *userService) GetByUsername(ctx context.Context, username string, tenantID uint) (*models.User, error) {
	user, err := s.userRepo.GetByUsername(ctx, username)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return user, nil
}

// Update updates user information
func (s *userService) Update(ctx context.Context, id uint, input UpdateUserInput) (*models.User, error) {
	// Get existing user
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Validate input
	if err := s.validateUpdateInput(ctx, input, user); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Update fields
	if input.Email != nil {
		user.Email = *input.Email
		user.EmailVerified = false // Reset email verification
		user.EmailVerifiedAt = nil
	}
	if input.Username != nil {
		user.Username = input.Username
	}
	if input.FirstName != nil {
		user.FirstName = input.FirstName
	}
	if input.LastName != nil {
		user.LastName = input.LastName
	}
	if input.DisplayName != nil {
		user.DisplayName = input.DisplayName
	}
	if input.Phone != nil {
		user.Phone = input.Phone
		user.PhoneVerified = false // Reset phone verification
		user.PhoneVerifiedAt = nil
	}
	if input.AvatarURL != nil {
		user.AvatarURL = input.AvatarURL
	}
	if input.Language != nil {
		user.Language = *input.Language
	}
	if input.Timezone != nil {
		user.Timezone = *input.Timezone
	}
	if input.Status != nil {
		user.Status = *input.Status
	}

	// Update user in database
	if err := s.userRepo.Update(ctx, user); err != nil {
		s.logger.WithError(err).Error("Failed to update user")
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	return user, nil
}

// UpdateStatus updates user status
func (s *userService) UpdateStatus(ctx context.Context, id uint, status models.UserStatus) error {
	// Check if user exists
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Update status
	if err := s.userRepo.UpdateStatus(ctx, user.ID, status); err != nil {
		s.logger.WithError(err).Error("Failed to update user status")
		return fmt.Errorf("failed to update user status: %w", err)
	}

	return nil
}

// Delete soft deletes a user
func (s *userService) Delete(ctx context.Context, id uint) error {
	// Check if user exists
	user, err := s.userRepo.GetByID(ctx, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	// Soft delete user
	if err := s.userRepo.SoftDelete(ctx, user.ID); err != nil {
		s.logger.WithError(err).Error("Failed to delete user")
		return fmt.Errorf("failed to delete user: %w", err)
	}

	return nil
}

// List retrieves users with pagination and filtering
func (s *userService) List(ctx context.Context, filter ListUserFilter) (*UserListResponse, error) {
	// Set default pagination if not provided
	if filter.Pagination == nil {
		filter.Pagination = &pagination.CursorPagination{
			Limit: 20,
		}
	}

	// Get users from repository
	users, paginationResponse, err := s.userRepo.List(ctx, filter.Pagination)
	if err != nil {
		s.logger.WithError(err).Error("Failed to list users")
		return nil, fmt.Errorf("failed to list users: %w", err)
	}

	// Apply additional filters
	if filter.Status != nil {
		users = s.filterByStatus(users, *filter.Status)
	}
	if filter.Role != nil {
		users = s.filterByRole(users, *filter.Role)
	}
	if filter.EmailVerified != nil {
		users = s.filterByEmailVerified(users, *filter.EmailVerified)
	}
	if filter.PhoneVerified != nil {
		users = s.filterByPhoneVerified(users, *filter.PhoneVerified)
	}
	if filter.TwoFactorEnabled != nil {
		users = s.filterByTwoFactor(users, *filter.TwoFactorEnabled)
	}
	if filter.CreatedAfter != nil {
		users = s.filterByCreatedAfter(users, *filter.CreatedAfter)
	}
	if filter.CreatedBefore != nil {
		users = s.filterByCreatedBefore(users, *filter.CreatedBefore)
	}
	if filter.LastLoginAfter != nil {
		users = s.filterByLastLoginAfter(users, *filter.LastLoginAfter)
	}
	if filter.LastLoginBefore != nil {
		users = s.filterByLastLoginBefore(users, *filter.LastLoginBefore)
	}
	if filter.SearchQuery != "" {
		users = s.filterBySearchQuery(users, filter.SearchQuery)
	}

	// Get total count
	total, err := s.userRepo.Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count users")
		return nil, fmt.Errorf("failed to count users: %w", err)
	}

	return &UserListResponse{
		Users:      users,
		Pagination: paginationResponse,
		Total:      total,
	}, nil
}

// Search searches users by query
func (s *userService) Search(ctx context.Context, tenantID uint, query string, pag *pagination.CursorPagination) (*UserSearchResponse, error) {
	// Set default pagination if not provided
	if pag == nil {
		pag = &pagination.CursorPagination{
			Cursor: "",
			Limit:  20,
		}
	}

	// Search users from repository
	users, paginationResponse, err := s.userRepo.Search(ctx, query, pag)
	if err != nil {
		s.logger.WithError(err).Error("Failed to search users")
		return nil, fmt.Errorf("failed to search users: %w", err)
	}

	// Get total count
	total, err := s.userRepo.Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count users")
		return nil, fmt.Errorf("failed to count users: %w", err)
	}

	return &UserSearchResponse{
		Users:      users,
		Pagination: paginationResponse,
		Total:      total,
		Query:      query,
	}, nil
}

// UpdatePassword updates user password
func (s *userService) UpdatePassword(ctx context.Context, userID uint, newPassword string) error {
	// Validate password
	if err := s.validatePassword(newPassword); err != nil {
		return fmt.Errorf("invalid password: %w", err)
	}

	// Hash password
	hashedPassword, err := s.hashPassword(newPassword)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// Update password
	if err := s.userRepo.UpdatePassword(ctx, userID, hashedPassword); err != nil {
		s.logger.WithError(err).Error("Failed to update password")
		return fmt.Errorf("failed to update password: %w", err)
	}

	return nil
}

// VerifyEmail verifies user email
func (s *userService) VerifyEmail(ctx context.Context, userID uint) error {
	if err := s.userRepo.UpdateEmailVerification(ctx, userID, true); err != nil {
		s.logger.WithError(err).Error("Failed to verify email")
		return fmt.Errorf("failed to verify email: %w", err)
	}
	return nil
}

// VerifyPhone verifies user phone
func (s *userService) VerifyPhone(ctx context.Context, userID uint) error {
	if err := s.userRepo.UpdatePhoneVerification(ctx, userID, true); err != nil {
		s.logger.WithError(err).Error("Failed to verify phone")
		return fmt.Errorf("failed to verify phone: %w", err)
	}
	return nil
}

// EnableTwoFactor enables two-factor authentication
func (s *userService) EnableTwoFactor(ctx context.Context, userID uint) (*TwoFactorSetup, error) {
	// Generate secret (this would use a proper 2FA library)
	secret := s.generateTwoFactorSecret()

	// Generate backup codes
	backupCodes := s.generateBackupCodes()

	// Enable two-factor in database
	if err := s.userRepo.EnableTwoFactor(ctx, userID, secret); err != nil {
		s.logger.WithError(err).Error("Failed to enable two-factor auth")
		return nil, fmt.Errorf("failed to enable two-factor auth: %w", err)
	}

	// Update recovery codes
	if err := s.userRepo.UpdateRecoveryCodes(ctx, userID, backupCodes); err != nil {
		s.logger.WithError(err).Error("Failed to update recovery codes")
		return nil, fmt.Errorf("failed to update recovery codes: %w", err)
	}

	return &TwoFactorSetup{
		Secret:      secret,
		QRCodeURL:   s.generateQRCodeURL(secret),
		BackupCodes: backupCodes,
	}, nil
}

// DisableTwoFactor disables two-factor authentication
func (s *userService) DisableTwoFactor(ctx context.Context, userID uint) error {
	if err := s.userRepo.DisableTwoFactor(ctx, userID); err != nil {
		s.logger.WithError(err).Error("Failed to disable two-factor auth")
		return fmt.Errorf("failed to disable two-factor auth: %w", err)
	}
	return nil
}

// GetUserStats retrieves user statistics (global stats)
func (s *userService) GetUserStats(ctx context.Context, tenantID uint) (*UserStats, error) {
	// Get basic counts
	totalUsers, err := s.userRepo.Count(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to count total users: %w", err)
	}

	activeUsers, err := s.userRepo.CountByStatus(ctx, models.UserStatusActive)
	if err != nil {
		return nil, fmt.Errorf("failed to count active users: %w", err)
	}

	inactiveUsers, err := s.userRepo.CountByStatus(ctx, models.UserStatusInactive)
	if err != nil {
		return nil, fmt.Errorf("failed to count inactive users: %w", err)
	}

	suspendedUsers, err := s.userRepo.CountByStatus(ctx, models.UserStatusSuspended)
	if err != nil {
		return nil, fmt.Errorf("failed to count suspended users: %w", err)
	}

	deletedUsers, err := s.userRepo.CountByStatus(ctx, models.UserStatusDeleted)
	if err != nil {
		return nil, fmt.Errorf("failed to count deleted users: %w", err)
	}

	// Role distribution is deprecated in multi-tenant architecture
	// Roles are now handled through tenant memberships
	roleDistribution := make(map[models.UserRole]int64)
	roleDistribution[models.UserRoleUser] = 0
	roleDistribution[models.UserRoleAdmin] = 0
	roleDistribution[models.UserRoleGuest] = 0

	// Get status distribution
	statusDistribution := map[models.UserStatus]int64{
		models.UserStatusActive:              activeUsers,
		models.UserStatusInactive:            inactiveUsers,
		models.UserStatusSuspended:           suspendedUsers,
		models.UserStatusDeleted:             deletedUsers,
		models.UserStatusPendingVerification: totalUsers - activeUsers - inactiveUsers - suspendedUsers - deletedUsers,
	}

	// Get recently active users
	recentlyActiveUsers, err := s.userRepo.GetRecentlyActive(ctx, 100)
	if err != nil {
		return nil, fmt.Errorf("failed to get recently active users: %w", err)
	}

	return &UserStats{
		TotalUsers:          totalUsers,
		ActiveUsers:         activeUsers,
		InactiveUsers:       inactiveUsers,
		SuspendedUsers:      suspendedUsers,
		DeletedUsers:        deletedUsers,
		VerifiedUsers:       0, // TODO: Implement
		UnverifiedUsers:     0, // TODO: Implement
		TwoFactorUsers:      0, // TODO: Implement
		RoleDistribution:    roleDistribution,
		StatusDistribution:  statusDistribution,
		RegistrationTrend:   []RegistrationPoint{}, // TODO: Implement
		ActivityTrend:       []ActivityPoint{},     // TODO: Implement
		AverageLoginCount:   0,                     // TODO: Implement
		RecentlyActiveCount: int64(len(recentlyActiveUsers)),
	}, nil
}

// BulkUpdateStatus updates status for multiple users
func (s *userService) BulkUpdateStatus(ctx context.Context, userIDs []uint, status models.UserStatus) error {
	if err := s.userRepo.BulkUpdateStatus(ctx, userIDs, status); err != nil {
		s.logger.WithError(err).Error("Failed to bulk update user status")
		return fmt.Errorf("failed to bulk update user status: %w", err)
	}
	return nil
}

// TransferToTenant transfers user to another tenant
func (s *userService) TransferToTenant(ctx context.Context, userID uint, newTenantID uint) error {
	// Transfer user is deprecated in multi-tenant architecture
	return fmt.Errorf("user transfer is deprecated - use tenant membership management instead")
}

// GetRecentlyActive retrieves recently active users
func (s *userService) GetRecentlyActive(ctx context.Context, tenantID uint, limit int) ([]models.User, error) {
	users, err := s.userRepo.GetRecentlyActive(ctx, limit)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get recently active users")
		return nil, fmt.Errorf("failed to get recently active users: %w", err)
	}
	return users, nil
}

// Helper methods

// validateCreateInput validates create user input
func (s *userService) validateCreateInput(ctx context.Context, input CreateUserInput) error {
	// Check if email already exists
	exists, err := s.userRepo.EmailExists(ctx, input.Email)
	if err != nil {
		return fmt.Errorf("failed to check email existence: %w", err)
	}
	if exists {
		return fmt.Errorf("email already exists")
	}

	// Check if username already exists
	exists, err = s.userRepo.UsernameExists(ctx, input.Username)
	if err != nil {
		return fmt.Errorf("failed to check username existence: %w", err)
	}
	if exists {
		return fmt.Errorf("username already exists")
	}

	// Validate password
	if err := s.validatePassword(input.Password); err != nil {
		return fmt.Errorf("invalid password: %w", err)
	}

	return nil
}

// validateUpdateInput validates update user input
func (s *userService) validateUpdateInput(ctx context.Context, input UpdateUserInput, user *models.User) error {
	// Check if email already exists (excluding current user)
	if input.Email != nil && *input.Email != user.Email {
		exists, err := s.userRepo.EmailExistsExcludingUser(ctx, *input.Email, user.ID)
		if err != nil {
			return fmt.Errorf("failed to check email existence: %w", err)
		}
		if exists {
			return fmt.Errorf("email already exists")
		}
	}

	// Check if username already exists (excluding current user)
	if input.Username != nil {
		currentUsername := ""
		if user.Username != nil {
			currentUsername = *user.Username
		}
		if *input.Username != currentUsername {
			exists, err := s.userRepo.UsernameExistsExcludingUser(ctx, *input.Username, user.ID)
			if err != nil {
				return fmt.Errorf("failed to check username existence: %w", err)
			}
			if exists {
				return fmt.Errorf("username already exists")
			}
		}
	}

	return nil
}

// validatePassword validates password strength
func (s *userService) validatePassword(password string) error {
	if len(password) < 8 {
		return fmt.Errorf("password must be at least 8 characters long")
	}
	// Add more password validation rules as needed
	return nil
}

// hashPassword hashes a password using bcrypt
func (s *userService) hashPassword(password string) (string, error) {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}
	return string(hashedBytes), nil
}

// createDefaultProfile creates a default profile for a user
func (s *userService) createDefaultProfile(ctx context.Context, userID uint) error {
	profile := &models.UserProfile{
		UserID:         userID,
		DisplayProfile: true,
		AllowContact:   true,
		ShowEmail:      false,
		ShowPhone:      false,
	}

	return s.profileRepo.Create(ctx, profile)
}

// createDefaultPreferences creates default preferences for a user
func (s *userService) createDefaultPreferences(ctx context.Context, userID uint) error {
	preferences := &models.UserPreferences{
		UserID:                 userID,
		EmailNotifications:     true,
		PushNotifications:      true,
		SMSNotifications:       false,
		MarketingEmails:        false,
		NewsletterSubscription: false,
		ProductUpdates:         true,
		SecurityAlerts:         true,
		ProfileVisibility:      models.ProfileVisibilityPublic,
		ShowOnlineStatus:       true,
		AllowSearch:            true,
		DataProcessingConsent:  false,
		Theme:                  models.ThemeLight,
		DashboardLayout:        "default",
		ItemsPerPage:           20,
		AutoSave:               true,
		KeyboardShortcuts:      true,
		TooltipsEnabled:        true,
	}

	return s.preferencesRepo.Create(ctx, preferences)
}

// generateTwoFactorSecret generates a 2FA secret
func (s *userService) generateTwoFactorSecret() string {
	// This would use a proper 2FA library like github.com/pquerna/otp
	return "JBSWY3DPEHPK3PXP" // placeholder
}

// generateBackupCodes generates backup codes
func (s *userService) generateBackupCodes() []string {
	// This would generate proper backup codes
	return []string{"123456", "789012", "345678", "901234", "567890"} // placeholder
}

// generateQRCodeURL generates QR code URL
func (s *userService) generateQRCodeURL(secret string) string {
	// This would generate a proper QR code URL
	return "https://chart.googleapis.com/chart?chs=200x200&chld=M|0&cht=qr&chl=otpauth://totp/Example:<EMAIL>?secret=" + secret + "&issuer=Example"
}

// Filter helper methods

func (s *userService) filterByStatus(users []models.User, status models.UserStatus) []models.User {
	var filtered []models.User
	for _, user := range users {
		if user.Status == status {
			filtered = append(filtered, user)
		}
	}
	return filtered
}

func (s *userService) filterByRole(users []models.User, role models.UserRole) []models.User {
	var filtered []models.User
	for _, user := range users {
		// Role filtering removed - roles are now handled through tenant memberships
		if true { // Temporary fix
			filtered = append(filtered, user)
		}
	}
	return filtered
}

func (s *userService) filterByEmailVerified(users []models.User, verified bool) []models.User {
	var filtered []models.User
	for _, user := range users {
		if user.EmailVerified == verified {
			filtered = append(filtered, user)
		}
	}
	return filtered
}

func (s *userService) filterByPhoneVerified(users []models.User, verified bool) []models.User {
	var filtered []models.User
	for _, user := range users {
		if user.PhoneVerified == verified {
			filtered = append(filtered, user)
		}
	}
	return filtered
}

func (s *userService) filterByTwoFactor(users []models.User, enabled bool) []models.User {
	var filtered []models.User
	for _, user := range users {
		if user.TwoFactorEnabled == enabled {
			filtered = append(filtered, user)
		}
	}
	return filtered
}

func (s *userService) filterByCreatedAfter(users []models.User, after time.Time) []models.User {
	var filtered []models.User
	for _, user := range users {
		if user.CreatedAt.After(after) {
			filtered = append(filtered, user)
		}
	}
	return filtered
}

func (s *userService) filterByCreatedBefore(users []models.User, before time.Time) []models.User {
	var filtered []models.User
	for _, user := range users {
		if user.CreatedAt.Before(before) {
			filtered = append(filtered, user)
		}
	}
	return filtered
}

func (s *userService) filterByLastLoginAfter(users []models.User, after time.Time) []models.User {
	var filtered []models.User
	for _, user := range users {
		if user.LastLoginAt != nil && user.LastLoginAt.After(after) {
			filtered = append(filtered, user)
		}
	}
	return filtered
}

func (s *userService) filterByLastLoginBefore(users []models.User, before time.Time) []models.User {
	var filtered []models.User
	for _, user := range users {
		if user.LastLoginAt != nil && user.LastLoginAt.Before(before) {
			filtered = append(filtered, user)
		}
	}
	return filtered
}

func (s *userService) filterBySearchQuery(users []models.User, query string) []models.User {
	var filtered []models.User
	for _, user := range users {
		if s.matchesSearchQuery(user, query) {
			filtered = append(filtered, user)
		}
	}
	return filtered
}

func (s *userService) matchesSearchQuery(user models.User, query string) bool {
	// Simple implementation - in production, use proper search
	if user.Email == query {
		return true
	}
	if user.Username != nil && *user.Username == query {
		return true
	}
	if user.FirstName != nil && *user.FirstName == query {
		return true
	}
	if user.LastName != nil && *user.LastName == query {
		return true
	}
	return false
}
