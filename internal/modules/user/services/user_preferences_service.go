package services

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// userPreferencesService implements UserPreferencesService interface
type userPreferencesService struct {
	preferencesRepo repositories.UserPreferencesRepository
	userRepo        repositories.UserRepository
	logger          utils.Logger
}

// NewUserPreferencesService creates a new user preferences service
func NewUserPreferencesService(
	preferencesRepo repositories.UserPreferencesRepository,
	userRepo repositories.UserRepository,
	logger utils.Logger,
) UserPreferencesService {
	return &userPreferencesService{
		preferencesRepo: preferencesRepo,
		userRepo:        userRepo,
		logger:          logger,
	}
}

// <PERSON>reate creates user preferences with defaults
func (s *userPreferencesService) Create(ctx context.Context, input CreateUserPreferencesInput) (*models.UserPreferences, error) {
	// Validate input
	if err := s.validateCreateInput(ctx, input); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Check if user exists
	_, err := s.userRepo.GetByID(ctx, input.UserID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// Check if preferences already exist
	exists, err := s.preferencesRepo.PreferencesExists(ctx, input.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to check preferences existence: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("preferences already exist for user")
	}

	// Create preferences model
	preferences := &models.UserPreferences{
		UserID:                 input.UserID,
		EmailNotifications:     input.EmailNotifications,
		PushNotifications:      input.PushNotifications,
		SMSNotifications:       input.SMSNotifications,
		MarketingEmails:        input.MarketingEmails,
		NewsletterSubscription: input.NewsletterSubscription,
		ProductUpdates:         input.ProductUpdates,
		SecurityAlerts:         input.SecurityAlerts,
		ProfileVisibility:      input.ProfileVisibility,
		ShowOnlineStatus:       input.ShowOnlineStatus,
		AllowSearch:            input.AllowSearch,
		DataProcessingConsent:  input.DataProcessingConsent,
		Theme:                  input.Theme,
		DashboardLayout:        input.DashboardLayout,
		ItemsPerPage:           input.ItemsPerPage,
		AutoSave:               input.AutoSave,
		KeyboardShortcuts:      input.KeyboardShortcuts,
		TooltipsEnabled:        input.TooltipsEnabled,
	}

	// Set default values if not provided
	if preferences.ProfileVisibility == "" {
		preferences.ProfileVisibility = models.ProfileVisibilityPublic
	}
	if preferences.Theme == "" {
		preferences.Theme = models.ThemeLight
	}
	if preferences.DashboardLayout == "" {
		preferences.DashboardLayout = "default"
	}
	if preferences.ItemsPerPage == 0 {
		preferences.ItemsPerPage = 20
	}

	// Set notification types
	if len(input.NotificationTypes) > 0 {
		if err := preferences.SetNotificationTypes(input.NotificationTypes); err != nil {
			return nil, fmt.Errorf("failed to set notification types: %w", err)
		}
	}

	// Set feature preferences
	if len(input.FeaturePreferences) > 0 {
		if err := preferences.SetFeaturePreferences(input.FeaturePreferences); err != nil {
			return nil, fmt.Errorf("failed to set feature preferences: %w", err)
		}
	}

	// Set custom preferences
	if len(input.CustomPreferences) > 0 {
		if err := preferences.SetCustomPreferences(input.CustomPreferences); err != nil {
			return nil, fmt.Errorf("failed to set custom preferences: %w", err)
		}
	}

	// Create preferences in database
	if err := s.preferencesRepo.Create(ctx, preferences); err != nil {
		s.logger.WithError(err).Error("Failed to create user preferences")
		return nil, fmt.Errorf("failed to create user preferences: %w", err)
	}

	return preferences, nil
}

// GetByUserID retrieves user preferences by user ID
func (s *userPreferencesService) GetByUserID(ctx context.Context, userID uint) (*models.UserPreferences, error) {
	preferences, err := s.preferencesRepo.GetByUserID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("preferences not found")
		}
		return nil, fmt.Errorf("failed to get preferences: %w", err)
	}
	return preferences, nil
}

// Update updates user preferences
func (s *userPreferencesService) Update(ctx context.Context, userID uint, input UpdateUserPreferencesInput) (*models.UserPreferences, error) {
	// Validate input
	if err := s.validateUpdateInput(ctx, input); err != nil {
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Get existing preferences
	preferences, err := s.preferencesRepo.GetByUserID(ctx, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("preferences not found")
		}
		return nil, fmt.Errorf("failed to get preferences: %w", err)
	}

	// Update fields
	if input.EmailNotifications != nil {
		preferences.EmailNotifications = *input.EmailNotifications
	}
	if input.PushNotifications != nil {
		preferences.PushNotifications = *input.PushNotifications
	}
	if input.SMSNotifications != nil {
		preferences.SMSNotifications = *input.SMSNotifications
	}
	if input.MarketingEmails != nil {
		preferences.MarketingEmails = *input.MarketingEmails
	}
	if input.NewsletterSubscription != nil {
		preferences.NewsletterSubscription = *input.NewsletterSubscription
	}
	if input.ProductUpdates != nil {
		preferences.ProductUpdates = *input.ProductUpdates
	}
	if input.SecurityAlerts != nil {
		preferences.SecurityAlerts = *input.SecurityAlerts
	}
	if input.ProfileVisibility != nil {
		preferences.ProfileVisibility = *input.ProfileVisibility
	}
	if input.ShowOnlineStatus != nil {
		preferences.ShowOnlineStatus = *input.ShowOnlineStatus
	}
	if input.AllowSearch != nil {
		preferences.AllowSearch = *input.AllowSearch
	}
	if input.DataProcessingConsent != nil {
		preferences.DataProcessingConsent = *input.DataProcessingConsent
	}
	if input.Theme != nil {
		preferences.Theme = *input.Theme
	}
	if input.DashboardLayout != nil {
		preferences.DashboardLayout = *input.DashboardLayout
	}
	if input.ItemsPerPage != nil {
		preferences.ItemsPerPage = *input.ItemsPerPage
	}
	if input.AutoSave != nil {
		preferences.AutoSave = *input.AutoSave
	}
	if input.KeyboardShortcuts != nil {
		preferences.KeyboardShortcuts = *input.KeyboardShortcuts
	}
	if input.TooltipsEnabled != nil {
		preferences.TooltipsEnabled = *input.TooltipsEnabled
	}

	// Update notification types
	if len(input.NotificationTypes) > 0 {
		if err := preferences.SetNotificationTypes(input.NotificationTypes); err != nil {
			return nil, fmt.Errorf("failed to set notification types: %w", err)
		}
	}

	// Update feature preferences
	if len(input.FeaturePreferences) > 0 {
		if err := preferences.SetFeaturePreferences(input.FeaturePreferences); err != nil {
			return nil, fmt.Errorf("failed to set feature preferences: %w", err)
		}
	}

	// Update custom preferences
	if len(input.CustomPreferences) > 0 {
		if err := preferences.SetCustomPreferences(input.CustomPreferences); err != nil {
			return nil, fmt.Errorf("failed to set custom preferences: %w", err)
		}
	}

	// Update preferences in database
	if err := s.preferencesRepo.Update(ctx, preferences); err != nil {
		s.logger.WithError(err).Error("Failed to update user preferences")
		return nil, fmt.Errorf("failed to update user preferences: %w", err)
	}

	return preferences, nil
}

// UpdateNotificationPreferences updates notification preferences
func (s *userPreferencesService) UpdateNotificationPreferences(ctx context.Context, userID uint, preferences NotificationPreferencesInput) error {
	// Validate input
	if err := s.validateNotificationPreferences(preferences); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	// Update notification preferences
	notificationPrefs := map[string]interface{}{
		"email_notifications":     preferences.EmailNotifications,
		"push_notifications":      preferences.PushNotifications,
		"sms_notifications":       preferences.SMSNotifications,
		"marketing_emails":        preferences.MarketingEmails,
		"newsletter_subscription": preferences.NewsletterSubscription,
		"product_updates":         preferences.ProductUpdates,
		"security_alerts":         preferences.SecurityAlerts,
	}

	if err := s.preferencesRepo.UpdateNotificationPreferences(ctx, userID, notificationPrefs); err != nil {
		s.logger.WithError(err).Error("Failed to update notification preferences")
		return fmt.Errorf("failed to update notification preferences: %w", err)
	}

	// Update notification types
	if len(preferences.NotificationTypes) > 0 {
		notificationTypesMap := make(map[string]interface{})
		for k, v := range preferences.NotificationTypes {
			notificationTypesMap[k] = v
		}
		if err := s.preferencesRepo.UpdateNotificationTypes(ctx, userID, notificationTypesMap); err != nil {
			s.logger.WithError(err).Error("Failed to update notification types")
			return fmt.Errorf("failed to update notification types: %w", err)
		}
	}

	return nil
}

// UpdatePrivacyPreferences updates privacy preferences
func (s *userPreferencesService) UpdatePrivacyPreferences(ctx context.Context, userID uint, preferences PrivacyPreferencesInput) error {
	// Validate input
	if err := s.validatePrivacyPreferences(preferences); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	// Update privacy preferences
	if err := s.preferencesRepo.UpdatePrivacyPreferences(ctx, userID, preferences.ProfileVisibility, preferences.ShowOnlineStatus, preferences.AllowSearch); err != nil {
		s.logger.WithError(err).Error("Failed to update privacy preferences")
		return fmt.Errorf("failed to update privacy preferences: %w", err)
	}

	return nil
}

// UpdateUIPreferences updates UI preferences
func (s *userPreferencesService) UpdateUIPreferences(ctx context.Context, userID uint, preferences UIPreferencesInput) error {
	// Validate input
	if err := s.validateUIPreferences(preferences); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	// Update UI preferences
	if err := s.preferencesRepo.UpdateUIPreferences(ctx, userID, preferences.Theme, preferences.DashboardLayout, preferences.ItemsPerPage); err != nil {
		s.logger.WithError(err).Error("Failed to update UI preferences")
		return fmt.Errorf("failed to update UI preferences: %w", err)
	}

	// Update application preferences
	if err := s.preferencesRepo.UpdateApplicationPreferences(ctx, userID, preferences.AutoSave, preferences.KeyboardShortcuts, preferences.TooltipsEnabled); err != nil {
		s.logger.WithError(err).Error("Failed to update application preferences")
		return fmt.Errorf("failed to update application preferences: %w", err)
	}

	return nil
}

// GetDefaultPreferences retrieves default preferences for tenant
func (s *userPreferencesService) GetDefaultPreferences(ctx context.Context, tenantID uint) (*models.UserPreferences, error) {
	preferences, err := s.preferencesRepo.GetDefaultPreferences(ctx, tenantID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get default preferences")
		return nil, fmt.Errorf("failed to get default preferences: %w", err)
	}
	return preferences, nil
}

// ValidatePreferences validates preferences data
func (s *userPreferencesService) ValidatePreferences(ctx context.Context, input UpdateUserPreferencesInput) error {
	return s.validateUpdateInput(ctx, input)
}

// ExportPreferences exports user preferences
func (s *userPreferencesService) ExportPreferences(ctx context.Context, userID uint) (map[string]interface{}, error) {
	preferences, err := s.preferencesRepo.ExportPreferences(ctx, userID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to export preferences")
		return nil, fmt.Errorf("failed to export preferences: %w", err)
	}
	return preferences, nil
}

// ImportPreferences imports user preferences
func (s *userPreferencesService) ImportPreferences(ctx context.Context, userID uint, preferences map[string]interface{}) error {
	// Validate preferences
	if err := s.validateImportPreferences(preferences); err != nil {
		return fmt.Errorf("validation failed: %w", err)
	}

	// Import preferences
	if err := s.preferencesRepo.ImportPreferences(ctx, userID, preferences); err != nil {
		s.logger.WithError(err).Error("Failed to import preferences")
		return fmt.Errorf("failed to import preferences: %w", err)
	}

	return nil
}

// GetPreferencesStats retrieves preferences statistics
func (s *userPreferencesService) GetPreferencesStats(ctx context.Context, tenantID uint) (*PreferencesStats, error) {
	stats, err := s.preferencesRepo.GetPreferencesStats(ctx, tenantID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get preferences stats")
		return nil, fmt.Errorf("failed to get preferences stats: %w", err)
	}

	return &PreferencesStats{
		TotalPreferences:     stats.TotalPreferences,
		ThemeDistribution:    stats.ThemeDistribution,
		LanguageDistribution: stats.LanguageDistribution,
		TimezoneDistribution: stats.TimezoneDistribution,
		NotificationStats:    &NotificationStats{},
		PrivacyStats:         &PrivacyStats{},
	}, nil
}

// Helper methods

// validateCreateInput validates create preferences input
func (s *userPreferencesService) validateCreateInput(ctx context.Context, input CreateUserPreferencesInput) error {
	// Validate profile visibility
	if input.ProfileVisibility != "" {
		validVisibilities := map[models.ProfileVisibility]bool{
			models.ProfileVisibilityPublic:      true,
			models.ProfileVisibilityPrivate:     true,
			models.ProfileVisibilityFriendsOnly: true,
		}
		if !validVisibilities[input.ProfileVisibility] {
			return fmt.Errorf("invalid profile visibility")
		}
	}

	// Validate theme
	if input.Theme != "" {
		validThemes := map[models.Theme]bool{
			models.ThemeLight:  true,
			models.ThemeDark:   true,
			models.ThemeSystem: true,
		}
		if !validThemes[input.Theme] {
			return fmt.Errorf("invalid theme")
		}
	}

	// Validate items per page
	if input.ItemsPerPage != 0 && (input.ItemsPerPage < 10 || input.ItemsPerPage > 100) {
		return fmt.Errorf("items per page must be between 10 and 100")
	}

	// Validate dashboard layout
	if input.DashboardLayout != "" {
		validLayouts := map[string]bool{
			"default":  true,
			"compact":  true,
			"expanded": true,
		}
		if !validLayouts[input.DashboardLayout] {
			return fmt.Errorf("invalid dashboard layout")
		}
	}

	// Validate notification types
	if len(input.NotificationTypes) > 0 {
		if err := s.validateNotificationTypes(input.NotificationTypes); err != nil {
			return fmt.Errorf("invalid notification types: %w", err)
		}
	}

	// Validate feature preferences
	if len(input.FeaturePreferences) > 0 {
		if err := s.validateFeaturePreferences(input.FeaturePreferences); err != nil {
			return fmt.Errorf("invalid feature preferences: %w", err)
		}
	}

	// Validate custom preferences
	if len(input.CustomPreferences) > 0 {
		if err := s.validateCustomPreferences(input.CustomPreferences); err != nil {
			return fmt.Errorf("invalid custom preferences: %w", err)
		}
	}

	return nil
}

// validateUpdateInput validates update preferences input
func (s *userPreferencesService) validateUpdateInput(ctx context.Context, input UpdateUserPreferencesInput) error {
	// Validate profile visibility
	if input.ProfileVisibility != nil {
		validVisibilities := map[models.ProfileVisibility]bool{
			models.ProfileVisibilityPublic:      true,
			models.ProfileVisibilityPrivate:     true,
			models.ProfileVisibilityFriendsOnly: true,
		}
		if !validVisibilities[*input.ProfileVisibility] {
			return fmt.Errorf("invalid profile visibility")
		}
	}

	// Validate theme
	if input.Theme != nil {
		validThemes := map[models.Theme]bool{
			models.ThemeLight:  true,
			models.ThemeDark:   true,
			models.ThemeSystem: true,
		}
		if !validThemes[*input.Theme] {
			return fmt.Errorf("invalid theme")
		}
	}

	// Validate items per page
	if input.ItemsPerPage != nil && (*input.ItemsPerPage < 10 || *input.ItemsPerPage > 100) {
		return fmt.Errorf("items per page must be between 10 and 100")
	}

	// Validate dashboard layout
	if input.DashboardLayout != nil {
		validLayouts := map[string]bool{
			"default":  true,
			"compact":  true,
			"expanded": true,
		}
		if !validLayouts[*input.DashboardLayout] {
			return fmt.Errorf("invalid dashboard layout")
		}
	}

	// Validate notification types
	if len(input.NotificationTypes) > 0 {
		if err := s.validateNotificationTypes(input.NotificationTypes); err != nil {
			return fmt.Errorf("invalid notification types: %w", err)
		}
	}

	// Validate feature preferences
	if len(input.FeaturePreferences) > 0 {
		if err := s.validateFeaturePreferences(input.FeaturePreferences); err != nil {
			return fmt.Errorf("invalid feature preferences: %w", err)
		}
	}

	// Validate custom preferences
	if len(input.CustomPreferences) > 0 {
		if err := s.validateCustomPreferences(input.CustomPreferences); err != nil {
			return fmt.Errorf("invalid custom preferences: %w", err)
		}
	}

	return nil
}

// validateNotificationPreferences validates notification preferences
func (s *userPreferencesService) validateNotificationPreferences(preferences NotificationPreferencesInput) error {
	// Validate notification types
	if len(preferences.NotificationTypes) > 0 {
		if err := s.validateNotificationTypes(preferences.NotificationTypes); err != nil {
			return fmt.Errorf("invalid notification types: %w", err)
		}
	}
	return nil
}

// validatePrivacyPreferences validates privacy preferences
func (s *userPreferencesService) validatePrivacyPreferences(preferences PrivacyPreferencesInput) error {
	// Validate profile visibility
	validVisibilities := map[models.ProfileVisibility]bool{
		models.ProfileVisibilityPublic:      true,
		models.ProfileVisibilityPrivate:     true,
		models.ProfileVisibilityFriendsOnly: true,
	}
	if !validVisibilities[preferences.ProfileVisibility] {
		return fmt.Errorf("invalid profile visibility")
	}

	return nil
}

// validateUIPreferences validates UI preferences
func (s *userPreferencesService) validateUIPreferences(preferences UIPreferencesInput) error {
	// Validate theme
	validThemes := map[models.Theme]bool{
		models.ThemeLight:  true,
		models.ThemeDark:   true,
		models.ThemeSystem: true,
	}
	if !validThemes[preferences.Theme] {
		return fmt.Errorf("invalid theme")
	}

	// Validate items per page
	if preferences.ItemsPerPage < 10 || preferences.ItemsPerPage > 100 {
		return fmt.Errorf("items per page must be between 10 and 100")
	}

	// Validate dashboard layout
	validLayouts := map[string]bool{
		"default":  true,
		"compact":  true,
		"expanded": true,
	}
	if !validLayouts[preferences.DashboardLayout] {
		return fmt.Errorf("invalid dashboard layout")
	}

	return nil
}

// validateNotificationTypes validates notification types
func (s *userPreferencesService) validateNotificationTypes(notificationTypes map[string]bool) error {
	if len(notificationTypes) > 50 {
		return fmt.Errorf("too many notification types (max 50)")
	}

	for key := range notificationTypes {
		if len(key) > 50 {
			return fmt.Errorf("notification type key '%s' is too long (max 50 characters)", key)
		}
	}

	return nil
}

// validateFeaturePreferences validates feature preferences
func (s *userPreferencesService) validateFeaturePreferences(featurePreferences map[string]interface{}) error {
	if len(featurePreferences) > 50 {
		return fmt.Errorf("too many feature preferences (max 50)")
	}

	for key := range featurePreferences {
		if len(key) > 50 {
			return fmt.Errorf("feature preference key '%s' is too long (max 50 characters)", key)
		}
	}

	return nil
}

// validateCustomPreferences validates custom preferences
func (s *userPreferencesService) validateCustomPreferences(customPreferences map[string]interface{}) error {
	if len(customPreferences) > 50 {
		return fmt.Errorf("too many custom preferences (max 50)")
	}

	for key := range customPreferences {
		if len(key) > 50 {
			return fmt.Errorf("custom preference key '%s' is too long (max 50 characters)", key)
		}
	}

	return nil
}

// validateImportPreferences validates import preferences
func (s *userPreferencesService) validateImportPreferences(preferences map[string]interface{}) error {
	if len(preferences) > 100 {
		return fmt.Errorf("too many preferences (max 100)")
	}

	for key := range preferences {
		if len(key) > 50 {
			return fmt.Errorf("preference key '%s' is too long (max 50 characters)", key)
		}
	}

	return nil
}
