package services

import (
	"context"
	"fmt"
	"strings"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// userSearchService implements UserSearchService interface
type userSearchService struct {
	userRepo    repositories.UserRepository
	profileRepo repositories.UserProfileRepository
	logger      utils.Logger
}

// NewUserSearchService creates a new user search service
func NewUserSearchService(
	userRepo repositories.UserRepository,
	profileRepo repositories.UserProfileRepository,
	logger utils.Logger,
) UserSearchService {
	return &userSearchService{
		userRepo:    userRepo,
		profileRepo: profileRepo,
		logger:      logger,
	}
}

// Search searches users by query with advanced filtering
func (s *userSearchService) Search(ctx context.Context, input UserSearchInput) (*UserSearchResponse, error) {
	// Set default pagination if not provided
	if input.Pagination == nil {
		input.Pagination = &pagination.CursorPagination{
			Cursor: "",
			Limit:  20,
		}
	}

	// Search users from repository
	users, paginationResponse, err := s.userRepo.Search(ctx, input.Query, input.Pagination)
	if err != nil {
		s.logger.WithError(err).Error("Failed to search users")
		return nil, fmt.Errorf("failed to search users: %w", err)
	}

	// Apply additional filters
	if input.Skills != nil && len(input.Skills) > 0 {
		users, err = s.filterBySkills(ctx, users, input.Skills)
		if err != nil {
			return nil, fmt.Errorf("failed to filter by skills: %w", err)
		}
	}

	if input.Location != "" {
		users, err = s.filterByLocation(ctx, users, input.Location)
		if err != nil {
			return nil, fmt.Errorf("failed to filter by location: %w", err)
		}
	}

	if input.Company != "" {
		users, err = s.filterByCompany(ctx, users, input.Company)
		if err != nil {
			return nil, fmt.Errorf("failed to filter by company: %w", err)
		}
	}

	if input.Role != nil {
		users = s.filterByRole(users, *input.Role)
	}

	if input.Status != nil {
		users = s.filterByStatus(users, *input.Status)
	}

	// Get total count
	total, err := s.userRepo.Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count users")
		return nil, fmt.Errorf("failed to count users: %w", err)
	}

	return &UserSearchResponse{
		Users:      users,
		Pagination: paginationResponse,
		Total:      total,
		Query:      input.Query,
	}, nil
}

// SearchBySkills searches users by skills
func (s *userSearchService) SearchBySkills(ctx context.Context, tenantID uint, skills []string, pag *pagination.CursorPagination) (*UserSearchResponse, error) {
	// Set default pagination if not provided
	if pag == nil {
		pag = &pagination.CursorPagination{
			Cursor: "",
			Limit:  20,
		}
	}

	// Search users from repository
	users, paginationResponse, err := s.userRepo.SearchBySkills(ctx, skills, pag)
	if err != nil {
		s.logger.WithError(err).Error("Failed to search users by skills")
		return nil, fmt.Errorf("failed to search users by skills: %w", err)
	}

	// Get total count
	total, err := s.userRepo.Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count users")
		return nil, fmt.Errorf("failed to count users: %w", err)
	}

	return &UserSearchResponse{
		Users:      users,
		Pagination: paginationResponse,
		Total:      total,
		Query:      fmt.Sprintf("skills:%s", strings.Join(skills, ",")),
	}, nil
}

// SearchByLocation searches users by location
func (s *userSearchService) SearchByLocation(ctx context.Context, tenantID uint, location string, pag *pagination.CursorPagination) (*UserSearchResponse, error) {
	// Set default pagination if not provided
	if pag == nil {
		pag = &pagination.CursorPagination{
			Cursor: "",
			Limit:  20,
		}
	}

	// Search users from repository
	users, paginationResponse, err := s.userRepo.SearchByLocation(ctx, location, pag)
	if err != nil {
		s.logger.WithError(err).Error("Failed to search users by location")
		return nil, fmt.Errorf("failed to search users by location: %w", err)
	}

	// Get total count
	total, err := s.userRepo.Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count users")
		return nil, fmt.Errorf("failed to count users: %w", err)
	}

	return &UserSearchResponse{
		Users:      users,
		Pagination: paginationResponse,
		Total:      total,
		Query:      fmt.Sprintf("location:%s", location),
	}, nil
}

// SearchByCompany searches users by company
func (s *userSearchService) SearchByCompany(ctx context.Context, tenantID uint, company string, pag *pagination.CursorPagination) (*UserSearchResponse, error) {
	// Set default pagination if not provided
	if pag == nil {
		pag = &pagination.CursorPagination{
			Cursor: "",
			Limit:  20,
		}
	}

	// Search users from repository
	users, paginationResponse, err := s.userRepo.SearchByCompany(ctx, company, pag)
	if err != nil {
		s.logger.WithError(err).Error("Failed to search users by company")
		return nil, fmt.Errorf("failed to search users by company: %w", err)
	}

	// Get total count
	total, err := s.userRepo.Count(ctx)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count users")
		return nil, fmt.Errorf("failed to count users: %w", err)
	}

	return &UserSearchResponse{
		Users:      users,
		Pagination: paginationResponse,
		Total:      total,
		Query:      fmt.Sprintf("company:%s", company),
	}, nil
}

// GetSearchSuggestions retrieves search suggestions
func (s *userSearchService) GetSearchSuggestions(ctx context.Context, tenantID uint, query string, limit int) ([]string, error) {
	suggestions, err := s.userRepo.GetSearchSuggestions(ctx, query, limit)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get search suggestions")
		return nil, fmt.Errorf("failed to get search suggestions: %w", err)
	}
	return suggestions, nil
}

// GetPopularSkills retrieves popular skills
func (s *userSearchService) GetPopularSkills(ctx context.Context, tenantID uint, limit int) ([]string, error) {
	skills, err := s.profileRepo.GetPopularSkills(ctx, tenantID, limit)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get popular skills")
		return nil, fmt.Errorf("failed to get popular skills: %w", err)
	}
	return skills, nil
}

// GetPopularLocations retrieves popular locations
func (s *userSearchService) GetPopularLocations(ctx context.Context, tenantID uint, limit int) ([]string, error) {
	locations, err := s.profileRepo.GetPopularLocations(ctx, tenantID, limit)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get popular locations")
		return nil, fmt.Errorf("failed to get popular locations: %w", err)
	}
	return locations, nil
}

// Helper methods

// filterBySkills filters users by skills
func (s *userSearchService) filterBySkills(ctx context.Context, users []models.User, skills []string) ([]models.User, error) {
	// This would use more sophisticated filtering in production
	// For now, we'll just return the users as-is
	return users, nil
}

// filterByLocation filters users by location
func (s *userSearchService) filterByLocation(ctx context.Context, users []models.User, location string) ([]models.User, error) {
	// This would use more sophisticated filtering in production
	// For now, we'll just return the users as-is
	return users, nil
}

// filterByCompany filters users by company
func (s *userSearchService) filterByCompany(ctx context.Context, users []models.User, company string) ([]models.User, error) {
	// This would use more sophisticated filtering in production
	// For now, we'll just return the users as-is
	return users, nil
}

// filterByRole filters users by role - deprecated in multi-tenant architecture
func (s *userSearchService) filterByRole(users []models.User, role models.UserRole) []models.User {
	// Role filtering removed - roles are now handled through tenant memberships
	return users
}

// filterByStatus filters users by status
func (s *userSearchService) filterByStatus(users []models.User, status models.UserStatus) []models.User {
	var filtered []models.User
	for _, user := range users {
		if user.Status == status {
			filtered = append(filtered, user)
		}
	}
	return filtered
}
