package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// UserProfileRepository defines the interface for user profile data operations
type UserProfileRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, profile *models.UserProfile) error
	GetByID(ctx context.Context, id uint) (*models.UserProfile, error)
	GetByUserID(ctx context.Context, userID uint) (*models.UserProfile, error)
	Update(ctx context.Context, profile *models.UserProfile) error
	Delete(ctx context.Context, id uint) error

	// Soft delete operations
	SoftDelete(ctx context.Context, id uint) error
	Restore(ctx context.Context, id uint) error

	// Profile completion operations
	UpdateCompletionStatus(ctx context.Context, userID uint) error
	GetProfilesWithLowCompletion(ctx context.Context, tenantID uint, threshold uint8, pagination *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error)
	GetProfilesByCompletionRange(ctx context.Context, tenantID uint, minCompletion, maxCompletion uint8, pagination *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error)

	// Search operations
	SearchByBio(ctx context.Context, tenantID uint, query string, pagination *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error)
	SearchByLocation(ctx context.Context, tenantID uint, location string, pagination *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error)
	SearchByCompany(ctx context.Context, tenantID uint, company string, pagination *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error)
	SearchBySkills(ctx context.Context, tenantID uint, skills []string, pagination *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error)
	SearchByInterests(ctx context.Context, tenantID uint, interests []string, pagination *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error)

	// List operations
	ListByTenant(ctx context.Context, tenantID uint, pagination *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error)
	ListByCompletionStatus(ctx context.Context, tenantID uint, completed bool, pagination *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error)
	ListByPublicStatus(ctx context.Context, tenantID uint, isPublic bool, pagination *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error)

	// Statistics
	GetCompletionStats(ctx context.Context, tenantID uint) (*ProfileCompletionStats, error)
	CountByTenant(ctx context.Context, tenantID uint) (int64, error)
	CountByCompletionStatus(ctx context.Context, tenantID uint, completed bool) (int64, error)
	GetPopularSkills(ctx context.Context, tenantID uint, limit int) ([]string, error)
	GetPopularLocations(ctx context.Context, tenantID uint, limit int) ([]string, error)

	// Bulk operations
	BulkUpdateCompletion(ctx context.Context, userIDs []uint) error
	BulkUpdatePublicStatus(ctx context.Context, userIDs []uint, isPublic bool) error

	// Custom field operations
	UpdateCustomFields(ctx context.Context, userID uint, customFields map[string]interface{}) error
	GetCustomFields(ctx context.Context, userID uint) (map[string]interface{}, error)

	// Validation
	ProfileExists(ctx context.Context, userID uint) (bool, error)
	UsernameExists(ctx context.Context, username string, tenantID uint) (bool, error)
	UsernameExistsExcludingUser(ctx context.Context, username string, tenantID uint, excludeUserID uint) (bool, error)
}

// ProfileCompletionStats represents profile completion statistics
type ProfileCompletionStats struct {
	TotalProfiles        int64   `json:"total_profiles"`
	CompletedProfiles    int64   `json:"completed_profiles"`
	IncompleteProfiles   int64   `json:"incomplete_profiles"`
	AverageCompletion    float64 `json:"average_completion"`
	CompletionRate       float64 `json:"completion_rate"`
	ProfilesWithBio      int64   `json:"profiles_with_bio"`
	ProfilesWithAvatar   int64   `json:"profiles_with_avatar"`
	ProfilesWithLocation int64   `json:"profiles_with_location"`
	ProfilesWithCompany  int64   `json:"profiles_with_company"`
	ProfilesWithSkills   int64   `json:"profiles_with_skills"`
}

// UserProfileFilter represents filter options for user profile queries
type UserProfileFilter struct {
	TenantID         uint     `json:"tenant_id"`
	IsPublic         *bool    `json:"is_public,omitempty"`
	ProfileCompleted *bool    `json:"profile_completed,omitempty"`
	MinCompletion    *uint8   `json:"min_completion,omitempty"`
	MaxCompletion    *uint8   `json:"max_completion,omitempty"`
	HasBio           *bool    `json:"has_bio,omitempty"`
	HasAvatar        *bool    `json:"has_avatar,omitempty"`
	HasLocation      *bool    `json:"has_location,omitempty"`
	HasCompany       *bool    `json:"has_company,omitempty"`
	HasSkills        *bool    `json:"has_skills,omitempty"`
	Location         string   `json:"location,omitempty"`
	Company          string   `json:"company,omitempty"`
	Skills           []string `json:"skills,omitempty"`
	Interests        []string `json:"interests,omitempty"`
	SearchQuery      string   `json:"search_query,omitempty"`
}

// UserProfileSearchOptions represents search options for user profile queries
type UserProfileSearchOptions struct {
	Filter     UserProfileFilter            `json:"filter"`
	Pagination *pagination.CursorPagination `json:"pagination"`
	SortBy     string                       `json:"sort_by"`
	SortOrder  string                       `json:"sort_order"`
	WithUser   bool                         `json:"with_user"`
}
