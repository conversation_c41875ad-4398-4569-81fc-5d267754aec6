package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// UserRepository defines the interface for user data operations
type UserRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, user *models.User) error
	GetByID(ctx context.Context, id uint) (*models.User, error)
	GetByEmail(ctx context.Context, email string) (*models.User, error)
	GetByUsername(ctx context.Context, username string) (*models.User, error)
	Update(ctx context.Context, user *models.User) error
	Delete(ctx context.Context, id uint) error

	// Soft delete operations
	SoftDelete(ctx context.Context, id uint) error
	Restore(ctx context.Context, id uint) error

	// List operations with pagination
	List(ctx context.Context, pagination *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error)
	ListByStatus(ctx context.Context, status models.UserStatus, pagination *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error)

	// Search operations
	Search(ctx context.Context, query string, pagination *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error)
	SearchBySkills(ctx context.Context, skills []string, pagination *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error)
	SearchByLocation(ctx context.Context, location string, pagination *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error)
	SearchByCompany(ctx context.Context, company string, pagination *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error)
	GetSearchSuggestions(ctx context.Context, query string, limit int) ([]string, error)

	// Authentication operations
	GetByEmailForAuth(ctx context.Context, email string) (*models.User, error)
	UpdatePassword(ctx context.Context, userID uint, passwordHash string) error
	UpdateEmailVerification(ctx context.Context, userID uint, verified bool) error
	UpdatePhoneVerification(ctx context.Context, userID uint, verified bool) error
	UpdateLoginInfo(ctx context.Context, userID uint, ip string) error

	// Two-factor authentication
	EnableTwoFactor(ctx context.Context, userID uint, secret string) error
	DisableTwoFactor(ctx context.Context, userID uint) error
	UpdateRecoveryCodes(ctx context.Context, userID uint, codes []string) error

	// Status operations
	UpdateStatus(ctx context.Context, userID uint, status models.UserStatus) error
	GetActiveUsers(ctx context.Context, pagination *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error)
	GetInactiveUsers(ctx context.Context, pagination *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error)

	// Statistics
	Count(ctx context.Context) (int64, error)
	CountByStatus(ctx context.Context, status models.UserStatus) (int64, error)

	// Bulk operations
	BulkUpdateStatus(ctx context.Context, userIDs []uint, status models.UserStatus) error
	BulkDelete(ctx context.Context, userIDs []uint) error

	// Activity tracking
	GetRecentlyActive(ctx context.Context, limit int) ([]models.User, error)
	GetUserActivity(ctx context.Context, userID uint) (*models.User, error)

	// Validation
	EmailExists(ctx context.Context, email string) (bool, error)
	UsernameExists(ctx context.Context, username string) (bool, error)
	EmailExistsExcludingUser(ctx context.Context, email string, excludeUserID uint) (bool, error)
	UsernameExistsExcludingUser(ctx context.Context, username string, excludeUserID uint) (bool, error)
}

// UserFilter represents filter options for user queries
type UserFilter struct {
	Status           *models.UserStatus `json:"status,omitempty"`
	EmailVerified    *bool              `json:"email_verified,omitempty"`
	PhoneVerified    *bool              `json:"phone_verified,omitempty"`
	TwoFactorEnabled *bool              `json:"two_factor_enabled,omitempty"`
	CreatedAfter     *string            `json:"created_after,omitempty"`
	CreatedBefore    *string            `json:"created_before,omitempty"`
	LastLoginAfter   *string            `json:"last_login_after,omitempty"`
	LastLoginBefore  *string            `json:"last_login_before,omitempty"`
	Skills           []string           `json:"skills,omitempty"`
	Location         string             `json:"location,omitempty"`
	Company          string             `json:"company,omitempty"`
	SearchQuery      string             `json:"search_query,omitempty"`
}

// UserSearchOptions represents search options for user queries
type UserSearchOptions struct {
	Filter          UserFilter                   `json:"filter"`
	Pagination      *pagination.CursorPagination `json:"pagination"`
	SortBy          string                       `json:"sort_by"`
	SortOrder       string                       `json:"sort_order"`
	WithProfile     bool                         `json:"with_profile"`
	WithPreferences bool                         `json:"with_preferences"`
	WithSocialLinks bool                         `json:"with_social_links"`
}
