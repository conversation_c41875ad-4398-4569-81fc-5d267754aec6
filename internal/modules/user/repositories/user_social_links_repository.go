package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// UserSocialLinksRepository defines the interface for user social links data operations
type UserSocialLinksRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, socialLink *models.UserSocialLink) error
	GetByID(ctx context.Context, id uint) (*models.UserSocialLink, error)
	GetByUserID(ctx context.Context, userID uint) ([]models.UserSocialLink, error)
	GetByUserIDAndPlatform(ctx context.Context, userID uint, platform models.SocialPlatform) (*models.UserSocialLink, error)
	Update(ctx context.Context, socialLink *models.UserSocialLink) error
	Delete(ctx context.Context, id uint) error

	// Soft delete operations
	SoftDelete(ctx context.Context, id uint) error
	Restore(ctx context.Context, id uint) error

	// List operations with pagination
	ListByTenant(ctx context.Context, tenantID uint, pagination *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error)
	ListByPlatform(ctx context.Context, tenantID uint, platform models.SocialPlatform, pagination *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error)
	ListByVerificationStatus(ctx context.Context, tenantID uint, verified bool, pagination *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error)
	ListByPublicStatus(ctx context.Context, tenantID uint, isPublic bool, pagination *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error)

	// Search operations
	SearchByPlatform(ctx context.Context, tenantID uint, platform models.SocialPlatform, pagination *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error)
	SearchByUsername(ctx context.Context, tenantID uint, username string, pagination *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error)
	SearchByURL(ctx context.Context, tenantID uint, url string, pagination *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error)

	// Verification operations
	VerifyLink(ctx context.Context, id uint) error
	UnverifyLink(ctx context.Context, id uint) error
	UpdateVerificationStatus(ctx context.Context, id uint, verified bool) error
	GetUnverifiedLinks(ctx context.Context, tenantID uint, pagination *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error)
	GetVerifiedLinks(ctx context.Context, tenantID uint, pagination *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error)

	// Display order operations
	UpdateDisplayOrder(ctx context.Context, id uint, displayOrder uint) error
	ReorderUserLinks(ctx context.Context, userID uint, linkOrders []models.UserSocialLinkOrder) error
	GetUserLinksOrdered(ctx context.Context, userID uint) ([]models.UserSocialLink, error)

	// Public/Private operations
	UpdatePublicStatus(ctx context.Context, id uint, isPublic bool) error
	GetPublicLinks(ctx context.Context, tenantID uint, pagination *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error)
	GetPrivateLinks(ctx context.Context, tenantID uint, pagination *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error)
	GetPublicLinksByUser(ctx context.Context, userID uint) ([]models.UserSocialLink, error)

	// Profile data operations
	UpdateProfileData(ctx context.Context, id uint, profileData map[string]interface{}) error
	GetProfileData(ctx context.Context, id uint) (map[string]interface{}, error)
	UpdateProfileDataField(ctx context.Context, id uint, field string, value interface{}) error
	GetProfileDataField(ctx context.Context, id uint, field string) (interface{}, error)

	// Bulk operations
	BulkCreate(ctx context.Context, socialLinks []models.UserSocialLink) error
	BulkUpdate(ctx context.Context, socialLinks []models.UserSocialLink) error
	BulkDelete(ctx context.Context, ids []uint) error
	BulkUpdateVerificationStatus(ctx context.Context, ids []uint, verified bool) error
	BulkUpdatePublicStatus(ctx context.Context, ids []uint, isPublic bool) error
	BulkUpdateDisplayOrder(ctx context.Context, updates []SocialLinkDisplayOrderUpdate) error

	// Platform-specific operations
	GetPlatformStats(ctx context.Context, tenantID uint) (*SocialPlatformStats, error)
	GetUserPlatformUsage(ctx context.Context, userID uint) ([]models.SocialPlatform, error)
	GetPopularPlatforms(ctx context.Context, tenantID uint, limit int) ([]PlatformUsage, error)

	// Statistics
	CountByTenant(ctx context.Context, tenantID uint) (int64, error)
	CountByPlatform(ctx context.Context, tenantID uint, platform models.SocialPlatform) (int64, error)
	CountByVerificationStatus(ctx context.Context, tenantID uint, verified bool) (int64, error)
	CountByPublicStatus(ctx context.Context, tenantID uint, isPublic bool) (int64, error)
	CountByUser(ctx context.Context, userID uint) (int64, error)

	// Validation
	LinkExists(ctx context.Context, userID uint, platform models.SocialPlatform) (bool, error)
	URLExists(ctx context.Context, userID uint, url string) (bool, error)
	UsernameExists(ctx context.Context, userID uint, platform models.SocialPlatform, username string) (bool, error)
	PlatformExists(ctx context.Context, userID uint, platform models.SocialPlatform) (bool, error)
	GetMaxDisplayOrder(ctx context.Context, userID uint) (uint, error)

	// Cleanup operations
	DeleteExpiredLinks(ctx context.Context) error
	DeleteUnverifiedLinks(ctx context.Context, olderThan int) error // days
	ArchiveInactiveLinks(ctx context.Context, olderThan int) error  // days

	// Advanced queries
	GetLinksWithProfileData(ctx context.Context, tenantID uint, pagination *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error)
	GetLinksCreatedBetween(ctx context.Context, tenantID uint, startDate, endDate string, pagination *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error)
	GetLinksUpdatedBetween(ctx context.Context, tenantID uint, startDate, endDate string, pagination *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error)
	GetMostRecentLinks(ctx context.Context, tenantID uint, limit int) ([]models.UserSocialLink, error)
}

// SocialLinkDisplayOrderUpdate represents a display order update for a social link
type SocialLinkDisplayOrderUpdate struct {
	ID           uint `json:"id"`
	DisplayOrder uint `json:"display_order"`
}

// SocialPlatformStats represents statistics for social platforms
type SocialPlatformStats struct {
	TotalLinks          int64                             `json:"total_links"`
	VerifiedLinks       int64                             `json:"verified_links"`
	UnverifiedLinks     int64                             `json:"unverified_links"`
	PublicLinks         int64                             `json:"public_links"`
	PrivateLinks        int64                             `json:"private_links"`
	PlatformCounts      map[models.SocialPlatform]int64   `json:"platform_counts"`
	VerificationRates   map[models.SocialPlatform]float64 `json:"verification_rates"`
	PublicRates         map[models.SocialPlatform]float64 `json:"public_rates"`
	UsersWithLinks      int64                             `json:"users_with_links"`
	AverageLinksPerUser float64                           `json:"average_links_per_user"`
}

// PlatformUsage represents usage statistics for a platform
type PlatformUsage struct {
	Platform      models.SocialPlatform `json:"platform"`
	Count         int64                 `json:"count"`
	VerifiedCount int64                 `json:"verified_count"`
	PublicCount   int64                 `json:"public_count"`
	UsageRate     float64               `json:"usage_rate"`
}

// UserSocialLinksFilter represents filter options for user social links queries
type UserSocialLinksFilter struct {
	TenantID        uint                   `json:"tenant_id"`
	UserID          *uint                  `json:"user_id,omitempty"`
	Platform        *models.SocialPlatform `json:"platform,omitempty"`
	IsVerified      *bool                  `json:"is_verified,omitempty"`
	IsPublic        *bool                  `json:"is_public,omitempty"`
	CreatedAfter    *string                `json:"created_after,omitempty"`
	CreatedBefore   *string                `json:"created_before,omitempty"`
	UpdatedAfter    *string                `json:"updated_after,omitempty"`
	UpdatedBefore   *string                `json:"updated_before,omitempty"`
	VerifiedAfter   *string                `json:"verified_after,omitempty"`
	VerifiedBefore  *string                `json:"verified_before,omitempty"`
	UsernamePattern string                 `json:"username_pattern,omitempty"`
	URLPattern      string                 `json:"url_pattern,omitempty"`
	HasProfileData  *bool                  `json:"has_profile_data,omitempty"`
}

// UserSocialLinksSearchOptions represents search options for user social links queries
type UserSocialLinksSearchOptions struct {
	Filter     UserSocialLinksFilter        `json:"filter"`
	Pagination *pagination.CursorPagination `json:"pagination"`
	SortBy     string                       `json:"sort_by"`
	SortOrder  string                       `json:"sort_order"`
	WithUser   bool                         `json:"with_user"`
}
