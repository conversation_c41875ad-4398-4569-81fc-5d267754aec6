package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// UserInvitationRepository defines the interface for user invitation operations
type UserInvitationRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, invitation *models.UserInvitation) error
	GetByID(ctx context.Context, id uint) (*models.UserInvitation, error)
	GetByToken(ctx context.Context, token string) (*models.UserInvitation, error)
	GetByEmail(ctx context.Context, email string) (*models.UserInvitation, error)
	GetByTenantAndEmail(ctx context.Context, tenantID uint, email string) (*models.UserInvitation, error)
	Update(ctx context.Context, invitation *models.UserInvitation) error
	Delete(ctx context.Context, id uint) error

	// List operations
	GetByTenantID(ctx context.Context, tenantID uint, pagination *pagination.CursorPagination) ([]models.UserInvitation, *pagination.CursorResponse, error)
	GetByStatus(ctx context.Context, status models.UserInvitationStatus, pagination *pagination.CursorPagination) ([]models.UserInvitation, *pagination.CursorResponse, error)
	GetByInviter(ctx context.Context, inviterID uint, pagination *pagination.CursorPagination) ([]models.UserInvitation, *pagination.CursorResponse, error)

	// Status operations
	UpdateStatus(ctx context.Context, id uint, status models.UserInvitationStatus) error
	GetPendingInvitations(ctx context.Context, tenantID uint) ([]models.UserInvitation, error)
	GetExpiredInvitations(ctx context.Context, tenantID uint) ([]models.UserInvitation, error)

	// Expiration operations
	ExtendExpiration(ctx context.Context, id uint, newExpiration time.Time) error
	MarkAsUsed(ctx context.Context, token string, acceptedByUserID uint) error
	MarkAsExpired(ctx context.Context, id uint) error

	// Validation
	TokenExists(ctx context.Context, token string) (bool, error)
	EmailHasPendingInvitation(ctx context.Context, tenantID uint, email string) (bool, error)
	IsValidToken(ctx context.Context, token string) (bool, error)

	// Statistics
	CountByTenant(ctx context.Context, tenantID uint) (int64, error)
	CountByStatus(ctx context.Context, tenantID uint, status models.UserInvitationStatus) (int64, error)
	CountByInviter(ctx context.Context, inviterID uint) (int64, error)

	// Cleanup operations
	DeleteExpiredInvitations(ctx context.Context, tenantID uint) error
	DeleteInvitationsByEmail(ctx context.Context, email string) error
}
