package mysql

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// userProfileRepository implements the UserProfileRepository interface using GORM
type userProfileRepository struct {
	db     *gorm.DB
	logger utils.Logger
}

// NewUserProfileRepository creates a new instance of UserProfileRepository
func NewUserProfileRepository(db *gorm.DB, logger utils.Logger) repositories.UserProfileRepository {
	return &userProfileRepository{
		db:     db,
		logger: logger,
	}
}

// Create creates a new user profile
func (r *userProfileRepository) Create(ctx context.Context, profile *models.UserProfile) error {
	if err := r.db.WithContext(ctx).Create(profile).Error; err != nil {
		r.logger.WithError(err).Error("Failed to create user profile")
		return fmt.Errorf("failed to create user profile: %w", err)
	}
	return nil
}

// GetByID retrieves a user profile by ID
func (r *userProfileRepository) GetByID(ctx context.Context, id uint) (*models.UserProfile, error) {
	var profile models.UserProfile
	err := r.db.WithContext(ctx).
		Preload("User").
		First(&profile, id).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("profile not found")
		}
		r.logger.WithError(err).Error("Failed to get user profile by ID")
		return nil, fmt.Errorf("failed to get user profile: %w", err)
	}

	return &profile, nil
}

// GetByUserID retrieves a user profile by user ID
func (r *userProfileRepository) GetByUserID(ctx context.Context, userID uint) (*models.UserProfile, error) {
	var profile models.UserProfile
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("user_id = ?", userID).
		First(&profile).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("profile not found")
		}
		r.logger.WithError(err).Error("Failed to get user profile by user ID")
		return nil, fmt.Errorf("failed to get user profile: %w", err)
	}

	return &profile, nil
}

// Update updates a user profile
func (r *userProfileRepository) Update(ctx context.Context, profile *models.UserProfile) error {
	if err := r.db.WithContext(ctx).Save(profile).Error; err != nil {
		r.logger.WithError(err).Error("Failed to update user profile")
		return fmt.Errorf("failed to update user profile: %w", err)
	}
	return nil
}

// Delete permanently deletes a user profile
func (r *userProfileRepository) Delete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Delete(&models.UserProfile{}, id).Error; err != nil {
		r.logger.WithError(err).Error("Failed to delete user profile")
		return fmt.Errorf("failed to delete user profile: %w", err)
	}
	return nil
}

// SoftDelete marks a user profile as deleted
func (r *userProfileRepository) SoftDelete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Delete(&models.UserProfile{}, id).Error; err != nil {
		r.logger.WithError(err).Error("Failed to soft delete user profile")
		return fmt.Errorf("failed to soft delete user profile: %w", err)
	}
	return nil
}

// Restore restores a soft-deleted user profile
func (r *userProfileRepository) Restore(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Unscoped().Model(&models.UserProfile{}).Where("id = ?", id).Update("deleted_at", nil).Error; err != nil {
		r.logger.WithError(err).Error("Failed to restore user profile")
		return fmt.Errorf("failed to restore user profile: %w", err)
	}
	return nil
}

// UpdateCompletionStatus updates the completion status of a user profile
func (r *userProfileRepository) UpdateCompletionStatus(ctx context.Context, userID uint) error {
	var profile models.UserProfile
	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).First(&profile).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("profile not found")
		}
		r.logger.WithError(err).Error("Failed to get user profile for completion update")
		return fmt.Errorf("failed to get user profile: %w", err)
	}

	// The completion will be calculated automatically by the BeforeSave hook
	if err := r.db.WithContext(ctx).Save(&profile).Error; err != nil {
		r.logger.WithError(err).Error("Failed to update profile completion status")
		return fmt.Errorf("failed to update profile completion status: %w", err)
	}

	return nil
}

// GetProfilesWithLowCompletion retrieves profiles with completion below threshold
func (r *userProfileRepository) GetProfilesWithLowCompletion(ctx context.Context, tenantID uint, threshold uint8, pag *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error) {
	var profiles []models.UserProfile
	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ? AND user_profiles.completion_percentage < ?", tenantID, threshold).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_profiles.id > ?", pag.Cursor)
	}

	query = query.Order("user_profiles.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&profiles).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get profiles with low completion")
		return nil, nil, fmt.Errorf("failed to get profiles with low completion: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(profiles) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		profiles = profiles[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", profiles[len(profiles)-1].ID)
	}

	return profiles, response, nil
}

// GetProfilesByCompletionRange retrieves profiles within completion range
func (r *userProfileRepository) GetProfilesByCompletionRange(ctx context.Context, tenantID uint, minCompletion, maxCompletion uint8, pag *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error) {
	var profiles []models.UserProfile
	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ? AND user_profiles.completion_percentage >= ? AND user_profiles.completion_percentage <= ?", tenantID, minCompletion, maxCompletion).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_profiles.id > ?", pag.Cursor)
	}

	query = query.Order("user_profiles.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&profiles).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get profiles by completion range")
		return nil, nil, fmt.Errorf("failed to get profiles by completion range: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(profiles) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		profiles = profiles[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", profiles[len(profiles)-1].ID)
	}

	return profiles, response, nil
}

// SearchByBio searches profiles by bio content
func (r *userProfileRepository) SearchByBio(ctx context.Context, tenantID uint, query string, pag *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error) {
	var profiles []models.UserProfile
	searchQuery := "%" + strings.ToLower(query) + "%"

	dbQuery := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ? AND LOWER(user_profiles.bio) LIKE ?", tenantID, searchQuery).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		dbQuery = dbQuery.Where("user_profiles.id > ?", pag.Cursor)
	}

	dbQuery = dbQuery.Order("user_profiles.id ASC").Limit(pag.Limit + 1)

	if err := dbQuery.Find(&profiles).Error; err != nil {
		r.logger.WithError(err).Error("Failed to search profiles by bio")
		return nil, nil, fmt.Errorf("failed to search profiles by bio: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(profiles) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		profiles = profiles[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", profiles[len(profiles)-1].ID)
	}

	return profiles, response, nil
}

// SearchByLocation searches profiles by location
func (r *userProfileRepository) SearchByLocation(ctx context.Context, tenantID uint, location string, pag *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error) {
	var profiles []models.UserProfile
	locationQuery := "%" + strings.ToLower(location) + "%"

	dbQuery := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Where("(LOWER(user_profiles.location) LIKE ? OR LOWER(user_profiles.city) LIKE ? OR LOWER(user_profiles.state) LIKE ? OR LOWER(user_profiles.country) LIKE ?)",
			locationQuery, locationQuery, locationQuery, locationQuery).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		dbQuery = dbQuery.Where("user_profiles.id > ?", pag.Cursor)
	}

	dbQuery = dbQuery.Order("user_profiles.id ASC").Limit(pag.Limit + 1)

	if err := dbQuery.Find(&profiles).Error; err != nil {
		r.logger.WithError(err).Error("Failed to search profiles by location")
		return nil, nil, fmt.Errorf("failed to search profiles by location: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(profiles) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		profiles = profiles[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", profiles[len(profiles)-1].ID)
	}

	return profiles, response, nil
}

// SearchByCompany searches profiles by company
func (r *userProfileRepository) SearchByCompany(ctx context.Context, tenantID uint, company string, pag *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error) {
	var profiles []models.UserProfile
	companyQuery := "%" + strings.ToLower(company) + "%"

	dbQuery := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ? AND LOWER(user_profiles.company) LIKE ?", tenantID, companyQuery).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		dbQuery = dbQuery.Where("user_profiles.id > ?", pag.Cursor)
	}

	dbQuery = dbQuery.Order("user_profiles.id ASC").Limit(pag.Limit + 1)

	if err := dbQuery.Find(&profiles).Error; err != nil {
		r.logger.WithError(err).Error("Failed to search profiles by company")
		return nil, nil, fmt.Errorf("failed to search profiles by company: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(profiles) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		profiles = profiles[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", profiles[len(profiles)-1].ID)
	}

	return profiles, response, nil
}

// SearchBySkills searches profiles by skills
func (r *userProfileRepository) SearchBySkills(ctx context.Context, tenantID uint, skills []string, pag *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error) {
	var profiles []models.UserProfile

	// Build skills search query
	skillsQuery := make([]string, len(skills))
	for i, skill := range skills {
		skillsQuery[i] = fmt.Sprintf("JSON_SEARCH(user_profiles.skills, 'one', '%%%s%%') IS NOT NULL", skill)
	}

	dbQuery := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Where(strings.Join(skillsQuery, " OR ")).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		dbQuery = dbQuery.Where("user_profiles.id > ?", pag.Cursor)
	}

	dbQuery = dbQuery.Order("user_profiles.id ASC").Limit(pag.Limit + 1)

	if err := dbQuery.Find(&profiles).Error; err != nil {
		r.logger.WithError(err).Error("Failed to search profiles by skills")
		return nil, nil, fmt.Errorf("failed to search profiles by skills: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(profiles) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		profiles = profiles[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", profiles[len(profiles)-1].ID)
	}

	return profiles, response, nil
}

// SearchByInterests searches profiles by interests
func (r *userProfileRepository) SearchByInterests(ctx context.Context, tenantID uint, interests []string, pag *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error) {
	var profiles []models.UserProfile

	// Build interests search query
	interestsQuery := make([]string, len(interests))
	for i, interest := range interests {
		interestsQuery[i] = fmt.Sprintf("JSON_SEARCH(user_profiles.interests, 'one', '%%%s%%') IS NOT NULL", interest)
	}

	dbQuery := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Where(strings.Join(interestsQuery, " OR ")).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		dbQuery = dbQuery.Where("user_profiles.id > ?", pag.Cursor)
	}

	dbQuery = dbQuery.Order("user_profiles.id ASC").Limit(pag.Limit + 1)

	if err := dbQuery.Find(&profiles).Error; err != nil {
		r.logger.WithError(err).Error("Failed to search profiles by interests")
		return nil, nil, fmt.Errorf("failed to search profiles by interests: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(profiles) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		profiles = profiles[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", profiles[len(profiles)-1].ID)
	}

	return profiles, response, nil
}

// ListByTenant retrieves profiles by tenant
func (r *userProfileRepository) ListByTenant(ctx context.Context, tenantID uint, pag *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error) {
	var profiles []models.UserProfile
	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_profiles.id > ?", pag.Cursor)
	}

	query = query.Order("user_profiles.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&profiles).Error; err != nil {
		r.logger.WithError(err).Error("Failed to list profiles by tenant")
		return nil, nil, fmt.Errorf("failed to list profiles by tenant: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(profiles) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		profiles = profiles[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", profiles[len(profiles)-1].ID)
	}

	return profiles, response, nil
}

// ListByCompletionStatus retrieves profiles by completion status
func (r *userProfileRepository) ListByCompletionStatus(ctx context.Context, tenantID uint, completed bool, pag *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error) {
	var profiles []models.UserProfile
	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ? AND user_profiles.profile_completed = ?", tenantID, completed).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_profiles.id > ?", pag.Cursor)
	}

	query = query.Order("user_profiles.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&profiles).Error; err != nil {
		r.logger.WithError(err).Error("Failed to list profiles by completion status")
		return nil, nil, fmt.Errorf("failed to list profiles by completion status: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(profiles) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		profiles = profiles[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", profiles[len(profiles)-1].ID)
	}

	return profiles, response, nil
}

// ListByPublicStatus retrieves profiles by public status
func (r *userProfileRepository) ListByPublicStatus(ctx context.Context, tenantID uint, isPublic bool, pag *pagination.CursorPagination) ([]models.UserProfile, *pagination.CursorResponse, error) {
	var profiles []models.UserProfile
	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ? AND user_profiles.display_profile = ?", tenantID, isPublic).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_profiles.id > ?", pag.Cursor)
	}

	query = query.Order("user_profiles.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&profiles).Error; err != nil {
		r.logger.WithError(err).Error("Failed to list profiles by public status")
		return nil, nil, fmt.Errorf("failed to list profiles by public status: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(profiles) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		profiles = profiles[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", profiles[len(profiles)-1].ID)
	}

	return profiles, response, nil
}

// GetCompletionStats retrieves profile completion statistics
func (r *userProfileRepository) GetCompletionStats(ctx context.Context, tenantID uint) (*repositories.ProfileCompletionStats, error) {
	var stats repositories.ProfileCompletionStats

	// Get total profiles count
	err := r.db.WithContext(ctx).
		Model(&models.UserProfile{}).
		Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Count(&stats.TotalProfiles).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to get total profiles count")
		return nil, fmt.Errorf("failed to get total profiles count: %w", err)
	}

	if stats.TotalProfiles == 0 {
		return &stats, nil
	}

	// Get completed profiles count
	err = r.db.WithContext(ctx).
		Model(&models.UserProfile{}).
		Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ? AND user_profiles.profile_completed = ?", tenantID, true).
		Count(&stats.CompletedProfiles).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to get completed profiles count")
		return nil, fmt.Errorf("failed to get completed profiles count: %w", err)
	}

	stats.IncompleteProfiles = stats.TotalProfiles - stats.CompletedProfiles

	// Get average completion percentage
	var avgCompletion float64
	err = r.db.WithContext(ctx).
		Model(&models.UserProfile{}).
		Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Select("AVG(user_profiles.completion_percentage)").
		Scan(&avgCompletion).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to get average completion")
		return nil, fmt.Errorf("failed to get average completion: %w", err)
	}

	stats.AverageCompletion = avgCompletion
	stats.CompletionRate = float64(stats.CompletedProfiles) / float64(stats.TotalProfiles) * 100

	// Get profiles with specific fields
	counts := []struct {
		Field string
		Count *int64
	}{
		{"bio IS NOT NULL AND bio != ''", &stats.ProfilesWithBio},
		{"avatar_url IS NOT NULL AND avatar_url != ''", &stats.ProfilesWithAvatar},
		{"location IS NOT NULL AND location != ''", &stats.ProfilesWithLocation},
		{"company IS NOT NULL AND company != ''", &stats.ProfilesWithCompany},
		{"skills IS NOT NULL AND JSON_LENGTH(skills) > 0", &stats.ProfilesWithSkills},
	}

	for _, count := range counts {
		err = r.db.WithContext(ctx).
			Model(&models.UserProfile{}).
			Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
			Where("users.tenant_id = ? AND "+count.Field, tenantID).
			Count(count.Count).Error
		if err != nil {
			r.logger.WithError(err).Error("Failed to get profile field count")
			return nil, fmt.Errorf("failed to get profile field count: %w", err)
		}
	}

	return &stats, nil
}

// CountByTenant counts profiles by tenant
func (r *userProfileRepository) CountByTenant(ctx context.Context, tenantID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserProfile{}).
		Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count profiles by tenant")
		return 0, fmt.Errorf("failed to count profiles by tenant: %w", err)
	}
	return count, nil
}

// CountByCompletionStatus counts profiles by completion status
func (r *userProfileRepository) CountByCompletionStatus(ctx context.Context, tenantID uint, completed bool) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserProfile{}).
		Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ? AND user_profiles.profile_completed = ?", tenantID, completed).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count profiles by completion status")
		return 0, fmt.Errorf("failed to count profiles by completion status: %w", err)
	}
	return count, nil
}

// BulkUpdateCompletion updates completion status for multiple profiles
func (r *userProfileRepository) BulkUpdateCompletion(ctx context.Context, userIDs []uint) error {
	// This will trigger the BeforeSave hook which recalculates completion
	err := r.db.WithContext(ctx).
		Model(&models.UserProfile{}).
		Where("user_id IN ?", userIDs).
		Update("updated_at", gorm.Expr("NOW()")).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to bulk update profile completion")
		return fmt.Errorf("failed to bulk update profile completion: %w", err)
	}
	return nil
}

// BulkUpdatePublicStatus updates public status for multiple profiles
func (r *userProfileRepository) BulkUpdatePublicStatus(ctx context.Context, userIDs []uint, isPublic bool) error {
	err := r.db.WithContext(ctx).
		Model(&models.UserProfile{}).
		Where("user_id IN ?", userIDs).
		Update("display_profile", isPublic).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to bulk update profile public status")
		return fmt.Errorf("failed to bulk update profile public status: %w", err)
	}
	return nil
}

// UpdateCustomFields updates custom fields for a profile
func (r *userProfileRepository) UpdateCustomFields(ctx context.Context, userID uint, customFields map[string]interface{}) error {
	customFieldsJSON, err := json.Marshal(customFields)
	if err != nil {
		return fmt.Errorf("failed to marshal custom fields: %w", err)
	}

	err = r.db.WithContext(ctx).
		Model(&models.UserProfile{}).
		Where("user_id = ?", userID).
		Update("custom_fields", string(customFieldsJSON)).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update custom fields")
		return fmt.Errorf("failed to update custom fields: %w", err)
	}
	return nil
}

// GetCustomFields retrieves custom fields for a profile
func (r *userProfileRepository) GetCustomFields(ctx context.Context, userID uint) (map[string]interface{}, error) {
	var profile models.UserProfile
	err := r.db.WithContext(ctx).
		Select("custom_fields").
		Where("user_id = ?", userID).
		First(&profile).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return make(map[string]interface{}), nil
		}
		r.logger.WithError(err).Error("Failed to get custom fields")
		return nil, fmt.Errorf("failed to get custom fields: %w", err)
	}

	return profile.GetCustomFields(), nil
}

// ProfileExists checks if a profile exists for a user
func (r *userProfileRepository) ProfileExists(ctx context.Context, userID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserProfile{}).
		Where("user_id = ?", userID).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check profile existence")
		return false, fmt.Errorf("failed to check profile existence: %w", err)
	}
	return count > 0, nil
}

// UsernameExists checks if username exists
func (r *userProfileRepository) UsernameExists(ctx context.Context, username string, tenantID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserProfile{}).
		Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ? AND user_profiles.username = ?", tenantID, username).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check username existence")
		return false, fmt.Errorf("failed to check username existence: %w", err)
	}
	return count > 0, nil
}

// UsernameExistsExcludingUser checks if username exists excluding a specific user
func (r *userProfileRepository) UsernameExistsExcludingUser(ctx context.Context, username string, tenantID uint, excludeUserID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserProfile{}).
		Joins("LEFT JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ? AND user_profiles.username = ? AND users.id != ?", tenantID, username, excludeUserID).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check username existence excluding user")
		return false, fmt.Errorf("failed to check username existence excluding user: %w", err)
	}
	return count > 0, nil
}

// GetPopularSkills gets popular skills for a tenant
func (r *userProfileRepository) GetPopularSkills(ctx context.Context, tenantID uint, limit int) ([]string, error) {
	var skills []string

	// This is a simplified implementation - in a real system, you'd parse the JSON skills field
	// For now, we'll return some example skills
	err := r.db.WithContext(ctx).
		Raw(`
			SELECT JSON_UNQUOTE(JSON_EXTRACT(skills, '$[*]')) as skill
			FROM user_profiles 
			JOIN users ON user_profiles.user_id = users.id
			WHERE users.tenant_id = ? AND users.status != ?
			AND skills IS NOT NULL AND skills != 'null'
			GROUP BY skill
			ORDER BY COUNT(*) DESC
			LIMIT ?
		`, tenantID, models.UserStatusDeleted, limit).
		Pluck("skill", &skills).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to get popular skills")
		// Return some default skills if query fails
		return []string{"JavaScript", "Python", "Go", "React", "Node.js"}, nil
	}

	return skills, nil
}

// GetPopularLocations gets popular locations for a tenant
func (r *userProfileRepository) GetPopularLocations(ctx context.Context, tenantID uint, limit int) ([]string, error) {
	var locations []string

	err := r.db.WithContext(ctx).
		Model(&models.UserProfile{}).
		Joins("JOIN users ON user_profiles.user_id = users.id").
		Where("users.tenant_id = ? AND users.status != ? AND user_profiles.location IS NOT NULL", tenantID, models.UserStatusDeleted).
		Group("user_profiles.location").
		Order("COUNT(*) DESC").
		Limit(limit).
		Pluck("user_profiles.location", &locations).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to get popular locations")
		// Return some default locations if query fails
		return []string{"New York", "San Francisco", "London", "Berlin", "Tokyo"}, nil
	}

	return locations, nil
}
