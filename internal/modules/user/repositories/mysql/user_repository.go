package mysql

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// userRepository implements the UserRepository interface using GORM
type userRepository struct {
	db     *gorm.DB
	logger utils.Logger
}

// NewUserRepository creates a new instance of UserRepository
func NewUserRepository(db *gorm.DB, logger utils.Logger) repositories.UserRepository {
	return &userRepository{
		db:     db,
		logger: logger,
	}
}

// Create creates a new user
func (r *userRepository) Create(ctx context.Context, user *models.User) error {
	if err := r.db.WithContext(ctx).Create(user).Error; err != nil {
		r.logger.WithError(err).Error("Failed to create user")
		return fmt.Errorf("failed to create user: %w", err)
	}
	return nil
}

// GetByID retrieves a user by ID
func (r *userRepository) GetByID(ctx context.Context, id uint) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).
		Preload("Profile").
		Preload("Preferences").
		Preload("SocialLinks", func(db *gorm.DB) *gorm.DB {
			return db.Order("display_order ASC")
		}).
		First(&user, id).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		r.logger.WithError(err).Error("Failed to get user by ID")
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return &user, nil
}

// GetByEmail retrieves a user by email (global lookup)
func (r *userRepository) GetByEmail(ctx context.Context, email string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).
		Where("email = ? AND status != ?", email, models.UserStatusDeleted).
		Preload("Profile").
		Preload("Preferences").
		Preload("SocialLinks", func(db *gorm.DB) *gorm.DB {
			return db.Order("display_order ASC")
		}).
		Preload("TenantMemberships").
		First(&user).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		r.logger.WithError(err).Error("Failed to get user by email")
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return &user, nil
}

// GetByUsername retrieves a user by username (global lookup)
func (r *userRepository) GetByUsername(ctx context.Context, username string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).
		Where("username = ? AND status != ?", username, models.UserStatusDeleted).
		Preload("Profile").
		Preload("Preferences").
		Preload("SocialLinks", func(db *gorm.DB) *gorm.DB {
			return db.Order("display_order ASC")
		}).
		Preload("TenantMemberships").
		First(&user).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		r.logger.WithError(err).Error("Failed to get user by username")
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return &user, nil
}

// Update updates a user
func (r *userRepository) Update(ctx context.Context, user *models.User) error {
	if err := r.db.WithContext(ctx).Save(user).Error; err != nil {
		r.logger.WithError(err).Error("Failed to update user")
		return fmt.Errorf("failed to update user: %w", err)
	}
	return nil
}

// Delete permanently deletes a user
func (r *userRepository) Delete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Delete(&models.User{}, id).Error; err != nil {
		r.logger.WithError(err).Error("Failed to delete user")
		return fmt.Errorf("failed to delete user: %w", err)
	}
	return nil
}

// SoftDelete marks a user as deleted
func (r *userRepository) SoftDelete(ctx context.Context, id uint) error {
	err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("id = ?", id).
		Update("status", models.UserStatusDeleted).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to soft delete user")
		return fmt.Errorf("failed to soft delete user: %w", err)
	}
	return nil
}

// Restore restores a soft-deleted user
func (r *userRepository) Restore(ctx context.Context, id uint) error {
	err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("id = ?", id).
		Update("status", models.UserStatusActive).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to restore user")
		return fmt.Errorf("failed to restore user: %w", err)
	}
	return nil
}

// List retrieves users with pagination (global listing)
func (r *userRepository) List(ctx context.Context, pag *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error) {
	var users []models.User
	query := r.db.WithContext(ctx).
		Where("status != ?", models.UserStatusDeleted).
		Preload("Profile").
		Preload("Preferences").
		Preload("TenantMemberships").
		Preload("SocialLinks", func(db *gorm.DB) *gorm.DB {
			return db.Order("display_order ASC")
		})

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("id > ?", pag.Cursor)
	}

	query = query.Order("id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&users).Error; err != nil {
		r.logger.WithError(err).Error("Failed to list users")
		return nil, nil, fmt.Errorf("failed to list users: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(users) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		users = users[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", users[len(users)-1].ID)
	}

	return users, response, nil
}

// ListByStatus retrieves users by status with pagination (global listing)
func (r *userRepository) ListByStatus(ctx context.Context, status models.UserStatus, pag *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error) {
	var users []models.User
	query := r.db.WithContext(ctx).
		Where("status = ?", status).
		Preload("Profile").
		Preload("Preferences").
		Preload("TenantMemberships").
		Preload("SocialLinks", func(db *gorm.DB) *gorm.DB {
			return db.Order("display_order ASC")
		})

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("id > ?", pag.Cursor)
	}

	query = query.Order("id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&users).Error; err != nil {
		r.logger.WithError(err).Error("Failed to list users by status")
		return nil, nil, fmt.Errorf("failed to list users by status: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(users) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		users = users[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", users[len(users)-1].ID)
	}

	return users, response, nil
}

// ListByRole retrieves users by role with pagination
func (r *userRepository) ListByRole(ctx context.Context, tenantID uint, role models.UserRole, pag *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error) {
	var users []models.User
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND role = ? AND status != ?", tenantID, role, models.UserStatusDeleted).
		Preload("Profile").
		Preload("Preferences").
		Preload("SocialLinks", func(db *gorm.DB) *gorm.DB {
			return db.Order("display_order ASC")
		})

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("id > ?", pag.Cursor)
	}

	query = query.Order("id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&users).Error; err != nil {
		r.logger.WithError(err).Error("Failed to list users by role")
		return nil, nil, fmt.Errorf("failed to list users by role: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(users) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		users = users[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", users[len(users)-1].ID)
	}

	return users, response, nil
}

// Search searches users by query
func (r *userRepository) Search(ctx context.Context, query string, pag *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error) {
	var users []models.User

	searchQuery := "%" + strings.ToLower(query) + "%"
	dbQuery := r.db.WithContext(ctx).
		Where("status != ?", models.UserStatusDeleted).
		Where("(LOWER(email) LIKE ? OR LOWER(first_name) LIKE ? OR LOWER(last_name) LIKE ? OR LOWER(display_name) LIKE ? OR LOWER(username) LIKE ?)",
			searchQuery, searchQuery, searchQuery, searchQuery, searchQuery).
		Preload("Profile").
		Preload("Preferences").
		Preload("TenantMemberships").
		Preload("SocialLinks", func(db *gorm.DB) *gorm.DB {
			return db.Order("display_order ASC")
		})

	// Apply cursor pagination
	if pag.Cursor != "" {
		dbQuery = dbQuery.Where("id > ?", pag.Cursor)
	}

	dbQuery = dbQuery.Order("id ASC").Limit(pag.Limit + 1)

	if err := dbQuery.Find(&users).Error; err != nil {
		r.logger.WithError(err).Error("Failed to search users")
		return nil, nil, fmt.Errorf("failed to search users: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(users) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		users = users[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", users[len(users)-1].ID)
	}

	return users, response, nil
}

// SearchBySkills searches users by skills (global search)
func (r *userRepository) SearchBySkills(ctx context.Context, skills []string, pag *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error) {
	var users []models.User

	// Build skills search query
	skillsQuery := make([]string, len(skills))
	for i, skill := range skills {
		skillsQuery[i] = fmt.Sprintf("JSON_SEARCH(user_profiles.skills, 'one', '%%%s%%') IS NOT NULL", skill)
	}

	dbQuery := r.db.WithContext(ctx).
		Joins("LEFT JOIN user_profiles ON users.id = user_profiles.user_id").
		Where("users.status != ?", models.UserStatusDeleted).
		Where(strings.Join(skillsQuery, " OR ")).
		Preload("Profile").
		Preload("TenantMemberships").
		Preload("Preferences").
		Preload("SocialLinks", func(db *gorm.DB) *gorm.DB {
			return db.Order("display_order ASC")
		})

	// Apply cursor pagination
	if pag.Cursor != "" {
		dbQuery = dbQuery.Where("users.id > ?", pag.Cursor)
	}

	dbQuery = dbQuery.Order("users.id ASC").Limit(pag.Limit + 1)

	if err := dbQuery.Find(&users).Error; err != nil {
		r.logger.WithError(err).Error("Failed to search users by skills")
		return nil, nil, fmt.Errorf("failed to search users by skills: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(users) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		users = users[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", users[len(users)-1].ID)
	}

	return users, response, nil
}

// SearchByLocation searches users by location
func (r *userRepository) SearchByLocation(ctx context.Context, location string, pag *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error) {
	var users []models.User

	locationQuery := "%" + strings.ToLower(location) + "%"
	dbQuery := r.db.WithContext(ctx).
		Joins("LEFT JOIN user_profiles ON users.id = user_profiles.user_id").
		Where("users.status != ?", models.UserStatusDeleted).
		Where("(LOWER(user_profiles.location) LIKE ? OR LOWER(user_profiles.city) LIKE ? OR LOWER(user_profiles.state) LIKE ? OR LOWER(user_profiles.country) LIKE ?)",
			locationQuery, locationQuery, locationQuery, locationQuery).
		Preload("Profile").
		Preload("Preferences").
		Preload("TenantMemberships").
		Preload("SocialLinks", func(db *gorm.DB) *gorm.DB {
			return db.Order("display_order ASC")
		})

	// Apply cursor pagination
	if pag.Cursor != "" {
		dbQuery = dbQuery.Where("users.id > ?", pag.Cursor)
	}

	dbQuery = dbQuery.Order("users.id ASC").Limit(pag.Limit + 1)

	if err := dbQuery.Find(&users).Error; err != nil {
		r.logger.WithError(err).Error("Failed to search users by location")
		return nil, nil, fmt.Errorf("failed to search users by location: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(users) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		users = users[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", users[len(users)-1].ID)
	}

	return users, response, nil
}

// GetByEmailForAuth retrieves a user by email for authentication (includes password hash)
func (r *userRepository) GetByEmailForAuth(ctx context.Context, email string) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).
		Where("email = ? AND status != ?", email, models.UserStatusDeleted).
		Preload("TenantMemberships").
		First(&user).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		r.logger.WithError(err).Error("Failed to get user by email for auth")
		return nil, fmt.Errorf("failed to get user for auth: %w", err)
	}

	return &user, nil
}

// UpdatePassword updates user password
func (r *userRepository) UpdatePassword(ctx context.Context, userID uint, passwordHash string) error {
	err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("id = ?", userID).
		Update("password_hash", passwordHash).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update user password")
		return fmt.Errorf("failed to update password: %w", err)
	}
	return nil
}

// UpdateEmailVerification updates email verification status
func (r *userRepository) UpdateEmailVerification(ctx context.Context, userID uint, verified bool) error {
	updates := map[string]interface{}{
		"email_verified": verified,
	}

	if verified {
		updates["email_verified_at"] = time.Now()
	} else {
		updates["email_verified_at"] = nil
	}

	err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("id = ?", userID).
		Updates(updates).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update email verification")
		return fmt.Errorf("failed to update email verification: %w", err)
	}
	return nil
}

// UpdatePhoneVerification updates phone verification status
func (r *userRepository) UpdatePhoneVerification(ctx context.Context, userID uint, verified bool) error {
	updates := map[string]interface{}{
		"phone_verified": verified,
	}

	if verified {
		updates["phone_verified_at"] = time.Now()
	} else {
		updates["phone_verified_at"] = nil
	}

	err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("id = ?", userID).
		Updates(updates).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update phone verification")
		return fmt.Errorf("failed to update phone verification: %w", err)
	}
	return nil
}

// UpdateLoginInfo updates user login information
func (r *userRepository) UpdateLoginInfo(ctx context.Context, userID uint, ip string) error {
	now := time.Now()
	err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("id = ?", userID).
		Updates(map[string]interface{}{
			"last_login_at": now,
			"last_login_ip": ip,
			"login_count":   gorm.Expr("login_count + 1"),
		}).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update login info")
		return fmt.Errorf("failed to update login info: %w", err)
	}
	return nil
}

// EnableTwoFactor enables two-factor authentication
func (r *userRepository) EnableTwoFactor(ctx context.Context, userID uint, secret string) error {
	err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("id = ?", userID).
		Updates(map[string]interface{}{
			"two_factor_enabled": true,
			"two_factor_secret":  secret,
		}).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to enable two-factor auth")
		return fmt.Errorf("failed to enable two-factor auth: %w", err)
	}
	return nil
}

// DisableTwoFactor disables two-factor authentication
func (r *userRepository) DisableTwoFactor(ctx context.Context, userID uint) error {
	err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("id = ?", userID).
		Updates(map[string]interface{}{
			"two_factor_enabled": false,
			"two_factor_secret":  nil,
			"recovery_codes":     "[]",
		}).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to disable two-factor auth")
		return fmt.Errorf("failed to disable two-factor auth: %w", err)
	}
	return nil
}

// UpdateRecoveryCodes updates recovery codes
func (r *userRepository) UpdateRecoveryCodes(ctx context.Context, userID uint, codes []string) error {
	codesJSON, err := json.Marshal(codes)
	if err != nil {
		return fmt.Errorf("failed to marshal recovery codes: %w", err)
	}

	err = r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("id = ?", userID).
		Update("recovery_codes", string(codesJSON)).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update recovery codes")
		return fmt.Errorf("failed to update recovery codes: %w", err)
	}
	return nil
}

// UpdateStatus updates user status
func (r *userRepository) UpdateStatus(ctx context.Context, userID uint, status models.UserStatus) error {
	err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("id = ?", userID).
		Update("status", status).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update user status")
		return fmt.Errorf("failed to update user status: %w", err)
	}
	return nil
}

// GetActiveUsers retrieves active users
func (r *userRepository) GetActiveUsers(ctx context.Context, pag *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error) {
	return r.ListByStatus(ctx, models.UserStatusActive, pag)
}

// GetInactiveUsers retrieves inactive users
func (r *userRepository) GetInactiveUsers(ctx context.Context, pag *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error) {
	return r.ListByStatus(ctx, models.UserStatusInactive, pag)
}

// CountByTenant counts users by tenant
// Count counts all users (global count)
func (r *userRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("status != ?", models.UserStatusDeleted).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count users")
		return 0, fmt.Errorf("failed to count users: %w", err)
	}
	return count, nil
}

// CountByStatus counts users by status (global count)
func (r *userRepository) CountByStatus(ctx context.Context, status models.UserStatus) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("status = ?", status).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count users by status")
		return 0, fmt.Errorf("failed to count users by status: %w", err)
	}
	return count, nil
}

// CountByRole is deprecated - roles are now handled through tenant memberships
// This method is kept for backward compatibility but returns 0

// BulkUpdateStatus updates status for multiple users
func (r *userRepository) BulkUpdateStatus(ctx context.Context, userIDs []uint, status models.UserStatus) error {
	err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("id IN ?", userIDs).
		Update("status", status).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to bulk update user status")
		return fmt.Errorf("failed to bulk update user status: %w", err)
	}
	return nil
}

// BulkDelete deletes multiple users
func (r *userRepository) BulkDelete(ctx context.Context, userIDs []uint) error {
	err := r.db.WithContext(ctx).
		Delete(&models.User{}, userIDs).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to bulk delete users")
		return fmt.Errorf("failed to bulk delete users: %w", err)
	}
	return nil
}

// GetUsersByTenant retrieves users by tenant
func (r *userRepository) GetUsersByTenant(ctx context.Context, tenantID uint, pag *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error) {
	return r.List(ctx, pag)
}

// TransferToTenant transfers user to another tenant - deprecated in multi-tenant architecture
func (r *userRepository) TransferToTenant(ctx context.Context, userID uint, newTenantID uint) error {
	// This operation is deprecated in the new multi-tenant architecture
	// Users now belong to multiple tenants through tenant memberships
	return fmt.Errorf("operation not supported in multi-tenant architecture")
}

// GetRecentlyActive retrieves recently active users
func (r *userRepository) GetRecentlyActive(ctx context.Context, limit int) ([]models.User, error) {
	var users []models.User
	err := r.db.WithContext(ctx).
		Where("status = ? AND last_login_at IS NOT NULL", models.UserStatusActive).
		Order("last_login_at DESC").
		Limit(limit).
		Preload("TenantMemberships").
		Find(&users).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to get recently active users")
		return nil, fmt.Errorf("failed to get recently active users: %w", err)
	}
	return users, nil
}

// GetUserActivity retrieves user activity
func (r *userRepository) GetUserActivity(ctx context.Context, userID uint) (*models.User, error) {
	var user models.User
	err := r.db.WithContext(ctx).
		Select("id, last_login_at, last_login_ip, login_count").
		First(&user, userID).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user not found")
		}
		r.logger.WithError(err).Error("Failed to get user activity")
		return nil, fmt.Errorf("failed to get user activity: %w", err)
	}

	return &user, nil
}

// EmailExists checks if email exists
func (r *userRepository) EmailExists(ctx context.Context, email string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("email = ? AND status != ?", email, models.UserStatusDeleted).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check email existence")
		return false, fmt.Errorf("failed to check email existence: %w", err)
	}
	return count > 0, nil
}

// UsernameExists checks if username exists (global check)
func (r *userRepository) UsernameExists(ctx context.Context, username string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("username = ? AND status != ?", username, models.UserStatusDeleted).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check username existence")
		return false, fmt.Errorf("failed to check username existence: %w", err)
	}
	return count > 0, nil
}

// EmailExistsExcludingUser checks if email exists excluding a specific user
func (r *userRepository) EmailExistsExcludingUser(ctx context.Context, email string, excludeUserID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("email = ? AND id != ? AND status != ?", email, excludeUserID, models.UserStatusDeleted).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check email existence excluding user")
		return false, fmt.Errorf("failed to check email existence excluding user: %w", err)
	}
	return count > 0, nil
}

// UsernameExistsExcludingUser checks if username exists excluding a specific user (global check)
func (r *userRepository) UsernameExistsExcludingUser(ctx context.Context, username string, excludeUserID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("username = ? AND id != ? AND status != ?", username, excludeUserID, models.UserStatusDeleted).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check username existence excluding user")
		return false, fmt.Errorf("failed to check username existence excluding user: %w", err)
	}
	return count > 0, nil
}

// SearchByCompany searches users by company with pagination
func (r *userRepository) SearchByCompany(ctx context.Context, company string, pag *pagination.CursorPagination) ([]models.User, *pagination.CursorResponse, error) {
	var users []models.User
	query := r.db.WithContext(ctx).
		Table("users").
		Joins("LEFT JOIN user_profiles ON users.id = user_profiles.user_id").
		Where("users.status != ?", models.UserStatusDeleted).
		Where("user_profiles.company LIKE ?", "%"+company+"%").
		Order("users.created_at DESC")

	// Apply cursor pagination
	if pag.Cursor != "" {
		cursorData, err := pagination.ParseCursor(pag.Cursor)
		if err != nil {
			return nil, nil, fmt.Errorf("invalid cursor: %w", err)
		}
		query = query.Where("users.created_at < ? OR (users.created_at = ? AND users.id < ?)",
			cursorData.CreatedAt, cursorData.CreatedAt, cursorData.ID)
	}

	if pag.Limit > 0 {
		query = query.Limit(pag.Limit + 1) // +1 to check if there are more results
	}

	if err := query.Find(&users).Error; err != nil {
		r.logger.WithError(err).Error("Failed to search users by company")
		return nil, nil, fmt.Errorf("failed to search users by company: %w", err)
	}

	// Build pagination response
	hasNext := len(users) > pag.Limit
	if hasNext {
		users = users[:pag.Limit]
	}

	var nextCursor string
	if hasNext && len(users) > 0 {
		lastUser := users[len(users)-1]
		nextCursor, _ = pagination.EncodeCursor(lastUser.ID, lastUser.CreatedAt)
	}

	paginationResponse := &pagination.CursorResponse{
		HasNext:    hasNext,
		NextCursor: nextCursor,
		Count:      len(users),
		HasMore:    hasNext,
		Limit:      pag.Limit,
	}

	return users, paginationResponse, nil
}

// GetSearchSuggestions gets search suggestions for users
func (r *userRepository) GetSearchSuggestions(ctx context.Context, query string, limit int) ([]string, error) {
	var suggestions []string

	// Search for email suggestions
	var emails []string
	if err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("status != ? AND email LIKE ?", models.UserStatusDeleted, "%"+query+"%").
		Limit(limit/2).
		Pluck("email", &emails).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get email suggestions")
		return nil, fmt.Errorf("failed to get email suggestions: %w", err)
	}

	suggestions = append(suggestions, emails...)

	// Search for username suggestions
	var usernames []string
	if err := r.db.WithContext(ctx).
		Model(&models.User{}).
		Where("status != ? AND username LIKE ?", models.UserStatusDeleted, "%"+query+"%").
		Limit(limit/2).
		Pluck("username", &usernames).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get username suggestions")
		return nil, fmt.Errorf("failed to get username suggestions: %w", err)
	}

	suggestions = append(suggestions, usernames...)

	// Limit results
	if len(suggestions) > limit {
		suggestions = suggestions[:limit]
	}

	return suggestions, nil
}
