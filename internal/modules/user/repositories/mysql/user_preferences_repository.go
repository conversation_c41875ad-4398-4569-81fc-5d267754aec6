package mysql

import (
	"context"
	"encoding/json"
	"fmt"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// userPreferencesRepository implements the UserPreferencesRepository interface using GORM
type userPreferencesRepository struct {
	db     *gorm.DB
	logger utils.Logger
}

// NewUserPreferencesRepository creates a new instance of UserPreferencesRepository
func NewUserPreferencesRepository(db *gorm.DB, logger utils.Logger) repositories.UserPreferencesRepository {
	return &userPreferencesRepository{
		db:     db,
		logger: logger,
	}
}

// Create creates a new user preferences
func (r *userPreferencesRepository) Create(ctx context.Context, preferences *models.UserPreferences) error {
	if err := r.db.WithContext(ctx).Create(preferences).Error; err != nil {
		r.logger.WithError(err).Error("Failed to create user preferences")
		return fmt.Errorf("failed to create user preferences: %w", err)
	}
	return nil
}

// GetByID retrieves user preferences by ID
func (r *userPreferencesRepository) GetByID(ctx context.Context, id uint) (*models.UserPreferences, error) {
	var preferences models.UserPreferences
	err := r.db.WithContext(ctx).
		Preload("User").
		First(&preferences, id).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("preferences not found")
		}
		r.logger.WithError(err).Error("Failed to get user preferences by ID")
		return nil, fmt.Errorf("failed to get user preferences: %w", err)
	}

	return &preferences, nil
}

// GetByUserID retrieves user preferences by user ID
func (r *userPreferencesRepository) GetByUserID(ctx context.Context, userID uint) (*models.UserPreferences, error) {
	var preferences models.UserPreferences
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("user_id = ?", userID).
		First(&preferences).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("preferences not found")
		}
		r.logger.WithError(err).Error("Failed to get user preferences by user ID")
		return nil, fmt.Errorf("failed to get user preferences: %w", err)
	}

	return &preferences, nil
}

// Update updates user preferences
func (r *userPreferencesRepository) Update(ctx context.Context, preferences *models.UserPreferences) error {
	if err := r.db.WithContext(ctx).Save(preferences).Error; err != nil {
		r.logger.WithError(err).Error("Failed to update user preferences")
		return fmt.Errorf("failed to update user preferences: %w", err)
	}
	return nil
}

// Delete permanently deletes user preferences
func (r *userPreferencesRepository) Delete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Delete(&models.UserPreferences{}, id).Error; err != nil {
		r.logger.WithError(err).Error("Failed to delete user preferences")
		return fmt.Errorf("failed to delete user preferences: %w", err)
	}
	return nil
}

// SoftDelete marks user preferences as deleted
func (r *userPreferencesRepository) SoftDelete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Delete(&models.UserPreferences{}, id).Error; err != nil {
		r.logger.WithError(err).Error("Failed to soft delete user preferences")
		return fmt.Errorf("failed to soft delete user preferences: %w", err)
	}
	return nil
}

// Restore restores soft-deleted user preferences
func (r *userPreferencesRepository) Restore(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Unscoped().Model(&models.UserPreferences{}).Where("id = ?", id).Update("deleted_at", nil).Error; err != nil {
		r.logger.WithError(err).Error("Failed to restore user preferences")
		return fmt.Errorf("failed to restore user preferences: %w", err)
	}
	return nil
}

// UpdateNotificationPreferences updates notification preferences
func (r *userPreferencesRepository) UpdateNotificationPreferences(ctx context.Context, userID uint, preferences map[string]interface{}) error {
	updates := make(map[string]interface{})

	if emailNotifications, ok := preferences["email_notifications"]; ok {
		updates["email_notifications"] = emailNotifications
	}
	if pushNotifications, ok := preferences["push_notifications"]; ok {
		updates["push_notifications"] = pushNotifications
	}
	if smsNotifications, ok := preferences["sms_notifications"]; ok {
		updates["sms_notifications"] = smsNotifications
	}
	// in_app_notifications field doesn't exist in model - skip

	if len(updates) == 0 {
		return nil
	}

	err := r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Where("user_id = ?", userID).
		Updates(updates).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update notification preferences")
		return fmt.Errorf("failed to update notification preferences: %w", err)
	}
	return nil
}

// GetNotificationPreferences retrieves notification preferences
func (r *userPreferencesRepository) GetNotificationPreferences(ctx context.Context, userID uint) (map[string]interface{}, error) {
	var preferences models.UserPreferences
	err := r.db.WithContext(ctx).
		Select("email_notifications, push_notifications, sms_notifications, in_app_notifications").
		Where("user_id = ?", userID).
		First(&preferences).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return make(map[string]interface{}), nil
		}
		r.logger.WithError(err).Error("Failed to get notification preferences")
		return nil, fmt.Errorf("failed to get notification preferences: %w", err)
	}

	return map[string]interface{}{
		"email_notifications":  preferences.EmailNotifications,
		"push_notifications":   preferences.PushNotifications,
		"sms_notifications":    preferences.SMSNotifications,
		"in_app_notifications": true, // Default value since field doesn't exist in model
	}, nil
}

// UpdateNotificationTypes updates notification types
func (r *userPreferencesRepository) UpdateNotificationTypes(ctx context.Context, userID uint, notificationTypes map[string]interface{}) error {
	notificationTypesJSON, err := json.Marshal(notificationTypes)
	if err != nil {
		return fmt.Errorf("failed to marshal notification types: %w", err)
	}

	err = r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Where("user_id = ?", userID).
		Update("notification_types", string(notificationTypesJSON)).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update notification types")
		return fmt.Errorf("failed to update notification types: %w", err)
	}
	return nil
}

// GetNotificationTypes retrieves notification types
func (r *userPreferencesRepository) GetNotificationTypes(ctx context.Context, userID uint) (map[string]interface{}, error) {
	var preferences models.UserPreferences
	err := r.db.WithContext(ctx).
		Select("notification_types").
		Where("user_id = ?", userID).
		First(&preferences).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return make(map[string]interface{}), nil
		}
		r.logger.WithError(err).Error("Failed to get notification types")
		return nil, fmt.Errorf("failed to get notification types: %w", err)
	}

	notificationTypes := preferences.GetNotificationTypes()
	result := make(map[string]interface{})
	for k, v := range notificationTypes {
		result[k] = v
	}
	return result, nil
}

// UpdateCommunicationPreferences updates communication preferences
func (r *userPreferencesRepository) UpdateCommunicationPreferences(ctx context.Context, userID uint, allowMarketing, allowNewsletter, allowSecurity bool) error {
	updates := map[string]interface{}{
		"marketing_emails":        allowMarketing,
		"newsletter_subscription": allowNewsletter,
		"security_alerts":         allowSecurity,
	}

	err := r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Where("user_id = ?", userID).
		Updates(updates).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update communication preferences")
		return fmt.Errorf("failed to update communication preferences: %w", err)
	}
	return nil
}

// GetCommunicationPreferences retrieves communication preferences
func (r *userPreferencesRepository) GetCommunicationPreferences(ctx context.Context, userID uint) (*repositories.CommunicationPreferences, error) {
	var preferences models.UserPreferences
	err := r.db.WithContext(ctx).
		Select("marketing_emails, newsletter_subscription, security_alerts").
		Where("user_id = ?", userID).
		First(&preferences).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return &repositories.CommunicationPreferences{}, nil
		}
		r.logger.WithError(err).Error("Failed to get communication preferences")
		return nil, fmt.Errorf("failed to get communication preferences: %w", err)
	}

	return &repositories.CommunicationPreferences{
		AllowMarketing:  preferences.MarketingEmails,
		AllowNewsletter: preferences.NewsletterSubscription,
		AllowSecurity:   preferences.SecurityAlerts,
	}, nil
}

// UpdatePrivacyPreferences updates privacy preferences
func (r *userPreferencesRepository) UpdatePrivacyPreferences(ctx context.Context, userID uint, visibility models.ProfileVisibility, showOnlineStatus, allowSearchIndexing bool) error {
	updates := map[string]interface{}{
		"profile_visibility": visibility,
		"show_online_status": showOnlineStatus,
		"allow_search":       allowSearchIndexing,
	}

	err := r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Where("user_id = ?", userID).
		Updates(updates).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update privacy preferences")
		return fmt.Errorf("failed to update privacy preferences: %w", err)
	}
	return nil
}

// GetPrivacyPreferences retrieves privacy preferences
func (r *userPreferencesRepository) GetPrivacyPreferences(ctx context.Context, userID uint) (*repositories.PrivacyPreferences, error) {
	var preferences models.UserPreferences
	err := r.db.WithContext(ctx).
		Select("profile_visibility, show_online_status, allow_search").
		Where("user_id = ?", userID).
		First(&preferences).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return &repositories.PrivacyPreferences{}, nil
		}
		r.logger.WithError(err).Error("Failed to get privacy preferences")
		return nil, fmt.Errorf("failed to get privacy preferences: %w", err)
	}

	return &repositories.PrivacyPreferences{
		ProfileVisibility:   preferences.ProfileVisibility,
		ShowOnlineStatus:    preferences.ShowOnlineStatus,
		AllowSearchIndexing: preferences.AllowSearch,
	}, nil
}

// UpdateUIPreferences updates UI preferences
func (r *userPreferencesRepository) UpdateUIPreferences(ctx context.Context, userID uint, theme models.Theme, layout string, itemsPerPage uint) error {
	updates := map[string]interface{}{
		"theme":            theme,
		"dashboard_layout": layout,
		"items_per_page":   itemsPerPage,
	}

	err := r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Where("user_id = ?", userID).
		Updates(updates).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update UI preferences")
		return fmt.Errorf("failed to update UI preferences: %w", err)
	}
	return nil
}

// GetUIPreferences retrieves UI preferences
func (r *userPreferencesRepository) GetUIPreferences(ctx context.Context, userID uint) (*repositories.UIPreferences, error) {
	var preferences models.UserPreferences
	err := r.db.WithContext(ctx).
		Select("theme, dashboard_layout, items_per_page").
		Where("user_id = ?", userID).
		First(&preferences).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return &repositories.UIPreferences{}, nil
		}
		r.logger.WithError(err).Error("Failed to get UI preferences")
		return nil, fmt.Errorf("failed to get UI preferences: %w", err)
	}

	return &repositories.UIPreferences{
		Theme:           preferences.Theme,
		DashboardLayout: preferences.DashboardLayout,
		ItemsPerPage:    preferences.ItemsPerPage,
	}, nil
}

// UpdateApplicationPreferences updates application preferences
func (r *userPreferencesRepository) UpdateApplicationPreferences(ctx context.Context, userID uint, autoSave, keyboardShortcuts, showTooltips bool) error {
	updates := map[string]interface{}{
		"auto_save":          autoSave,
		"keyboard_shortcuts": keyboardShortcuts,
		"tooltips_enabled":   showTooltips,
	}

	err := r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Where("user_id = ?", userID).
		Updates(updates).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update application preferences")
		return fmt.Errorf("failed to update application preferences: %w", err)
	}
	return nil
}

// GetApplicationPreferences retrieves application preferences
func (r *userPreferencesRepository) GetApplicationPreferences(ctx context.Context, userID uint) (*repositories.ApplicationPreferences, error) {
	var preferences models.UserPreferences
	err := r.db.WithContext(ctx).
		Select("auto_save, keyboard_shortcuts, tooltips_enabled").
		Where("user_id = ?", userID).
		First(&preferences).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return &repositories.ApplicationPreferences{}, nil
		}
		r.logger.WithError(err).Error("Failed to get application preferences")
		return nil, fmt.Errorf("failed to get application preferences: %w", err)
	}

	return &repositories.ApplicationPreferences{
		AutoSave:          preferences.AutoSave,
		KeyboardShortcuts: preferences.KeyboardShortcuts,
		ShowTooltips:      preferences.TooltipsEnabled,
	}, nil
}

// UpdateFeaturePreferences updates feature preferences
func (r *userPreferencesRepository) UpdateFeaturePreferences(ctx context.Context, userID uint, featurePreferences map[string]interface{}) error {
	featurePreferencesJSON, err := json.Marshal(featurePreferences)
	if err != nil {
		return fmt.Errorf("failed to marshal feature preferences: %w", err)
	}

	err = r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Where("user_id = ?", userID).
		Update("feature_preferences", string(featurePreferencesJSON)).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update feature preferences")
		return fmt.Errorf("failed to update feature preferences: %w", err)
	}
	return nil
}

// GetFeaturePreferences retrieves feature preferences
func (r *userPreferencesRepository) GetFeaturePreferences(ctx context.Context, userID uint) (map[string]interface{}, error) {
	var preferences models.UserPreferences
	err := r.db.WithContext(ctx).
		Select("feature_preferences").
		Where("user_id = ?", userID).
		First(&preferences).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return make(map[string]interface{}), nil
		}
		r.logger.WithError(err).Error("Failed to get feature preferences")
		return nil, fmt.Errorf("failed to get feature preferences: %w", err)
	}

	return preferences.GetFeaturePreferences(), nil
}

// UpdateCustomPreferences updates custom preferences
func (r *userPreferencesRepository) UpdateCustomPreferences(ctx context.Context, userID uint, customPreferences map[string]interface{}) error {
	customPreferencesJSON, err := json.Marshal(customPreferences)
	if err != nil {
		return fmt.Errorf("failed to marshal custom preferences: %w", err)
	}

	err = r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Where("user_id = ?", userID).
		Update("custom_preferences", string(customPreferencesJSON)).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update custom preferences")
		return fmt.Errorf("failed to update custom preferences: %w", err)
	}
	return nil
}

// GetCustomPreferences retrieves custom preferences
func (r *userPreferencesRepository) GetCustomPreferences(ctx context.Context, userID uint) (map[string]interface{}, error) {
	var preferences models.UserPreferences
	err := r.db.WithContext(ctx).
		Select("custom_preferences").
		Where("user_id = ?", userID).
		First(&preferences).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return make(map[string]interface{}), nil
		}
		r.logger.WithError(err).Error("Failed to get custom preferences")
		return nil, fmt.Errorf("failed to get custom preferences: %w", err)
	}

	return preferences.GetCustomPreferences(), nil
}

// BulkUpdateTheme updates theme for multiple users
func (r *userPreferencesRepository) BulkUpdateTheme(ctx context.Context, userIDs []uint, theme models.Theme) error {
	err := r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Where("user_id IN ?", userIDs).
		Update("theme", theme).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to bulk update theme")
		return fmt.Errorf("failed to bulk update theme: %w", err)
	}
	return nil
}

// BulkUpdateLanguage updates language for multiple users
func (r *userPreferencesRepository) BulkUpdateLanguage(ctx context.Context, userIDs []uint, language string) error {
	err := r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Where("user_id IN ?", userIDs).
		Update("language", language).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to bulk update language")
		return fmt.Errorf("failed to bulk update language: %w", err)
	}
	return nil
}

// BulkUpdateTimezone updates timezone for multiple users
func (r *userPreferencesRepository) BulkUpdateTimezone(ctx context.Context, userIDs []uint, timezone string) error {
	err := r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Where("user_id IN ?", userIDs).
		Update("timezone", timezone).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to bulk update timezone")
		return fmt.Errorf("failed to bulk update timezone: %w", err)
	}
	return nil
}

// BulkUpdateNotificationSettings updates notification settings for multiple users
func (r *userPreferencesRepository) BulkUpdateNotificationSettings(ctx context.Context, userIDs []uint, settings map[string]interface{}) error {
	updates := make(map[string]interface{})

	if emailNotifications, ok := settings["email_notifications"]; ok {
		updates["email_notifications"] = emailNotifications
	}
	if pushNotifications, ok := settings["push_notifications"]; ok {
		updates["push_notifications"] = pushNotifications
	}
	if smsNotifications, ok := settings["sms_notifications"]; ok {
		updates["sms_notifications"] = smsNotifications
	}
	if inAppNotifications, ok := settings["in_app_notifications"]; ok {
		updates["in_app_notifications"] = inAppNotifications
	}

	if len(updates) == 0 {
		return nil
	}

	err := r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Where("user_id IN ?", userIDs).
		Updates(updates).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to bulk update notification settings")
		return fmt.Errorf("failed to bulk update notification settings: %w", err)
	}
	return nil
}

// ListByTenant retrieves preferences by tenant
func (r *userPreferencesRepository) ListByTenant(ctx context.Context, tenantID uint, pag *pagination.CursorPagination) ([]models.UserPreferences, *pagination.CursorResponse, error) {
	var preferences []models.UserPreferences
	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_preferences.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_preferences.id > ?", pag.Cursor)
	}

	query = query.Order("user_preferences.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&preferences).Error; err != nil {
		r.logger.WithError(err).Error("Failed to list preferences by tenant")
		return nil, nil, fmt.Errorf("failed to list preferences by tenant: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(preferences) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		preferences = preferences[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", preferences[len(preferences)-1].ID)
	}

	return preferences, response, nil
}

// ListByTheme retrieves preferences by theme
func (r *userPreferencesRepository) ListByTheme(ctx context.Context, tenantID uint, theme models.Theme, pag *pagination.CursorPagination) ([]models.UserPreferences, *pagination.CursorResponse, error) {
	var preferences []models.UserPreferences
	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_preferences.user_id = users.id").
		Where("users.tenant_id = ? AND user_preferences.theme = ?", tenantID, theme).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_preferences.id > ?", pag.Cursor)
	}

	query = query.Order("user_preferences.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&preferences).Error; err != nil {
		r.logger.WithError(err).Error("Failed to list preferences by theme")
		return nil, nil, fmt.Errorf("failed to list preferences by theme: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(preferences) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		preferences = preferences[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", preferences[len(preferences)-1].ID)
	}

	return preferences, response, nil
}

// ListByLanguage retrieves preferences by language
func (r *userPreferencesRepository) ListByLanguage(ctx context.Context, tenantID uint, language string, pag *pagination.CursorPagination) ([]models.UserPreferences, *pagination.CursorResponse, error) {
	var preferences []models.UserPreferences
	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_preferences.user_id = users.id").
		Where("users.tenant_id = ? AND user_preferences.language = ?", tenantID, language).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_preferences.id > ?", pag.Cursor)
	}

	query = query.Order("user_preferences.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&preferences).Error; err != nil {
		r.logger.WithError(err).Error("Failed to list preferences by language")
		return nil, nil, fmt.Errorf("failed to list preferences by language: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(preferences) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		preferences = preferences[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", preferences[len(preferences)-1].ID)
	}

	return preferences, response, nil
}

// ListByTimezone retrieves preferences by timezone
func (r *userPreferencesRepository) ListByTimezone(ctx context.Context, tenantID uint, timezone string, pag *pagination.CursorPagination) ([]models.UserPreferences, *pagination.CursorResponse, error) {
	var preferences []models.UserPreferences
	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_preferences.user_id = users.id").
		Where("users.tenant_id = ? AND user_preferences.timezone = ?", tenantID, timezone).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_preferences.id > ?", pag.Cursor)
	}

	query = query.Order("user_preferences.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&preferences).Error; err != nil {
		r.logger.WithError(err).Error("Failed to list preferences by timezone")
		return nil, nil, fmt.Errorf("failed to list preferences by timezone: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(preferences) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		preferences = preferences[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", preferences[len(preferences)-1].ID)
	}

	return preferences, response, nil
}

// GetPreferencesStats retrieves preferences statistics
func (r *userPreferencesRepository) GetPreferencesStats(ctx context.Context, tenantID uint) (*repositories.PreferencesStats, error) {
	var stats repositories.PreferencesStats

	// Get total preferences count
	err := r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Joins("LEFT JOIN users ON user_preferences.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Count(&stats.TotalPreferences).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to get total preferences count")
		return nil, fmt.Errorf("failed to get total preferences count: %w", err)
	}

	if stats.TotalPreferences == 0 {
		return &stats, nil
	}

	// Get theme distribution
	var themeStats []struct {
		Theme models.Theme `json:"theme"`
		Count int64        `json:"count"`
	}
	err = r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Joins("LEFT JOIN users ON user_preferences.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Select("theme, COUNT(*) as count").
		Group("theme").
		Scan(&themeStats).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to get theme distribution")
		return nil, fmt.Errorf("failed to get theme distribution: %w", err)
	}

	stats.ThemeDistribution = make(map[models.Theme]int64)
	for _, themeStat := range themeStats {
		stats.ThemeDistribution[themeStat.Theme] = themeStat.Count
	}

	// Get language distribution
	var languageStats []struct {
		Language string `json:"language"`
		Count    int64  `json:"count"`
	}
	err = r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Joins("LEFT JOIN users ON user_preferences.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Select("language, COUNT(*) as count").
		Group("language").
		Scan(&languageStats).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to get language distribution")
		return nil, fmt.Errorf("failed to get language distribution: %w", err)
	}

	stats.LanguageDistribution = make(map[string]int64)
	for _, languageStat := range languageStats {
		stats.LanguageDistribution[languageStat.Language] = languageStat.Count
	}

	// Get timezone distribution
	var timezoneStats []struct {
		Timezone string `json:"timezone"`
		Count    int64  `json:"count"`
	}
	err = r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Joins("LEFT JOIN users ON user_preferences.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Select("timezone, COUNT(*) as count").
		Group("timezone").
		Scan(&timezoneStats).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to get timezone distribution")
		return nil, fmt.Errorf("failed to get timezone distribution: %w", err)
	}

	stats.TimezoneDistribution = make(map[string]int64)
	for _, timezoneStat := range timezoneStats {
		stats.TimezoneDistribution[timezoneStat.Timezone] = timezoneStat.Count
	}

	return &stats, nil
}

// CountByTenant counts preferences by tenant
func (r *userPreferencesRepository) CountByTenant(ctx context.Context, tenantID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Joins("LEFT JOIN users ON user_preferences.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count preferences by tenant")
		return 0, fmt.Errorf("failed to count preferences by tenant: %w", err)
	}
	return count, nil
}

// CountByTheme counts preferences by theme
func (r *userPreferencesRepository) CountByTheme(ctx context.Context, tenantID uint, theme models.Theme) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Joins("LEFT JOIN users ON user_preferences.user_id = users.id").
		Where("users.tenant_id = ? AND user_preferences.theme = ?", tenantID, theme).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count preferences by theme")
		return 0, fmt.Errorf("failed to count preferences by theme: %w", err)
	}
	return count, nil
}

// CountByLanguage counts preferences by language
func (r *userPreferencesRepository) CountByLanguage(ctx context.Context, tenantID uint, language string) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Joins("LEFT JOIN users ON user_preferences.user_id = users.id").
		Where("users.tenant_id = ? AND user_preferences.language = ?", tenantID, language).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count preferences by language")
		return 0, fmt.Errorf("failed to count preferences by language: %w", err)
	}
	return count, nil
}

// PreferencesExists checks if preferences exist for a user
func (r *userPreferencesRepository) PreferencesExists(ctx context.Context, userID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Where("user_id = ?", userID).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check preferences existence")
		return false, fmt.Errorf("failed to check preferences existence: %w", err)
	}
	return count > 0, nil
}

// ExportPreferences exports all preferences for a user
func (r *userPreferencesRepository) ExportPreferences(ctx context.Context, userID uint) (map[string]interface{}, error) {
	var preferences models.UserPreferences
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		First(&preferences).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return make(map[string]interface{}), nil
		}
		r.logger.WithError(err).Error("Failed to export preferences")
		return nil, fmt.Errorf("failed to export preferences: %w", err)
	}

	return map[string]interface{}{
		"theme":                   preferences.Theme,
		"dashboard_layout":        preferences.DashboardLayout,
		"items_per_page":          preferences.ItemsPerPage,
		"email_notifications":     preferences.EmailNotifications,
		"push_notifications":      preferences.PushNotifications,
		"sms_notifications":       preferences.SMSNotifications,
		"marketing_emails":        preferences.MarketingEmails,
		"newsletter_subscription": preferences.NewsletterSubscription,
		"security_alerts":         preferences.SecurityAlerts,
		"profile_visibility":      preferences.ProfileVisibility,
		"show_online_status":      preferences.ShowOnlineStatus,
		"allow_search":            preferences.AllowSearch,
		"auto_save":               preferences.AutoSave,
		"keyboard_shortcuts":      preferences.KeyboardShortcuts,
		"tooltips_enabled":        preferences.TooltipsEnabled,
		"notification_types":      preferences.GetNotificationTypes(),
		"feature_preferences":     preferences.GetFeaturePreferences(),
		"custom_preferences":      preferences.GetCustomPreferences(),
	}, nil
}

// ImportPreferences imports preferences for a user
func (r *userPreferencesRepository) ImportPreferences(ctx context.Context, userID uint, preferences map[string]interface{}) error {
	updates := make(map[string]interface{})

	// Map the preferences to database fields
	if language, ok := preferences["language"].(string); ok {
		updates["language"] = language
	}
	if timezone, ok := preferences["timezone"].(string); ok {
		updates["timezone"] = timezone
	}
	if theme, ok := preferences["theme"].(string); ok {
		updates["theme"] = theme
	}
	if dashboardLayout, ok := preferences["dashboard_layout"].(string); ok {
		updates["dashboard_layout"] = dashboardLayout
	}
	if itemsPerPage, ok := preferences["items_per_page"].(float64); ok {
		updates["items_per_page"] = uint(itemsPerPage)
	}
	if emailNotifications, ok := preferences["email_notifications"].(bool); ok {
		updates["email_notifications"] = emailNotifications
	}
	if pushNotifications, ok := preferences["push_notifications"].(bool); ok {
		updates["push_notifications"] = pushNotifications
	}
	if smsNotifications, ok := preferences["sms_notifications"].(bool); ok {
		updates["sms_notifications"] = smsNotifications
	}
	if inAppNotifications, ok := preferences["in_app_notifications"].(bool); ok {
		updates["in_app_notifications"] = inAppNotifications
	}
	if allowMarketing, ok := preferences["allow_marketing"].(bool); ok {
		updates["allow_marketing"] = allowMarketing
	}
	if allowNewsletter, ok := preferences["allow_newsletter"].(bool); ok {
		updates["allow_newsletter"] = allowNewsletter
	}
	if allowSecurity, ok := preferences["allow_security"].(bool); ok {
		updates["allow_security"] = allowSecurity
	}
	if profileVisibility, ok := preferences["profile_visibility"].(string); ok {
		updates["profile_visibility"] = profileVisibility
	}
	if showOnlineStatus, ok := preferences["show_online_status"].(bool); ok {
		updates["show_online_status"] = showOnlineStatus
	}
	if allowSearchIndexing, ok := preferences["allow_search_indexing"].(bool); ok {
		updates["allow_search_indexing"] = allowSearchIndexing
	}
	if autoSave, ok := preferences["auto_save"].(bool); ok {
		updates["auto_save"] = autoSave
	}
	if keyboardShortcuts, ok := preferences["keyboard_shortcuts"].(bool); ok {
		updates["keyboard_shortcuts"] = keyboardShortcuts
	}
	if showTooltips, ok := preferences["show_tooltips"].(bool); ok {
		updates["show_tooltips"] = showTooltips
	}

	// Handle JSON fields
	if notificationTypes, ok := preferences["notification_types"].(map[string]interface{}); ok {
		notificationTypesJSON, err := json.Marshal(notificationTypes)
		if err != nil {
			return fmt.Errorf("failed to marshal notification types: %w", err)
		}
		updates["notification_types"] = string(notificationTypesJSON)
	}
	if featurePreferences, ok := preferences["feature_preferences"].(map[string]interface{}); ok {
		featurePreferencesJSON, err := json.Marshal(featurePreferences)
		if err != nil {
			return fmt.Errorf("failed to marshal feature preferences: %w", err)
		}
		updates["feature_preferences"] = string(featurePreferencesJSON)
	}
	if customPreferences, ok := preferences["custom_preferences"].(map[string]interface{}); ok {
		customPreferencesJSON, err := json.Marshal(customPreferences)
		if err != nil {
			return fmt.Errorf("failed to marshal custom preferences: %w", err)
		}
		updates["custom_preferences"] = string(customPreferencesJSON)
	}

	if len(updates) == 0 {
		return nil
	}

	err := r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Where("user_id = ?", userID).
		Updates(updates).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to import preferences")
		return fmt.Errorf("failed to import preferences: %w", err)
	}
	return nil
}

// SyncPreferencesFromUser syncs preferences from user data
func (r *userPreferencesRepository) SyncPreferencesFromUser(ctx context.Context, userID uint, userPreferences map[string]interface{}) error {
	updates := make(map[string]interface{})

	// Sync language and timezone from user data
	if language, ok := userPreferences["language"].(string); ok {
		updates["language"] = language
	}
	if timezone, ok := userPreferences["timezone"].(string); ok {
		updates["timezone"] = timezone
	}

	if len(updates) == 0 {
		return nil
	}

	err := r.db.WithContext(ctx).
		Model(&models.UserPreferences{}).
		Where("user_id = ?", userID).
		Updates(updates).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to sync preferences from user")
		return fmt.Errorf("failed to sync preferences from user: %w", err)
	}
	return nil
}

// GetDefaultPreferences retrieves default preferences for a tenant
func (r *userPreferencesRepository) GetDefaultPreferences(ctx context.Context, tenantID uint) (*models.UserPreferences, error) {
	// For now, return a default preferences object
	// In a real implementation, this might come from tenant settings
	return &models.UserPreferences{
		Theme:                  models.ThemeLight,
		DashboardLayout:        "default",
		ItemsPerPage:           20,
		EmailNotifications:     true,
		PushNotifications:      true,
		SMSNotifications:       false,
		MarketingEmails:        false,
		NewsletterSubscription: false,
		SecurityAlerts:         true,
		ProfileVisibility:      models.ProfileVisibilityPublic,
		ShowOnlineStatus:       true,
		AllowSearch:            true,
		AutoSave:               true,
		KeyboardShortcuts:      true,
		TooltipsEnabled:        true,
	}, nil
}
