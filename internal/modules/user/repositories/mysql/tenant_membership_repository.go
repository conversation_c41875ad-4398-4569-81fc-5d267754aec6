package mysql

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// tenantMembershipRepository implements the TenantMembershipRepository interface
type tenantMembershipRepository struct {
	db     *gorm.DB
	logger utils.Logger
}

// NewTenantMembershipRepository creates a new instance of TenantMembershipRepository
func NewTenantMembershipRepository(db *gorm.DB, logger utils.Logger) repositories.TenantMembershipRepository {
	return &tenantMembershipRepository{
		db:     db,
		logger: logger,
	}
}

// Create creates a new tenant membership
func (r *tenantMembershipRepository) Create(ctx context.Context, membership *models.TenantMembership) error {
	if err := r.db.WithContext(ctx).Create(membership).Error; err != nil {
		r.logger.WithError(err).Error("Failed to create tenant membership")
		return fmt.Errorf("failed to create tenant membership: %w", err)
	}
	return nil
}

// GetByID retrieves a tenant membership by ID
func (r *tenantMembershipRepository) GetByID(ctx context.Context, id uint) (*models.TenantMembership, error) {
	var membership models.TenantMembership
	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		Preload("User").
		Preload("InvitedByUser").
		First(&membership).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("tenant membership not found")
		}
		r.logger.WithError(err).Error("Failed to get tenant membership by ID")
		return nil, fmt.Errorf("failed to get tenant membership: %w", err)
	}

	return &membership, nil
}

// GetByUserAndTenant retrieves a tenant membership by user and tenant ID
func (r *tenantMembershipRepository) GetByUserAndTenant(ctx context.Context, userID, tenantID uint) (*models.TenantMembership, error) {
	var membership models.TenantMembership
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND tenant_id = ?", userID, tenantID).
		Preload("User").
		Preload("InvitedByUser").
		First(&membership).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("tenant membership not found")
		}
		r.logger.WithError(err).Error("Failed to get tenant membership by user and tenant")
		return nil, fmt.Errorf("failed to get tenant membership: %w", err)
	}

	return &membership, nil
}

// Update updates a tenant membership
func (r *tenantMembershipRepository) Update(ctx context.Context, membership *models.TenantMembership) error {
	if err := r.db.WithContext(ctx).Save(membership).Error; err != nil {
		r.logger.WithError(err).Error("Failed to update tenant membership")
		return fmt.Errorf("failed to update tenant membership: %w", err)
	}
	return nil
}

// Delete deletes a tenant membership
func (r *tenantMembershipRepository) Delete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Delete(&models.TenantMembership{}, id).Error; err != nil {
		r.logger.WithError(err).Error("Failed to delete tenant membership")
		return fmt.Errorf("failed to delete tenant membership: %w", err)
	}
	return nil
}

// GetByUserID retrieves all tenant memberships for a user
func (r *tenantMembershipRepository) GetByUserID(ctx context.Context, userID uint) ([]models.TenantMembership, error) {
	var memberships []models.TenantMembership
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND status != ?", userID, models.TenantMembershipStatusDeleted).
		Preload("User").
		Preload("InvitedByUser").
		Order("created_at DESC").
		Find(&memberships).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to get tenant memberships by user ID")
		return nil, fmt.Errorf("failed to get tenant memberships by user ID: %w", err)
	}

	return memberships, nil
}

// GetPrimaryByUserID retrieves the primary tenant membership for a user
func (r *tenantMembershipRepository) GetPrimaryByUserID(ctx context.Context, userID uint) (*models.TenantMembership, error) {
	var membership models.TenantMembership
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND is_primary = ? AND status = ?", userID, true, models.TenantMembershipStatusActive).
		Preload("User").
		First(&membership).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("primary tenant membership not found for user")
		}
		r.logger.WithError(err).Error("Failed to get primary tenant membership by user ID")
		return nil, fmt.Errorf("failed to get primary tenant membership: %w", err)
	}

	return &membership, nil
}

// GetByTenantID retrieves all tenant memberships for a tenant with pagination
func (r *tenantMembershipRepository) GetByTenantID(ctx context.Context, tenantID uint, pag *pagination.CursorPagination) ([]models.TenantMembership, *pagination.CursorResponse, error) {
	var memberships []models.TenantMembership
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status != ?", tenantID, models.TenantMembershipStatusDeleted).
		Preload("User").
		Preload("InvitedByUser")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("id > ?", pag.Cursor)
	}

	query = query.Order("id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&memberships).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get tenant memberships by tenant ID")
		return nil, nil, fmt.Errorf("failed to get tenant memberships by tenant ID: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(memberships) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		memberships = memberships[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", memberships[len(memberships)-1].ID)
	}

	return memberships, response, nil
}

// GetByStatus retrieves tenant memberships by status with pagination
func (r *tenantMembershipRepository) GetByStatus(ctx context.Context, status models.TenantMembershipStatus, pag *pagination.CursorPagination) ([]models.TenantMembership, *pagination.CursorResponse, error) {
	var memberships []models.TenantMembership
	query := r.db.WithContext(ctx).
		Where("status = ?", status).
		Preload("User").
		Preload("InvitedByUser")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("id > ?", pag.Cursor)
	}

	query = query.Order("id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&memberships).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get tenant memberships by status")
		return nil, nil, fmt.Errorf("failed to get tenant memberships by status: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(memberships) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		memberships = memberships[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", memberships[len(memberships)-1].ID)
	}

	return memberships, response, nil
}

// UpdateStatus updates the status of a tenant membership
func (r *tenantMembershipRepository) UpdateStatus(ctx context.Context, id uint, status models.TenantMembershipStatus) error {
	err := r.db.WithContext(ctx).
		Model(&models.TenantMembership{}).
		Where("id = ?", id).
		Update("status", status).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update tenant membership status")
		return fmt.Errorf("failed to update tenant membership status: %w", err)
	}
	return nil
}

// GetActiveMembers retrieves active members for a tenant
func (r *tenantMembershipRepository) GetActiveMembers(ctx context.Context, tenantID uint) ([]models.TenantMembership, error) {
	var memberships []models.TenantMembership
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, models.TenantMembershipStatusActive).
		Preload("User").
		Preload("InvitedByUser").
		Order("joined_at DESC").
		Find(&memberships).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to get active members")
		return nil, fmt.Errorf("failed to get active members: %w", err)
	}

	return memberships, nil
}

// GetPendingMembers retrieves pending members for a tenant
func (r *tenantMembershipRepository) GetPendingMembers(ctx context.Context, tenantID uint) ([]models.TenantMembership, error) {
	var memberships []models.TenantMembership
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, models.TenantMembershipStatusPending).
		Preload("User").
		Preload("InvitedByUser").
		Order("created_at DESC").
		Find(&memberships).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to get pending members")
		return nil, fmt.Errorf("failed to get pending members: %w", err)
	}

	return memberships, nil
}

// UpdateActivity updates the last activity timestamp for a membership
func (r *tenantMembershipRepository) UpdateActivity(ctx context.Context, id uint) error {
	now := time.Now()
	err := r.db.WithContext(ctx).
		Model(&models.TenantMembership{}).
		Where("id = ?", id).
		Update("last_activity_at", now).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update membership activity")
		return fmt.Errorf("failed to update membership activity: %w", err)
	}
	return nil
}

// GetRecentActivity retrieves recent activity for a tenant
func (r *tenantMembershipRepository) GetRecentActivity(ctx context.Context, tenantID uint, limit int) ([]models.TenantMembership, error) {
	var memberships []models.TenantMembership
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ? AND last_activity_at IS NOT NULL", tenantID, models.TenantMembershipStatusActive).
		Preload("User").
		Order("last_activity_at DESC").
		Limit(limit).
		Find(&memberships).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to get recent activity")
		return nil, fmt.Errorf("failed to get recent activity: %w", err)
	}

	return memberships, nil
}

// UserBelongsToTenant checks if a user belongs to a tenant
func (r *tenantMembershipRepository) UserBelongsToTenant(ctx context.Context, userID, tenantID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TenantMembership{}).
		Where("user_id = ? AND tenant_id = ? AND status = ?", userID, tenantID, models.TenantMembershipStatusActive).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check user tenant membership")
		return false, fmt.Errorf("failed to check user tenant membership: %w", err)
	}

	return count > 0, nil
}

// LocalUsernameExists checks if a local username exists within a tenant
func (r *tenantMembershipRepository) LocalUsernameExists(ctx context.Context, tenantID uint, username string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TenantMembership{}).
		Where("tenant_id = ? AND local_username = ? AND status != ?", tenantID, username, models.TenantMembershipStatusDeleted).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check local username existence")
		return false, fmt.Errorf("failed to check local username existence: %w", err)
	}

	return count > 0, nil
}

// LocalUsernameExistsExcludingUser checks if a local username exists within a tenant excluding a specific user
func (r *tenantMembershipRepository) LocalUsernameExistsExcludingUser(ctx context.Context, tenantID uint, username string, excludeUserID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TenantMembership{}).
		Where("tenant_id = ? AND local_username = ? AND user_id != ? AND status != ?", tenantID, username, excludeUserID, models.TenantMembershipStatusDeleted).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check local username existence excluding user")
		return false, fmt.Errorf("failed to check local username existence excluding user: %w", err)
	}

	return count > 0, nil
}

// CountByTenant counts memberships by tenant
func (r *tenantMembershipRepository) CountByTenant(ctx context.Context, tenantID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TenantMembership{}).
		Where("tenant_id = ? AND status != ?", tenantID, models.TenantMembershipStatusDeleted).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count memberships by tenant")
		return 0, fmt.Errorf("failed to count memberships by tenant: %w", err)
	}

	return count, nil
}

// CountByStatus counts memberships by status within a tenant
func (r *tenantMembershipRepository) CountByStatus(ctx context.Context, tenantID uint, status models.TenantMembershipStatus) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TenantMembership{}).
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count memberships by status")
		return 0, fmt.Errorf("failed to count memberships by status: %w", err)
	}

	return count, nil
}

// GetUserRole retrieves user role for a specific tenant (placeholder - needs RBAC integration)
func (r *tenantMembershipRepository) GetUserRole(ctx context.Context, userID, tenantID uint) (string, error) {
	// Check if user is an active member first
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TenantMembership{}).
		Where("user_id = ? AND tenant_id = ? AND status = ?", userID, tenantID, models.TenantMembershipStatusActive).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check user membership")
		return "", fmt.Errorf("failed to check user membership: %w", err)
	}

	if count == 0 {
		return "", fmt.Errorf("user not found in tenant")
	}

	// TODO: Integrate with RBAC system to get actual role
	// For now, return default role based on membership
	return "member", nil
}

// IsUserActiveInTenant checks if user is active member of tenant
func (r *tenantMembershipRepository) IsUserActiveInTenant(ctx context.Context, userID, tenantID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TenantMembership{}).
		Where("user_id = ? AND tenant_id = ? AND status = ?", userID, tenantID, models.TenantMembershipStatusActive).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check user active status in tenant")
		return false, fmt.Errorf("failed to check user active status: %w", err)
	}

	return count > 0, nil
}

// CountByUser counts memberships by user
func (r *tenantMembershipRepository) CountByUser(ctx context.Context, userID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TenantMembership{}).
		Where("user_id = ? AND status != ?", userID, models.TenantMembershipStatusDeleted).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count memberships by user")
		return 0, fmt.Errorf("failed to count memberships by user: %w", err)
	}

	return count, nil
}
