package mysql

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// userSocialLinksRepository implements the UserSocialLinksRepository interface using GORM
type userSocialLinksRepository struct {
	db     *gorm.DB
	logger utils.Logger
}

// NewUserSocialLinksRepository creates a new instance of UserSocialLinksRepository
func NewUserSocialLinksRepository(db *gorm.DB, logger utils.Logger) repositories.UserSocialLinksRepository {
	return &userSocialLinksRepository{
		db:     db,
		logger: logger,
	}
}

// Create creates a new user social link
func (r *userSocialLinksRepository) Create(ctx context.Context, socialLink *models.UserSocialLink) error {
	if err := r.db.WithContext(ctx).Create(socialLink).Error; err != nil {
		r.logger.WithError(err).Error("Failed to create user social link")
		return fmt.Errorf("failed to create user social link: %w", err)
	}
	return nil
}

// GetByID retrieves a user social link by ID
func (r *userSocialLinksRepository) GetByID(ctx context.Context, id uint) (*models.UserSocialLink, error) {
	var socialLink models.UserSocialLink
	err := r.db.WithContext(ctx).
		Preload("User").
		First(&socialLink, id).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("social link not found")
		}
		r.logger.WithError(err).Error("Failed to get user social link by ID")
		return nil, fmt.Errorf("failed to get user social link: %w", err)
	}

	return &socialLink, nil
}

// GetByUserID retrieves all social links for a user
func (r *userSocialLinksRepository) GetByUserID(ctx context.Context, userID uint) ([]models.UserSocialLink, error) {
	var socialLinks []models.UserSocialLink
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("display_order ASC, created_at ASC").
		Find(&socialLinks).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to get user social links by user ID")
		return nil, fmt.Errorf("failed to get user social links: %w", err)
	}

	return socialLinks, nil
}

// GetByUserIDAndPlatform retrieves a social link by user ID and platform
func (r *userSocialLinksRepository) GetByUserIDAndPlatform(ctx context.Context, userID uint, platform models.SocialPlatform) (*models.UserSocialLink, error) {
	var socialLink models.UserSocialLink
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND platform = ?", userID, platform).
		First(&socialLink).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("social link not found")
		}
		r.logger.WithError(err).Error("Failed to get user social link by user ID and platform")
		return nil, fmt.Errorf("failed to get user social link: %w", err)
	}

	return &socialLink, nil
}

// Update updates a user social link
func (r *userSocialLinksRepository) Update(ctx context.Context, socialLink *models.UserSocialLink) error {
	if err := r.db.WithContext(ctx).Save(socialLink).Error; err != nil {
		r.logger.WithError(err).Error("Failed to update user social link")
		return fmt.Errorf("failed to update user social link: %w", err)
	}
	return nil
}

// Delete permanently deletes a user social link
func (r *userSocialLinksRepository) Delete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Delete(&models.UserSocialLink{}, id).Error; err != nil {
		r.logger.WithError(err).Error("Failed to delete user social link")
		return fmt.Errorf("failed to delete user social link: %w", err)
	}
	return nil
}

// SoftDelete marks a user social link as deleted
func (r *userSocialLinksRepository) SoftDelete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Delete(&models.UserSocialLink{}, id).Error; err != nil {
		r.logger.WithError(err).Error("Failed to soft delete user social link")
		return fmt.Errorf("failed to soft delete user social link: %w", err)
	}
	return nil
}

// Restore restores a soft-deleted user social link
func (r *userSocialLinksRepository) Restore(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Unscoped().Model(&models.UserSocialLink{}).Where("id = ?", id).Update("deleted_at", nil).Error; err != nil {
		r.logger.WithError(err).Error("Failed to restore user social link")
		return fmt.Errorf("failed to restore user social link: %w", err)
	}
	return nil
}

// ListByTenant retrieves social links by tenant
func (r *userSocialLinksRepository) ListByTenant(ctx context.Context, tenantID uint, pag *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error) {
	var socialLinks []models.UserSocialLink
	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_social_links.id > ?", pag.Cursor)
	}

	query = query.Order("user_social_links.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&socialLinks).Error; err != nil {
		r.logger.WithError(err).Error("Failed to list social links by tenant")
		return nil, nil, fmt.Errorf("failed to list social links by tenant: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(socialLinks) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		socialLinks = socialLinks[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", socialLinks[len(socialLinks)-1].ID)
	}

	return socialLinks, response, nil
}

// ListByPlatform retrieves social links by platform
func (r *userSocialLinksRepository) ListByPlatform(ctx context.Context, tenantID uint, platform models.SocialPlatform, pag *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error) {
	var socialLinks []models.UserSocialLink
	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ? AND user_social_links.platform = ?", tenantID, platform).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_social_links.id > ?", pag.Cursor)
	}

	query = query.Order("user_social_links.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&socialLinks).Error; err != nil {
		r.logger.WithError(err).Error("Failed to list social links by platform")
		return nil, nil, fmt.Errorf("failed to list social links by platform: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(socialLinks) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		socialLinks = socialLinks[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", socialLinks[len(socialLinks)-1].ID)
	}

	return socialLinks, response, nil
}

// ListByVerificationStatus retrieves social links by verification status
func (r *userSocialLinksRepository) ListByVerificationStatus(ctx context.Context, tenantID uint, verified bool, pag *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error) {
	var socialLinks []models.UserSocialLink
	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ? AND user_social_links.is_verified = ?", tenantID, verified).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_social_links.id > ?", pag.Cursor)
	}

	query = query.Order("user_social_links.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&socialLinks).Error; err != nil {
		r.logger.WithError(err).Error("Failed to list social links by verification status")
		return nil, nil, fmt.Errorf("failed to list social links by verification status: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(socialLinks) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		socialLinks = socialLinks[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", socialLinks[len(socialLinks)-1].ID)
	}

	return socialLinks, response, nil
}

// ListByPublicStatus retrieves social links by public status
func (r *userSocialLinksRepository) ListByPublicStatus(ctx context.Context, tenantID uint, isPublic bool, pag *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error) {
	var socialLinks []models.UserSocialLink
	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ? AND user_social_links.is_public = ?", tenantID, isPublic).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_social_links.id > ?", pag.Cursor)
	}

	query = query.Order("user_social_links.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&socialLinks).Error; err != nil {
		r.logger.WithError(err).Error("Failed to list social links by public status")
		return nil, nil, fmt.Errorf("failed to list social links by public status: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(socialLinks) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		socialLinks = socialLinks[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", socialLinks[len(socialLinks)-1].ID)
	}

	return socialLinks, response, nil
}

// SearchByPlatform searches social links by platform
func (r *userSocialLinksRepository) SearchByPlatform(ctx context.Context, tenantID uint, platform models.SocialPlatform, pag *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error) {
	return r.ListByPlatform(ctx, tenantID, platform, pag)
}

// SearchByUsername searches social links by username pattern
func (r *userSocialLinksRepository) SearchByUsername(ctx context.Context, tenantID uint, username string, pag *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error) {
	var socialLinks []models.UserSocialLink
	usernameQuery := "%" + strings.ToLower(username) + "%"

	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ? AND LOWER(user_social_links.username) LIKE ?", tenantID, usernameQuery).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_social_links.id > ?", pag.Cursor)
	}

	query = query.Order("user_social_links.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&socialLinks).Error; err != nil {
		r.logger.WithError(err).Error("Failed to search social links by username")
		return nil, nil, fmt.Errorf("failed to search social links by username: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(socialLinks) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		socialLinks = socialLinks[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", socialLinks[len(socialLinks)-1].ID)
	}

	return socialLinks, response, nil
}

// SearchByURL searches social links by URL pattern
func (r *userSocialLinksRepository) SearchByURL(ctx context.Context, tenantID uint, url string, pag *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error) {
	var socialLinks []models.UserSocialLink
	urlQuery := "%" + strings.ToLower(url) + "%"

	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ? AND LOWER(user_social_links.url) LIKE ?", tenantID, urlQuery).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_social_links.id > ?", pag.Cursor)
	}

	query = query.Order("user_social_links.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&socialLinks).Error; err != nil {
		r.logger.WithError(err).Error("Failed to search social links by URL")
		return nil, nil, fmt.Errorf("failed to search social links by URL: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(socialLinks) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		socialLinks = socialLinks[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", socialLinks[len(socialLinks)-1].ID)
	}

	return socialLinks, response, nil
}

// VerifyLink marks a social link as verified
func (r *userSocialLinksRepository) VerifyLink(ctx context.Context, id uint) error {
	now := time.Now()
	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"is_verified": true,
			"verified_at": now,
		}).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to verify social link")
		return fmt.Errorf("failed to verify social link: %w", err)
	}
	return nil
}

// UnverifyLink marks a social link as unverified
func (r *userSocialLinksRepository) UnverifyLink(ctx context.Context, id uint) error {
	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"is_verified": false,
			"verified_at": nil,
		}).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to unverify social link")
		return fmt.Errorf("failed to unverify social link: %w", err)
	}
	return nil
}

// UpdateVerificationStatus updates the verification status of a social link
func (r *userSocialLinksRepository) UpdateVerificationStatus(ctx context.Context, id uint, verified bool) error {
	updates := map[string]interface{}{
		"is_verified": verified,
	}

	if verified {
		updates["verified_at"] = time.Now()
	} else {
		updates["verified_at"] = nil
	}

	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Where("id = ?", id).
		Updates(updates).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update verification status")
		return fmt.Errorf("failed to update verification status: %w", err)
	}
	return nil
}

// GetUnverifiedLinks retrieves unverified social links
func (r *userSocialLinksRepository) GetUnverifiedLinks(ctx context.Context, tenantID uint, pag *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error) {
	return r.ListByVerificationStatus(ctx, tenantID, false, pag)
}

// GetVerifiedLinks retrieves verified social links
func (r *userSocialLinksRepository) GetVerifiedLinks(ctx context.Context, tenantID uint, pag *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error) {
	return r.ListByVerificationStatus(ctx, tenantID, true, pag)
}

// UpdateDisplayOrder updates the display order of a social link
func (r *userSocialLinksRepository) UpdateDisplayOrder(ctx context.Context, id uint, displayOrder uint) error {
	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Where("id = ?", id).
		Update("display_order", displayOrder).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update display order")
		return fmt.Errorf("failed to update display order: %w", err)
	}
	return nil
}

// ReorderUserLinks reorders social links for a user
func (r *userSocialLinksRepository) ReorderUserLinks(ctx context.Context, userID uint, linkOrders []models.UserSocialLinkOrder) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, linkOrder := range linkOrders {
			if err := tx.Model(&models.UserSocialLink{}).
				Where("id = ? AND user_id = ?", linkOrder.ID, userID).
				Update("display_order", linkOrder.DisplayOrder).Error; err != nil {
				return fmt.Errorf("failed to update display order for link %d: %w", linkOrder.ID, err)
			}
		}
		return nil
	})
}

// GetUserLinksOrdered retrieves social links for a user ordered by display order
func (r *userSocialLinksRepository) GetUserLinksOrdered(ctx context.Context, userID uint) ([]models.UserSocialLink, error) {
	var socialLinks []models.UserSocialLink
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("display_order ASC, created_at ASC").
		Find(&socialLinks).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to get user social links ordered")
		return nil, fmt.Errorf("failed to get user social links ordered: %w", err)
	}

	return socialLinks, nil
}

// UpdatePublicStatus updates the public status of a social link
func (r *userSocialLinksRepository) UpdatePublicStatus(ctx context.Context, id uint, isPublic bool) error {
	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Where("id = ?", id).
		Update("is_public", isPublic).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update public status")
		return fmt.Errorf("failed to update public status: %w", err)
	}
	return nil
}

// GetPublicLinks retrieves public social links
func (r *userSocialLinksRepository) GetPublicLinks(ctx context.Context, tenantID uint, pag *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error) {
	return r.ListByPublicStatus(ctx, tenantID, true, pag)
}

// GetPrivateLinks retrieves private social links
func (r *userSocialLinksRepository) GetPrivateLinks(ctx context.Context, tenantID uint, pag *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error) {
	return r.ListByPublicStatus(ctx, tenantID, false, pag)
}

// GetPublicLinksByUser retrieves public social links for a user
func (r *userSocialLinksRepository) GetPublicLinksByUser(ctx context.Context, userID uint) ([]models.UserSocialLink, error) {
	var socialLinks []models.UserSocialLink
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND is_public = ?", userID, true).
		Order("display_order ASC, created_at ASC").
		Find(&socialLinks).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to get public social links by user")
		return nil, fmt.Errorf("failed to get public social links by user: %w", err)
	}

	return socialLinks, nil
}

// UpdateProfileData updates profile data for a social link
func (r *userSocialLinksRepository) UpdateProfileData(ctx context.Context, id uint, profileData map[string]interface{}) error {
	profileDataJSON, err := json.Marshal(profileData)
	if err != nil {
		return fmt.Errorf("failed to marshal profile data: %w", err)
	}

	err = r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Where("id = ?", id).
		Update("profile_data", string(profileDataJSON)).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update profile data")
		return fmt.Errorf("failed to update profile data: %w", err)
	}
	return nil
}

// GetProfileData retrieves profile data for a social link
func (r *userSocialLinksRepository) GetProfileData(ctx context.Context, id uint) (map[string]interface{}, error) {
	var socialLink models.UserSocialLink
	err := r.db.WithContext(ctx).
		Select("profile_data").
		Where("id = ?", id).
		First(&socialLink).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return make(map[string]interface{}), nil
		}
		r.logger.WithError(err).Error("Failed to get profile data")
		return nil, fmt.Errorf("failed to get profile data: %w", err)
	}

	return socialLink.GetProfileData(), nil
}

// UpdateProfileDataField updates a specific field in profile data
func (r *userSocialLinksRepository) UpdateProfileDataField(ctx context.Context, id uint, field string, value interface{}) error {
	// Get current profile data
	profileData, err := r.GetProfileData(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to get current profile data: %w", err)
	}

	// Update the field
	profileData[field] = value

	// Save the updated profile data
	return r.UpdateProfileData(ctx, id, profileData)
}

// GetProfileDataField retrieves a specific field from profile data
func (r *userSocialLinksRepository) GetProfileDataField(ctx context.Context, id uint, field string) (interface{}, error) {
	profileData, err := r.GetProfileData(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get profile data: %w", err)
	}

	value, exists := profileData[field]
	if !exists {
		return nil, fmt.Errorf("field '%s' not found in profile data", field)
	}

	return value, nil
}

// BulkCreate creates multiple social links
func (r *userSocialLinksRepository) BulkCreate(ctx context.Context, socialLinks []models.UserSocialLink) error {
	if len(socialLinks) == 0 {
		return nil
	}

	err := r.db.WithContext(ctx).CreateInBatches(socialLinks, 100).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to bulk create social links")
		return fmt.Errorf("failed to bulk create social links: %w", err)
	}
	return nil
}

// BulkUpdate updates multiple social links
func (r *userSocialLinksRepository) BulkUpdate(ctx context.Context, socialLinks []models.UserSocialLink) error {
	if len(socialLinks) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, socialLink := range socialLinks {
			if err := tx.Save(&socialLink).Error; err != nil {
				return fmt.Errorf("failed to update social link %d: %w", socialLink.ID, err)
			}
		}
		return nil
	})
}

// BulkDelete deletes multiple social links
func (r *userSocialLinksRepository) BulkDelete(ctx context.Context, ids []uint) error {
	if len(ids) == 0 {
		return nil
	}

	err := r.db.WithContext(ctx).Delete(&models.UserSocialLink{}, ids).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to bulk delete social links")
		return fmt.Errorf("failed to bulk delete social links: %w", err)
	}
	return nil
}

// BulkUpdateVerificationStatus updates verification status for multiple social links
func (r *userSocialLinksRepository) BulkUpdateVerificationStatus(ctx context.Context, ids []uint, verified bool) error {
	if len(ids) == 0 {
		return nil
	}

	updates := map[string]interface{}{
		"is_verified": verified,
	}

	if verified {
		updates["verified_at"] = time.Now()
	} else {
		updates["verified_at"] = nil
	}

	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Where("id IN ?", ids).
		Updates(updates).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to bulk update verification status")
		return fmt.Errorf("failed to bulk update verification status: %w", err)
	}
	return nil
}

// BulkUpdatePublicStatus updates public status for multiple social links
func (r *userSocialLinksRepository) BulkUpdatePublicStatus(ctx context.Context, ids []uint, isPublic bool) error {
	if len(ids) == 0 {
		return nil
	}

	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Where("id IN ?", ids).
		Update("is_public", isPublic).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to bulk update public status")
		return fmt.Errorf("failed to bulk update public status: %w", err)
	}
	return nil
}

// BulkUpdateDisplayOrder updates display order for multiple social links
func (r *userSocialLinksRepository) BulkUpdateDisplayOrder(ctx context.Context, updates []repositories.SocialLinkDisplayOrderUpdate) error {
	if len(updates) == 0 {
		return nil
	}

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, update := range updates {
			if err := tx.Model(&models.UserSocialLink{}).
				Where("id = ?", update.ID).
				Update("display_order", update.DisplayOrder).Error; err != nil {
				return fmt.Errorf("failed to update display order for link %d: %w", update.ID, err)
			}
		}
		return nil
	})
}

// GetPlatformStats retrieves platform statistics
func (r *userSocialLinksRepository) GetPlatformStats(ctx context.Context, tenantID uint) (*repositories.SocialPlatformStats, error) {
	var stats repositories.SocialPlatformStats

	// Get total links count
	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Count(&stats.TotalLinks).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to get total links count")
		return nil, fmt.Errorf("failed to get total links count: %w", err)
	}

	if stats.TotalLinks == 0 {
		return &stats, nil
	}

	// Get verified links count
	err = r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ? AND user_social_links.is_verified = ?", tenantID, true).
		Count(&stats.VerifiedLinks).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to get verified links count")
		return nil, fmt.Errorf("failed to get verified links count: %w", err)
	}

	stats.UnverifiedLinks = stats.TotalLinks - stats.VerifiedLinks

	// Get public links count
	err = r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ? AND user_social_links.is_public = ?", tenantID, true).
		Count(&stats.PublicLinks).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to get public links count")
		return nil, fmt.Errorf("failed to get public links count: %w", err)
	}

	stats.PrivateLinks = stats.TotalLinks - stats.PublicLinks

	// Get platform counts
	var platformStats []struct {
		Platform models.SocialPlatform `json:"platform"`
		Count    int64                 `json:"count"`
	}
	err = r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Select("platform, COUNT(*) as count").
		Group("platform").
		Scan(&platformStats).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to get platform counts")
		return nil, fmt.Errorf("failed to get platform counts: %w", err)
	}

	stats.PlatformCounts = make(map[models.SocialPlatform]int64)
	for _, platformStat := range platformStats {
		stats.PlatformCounts[platformStat.Platform] = platformStat.Count
	}

	// Get users with links count
	err = r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Select("COUNT(DISTINCT user_social_links.user_id)").
		Scan(&stats.UsersWithLinks).Error
	if err != nil {
		r.logger.WithError(err).Error("Failed to get users with links count")
		return nil, fmt.Errorf("failed to get users with links count: %w", err)
	}

	// Calculate average links per user
	if stats.UsersWithLinks > 0 {
		stats.AverageLinksPerUser = float64(stats.TotalLinks) / float64(stats.UsersWithLinks)
	}

	return &stats, nil
}

// GetUserPlatformUsage retrieves platform usage for a user
func (r *userSocialLinksRepository) GetUserPlatformUsage(ctx context.Context, userID uint) ([]models.SocialPlatform, error) {
	var platforms []models.SocialPlatform
	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Where("user_id = ?", userID).
		Select("DISTINCT platform").
		Pluck("platform", &platforms).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to get user platform usage")
		return nil, fmt.Errorf("failed to get user platform usage: %w", err)
	}

	return platforms, nil
}

// GetPopularPlatforms retrieves popular platforms
func (r *userSocialLinksRepository) GetPopularPlatforms(ctx context.Context, tenantID uint, limit int) ([]repositories.PlatformUsage, error) {
	var platformUsages []repositories.PlatformUsage

	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Select("platform, COUNT(*) as count").
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Group("platform").
		Order("count DESC").
		Limit(limit).
		Scan(&platformUsages).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to get popular platforms")
		return nil, fmt.Errorf("failed to get popular platforms: %w", err)
	}

	return platformUsages, nil
}

// CountByTenant counts social links by tenant
func (r *userSocialLinksRepository) CountByTenant(ctx context.Context, tenantID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count social links by tenant")
		return 0, fmt.Errorf("failed to count social links by tenant: %w", err)
	}
	return count, nil
}

// CountByPlatform counts social links by platform
func (r *userSocialLinksRepository) CountByPlatform(ctx context.Context, tenantID uint, platform models.SocialPlatform) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ? AND user_social_links.platform = ?", tenantID, platform).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count social links by platform")
		return 0, fmt.Errorf("failed to count social links by platform: %w", err)
	}
	return count, nil
}

// CountByVerificationStatus counts social links by verification status
func (r *userSocialLinksRepository) CountByVerificationStatus(ctx context.Context, tenantID uint, verified bool) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ? AND user_social_links.is_verified = ?", tenantID, verified).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count social links by verification status")
		return 0, fmt.Errorf("failed to count social links by verification status: %w", err)
	}
	return count, nil
}

// CountByPublicStatus counts social links by public status
func (r *userSocialLinksRepository) CountByPublicStatus(ctx context.Context, tenantID uint, isPublic bool) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ? AND user_social_links.is_public = ?", tenantID, isPublic).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count social links by public status")
		return 0, fmt.Errorf("failed to count social links by public status: %w", err)
	}
	return count, nil
}

// CountByUser counts social links by user
func (r *userSocialLinksRepository) CountByUser(ctx context.Context, userID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Where("user_id = ?", userID).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count social links by user")
		return 0, fmt.Errorf("failed to count social links by user: %w", err)
	}
	return count, nil
}

// LinkExists checks if a social link exists for a user and platform
func (r *userSocialLinksRepository) LinkExists(ctx context.Context, userID uint, platform models.SocialPlatform) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Where("user_id = ? AND platform = ?", userID, platform).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check link existence")
		return false, fmt.Errorf("failed to check link existence: %w", err)
	}
	return count > 0, nil
}

// URLExists checks if a URL exists for a user
func (r *userSocialLinksRepository) URLExists(ctx context.Context, userID uint, url string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Where("user_id = ? AND url = ?", userID, url).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check URL existence")
		return false, fmt.Errorf("failed to check URL existence: %w", err)
	}
	return count > 0, nil
}

// UsernameExists checks if a username exists for a user and platform
func (r *userSocialLinksRepository) UsernameExists(ctx context.Context, userID uint, platform models.SocialPlatform, username string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Where("user_id = ? AND platform = ? AND username = ?", userID, platform, username).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check username existence")
		return false, fmt.Errorf("failed to check username existence: %w", err)
	}
	return count > 0, nil
}

// DeleteExpiredLinks deletes expired social links (placeholder implementation)
func (r *userSocialLinksRepository) DeleteExpiredLinks(ctx context.Context) error {
	// This is a placeholder - in a real implementation, you would define what "expired" means
	// For example, links that haven't been updated in X days or have specific expiration flags
	return nil
}

// DeleteUnverifiedLinks deletes unverified social links older than specified days
func (r *userSocialLinksRepository) DeleteUnverifiedLinks(ctx context.Context, olderThan int) error {
	cutoffDate := time.Now().AddDate(0, 0, -olderThan)

	err := r.db.WithContext(ctx).
		Where("is_verified = ? AND created_at < ?", false, cutoffDate).
		Delete(&models.UserSocialLink{}).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to delete unverified links")
		return fmt.Errorf("failed to delete unverified links: %w", err)
	}
	return nil
}

// ArchiveInactiveLinks archives inactive social links (placeholder implementation)
func (r *userSocialLinksRepository) ArchiveInactiveLinks(ctx context.Context, olderThan int) error {
	// This is a placeholder - in a real implementation, you would define what "inactive" means
	// and how to archive (maybe set a flag or move to archive table)
	return nil
}

// GetLinksWithProfileData retrieves social links with profile data
func (r *userSocialLinksRepository) GetLinksWithProfileData(ctx context.Context, tenantID uint, pag *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error) {
	var socialLinks []models.UserSocialLink
	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ? AND user_social_links.profile_data IS NOT NULL AND user_social_links.profile_data != '{}'", tenantID).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_social_links.id > ?", pag.Cursor)
	}

	query = query.Order("user_social_links.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&socialLinks).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get links with profile data")
		return nil, nil, fmt.Errorf("failed to get links with profile data: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(socialLinks) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		socialLinks = socialLinks[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", socialLinks[len(socialLinks)-1].ID)
	}

	return socialLinks, response, nil
}

// GetLinksCreatedBetween retrieves social links created between dates
func (r *userSocialLinksRepository) GetLinksCreatedBetween(ctx context.Context, tenantID uint, startDate, endDate string, pag *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error) {
	var socialLinks []models.UserSocialLink
	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ? AND user_social_links.created_at BETWEEN ? AND ?", tenantID, startDate, endDate).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_social_links.id > ?", pag.Cursor)
	}

	query = query.Order("user_social_links.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&socialLinks).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get links created between dates")
		return nil, nil, fmt.Errorf("failed to get links created between dates: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(socialLinks) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		socialLinks = socialLinks[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", socialLinks[len(socialLinks)-1].ID)
	}

	return socialLinks, response, nil
}

// GetLinksUpdatedBetween retrieves social links updated between dates
func (r *userSocialLinksRepository) GetLinksUpdatedBetween(ctx context.Context, tenantID uint, startDate, endDate string, pag *pagination.CursorPagination) ([]models.UserSocialLink, *pagination.CursorResponse, error) {
	var socialLinks []models.UserSocialLink
	query := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ? AND user_social_links.updated_at BETWEEN ? AND ?", tenantID, startDate, endDate).
		Preload("User")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("user_social_links.id > ?", pag.Cursor)
	}

	query = query.Order("user_social_links.id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&socialLinks).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get links updated between dates")
		return nil, nil, fmt.Errorf("failed to get links updated between dates: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(socialLinks) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		socialLinks = socialLinks[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", socialLinks[len(socialLinks)-1].ID)
	}

	return socialLinks, response, nil
}

// GetMostRecentLinks retrieves the most recent social links
func (r *userSocialLinksRepository) GetMostRecentLinks(ctx context.Context, tenantID uint, limit int) ([]models.UserSocialLink, error) {
	var socialLinks []models.UserSocialLink
	err := r.db.WithContext(ctx).
		Joins("LEFT JOIN users ON user_social_links.user_id = users.id").
		Where("users.tenant_id = ?", tenantID).
		Order("user_social_links.created_at DESC").
		Limit(limit).
		Preload("User").
		Find(&socialLinks).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to get most recent links")
		return nil, fmt.Errorf("failed to get most recent links: %w", err)
	}

	return socialLinks, nil
}

// PlatformExists checks if a platform exists for a user
func (r *userSocialLinksRepository) PlatformExists(ctx context.Context, userID uint, platform models.SocialPlatform) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Where("user_id = ? AND platform = ?", userID, platform).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check platform existence")
		return false, fmt.Errorf("failed to check platform existence: %w", err)
	}

	return count > 0, nil
}

// GetMaxDisplayOrder gets the maximum display order for a user's social links
func (r *userSocialLinksRepository) GetMaxDisplayOrder(ctx context.Context, userID uint) (uint, error) {
	var maxOrder uint
	err := r.db.WithContext(ctx).
		Model(&models.UserSocialLink{}).
		Where("user_id = ?", userID).
		Select("COALESCE(MAX(display_order), 0)").
		Scan(&maxOrder).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to get max display order")
		return 0, fmt.Errorf("failed to get max display order: %w", err)
	}

	return maxOrder, nil
}
