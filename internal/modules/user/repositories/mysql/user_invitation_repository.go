package mysql

import (
	"context"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// userInvitationRepository implements the UserInvitationRepository interface
type userInvitationRepository struct {
	db     *gorm.DB
	logger utils.Logger
}

// NewUserInvitationRepository creates a new instance of UserInvitationRepository
func NewUserInvitationRepository(db *gorm.DB, logger utils.Logger) repositories.UserInvitationRepository {
	return &userInvitationRepository{
		db:     db,
		logger: logger,
	}
}

// Create creates a new user invitation
func (r *userInvitationRepository) Create(ctx context.Context, invitation *models.UserInvitation) error {
	if err := r.db.WithContext(ctx).Create(invitation).Error; err != nil {
		r.logger.WithError(err).Error("Failed to create user invitation")
		return fmt.Errorf("failed to create user invitation: %w", err)
	}
	return nil
}

// GetByID retrieves a user invitation by ID
func (r *userInvitationRepository) GetByID(ctx context.Context, id uint) (*models.UserInvitation, error) {
	var invitation models.UserInvitation
	err := r.db.WithContext(ctx).
		Where("id = ?", id).
		Preload("InvitedByUser").
		Preload("AcceptedByUser").
		First(&invitation).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user invitation not found")
		}
		r.logger.WithError(err).Error("Failed to get user invitation by ID")
		return nil, fmt.Errorf("failed to get user invitation: %w", err)
	}

	return &invitation, nil
}

// GetByToken retrieves a user invitation by token
func (r *userInvitationRepository) GetByToken(ctx context.Context, token string) (*models.UserInvitation, error) {
	var invitation models.UserInvitation
	err := r.db.WithContext(ctx).
		Where("token = ?", token).
		Preload("InvitedByUser").
		Preload("AcceptedByUser").
		First(&invitation).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user invitation not found")
		}
		r.logger.WithError(err).Error("Failed to get user invitation by token")
		return nil, fmt.Errorf("failed to get user invitation: %w", err)
	}

	return &invitation, nil
}

// GetByEmail retrieves user invitations by email
func (r *userInvitationRepository) GetByEmail(ctx context.Context, email string) (*models.UserInvitation, error) {
	var invitation models.UserInvitation
	err := r.db.WithContext(ctx).
		Where("email = ? AND status = ?", email, models.UserInvitationStatusPending).
		Preload("InvitedByUser").
		Preload("AcceptedByUser").
		Order("created_at DESC").
		First(&invitation).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user invitation not found")
		}
		r.logger.WithError(err).Error("Failed to get user invitation by email")
		return nil, fmt.Errorf("failed to get user invitation: %w", err)
	}

	return &invitation, nil
}

// GetByTenantAndEmail retrieves a user invitation by tenant and email
func (r *userInvitationRepository) GetByTenantAndEmail(ctx context.Context, tenantID uint, email string) (*models.UserInvitation, error) {
	var invitation models.UserInvitation
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND email = ? AND status = ?", tenantID, email, models.UserInvitationStatusPending).
		Preload("InvitedByUser").
		Preload("AcceptedByUser").
		Order("created_at DESC").
		First(&invitation).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("user invitation not found")
		}
		r.logger.WithError(err).Error("Failed to get user invitation by tenant and email")
		return nil, fmt.Errorf("failed to get user invitation: %w", err)
	}

	return &invitation, nil
}

// Update updates a user invitation
func (r *userInvitationRepository) Update(ctx context.Context, invitation *models.UserInvitation) error {
	if err := r.db.WithContext(ctx).Save(invitation).Error; err != nil {
		r.logger.WithError(err).Error("Failed to update user invitation")
		return fmt.Errorf("failed to update user invitation: %w", err)
	}
	return nil
}

// Delete deletes a user invitation
func (r *userInvitationRepository) Delete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Delete(&models.UserInvitation{}, id).Error; err != nil {
		r.logger.WithError(err).Error("Failed to delete user invitation")
		return fmt.Errorf("failed to delete user invitation: %w", err)
	}
	return nil
}

// GetByTenantID retrieves all user invitations for a tenant with pagination
func (r *userInvitationRepository) GetByTenantID(ctx context.Context, tenantID uint, pag *pagination.CursorPagination) ([]models.UserInvitation, *pagination.CursorResponse, error) {
	var invitations []models.UserInvitation
	query := r.db.WithContext(ctx).
		Where("tenant_id = ?", tenantID).
		Preload("InvitedByUser").
		Preload("AcceptedByUser")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("id > ?", pag.Cursor)
	}

	query = query.Order("id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&invitations).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get user invitations by tenant ID")
		return nil, nil, fmt.Errorf("failed to get user invitations by tenant ID: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(invitations) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		invitations = invitations[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", invitations[len(invitations)-1].ID)
	}

	return invitations, response, nil
}

// GetByStatus retrieves user invitations by status with pagination
func (r *userInvitationRepository) GetByStatus(ctx context.Context, status models.UserInvitationStatus, pag *pagination.CursorPagination) ([]models.UserInvitation, *pagination.CursorResponse, error) {
	var invitations []models.UserInvitation
	query := r.db.WithContext(ctx).
		Where("status = ?", status).
		Preload("InvitedByUser").
		Preload("AcceptedByUser")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("id > ?", pag.Cursor)
	}

	query = query.Order("id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&invitations).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get user invitations by status")
		return nil, nil, fmt.Errorf("failed to get user invitations by status: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(invitations) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		invitations = invitations[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", invitations[len(invitations)-1].ID)
	}

	return invitations, response, nil
}

// GetByInviter retrieves user invitations by inviter with pagination
func (r *userInvitationRepository) GetByInviter(ctx context.Context, inviterID uint, pag *pagination.CursorPagination) ([]models.UserInvitation, *pagination.CursorResponse, error) {
	var invitations []models.UserInvitation
	query := r.db.WithContext(ctx).
		Where("invited_by_user_id = ?", inviterID).
		Preload("InvitedByUser").
		Preload("AcceptedByUser")

	// Apply cursor pagination
	if pag.Cursor != "" {
		query = query.Where("id > ?", pag.Cursor)
	}

	query = query.Order("id ASC").Limit(pag.Limit + 1)

	if err := query.Find(&invitations).Error; err != nil {
		r.logger.WithError(err).Error("Failed to get user invitations by inviter")
		return nil, nil, fmt.Errorf("failed to get user invitations by inviter: %w", err)
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(invitations) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		invitations = invitations[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", invitations[len(invitations)-1].ID)
	}

	return invitations, response, nil
}

// UpdateStatus updates the status of a user invitation
func (r *userInvitationRepository) UpdateStatus(ctx context.Context, id uint, status models.UserInvitationStatus) error {
	err := r.db.WithContext(ctx).
		Model(&models.UserInvitation{}).
		Where("id = ?", id).
		Update("status", status).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to update user invitation status")
		return fmt.Errorf("failed to update user invitation status: %w", err)
	}
	return nil
}

// GetPendingInvitations retrieves pending invitations for a tenant
func (r *userInvitationRepository) GetPendingInvitations(ctx context.Context, tenantID uint) ([]models.UserInvitation, error) {
	var invitations []models.UserInvitation
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, models.UserInvitationStatusPending).
		Preload("InvitedByUser").
		Preload("AcceptedByUser").
		Order("created_at DESC").
		Find(&invitations).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to get pending invitations")
		return nil, fmt.Errorf("failed to get pending invitations: %w", err)
	}

	return invitations, nil
}

// GetExpiredInvitations retrieves expired invitations for a tenant
func (r *userInvitationRepository) GetExpiredInvitations(ctx context.Context, tenantID uint) ([]models.UserInvitation, error) {
	var invitations []models.UserInvitation
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND (status = ? OR expires_at < ?)", tenantID, models.UserInvitationStatusExpired, time.Now()).
		Preload("InvitedByUser").
		Preload("AcceptedByUser").
		Order("created_at DESC").
		Find(&invitations).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to get expired invitations")
		return nil, fmt.Errorf("failed to get expired invitations: %w", err)
	}

	return invitations, nil
}

// ExtendExpiration extends the expiration time of an invitation
func (r *userInvitationRepository) ExtendExpiration(ctx context.Context, id uint, newExpiration time.Time) error {
	err := r.db.WithContext(ctx).
		Model(&models.UserInvitation{}).
		Where("id = ?", id).
		Update("expires_at", newExpiration).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to extend invitation expiration")
		return fmt.Errorf("failed to extend invitation expiration: %w", err)
	}
	return nil
}

// MarkAsUsed marks an invitation as used/accepted
func (r *userInvitationRepository) MarkAsUsed(ctx context.Context, token string, acceptedByUserID uint) error {
	now := time.Now()
	err := r.db.WithContext(ctx).
		Model(&models.UserInvitation{}).
		Where("token = ?", token).
		Updates(map[string]interface{}{
			"status":              models.UserInvitationStatusAccepted,
			"accepted_by_user_id": acceptedByUserID,
			"accepted_at":         now,
		}).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to mark invitation as used")
		return fmt.Errorf("failed to mark invitation as used: %w", err)
	}
	return nil
}

// MarkAsExpired marks an invitation as expired
func (r *userInvitationRepository) MarkAsExpired(ctx context.Context, id uint) error {
	err := r.db.WithContext(ctx).
		Model(&models.UserInvitation{}).
		Where("id = ?", id).
		Update("status", models.UserInvitationStatusExpired).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to mark invitation as expired")
		return fmt.Errorf("failed to mark invitation as expired: %w", err)
	}
	return nil
}

// TokenExists checks if a token exists
func (r *userInvitationRepository) TokenExists(ctx context.Context, token string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserInvitation{}).
		Where("token = ?", token).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check token existence")
		return false, fmt.Errorf("failed to check token existence: %w", err)
	}

	return count > 0, nil
}

// EmailHasPendingInvitation checks if an email has pending invitations for a tenant
func (r *userInvitationRepository) EmailHasPendingInvitation(ctx context.Context, tenantID uint, email string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserInvitation{}).
		Where("tenant_id = ? AND email = ? AND status = ?", tenantID, email, models.UserInvitationStatusPending).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check email pending invitation")
		return false, fmt.Errorf("failed to check email pending invitation: %w", err)
	}

	return count > 0, nil
}

// IsValidToken checks if a token is valid (exists, not expired, pending)
func (r *userInvitationRepository) IsValidToken(ctx context.Context, token string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserInvitation{}).
		Where("token = ? AND status = ? AND expires_at > ?", token, models.UserInvitationStatusPending, time.Now()).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to check token validity")
		return false, fmt.Errorf("failed to check token validity: %w", err)
	}

	return count > 0, nil
}

// CountByTenant counts invitations by tenant
func (r *userInvitationRepository) CountByTenant(ctx context.Context, tenantID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserInvitation{}).
		Where("tenant_id = ?", tenantID).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count invitations by tenant")
		return 0, fmt.Errorf("failed to count invitations by tenant: %w", err)
	}

	return count, nil
}

// CountByStatus counts invitations by status within a tenant
func (r *userInvitationRepository) CountByStatus(ctx context.Context, tenantID uint, status models.UserInvitationStatus) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserInvitation{}).
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count invitations by status")
		return 0, fmt.Errorf("failed to count invitations by status: %w", err)
	}

	return count, nil
}

// CountByInviter counts invitations by inviter
func (r *userInvitationRepository) CountByInviter(ctx context.Context, inviterID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.UserInvitation{}).
		Where("invited_by_user_id = ?", inviterID).
		Count(&count).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to count invitations by inviter")
		return 0, fmt.Errorf("failed to count invitations by inviter: %w", err)
	}

	return count, nil
}

// DeleteExpiredInvitations deletes expired invitations for a tenant
func (r *userInvitationRepository) DeleteExpiredInvitations(ctx context.Context, tenantID uint) error {
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND (status = ? OR expires_at < ?)", tenantID, models.UserInvitationStatusExpired, time.Now()).
		Delete(&models.UserInvitation{}).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to delete expired invitations")
		return fmt.Errorf("failed to delete expired invitations: %w", err)
	}
	return nil
}

// DeleteInvitationsByEmail deletes all invitations for an email
func (r *userInvitationRepository) DeleteInvitationsByEmail(ctx context.Context, email string) error {
	err := r.db.WithContext(ctx).
		Where("email = ?", email).
		Delete(&models.UserInvitation{}).Error

	if err != nil {
		r.logger.WithError(err).Error("Failed to delete invitations by email")
		return fmt.Errorf("failed to delete invitations by email: %w", err)
	}
	return nil
}
