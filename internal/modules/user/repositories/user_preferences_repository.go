package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// UserPreferencesRepository defines the interface for user preferences data operations
type UserPreferencesRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, preferences *models.UserPreferences) error
	GetByID(ctx context.Context, id uint) (*models.UserPreferences, error)
	GetByUserID(ctx context.Context, userID uint) (*models.UserPreferences, error)
	Update(ctx context.Context, preferences *models.UserPreferences) error
	Delete(ctx context.Context, id uint) error

	// Soft delete operations
	SoftDelete(ctx context.Context, id uint) error
	Restore(ctx context.Context, id uint) error

	// Notification preferences
	UpdateNotificationPreferences(ctx context.Context, userID uint, preferences map[string]interface{}) error
	GetNotificationPreferences(ctx context.Context, userID uint) (map[string]interface{}, error)
	UpdateNotificationTypes(ctx context.Context, userID uint, notificationTypes map[string]interface{}) error
	GetNotificationTypes(ctx context.Context, userID uint) (map[string]interface{}, error)

	// Communication preferences
	UpdateCommunicationPreferences(ctx context.Context, userID uint, allowMarketing, allowNewsletter, allowSecurity bool) error
	GetCommunicationPreferences(ctx context.Context, userID uint) (*CommunicationPreferences, error)

	// Privacy preferences
	UpdatePrivacyPreferences(ctx context.Context, userID uint, visibility models.ProfileVisibility, showOnlineStatus, allowSearchIndexing bool) error
	GetPrivacyPreferences(ctx context.Context, userID uint) (*PrivacyPreferences, error)

	// UI/UX preferences
	UpdateUIPreferences(ctx context.Context, userID uint, theme models.Theme, layout string, itemsPerPage uint) error
	GetUIPreferences(ctx context.Context, userID uint) (*UIPreferences, error)

	// Application preferences
	UpdateApplicationPreferences(ctx context.Context, userID uint, autoSave, keyboardShortcuts, showTooltips bool) error
	GetApplicationPreferences(ctx context.Context, userID uint) (*ApplicationPreferences, error)

	// Feature preferences
	UpdateFeaturePreferences(ctx context.Context, userID uint, featurePreferences map[string]interface{}) error
	GetFeaturePreferences(ctx context.Context, userID uint) (map[string]interface{}, error)

	// Custom preferences
	UpdateCustomPreferences(ctx context.Context, userID uint, customPreferences map[string]interface{}) error
	GetCustomPreferences(ctx context.Context, userID uint) (map[string]interface{}, error)

	// Bulk operations
	BulkUpdateTheme(ctx context.Context, userIDs []uint, theme models.Theme) error
	BulkUpdateLanguage(ctx context.Context, userIDs []uint, language string) error
	BulkUpdateTimezone(ctx context.Context, userIDs []uint, timezone string) error
	BulkUpdateNotificationSettings(ctx context.Context, userIDs []uint, settings map[string]interface{}) error

	// List operations
	ListByTenant(ctx context.Context, tenantID uint, pagination *pagination.CursorPagination) ([]models.UserPreferences, *pagination.CursorResponse, error)
	ListByTheme(ctx context.Context, tenantID uint, theme models.Theme, pagination *pagination.CursorPagination) ([]models.UserPreferences, *pagination.CursorResponse, error)
	ListByLanguage(ctx context.Context, tenantID uint, language string, pagination *pagination.CursorPagination) ([]models.UserPreferences, *pagination.CursorResponse, error)
	ListByTimezone(ctx context.Context, tenantID uint, timezone string, pagination *pagination.CursorPagination) ([]models.UserPreferences, *pagination.CursorResponse, error)

	// Statistics
	GetPreferencesStats(ctx context.Context, tenantID uint) (*PreferencesStats, error)
	CountByTenant(ctx context.Context, tenantID uint) (int64, error)
	CountByTheme(ctx context.Context, tenantID uint, theme models.Theme) (int64, error)
	CountByLanguage(ctx context.Context, tenantID uint, language string) (int64, error)

	// Validation
	PreferencesExists(ctx context.Context, userID uint) (bool, error)

	// Import/Export
	ExportPreferences(ctx context.Context, userID uint) (map[string]interface{}, error)
	ImportPreferences(ctx context.Context, userID uint, preferences map[string]interface{}) error

	// Preference sync
	SyncPreferencesFromUser(ctx context.Context, userID uint, userPreferences map[string]interface{}) error
	GetDefaultPreferences(ctx context.Context, tenantID uint) (*models.UserPreferences, error)
}

// CommunicationPreferences represents communication preferences
type CommunicationPreferences struct {
	AllowMarketing  bool `json:"allow_marketing"`
	AllowNewsletter bool `json:"allow_newsletter"`
	AllowSecurity   bool `json:"allow_security"`
}

// PrivacyPreferences represents privacy preferences
type PrivacyPreferences struct {
	ProfileVisibility   models.ProfileVisibility `json:"profile_visibility"`
	ShowOnlineStatus    bool                     `json:"show_online_status"`
	AllowSearchIndexing bool                     `json:"allow_search_indexing"`
}

// UIPreferences represents UI/UX preferences
type UIPreferences struct {
	Theme           models.Theme `json:"theme"`
	DashboardLayout string       `json:"dashboard_layout"`
	ItemsPerPage    uint         `json:"items_per_page"`
}

// ApplicationPreferences represents application preferences
type ApplicationPreferences struct {
	AutoSave          bool `json:"auto_save"`
	KeyboardShortcuts bool `json:"keyboard_shortcuts"`
	ShowTooltips      bool `json:"show_tooltips"`
}

// PreferencesStats represents preferences statistics
type PreferencesStats struct {
	TotalPreferences     int64                  `json:"total_preferences"`
	ThemeDistribution    map[models.Theme]int64 `json:"theme_distribution"`
	LanguageDistribution map[string]int64       `json:"language_distribution"`
	TimezoneDistribution map[string]int64       `json:"timezone_distribution"`
	NotificationStats    *NotificationStats     `json:"notification_stats"`
	PrivacyStats         *PrivacyStats          `json:"privacy_stats"`
}

// NotificationStats represents notification preferences statistics
type NotificationStats struct {
	EmailEnabled    int64 `json:"email_enabled"`
	PushEnabled     int64 `json:"push_enabled"`
	SMSEnabled      int64 `json:"sms_enabled"`
	InAppEnabled    int64 `json:"in_app_enabled"`
	MarketingAllow  int64 `json:"marketing_allow"`
	NewsletterAllow int64 `json:"newsletter_allow"`
	SecurityAllow   int64 `json:"security_allow"`
}

// PrivacyStats represents privacy preferences statistics
type PrivacyStats struct {
	PublicProfiles      int64 `json:"public_profiles"`
	PrivateProfiles     int64 `json:"private_profiles"`
	FriendsProfiles     int64 `json:"friends_profiles"`
	OnlineStatusShow    int64 `json:"online_status_show"`
	SearchIndexingAllow int64 `json:"search_indexing_allow"`
}

// UserPreferencesFilter represents filter options for user preferences queries
type UserPreferencesFilter struct {
	TenantID            uint                      `json:"tenant_id"`
	Theme               *models.Theme             `json:"theme,omitempty"`
	Language            string                    `json:"language,omitempty"`
	Timezone            string                    `json:"timezone,omitempty"`
	ProfileVisibility   *models.ProfileVisibility `json:"profile_visibility,omitempty"`
	EmailNotifications  *bool                     `json:"email_notifications,omitempty"`
	PushNotifications   *bool                     `json:"push_notifications,omitempty"`
	SMSNotifications    *bool                     `json:"sms_notifications,omitempty"`
	AllowMarketing      *bool                     `json:"allow_marketing,omitempty"`
	AllowNewsletter     *bool                     `json:"allow_newsletter,omitempty"`
	ShowOnlineStatus    *bool                     `json:"show_online_status,omitempty"`
	AllowSearchIndexing *bool                     `json:"allow_search_indexing,omitempty"`
	AutoSave            *bool                     `json:"auto_save,omitempty"`
	KeyboardShortcuts   *bool                     `json:"keyboard_shortcuts,omitempty"`
	ShowTooltips        *bool                     `json:"show_tooltips,omitempty"`
}

// UserPreferencesSearchOptions represents search options for user preferences queries
type UserPreferencesSearchOptions struct {
	Filter     UserPreferencesFilter        `json:"filter"`
	Pagination *pagination.CursorPagination `json:"pagination"`
	SortBy     string                       `json:"sort_by"`
	SortOrder  string                       `json:"sort_order"`
	WithUser   bool                         `json:"with_user"`
}
