package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// TenantMembershipRepository defines the interface for tenant membership operations
type TenantMembershipRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, membership *models.TenantMembership) error
	GetByID(ctx context.Context, id uint) (*models.TenantMembership, error)
	GetByUserAndTenant(ctx context.Context, userID, tenantID uint) (*models.TenantMembership, error)
	Update(ctx context.Context, membership *models.TenantMembership) error
	Delete(ctx context.Context, id uint) error

	// List operations
	GetByUserID(ctx context.Context, userID uint) ([]models.TenantMembership, error)
	GetPrimaryByUserID(ctx context.Context, userID uint) (*models.TenantMembership, error)
	GetByTenantID(ctx context.Context, tenantID uint, pagination *pagination.CursorPagination) ([]models.TenantMembership, *pagination.CursorResponse, error)
	GetByStatus(ctx context.Context, status models.TenantMembershipStatus, pagination *pagination.CursorPagination) ([]models.TenantMembership, *pagination.CursorResponse, error)

	// Status operations
	UpdateStatus(ctx context.Context, id uint, status models.TenantMembershipStatus) error
	GetActiveMembers(ctx context.Context, tenantID uint) ([]models.TenantMembership, error)
	GetPendingMembers(ctx context.Context, tenantID uint) ([]models.TenantMembership, error)

	// Activity tracking
	UpdateActivity(ctx context.Context, id uint) error
	GetRecentActivity(ctx context.Context, tenantID uint, limit int) ([]models.TenantMembership, error)

	// Validation
	UserBelongsToTenant(ctx context.Context, userID, tenantID uint) (bool, error)
	LocalUsernameExists(ctx context.Context, tenantID uint, username string) (bool, error)
	LocalUsernameExistsExcludingUser(ctx context.Context, tenantID uint, username string, excludeUserID uint) (bool, error)

	// Role-specific queries for new auth architecture
	GetUserRole(ctx context.Context, userID, tenantID uint) (string, error)
	IsUserActiveInTenant(ctx context.Context, userID, tenantID uint) (bool, error)

	// Statistics
	CountByTenant(ctx context.Context, tenantID uint) (int64, error)
	CountByStatus(ctx context.Context, tenantID uint, status models.TenantMembershipStatus) (int64, error)
	CountByUser(ctx context.Context, userID uint) (int64, error)
}
