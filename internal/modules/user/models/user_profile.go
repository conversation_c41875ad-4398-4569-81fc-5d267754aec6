package models

import (
	"encoding/json"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// Gender represents the gender options
// @Enum male,female,other,prefer_not_to_say
type Gender string

const (
	GenderMale           Gender = "male"
	GenderFemale         Gender = "female"
	GenderOther          Gender = "other"
	GenderPreferNotToSay Gender = "prefer_not_to_say"
)

// UserProfile represents the extended user profile information
type UserProfile struct {
	ID       uint `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID uint `gorm:"not null;index" json:"tenant_id"`
	UserID   uint `gorm:"not null;uniqueIndex:uk_user_profiles_tenant_user" json:"user_id"`

	// Personal Information
	Bio      *string `gorm:"type:text" json:"bio,omitempty" validate:"omitempty,max=1000"`
	Title    *string `gorm:"type:varchar(255)" json:"title,omitempty" validate:"omitempty,max=255"`
	Company  *string `gorm:"type:varchar(255)" json:"company,omitempty" validate:"omitempty,max=255"`
	Location *string `gorm:"type:varchar(255)" json:"location,omitempty" validate:"omitempty,max=255"`
	Website  *string `gorm:"type:varchar(500)" json:"website,omitempty" validate:"omitempty,url"`

	// Demographics
	BirthDate *time.Time `gorm:"type:date" json:"birth_date,omitempty"`
	Gender    *Gender    `gorm:"type:varchar(50)" json:"gender,omitempty" validate:"omitempty,oneof=male female other prefer_not_to_say"`

	// Address Information
	AddressLine1 *string `gorm:"type:varchar(255)" json:"address_line1,omitempty" validate:"omitempty,max=255"`
	AddressLine2 *string `gorm:"type:varchar(255)" json:"address_line2,omitempty" validate:"omitempty,max=255"`
	City         *string `gorm:"type:varchar(100)" json:"city,omitempty" validate:"omitempty,max=100"`
	State        *string `gorm:"type:varchar(100)" json:"state,omitempty" validate:"omitempty,max=100"`
	PostalCode   *string `gorm:"type:varchar(20)" json:"postal_code,omitempty" validate:"omitempty,max=20"`
	Country      *string `gorm:"type:varchar(100)" json:"country,omitempty" validate:"omitempty,max=100"`

	// Professional Information
	JobTitle   *string        `gorm:"type:varchar(255)" json:"job_title,omitempty" validate:"omitempty,max=255"`
	Department *string        `gorm:"type:varchar(255)" json:"department,omitempty" validate:"omitempty,max=255"`
	Skills     datatypes.JSON `gorm:"type:json;default:'[]'" json:"skills,omitempty" swaggertype:"array,string"`    // JSON array of skills
	Interests  datatypes.JSON `gorm:"type:json;default:'[]'" json:"interests,omitempty" swaggertype:"array,string"` // JSON array of interests

	// Social Information
	DisplayProfile bool `gorm:"default:true" json:"display_profile"`
	AllowContact   bool `gorm:"default:true" json:"allow_contact"`
	ShowEmail      bool `gorm:"default:false" json:"show_email"`
	ShowPhone      bool `gorm:"default:false" json:"show_phone"`

	// Profile Completion
	ProfileCompleted     bool  `gorm:"default:false" json:"profile_completed"`
	CompletionPercentage uint8 `gorm:"default:0" json:"completion_percentage" validate:"min=0,max=100"`

	// Custom Fields (tenant-specific)
	CustomFields datatypes.JSON `gorm:"type:json;default:'{}'" json:"custom_fields,omitempty" swaggertype:"object"`

	// Timestamps
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName returns the table name for the UserProfile model
func (UserProfile) TableName() string {
	return "user_profiles"
}

// BeforeCreate hook to set default values
func (up *UserProfile) BeforeCreate(tx *gorm.DB) error {
	// Initialize JSON fields if they are nil
	if up.Skills == nil {
		up.Skills = datatypes.JSON("[]")
	}
	if up.Interests == nil {
		up.Interests = datatypes.JSON("[]")
	}
	if up.CustomFields == nil {
		up.CustomFields = datatypes.JSON("{}")
	}

	return nil
}

// BeforeUpdate hook to calculate completion percentage
func (up *UserProfile) BeforeUpdate(tx *gorm.DB) error {
	up.calculateCompletionPercentage()
	return nil
}

// BeforeSave hook to calculate completion percentage
func (up *UserProfile) BeforeSave(tx *gorm.DB) error {
	up.calculateCompletionPercentage()
	return nil
}

// calculateCompletionPercentage calculates the profile completion percentage
func (up *UserProfile) calculateCompletionPercentage() {
	totalFields := 15 // Total number of profile fields we consider for completion
	completedFields := 0

	// Check each field for completion
	if up.Bio != nil && *up.Bio != "" {
		completedFields++
	}
	if up.Title != nil && *up.Title != "" {
		completedFields++
	}
	if up.Company != nil && *up.Company != "" {
		completedFields++
	}
	if up.Location != nil && *up.Location != "" {
		completedFields++
	}
	if up.Website != nil && *up.Website != "" {
		completedFields++
	}
	if up.BirthDate != nil {
		completedFields++
	}
	if up.Gender != nil {
		completedFields++
	}
	if up.AddressLine1 != nil && *up.AddressLine1 != "" {
		completedFields++
	}
	if up.City != nil && *up.City != "" {
		completedFields++
	}
	if up.State != nil && *up.State != "" {
		completedFields++
	}
	if up.PostalCode != nil && *up.PostalCode != "" {
		completedFields++
	}
	if up.Country != nil && *up.Country != "" {
		completedFields++
	}
	if up.JobTitle != nil && *up.JobTitle != "" {
		completedFields++
	}
	if up.Department != nil && *up.Department != "" {
		completedFields++
	}
	if up.Skills != nil && string(up.Skills) != "[]" {
		completedFields++
	}

	// Calculate percentage
	percentage := (completedFields * 100) / totalFields
	up.CompletionPercentage = uint8(percentage)

	// Mark as completed if percentage is 80% or higher
	up.ProfileCompleted = percentage >= 80
}

// GetSkills returns the skills as a string slice
func (up *UserProfile) GetSkills() []string {
	if up.Skills == nil {
		return []string{}
	}

	var skills []string
	if err := json.Unmarshal(up.Skills, &skills); err != nil {
		return []string{}
	}

	return skills
}

// SetSkills sets the skills from a string slice
func (up *UserProfile) SetSkills(skills []string) error {
	if skills == nil {
		skills = []string{}
	}

	jsonData, err := json.Marshal(skills)
	if err != nil {
		return err
	}

	up.Skills = datatypes.JSON(jsonData)
	return nil
}

// GetInterests returns the interests as a string slice
func (up *UserProfile) GetInterests() []string {
	if up.Interests == nil {
		return []string{}
	}

	var interests []string
	if err := json.Unmarshal(up.Interests, &interests); err != nil {
		return []string{}
	}

	return interests
}

// SetInterests sets the interests from a string slice
func (up *UserProfile) SetInterests(interests []string) error {
	if interests == nil {
		interests = []string{}
	}

	jsonData, err := json.Marshal(interests)
	if err != nil {
		return err
	}

	up.Interests = datatypes.JSON(jsonData)
	return nil
}

// GetCustomFields returns the custom fields as a map
func (up *UserProfile) GetCustomFields() map[string]interface{} {
	if up.CustomFields == nil {
		return make(map[string]interface{})
	}

	var customFields map[string]interface{}
	if err := json.Unmarshal(up.CustomFields, &customFields); err != nil {
		return make(map[string]interface{})
	}

	return customFields
}

// SetCustomFields sets the custom fields from a map
func (up *UserProfile) SetCustomFields(customFields map[string]interface{}) error {
	if customFields == nil {
		customFields = make(map[string]interface{})
	}

	jsonData, err := json.Marshal(customFields)
	if err != nil {
		return err
	}

	up.CustomFields = datatypes.JSON(jsonData)
	return nil
}

// GetFullAddress returns the full address as a formatted string
func (up *UserProfile) GetFullAddress() string {
	var address string

	if up.AddressLine1 != nil && *up.AddressLine1 != "" {
		address = *up.AddressLine1
	}

	if up.AddressLine2 != nil && *up.AddressLine2 != "" {
		if address != "" {
			address += ", "
		}
		address += *up.AddressLine2
	}

	if up.City != nil && *up.City != "" {
		if address != "" {
			address += ", "
		}
		address += *up.City
	}

	if up.State != nil && *up.State != "" {
		if address != "" {
			address += ", "
		}
		address += *up.State
	}

	if up.PostalCode != nil && *up.PostalCode != "" {
		if address != "" {
			address += " "
		}
		address += *up.PostalCode
	}

	if up.Country != nil && *up.Country != "" {
		if address != "" {
			address += ", "
		}
		address += *up.Country
	}

	return address
}

// UserProfileCreateRequest represents the request to create a user profile
type UserProfileCreateRequest struct {
	TenantID       uint                   `json:"tenant_id" validate:"required,min=1"`
	UserID         uint                   `json:"user_id" validate:"required,min=1"`
	Bio            *string                `json:"bio,omitempty" validate:"omitempty,max=1000"`
	Title          *string                `json:"title,omitempty" validate:"omitempty,max=255"`
	Company        *string                `json:"company,omitempty" validate:"omitempty,max=255"`
	Location       *string                `json:"location,omitempty" validate:"omitempty,max=255"`
	Website        *string                `json:"website,omitempty" validate:"omitempty,url"`
	BirthDate      *time.Time             `json:"birth_date,omitempty"`
	Gender         *Gender                `json:"gender,omitempty" validate:"omitempty,oneof=male female other prefer_not_to_say"`
	AddressLine1   *string                `json:"address_line1,omitempty" validate:"omitempty,max=255"`
	AddressLine2   *string                `json:"address_line2,omitempty" validate:"omitempty,max=255"`
	City           *string                `json:"city,omitempty" validate:"omitempty,max=100"`
	State          *string                `json:"state,omitempty" validate:"omitempty,max=100"`
	PostalCode     *string                `json:"postal_code,omitempty" validate:"omitempty,max=20"`
	Country        *string                `json:"country,omitempty" validate:"omitempty,max=100"`
	JobTitle       *string                `json:"job_title,omitempty" validate:"omitempty,max=255"`
	Department     *string                `json:"department,omitempty" validate:"omitempty,max=255"`
	Skills         []string               `json:"skills,omitempty"`
	Interests      []string               `json:"interests,omitempty"`
	DisplayProfile bool                   `json:"display_profile"`
	AllowContact   bool                   `json:"allow_contact"`
	ShowEmail      bool                   `json:"show_email"`
	ShowPhone      bool                   `json:"show_phone"`
	CustomFields   map[string]interface{} `json:"custom_fields,omitempty"`
}

// UserProfileUpdateRequest represents the request to update a user profile
type UserProfileUpdateRequest struct {
	Bio            *string                `json:"bio,omitempty" validate:"omitempty,max=1000"`
	Title          *string                `json:"title,omitempty" validate:"omitempty,max=255"`
	Company        *string                `json:"company,omitempty" validate:"omitempty,max=255"`
	Location       *string                `json:"location,omitempty" validate:"omitempty,max=255"`
	Website        *string                `json:"website,omitempty" validate:"omitempty,url"`
	BirthDate      *time.Time             `json:"birth_date,omitempty"`
	Gender         *Gender                `json:"gender,omitempty" validate:"omitempty,oneof=male female other prefer_not_to_say"`
	AddressLine1   *string                `json:"address_line1,omitempty" validate:"omitempty,max=255"`
	AddressLine2   *string                `json:"address_line2,omitempty" validate:"omitempty,max=255"`
	City           *string                `json:"city,omitempty" validate:"omitempty,max=100"`
	State          *string                `json:"state,omitempty" validate:"omitempty,max=100"`
	PostalCode     *string                `json:"postal_code,omitempty" validate:"omitempty,max=20"`
	Country        *string                `json:"country,omitempty" validate:"omitempty,max=100"`
	JobTitle       *string                `json:"job_title,omitempty" validate:"omitempty,max=255"`
	Department     *string                `json:"department,omitempty" validate:"omitempty,max=255"`
	Skills         []string               `json:"skills,omitempty"`
	Interests      []string               `json:"interests,omitempty"`
	DisplayProfile bool                   `json:"display_profile"`
	AllowContact   bool                   `json:"allow_contact"`
	ShowEmail      bool                   `json:"show_email"`
	ShowPhone      bool                   `json:"show_phone"`
	CustomFields   map[string]interface{} `json:"custom_fields,omitempty"`
}

// UserProfileResponse represents the response when returning user profile data
type UserProfileResponse struct {
	ID                   uint                   `json:"id"`
	TenantID             uint                   `json:"tenant_id"`
	UserID               uint                   `json:"user_id"`
	Bio                  *string                `json:"bio,omitempty"`
	Title                *string                `json:"title,omitempty"`
	Company              *string                `json:"company,omitempty"`
	Location             *string                `json:"location,omitempty"`
	Website              *string                `json:"website,omitempty"`
	BirthDate            *time.Time             `json:"birth_date,omitempty"`
	Gender               *Gender                `json:"gender,omitempty"`
	AddressLine1         *string                `json:"address_line1,omitempty"`
	AddressLine2         *string                `json:"address_line2,omitempty"`
	City                 *string                `json:"city,omitempty"`
	State                *string                `json:"state,omitempty"`
	PostalCode           *string                `json:"postal_code,omitempty"`
	Country              *string                `json:"country,omitempty"`
	JobTitle             *string                `json:"job_title,omitempty"`
	Department           *string                `json:"department,omitempty"`
	Skills               []string               `json:"skills,omitempty"`
	Interests            []string               `json:"interests,omitempty"`
	DisplayProfile       bool                   `json:"display_profile"`
	AllowContact         bool                   `json:"allow_contact"`
	ShowEmail            bool                   `json:"show_email"`
	ShowPhone            bool                   `json:"show_phone"`
	ProfileCompleted     bool                   `json:"profile_completed"`
	CompletionPercentage uint8                  `json:"completion_percentage"`
	CustomFields         map[string]interface{} `json:"custom_fields,omitempty"`
	CreatedAt            time.Time              `json:"created_at"`
	UpdatedAt            time.Time              `json:"updated_at"`
	FullAddress          string                 `json:"full_address,omitempty"`
}

// FromUserProfile converts a UserProfile model to UserProfileResponse
func (upr *UserProfileResponse) FromUserProfile(profile *UserProfile) {
	upr.ID = profile.ID
	upr.TenantID = profile.TenantID
	upr.UserID = profile.UserID
	upr.Bio = profile.Bio
	upr.Title = profile.Title
	upr.Company = profile.Company
	upr.Location = profile.Location
	upr.Website = profile.Website
	upr.BirthDate = profile.BirthDate
	upr.Gender = profile.Gender
	upr.AddressLine1 = profile.AddressLine1
	upr.AddressLine2 = profile.AddressLine2
	upr.City = profile.City
	upr.State = profile.State
	upr.PostalCode = profile.PostalCode
	upr.Country = profile.Country
	upr.JobTitle = profile.JobTitle
	upr.Department = profile.Department
	upr.Skills = profile.GetSkills()
	upr.Interests = profile.GetInterests()
	upr.DisplayProfile = profile.DisplayProfile
	upr.AllowContact = profile.AllowContact
	upr.ShowEmail = profile.ShowEmail
	upr.ShowPhone = profile.ShowPhone
	upr.ProfileCompleted = profile.ProfileCompleted
	upr.CompletionPercentage = profile.CompletionPercentage
	upr.CustomFields = profile.GetCustomFields()
	upr.CreatedAt = profile.CreatedAt
	upr.UpdatedAt = profile.UpdatedAt
	upr.FullAddress = profile.GetFullAddress()
}
