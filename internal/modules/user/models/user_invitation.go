package models

import (
	"time"
)

// UserInvitationStatus represents the status of a user invitation
// @Enum pending,accepted,rejected,expired,revoked
type UserInvitationStatus string

const (
	UserInvitationStatusPending  UserInvitationStatus = "pending"
	UserInvitationStatusAccepted UserInvitationStatus = "accepted"
	UserInvitationStatusRejected UserInvitationStatus = "rejected"
	UserInvitationStatusExpired  UserInvitationStatus = "expired"
	UserInvitationStatusRevoked  UserInvitationStatus = "revoked"
)

// UserInvitation represents a user invitation to join a tenant
type UserInvitation struct {
	ID        uint  `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint  `gorm:"not null;index" json:"tenant_id"`
	WebsiteID *uint `gorm:"index" json:"website_id,omitempty"`

	// Invitation Details
	Token string `gorm:"type:varchar(255);not null;uniqueIndex" json:"token"`
	Email string `gorm:"type:varchar(255);not null;index" json:"email"`

	// Invitation Configuration
	RoleID  *uint   `gorm:"index" json:"role_id,omitempty"`
	Message *string `gorm:"type:text" json:"message,omitempty"`

	// Status and Tracking
	Status        UserInvitationStatus `gorm:"type:varchar(50);default:'pending';not null;index" json:"status"`
	InvitedBy     uint                 `gorm:"not null;index" json:"invited_by"`
	InvitedUserID *uint                `gorm:"index" json:"invited_user_id,omitempty"`

	// Timestamps
	ExpiresAt  time.Time  `gorm:"not null;index" json:"expires_at"`
	AcceptedAt *time.Time `json:"accepted_at,omitempty"`
	RejectedAt *time.Time `json:"rejected_at,omitempty"`
	RevokedAt  *time.Time `json:"revoked_at,omitempty"`
	CreatedAt  time.Time  `json:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at"`

	// Relationships
	InvitedByUser *User `gorm:"foreignKey:InvitedBy" json:"invited_by_user,omitempty"`
	InvitedUser   *User `gorm:"foreignKey:InvitedUserID" json:"invited_user,omitempty"`
}

// TableName returns the table name for the UserInvitation model
func (UserInvitation) TableName() string {
	return "user_invitations"
}

// IsPending checks if the invitation is pending
func (ui *UserInvitation) IsPending() bool {
	return ui.Status == UserInvitationStatusPending && ui.ExpiresAt.After(time.Now())
}

// IsExpired checks if the invitation has expired
func (ui *UserInvitation) IsExpired() bool {
	return ui.ExpiresAt.Before(time.Now()) || ui.Status == UserInvitationStatusExpired
}

// IsAccepted checks if the invitation has been accepted
func (ui *UserInvitation) IsAccepted() bool {
	return ui.Status == UserInvitationStatusAccepted
}

// IsRejected checks if the invitation has been rejected
func (ui *UserInvitation) IsRejected() bool {
	return ui.Status == UserInvitationStatusRejected
}

// IsRevoked checks if the invitation has been revoked
func (ui *UserInvitation) IsRevoked() bool {
	return ui.Status == UserInvitationStatusRevoked
}

// Accept marks the invitation as accepted
func (ui *UserInvitation) Accept(userID uint) {
	now := time.Now()
	ui.Status = UserInvitationStatusAccepted
	ui.AcceptedAt = &now
	ui.InvitedUserID = &userID
}

// Reject marks the invitation as rejected
func (ui *UserInvitation) Reject() {
	now := time.Now()
	ui.Status = UserInvitationStatusRejected
	ui.RejectedAt = &now
}

// Revoke marks the invitation as revoked
func (ui *UserInvitation) Revoke() {
	now := time.Now()
	ui.Status = UserInvitationStatusRevoked
	ui.RevokedAt = &now
}

// MarkExpired marks the invitation as expired
func (ui *UserInvitation) MarkExpired() {
	ui.Status = UserInvitationStatusExpired
}

// UserInvitationFilter represents filters for querying user invitations
type UserInvitationFilter struct {
	TenantID       uint                 `json:"tenant_id,omitempty"`
	WebsiteID      *uint                `json:"website_id,omitempty"`
	Email          string               `json:"email,omitempty"`
	Status         UserInvitationStatus `json:"status,omitempty"`
	InvitedBy      uint                 `json:"invited_by,omitempty"`
	InvitedUserID  *uint                `json:"invited_user_id,omitempty"`
	Search         string               `json:"search,omitempty"`
	Page           int                  `json:"page,omitempty"`
	PageSize       int                  `json:"page_size,omitempty"`
	SortBy         string               `json:"sort_by,omitempty"`
	SortOrder      string               `json:"sort_order,omitempty"`
	IncludeExpired bool                 `json:"include_expired,omitempty"`
}

// UserInvitationCreateRequest represents the request to create a user invitation
type UserInvitationCreateRequest struct {
	TenantID  uint    `json:"tenant_id" validate:"required,min=1"`
	WebsiteID *uint   `json:"website_id,omitempty" validate:"omitempty,min=1"`
	Email     string  `json:"email" validate:"required,email"`
	RoleID    *uint   `json:"role_id,omitempty" validate:"omitempty,min=1"`
	Message   *string `json:"message,omitempty" validate:"omitempty,max=1000"`
	ExpiresIn int     `json:"expires_in,omitempty" validate:"omitempty,min=1,max=8760"` // Hours, max 1 year
}

// UserInvitationUpdateRequest represents the request to update a user invitation
type UserInvitationUpdateRequest struct {
	RoleID    *uint      `json:"role_id,omitempty" validate:"omitempty,min=1"`
	Message   *string    `json:"message,omitempty" validate:"omitempty,max=1000"`
	ExpiresAt *time.Time `json:"expires_at,omitempty"`
}

// UserInvitationResponse represents the response when returning user invitation data
type UserInvitationResponse struct {
	ID            uint                 `json:"id"`
	TenantID      uint                 `json:"tenant_id"`
	WebsiteID     *uint                `json:"website_id,omitempty"`
	Token         string               `json:"token"`
	Email         string               `json:"email"`
	RoleID        *uint                `json:"role_id,omitempty"`
	Message       *string              `json:"message,omitempty"`
	Status        UserInvitationStatus `json:"status"`
	InvitedBy     uint                 `json:"invited_by"`
	InvitedUserID *uint                `json:"invited_user_id,omitempty"`
	ExpiresAt     time.Time            `json:"expires_at"`
	AcceptedAt    *time.Time           `json:"accepted_at,omitempty"`
	RejectedAt    *time.Time           `json:"rejected_at,omitempty"`
	RevokedAt     *time.Time           `json:"revoked_at,omitempty"`
	CreatedAt     time.Time            `json:"created_at"`
	UpdatedAt     time.Time            `json:"updated_at"`

	// Relationships
	InvitedByUser *UserResponse `json:"invited_by_user,omitempty"`
	InvitedUser   *UserResponse `json:"invited_user,omitempty"`
}

// FromUserInvitation converts a UserInvitation model to UserInvitationResponse
func (uir *UserInvitationResponse) FromUserInvitation(ui *UserInvitation) {
	uir.ID = ui.ID
	uir.TenantID = ui.TenantID
	uir.WebsiteID = ui.WebsiteID
	uir.Token = ui.Token
	uir.Email = ui.Email
	uir.RoleID = ui.RoleID
	uir.Message = ui.Message
	uir.Status = ui.Status
	uir.InvitedBy = ui.InvitedBy
	uir.InvitedUserID = ui.InvitedUserID
	uir.ExpiresAt = ui.ExpiresAt
	uir.AcceptedAt = ui.AcceptedAt
	uir.RejectedAt = ui.RejectedAt
	uir.RevokedAt = ui.RevokedAt
	uir.CreatedAt = ui.CreatedAt
	uir.UpdatedAt = ui.UpdatedAt

	// Convert relationships
	if ui.InvitedByUser != nil {
		invitedByResponse := &UserResponse{}
		invitedByResponse.FromUser(ui.InvitedByUser)
		uir.InvitedByUser = invitedByResponse
	}
	if ui.InvitedUser != nil {
		invitedUserResponse := &UserResponse{}
		invitedUserResponse.FromUser(ui.InvitedUser)
		uir.InvitedUser = invitedUserResponse
	}
}

// UserInvitationAcceptRequest represents the request to accept an invitation
type UserInvitationAcceptRequest struct {
	Token string `json:"token" validate:"required"`
}

// UserInvitationRejectRequest represents the request to reject an invitation
type UserInvitationRejectRequest struct {
	Token string `json:"token" validate:"required"`
}
