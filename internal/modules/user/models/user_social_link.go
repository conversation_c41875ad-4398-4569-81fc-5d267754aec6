package models

import (
	"encoding/json"
	"time"

	"gorm.io/datatypes"
	"gorm.io/gorm"
)

// SocialPlatform represents the social media platform
// @Enum twitter,linkedin,github,facebook,instagram,youtube,tiktok,snapchat,discord,twitch,reddit,pinterest,medium,dev,stackoverflow,behance,dribbble,website,other
type SocialPlatform string

const (
	PlatformTwitter       SocialPlatform = "twitter"
	PlatformLinkedIn      SocialPlatform = "linkedin"
	PlatformGitHub        SocialPlatform = "github"
	PlatformFacebook      SocialPlatform = "facebook"
	PlatformInstagram     SocialPlatform = "instagram"
	PlatformYouTube       SocialPlatform = "youtube"
	PlatformTikTok        SocialPlatform = "tiktok"
	PlatformSnapchat      SocialPlatform = "snapchat"
	PlatformDiscord       SocialPlatform = "discord"
	PlatformTwitch        SocialPlatform = "twitch"
	PlatformReddit        SocialPlatform = "reddit"
	PlatformPinterest     SocialPlatform = "pinterest"
	PlatformMedium        SocialPlatform = "medium"
	PlatformDev           SocialPlatform = "dev"
	PlatformStackOverflow SocialPlatform = "stackoverflow"
	PlatformBehance       SocialPlatform = "behance"
	PlatformDribbble      SocialPlatform = "dribbble"
	PlatformWebsite       SocialPlatform = "website"
	PlatformOther         SocialPlatform = "other"
)

// UserSocialLink represents a social media link for a user
type UserSocialLink struct {
	ID       uint `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID uint `gorm:"not null;index" json:"tenant_id"`
	UserID   uint `gorm:"not null;index" json:"user_id"`

	// Social Platform Information
	Platform SocialPlatform `gorm:"type:varchar(50);not null" json:"platform" validate:"required,oneof=twitter linkedin github facebook instagram youtube tiktok snapchat discord twitch reddit pinterest medium dev stackoverflow behance dribbble website other"`
	Username string         `gorm:"type:varchar(255);not null" json:"username" validate:"required,min=1,max=255"`
	URL      string         `gorm:"type:varchar(500);not null" json:"url" validate:"required,url"`

	// Display Settings
	DisplayOrder uint `gorm:"default:0" json:"display_order"`
	IsPublic     bool `gorm:"default:true" json:"is_public"`
	IsVerified   bool `gorm:"default:false" json:"is_verified"`

	// Metadata
	ProfileData datatypes.JSON `gorm:"type:json;default:'{}'" json:"profile_data,omitempty" swaggertype:"object"`

	// Timestamps
	CreatedAt  time.Time  `json:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at"`
	VerifiedAt *time.Time `json:"verified_at,omitempty"`

	// Relationships
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName returns the table name for the UserSocialLink model
func (UserSocialLink) TableName() string {
	return "user_social_links"
}

// BeforeCreate hook to set default values
func (usl *UserSocialLink) BeforeCreate(tx *gorm.DB) error {
	// Initialize JSON fields if they are nil
	if usl.ProfileData == nil {
		usl.ProfileData = datatypes.JSON("{}")
	}

	return nil
}

// GetProfileData returns the profile data as a map
func (usl *UserSocialLink) GetProfileData() map[string]interface{} {
	if usl.ProfileData == nil {
		return make(map[string]interface{})
	}

	var profileData map[string]interface{}
	if err := json.Unmarshal(usl.ProfileData, &profileData); err != nil {
		return make(map[string]interface{})
	}

	return profileData
}

// SetProfileData sets the profile data from a map
func (usl *UserSocialLink) SetProfileData(profileData map[string]interface{}) error {
	if profileData == nil {
		profileData = make(map[string]interface{})
	}

	jsonData, err := json.Marshal(profileData)
	if err != nil {
		return err
	}

	usl.ProfileData = datatypes.JSON(jsonData)
	return nil
}

// GetProfileDataField gets a specific field from profile data
func (usl *UserSocialLink) GetProfileDataField(field string) (interface{}, bool) {
	profileData := usl.GetProfileData()
	value, exists := profileData[field]
	return value, exists
}

// SetProfileDataField sets a specific field in profile data
func (usl *UserSocialLink) SetProfileDataField(field string, value interface{}) error {
	profileData := usl.GetProfileData()
	profileData[field] = value
	return usl.SetProfileData(profileData)
}

// Verify marks the social link as verified
func (usl *UserSocialLink) Verify() {
	usl.IsVerified = true
	now := time.Now()
	usl.VerifiedAt = &now
}

// Unverify marks the social link as unverified
func (usl *UserSocialLink) Unverify() {
	usl.IsVerified = false
	usl.VerifiedAt = nil
}

// IsValidURL checks if the URL matches the expected pattern for the platform
func (usl *UserSocialLink) IsValidURL() bool {
	// Basic validation - in real implementation, you'd have platform-specific URL validation
	switch usl.Platform {
	case PlatformTwitter:
		return containsAny(usl.URL, "twitter.com", "x.com")
	case PlatformLinkedIn:
		return containsAny(usl.URL, "linkedin.com")
	case PlatformGitHub:
		return containsAny(usl.URL, "github.com")
	case PlatformFacebook:
		return containsAny(usl.URL, "facebook.com")
	case PlatformInstagram:
		return containsAny(usl.URL, "instagram.com")
	case PlatformYouTube:
		return containsAny(usl.URL, "youtube.com")
	case PlatformTikTok:
		return containsAny(usl.URL, "tiktok.com")
	case PlatformSnapchat:
		return containsAny(usl.URL, "snapchat.com")
	case PlatformDiscord:
		return containsAny(usl.URL, "discord.com", "discord.gg")
	case PlatformTwitch:
		return containsAny(usl.URL, "twitch.tv")
	case PlatformReddit:
		return containsAny(usl.URL, "reddit.com")
	case PlatformPinterest:
		return containsAny(usl.URL, "pinterest.com")
	case PlatformMedium:
		return containsAny(usl.URL, "medium.com")
	case PlatformDev:
		return containsAny(usl.URL, "dev.to")
	case PlatformStackOverflow:
		return containsAny(usl.URL, "stackoverflow.com")
	case PlatformBehance:
		return containsAny(usl.URL, "behance.net")
	case PlatformDribbble:
		return containsAny(usl.URL, "dribbble.com")
	case PlatformWebsite, PlatformOther:
		return true // Accept any valid URL for generic platforms
	default:
		return true
	}
}

// containsAny checks if a string contains any of the provided substrings
func containsAny(str string, substrings ...string) bool {
	for _, substring := range substrings {
		if len(str) >= len(substring) {
			for i := 0; i <= len(str)-len(substring); i++ {
				if str[i:i+len(substring)] == substring {
					return true
				}
			}
		}
	}
	return false
}

// GetPlatformIcon returns the icon class/name for the platform
func (usl *UserSocialLink) GetPlatformIcon() string {
	switch usl.Platform {
	case PlatformTwitter:
		return "fab fa-twitter"
	case PlatformLinkedIn:
		return "fab fa-linkedin"
	case PlatformGitHub:
		return "fab fa-github"
	case PlatformFacebook:
		return "fab fa-facebook"
	case PlatformInstagram:
		return "fab fa-instagram"
	case PlatformYouTube:
		return "fab fa-youtube"
	case PlatformTikTok:
		return "fab fa-tiktok"
	case PlatformSnapchat:
		return "fab fa-snapchat"
	case PlatformDiscord:
		return "fab fa-discord"
	case PlatformTwitch:
		return "fab fa-twitch"
	case PlatformReddit:
		return "fab fa-reddit"
	case PlatformPinterest:
		return "fab fa-pinterest"
	case PlatformMedium:
		return "fab fa-medium"
	case PlatformDev:
		return "fab fa-dev"
	case PlatformStackOverflow:
		return "fab fa-stack-overflow"
	case PlatformBehance:
		return "fab fa-behance"
	case PlatformDribbble:
		return "fab fa-dribbble"
	case PlatformWebsite:
		return "fas fa-globe"
	case PlatformOther:
		return "fas fa-link"
	default:
		return "fas fa-link"
	}
}

// GetPlatformColor returns the brand color for the platform
func (usl *UserSocialLink) GetPlatformColor() string {
	switch usl.Platform {
	case PlatformTwitter:
		return "#1DA1F2"
	case PlatformLinkedIn:
		return "#0077B5"
	case PlatformGitHub:
		return "#333333"
	case PlatformFacebook:
		return "#1877F2"
	case PlatformInstagram:
		return "#E4405F"
	case PlatformYouTube:
		return "#FF0000"
	case PlatformTikTok:
		return "#000000"
	case PlatformSnapchat:
		return "#FFFC00"
	case PlatformDiscord:
		return "#5865F2"
	case PlatformTwitch:
		return "#9146FF"
	case PlatformReddit:
		return "#FF4500"
	case PlatformPinterest:
		return "#BD081C"
	case PlatformMedium:
		return "#00AB6B"
	case PlatformDev:
		return "#0A0A0A"
	case PlatformStackOverflow:
		return "#F58025"
	case PlatformBehance:
		return "#1769FF"
	case PlatformDribbble:
		return "#EA4C89"
	case PlatformWebsite:
		return "#6B7280"
	case PlatformOther:
		return "#6B7280"
	default:
		return "#6B7280"
	}
}

// UserSocialLinkCreateRequest represents the request to create a social link
type UserSocialLinkCreateRequest struct {
	TenantID     uint                   `json:"tenant_id" validate:"required,min=1"`
	UserID       uint                   `json:"user_id" validate:"required,min=1"`
	Platform     SocialPlatform         `json:"platform" validate:"required,oneof=twitter linkedin github facebook instagram youtube tiktok snapchat discord twitch reddit pinterest medium dev stackoverflow behance dribbble website other"`
	Username     string                 `json:"username" validate:"required,min=1,max=255"`
	URL          string                 `json:"url" validate:"required,url"`
	DisplayOrder uint                   `json:"display_order"`
	IsPublic     bool                   `json:"is_public"`
	ProfileData  map[string]interface{} `json:"profile_data,omitempty"`
}

// UserSocialLinkUpdateRequest represents the request to update a social link
type UserSocialLinkUpdateRequest struct {
	Username     string                 `json:"username" validate:"required,min=1,max=255"`
	URL          string                 `json:"url" validate:"required,url"`
	DisplayOrder uint                   `json:"display_order"`
	IsPublic     bool                   `json:"is_public"`
	ProfileData  map[string]interface{} `json:"profile_data,omitempty"`
}

// UserSocialLinkResponse represents the response when returning social link data
type UserSocialLinkResponse struct {
	ID            uint                   `json:"id"`
	TenantID      uint                   `json:"tenant_id"`
	UserID        uint                   `json:"user_id"`
	Platform      SocialPlatform         `json:"platform"`
	Username      string                 `json:"username"`
	URL           string                 `json:"url"`
	DisplayOrder  uint                   `json:"display_order"`
	IsPublic      bool                   `json:"is_public"`
	IsVerified    bool                   `json:"is_verified"`
	ProfileData   map[string]interface{} `json:"profile_data,omitempty"`
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
	VerifiedAt    *time.Time             `json:"verified_at,omitempty"`
	PlatformIcon  string                 `json:"platform_icon"`
	PlatformColor string                 `json:"platform_color"`
}

// FromUserSocialLink converts a UserSocialLink model to UserSocialLinkResponse
func (uslr *UserSocialLinkResponse) FromUserSocialLink(socialLink *UserSocialLink) {
	uslr.ID = socialLink.ID
	uslr.TenantID = socialLink.TenantID
	uslr.UserID = socialLink.UserID
	uslr.Platform = socialLink.Platform
	uslr.Username = socialLink.Username
	uslr.URL = socialLink.URL
	uslr.DisplayOrder = socialLink.DisplayOrder
	uslr.IsPublic = socialLink.IsPublic
	uslr.IsVerified = socialLink.IsVerified
	uslr.ProfileData = socialLink.GetProfileData()
	uslr.CreatedAt = socialLink.CreatedAt
	uslr.UpdatedAt = socialLink.UpdatedAt
	uslr.VerifiedAt = socialLink.VerifiedAt
	uslr.PlatformIcon = socialLink.GetPlatformIcon()
	uslr.PlatformColor = socialLink.GetPlatformColor()
}

// UserSocialLinksReorderRequest represents the request to reorder social links
type UserSocialLinksReorderRequest struct {
	LinkOrders []UserSocialLinkOrder `json:"link_orders" validate:"required,min=1"`
}

// UserSocialLinkOrder represents the order of a social link
type UserSocialLinkOrder struct {
	ID           uint `json:"id" validate:"required,min=1"`
	DisplayOrder uint `json:"display_order"`
}

// UserSocialLinkVerifyRequest represents the request to verify a social link
type UserSocialLinkVerifyRequest struct {
	VerificationCode string                 `json:"verification_code,omitempty"`
	VerificationData map[string]interface{} `json:"verification_data,omitempty"`
}

// UserSocialLinkBulkUpdateRequest represents the request to bulk update social links
type UserSocialLinkBulkUpdateRequest struct {
	SocialLinks []UserSocialLinkBulkItem `json:"social_links" validate:"required,min=1"`
}

// UserSocialLinkBulkItem represents a single social link in bulk update
type UserSocialLinkBulkItem struct {
	ID           *uint                  `json:"id,omitempty"` // nil for new links
	Platform     SocialPlatform         `json:"platform" validate:"required,oneof=twitter linkedin github facebook instagram youtube tiktok snapchat discord twitch reddit pinterest medium dev stackoverflow behance dribbble website other"`
	Username     string                 `json:"username" validate:"required,min=1,max=255"`
	URL          string                 `json:"url" validate:"required,url"`
	DisplayOrder uint                   `json:"display_order"`
	IsPublic     bool                   `json:"is_public"`
	ProfileData  map[string]interface{} `json:"profile_data,omitempty"`
}

// SocialPlatformInfo represents information about a social platform
type SocialPlatformInfo struct {
	Platform    SocialPlatform `json:"platform"`
	Name        string         `json:"name"`
	Icon        string         `json:"icon"`
	Color       string         `json:"color"`
	URLPattern  string         `json:"url_pattern"`
	Description string         `json:"description"`
}

// GetSupportedPlatforms returns a list of supported social platforms
func GetSupportedPlatforms() []SocialPlatformInfo {
	return []SocialPlatformInfo{
		{Platform: PlatformTwitter, Name: "Twitter", Icon: "fab fa-twitter", Color: "#1DA1F2", URLPattern: "https://twitter.com/username", Description: "Twitter social media platform"},
		{Platform: PlatformLinkedIn, Name: "LinkedIn", Icon: "fab fa-linkedin", Color: "#0077B5", URLPattern: "https://linkedin.com/in/username", Description: "Professional networking platform"},
		{Platform: PlatformGitHub, Name: "GitHub", Icon: "fab fa-github", Color: "#333333", URLPattern: "https://github.com/username", Description: "Code repository hosting platform"},
		{Platform: PlatformFacebook, Name: "Facebook", Icon: "fab fa-facebook", Color: "#1877F2", URLPattern: "https://facebook.com/username", Description: "Social networking platform"},
		{Platform: PlatformInstagram, Name: "Instagram", Icon: "fab fa-instagram", Color: "#E4405F", URLPattern: "https://instagram.com/username", Description: "Photo and video sharing platform"},
		{Platform: PlatformYouTube, Name: "YouTube", Icon: "fab fa-youtube", Color: "#FF0000", URLPattern: "https://youtube.com/c/username", Description: "Video sharing platform"},
		{Platform: PlatformTikTok, Name: "TikTok", Icon: "fab fa-tiktok", Color: "#000000", URLPattern: "https://tiktok.com/@username", Description: "Short video platform"},
		{Platform: PlatformSnapchat, Name: "Snapchat", Icon: "fab fa-snapchat", Color: "#FFFC00", URLPattern: "https://snapchat.com/add/username", Description: "Multimedia messaging platform"},
		{Platform: PlatformDiscord, Name: "Discord", Icon: "fab fa-discord", Color: "#5865F2", URLPattern: "https://discord.com/users/username", Description: "Voice and text chat platform"},
		{Platform: PlatformTwitch, Name: "Twitch", Icon: "fab fa-twitch", Color: "#9146FF", URLPattern: "https://twitch.tv/username", Description: "Live streaming platform"},
		{Platform: PlatformReddit, Name: "Reddit", Icon: "fab fa-reddit", Color: "#FF4500", URLPattern: "https://reddit.com/u/username", Description: "Social news aggregation platform"},
		{Platform: PlatformPinterest, Name: "Pinterest", Icon: "fab fa-pinterest", Color: "#BD081C", URLPattern: "https://pinterest.com/username", Description: "Image sharing and discovery platform"},
		{Platform: PlatformMedium, Name: "Medium", Icon: "fab fa-medium", Color: "#00AB6B", URLPattern: "https://medium.com/@username", Description: "Publishing platform"},
		{Platform: PlatformDev, Name: "DEV Community", Icon: "fab fa-dev", Color: "#0A0A0A", URLPattern: "https://dev.to/username", Description: "Community for software developers"},
		{Platform: PlatformStackOverflow, Name: "Stack Overflow", Icon: "fab fa-stack-overflow", Color: "#F58025", URLPattern: "https://stackoverflow.com/users/id/username", Description: "Question and answer platform for programmers"},
		{Platform: PlatformBehance, Name: "Behance", Icon: "fab fa-behance", Color: "#1769FF", URLPattern: "https://behance.net/username", Description: "Creative portfolio platform"},
		{Platform: PlatformDribbble, Name: "Dribbble", Icon: "fab fa-dribbble", Color: "#EA4C89", URLPattern: "https://dribbble.com/username", Description: "Design portfolio platform"},
		{Platform: PlatformWebsite, Name: "Website", Icon: "fas fa-globe", Color: "#6B7280", URLPattern: "https://example.com", Description: "Personal or business website"},
		{Platform: PlatformOther, Name: "Other", Icon: "fas fa-link", Color: "#6B7280", URLPattern: "https://example.com", Description: "Other social media platform"},
	}
}
