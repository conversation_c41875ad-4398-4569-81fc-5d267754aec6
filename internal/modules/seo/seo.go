package seo

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"gorm.io/gorm"
)

// SEOModule represents the SEO module with all its components
type SEOModule struct {
	// Repositories
	RedirectRepository repositories.SEORedirectRepository
	SitemapRepository  repositories.SEOSitemapRepository
	MetaRepository     repositories.SEOMetaRepository

	// Services
	RedirectService services.SEORedirectService
	SitemapService  services.SEOSitemapService
	MetaService     services.SEOMetaService

	// Handlers
	RedirectHandler *handlers.SEORedirectHandler
	SitemapHandler  *handlers.SEOSitemapHandler
	MetaHandler     *handlers.SEOMetaHandler
}

// NewSEOModule creates a new SEO module instance
func NewSEOModule(db *gorm.DB, validator validator.Validator, logger utils.Logger) *SEOModule {
	// Initialize repositories
	redirectRepo := repositories.NewSEORedirectRepository(db)
	sitemapRepo := repositories.NewSEOSitemapRepository(db)
	sqlDB, _ := db.DB()
	metaRepo := repositories.NewSEOMetaRepository(sqlDB)

	// Initialize services
	redirectService := services.NewSEORedirectService(redirectRepo, logger)
	sitemapService := services.NewSEOSitemapService(sitemapRepo, logger)
	metaService := services.NewSEOMetaService(metaRepo, logger)

	// Initialize handlers
	redirectHandler := handlers.NewSEORedirectHandler(redirectService, logger)
	sitemapHandler := handlers.NewSEOSitemapHandler(sitemapService, logger)
	metaHandler := handlers.NewSEOMetaHandler(metaService, validator, logger)

	return &SEOModule{
		RedirectRepository: redirectRepo,
		SitemapRepository:  sitemapRepo,
		MetaRepository:     metaRepo,
		RedirectService:    redirectService,
		SitemapService:     sitemapService,
		MetaService:        metaService,
		RedirectHandler:    redirectHandler,
		SitemapHandler:     sitemapHandler,
		MetaHandler:        metaHandler,
	}
}

// RegisterRoutes registers all SEO module routes
func (m *SEOModule) RegisterRoutes(router *gin.RouterGroup) {
	RegisterRoutes(router, m.RedirectHandler, m.SitemapHandler, m.MetaHandler)
}

// GetRedirectService returns the redirect service
func (m *SEOModule) GetRedirectService() services.SEORedirectService {
	return m.RedirectService
}

// GetSitemapService returns the sitemap service
func (m *SEOModule) GetSitemapService() services.SEOSitemapService {
	return m.SitemapService
}

// GetMetaService returns the meta service
func (m *SEOModule) GetMetaService() services.SEOMetaService {
	return m.MetaService
}
