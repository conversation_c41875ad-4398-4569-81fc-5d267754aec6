package models

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"time"
)

// Keyword represents a keyword tracked for SEO
type Keyword struct {
	ID        uint `json:"id" db:"id"`
	WebsiteID uint `json:"website_id" db:"website_id"`
	TenantID  uint `json:"tenant_id" db:"tenant_id"`

	// Keyword Information
	Keyword     string  `json:"keyword" db:"keyword"`
	KeywordHash string  `json:"keyword_hash" db:"keyword_hash"`
	Language    string  `json:"language" db:"language"`
	Country     string  `json:"country" db:"country"`
	Location    *string `json:"location" db:"location"`
	Device      string  `json:"device" db:"device"`

	// Search Volume and Competition
	SearchVolume     *uint    `json:"search_volume" db:"search_volume"`
	MonthlySearches  *uint    `json:"monthly_searches" db:"monthly_searches"`
	Competition      *float64 `json:"competition" db:"competition"`
	CompetitionLevel string   `json:"competition_level" db:"competition_level"`
	CostPerClick     *float64 `json:"cost_per_click" db:"cost_per_click"`

	// Ranking Information
	CurrentRank  *uint    `json:"current_rank" db:"current_rank"`
	PreviousRank *uint    `json:"previous_rank" db:"previous_rank"`
	BestRank     *uint    `json:"best_rank" db:"best_rank"`
	WorstRank    *uint    `json:"worst_rank" db:"worst_rank"`
	AverageRank  *float64 `json:"average_rank" db:"average_rank"`
	RankingURL   *string  `json:"ranking_url" db:"ranking_url"`

	// Tracking Configuration
	TrackingEnabled   bool       `json:"tracking_enabled" db:"tracking_enabled"`
	TrackingFrequency string     `json:"tracking_frequency" db:"tracking_frequency"`
	LastTracked       *time.Time `json:"last_tracked" db:"last_tracked"`
	NextTracking      *time.Time `json:"next_tracking" db:"next_tracking"`

	// Keyword Metrics
	Difficulty       *float64 `json:"difficulty" db:"difficulty"`
	Opportunity      *float64 `json:"opportunity" db:"opportunity"`
	TrendScore       *float64 `json:"trend_score" db:"trend_score"`
	SeasonalityScore *float64 `json:"seasonality_score" db:"seasonality_score"`

	// SERP Features
	SERPFeatures    SERPFeatures `json:"serp_features" db:"serp_features"`
	FeaturedSnippet *bool        `json:"featured_snippet" db:"featured_snippet"`
	LocalPack       *bool        `json:"local_pack" db:"local_pack"`
	KnowledgePanel  *bool        `json:"knowledge_panel" db:"knowledge_panel"`
	ImagePack       *bool        `json:"image_pack" db:"image_pack"`
	VideoPack       *bool        `json:"video_pack" db:"video_pack"`

	// Keyword Grouping
	KeywordGroupID *uint       `json:"keyword_group_id" db:"keyword_group_id"`
	Tags           KeywordTags `json:"tags" db:"tags"`
	Category       *string     `json:"category" db:"category"`
	Intent         string      `json:"intent" db:"intent"`

	// Performance Metrics
	Clicks          *uint    `json:"clicks" db:"clicks"`
	Impressions     *uint    `json:"impressions" db:"impressions"`
	CTR             *float64 `json:"ctr" db:"ctr"`
	AveragePosition *float64 `json:"average_position" db:"average_position"`

	// Target and Goals
	TargetRank       *uint   `json:"target_rank" db:"target_rank"`
	TargetURL        *string `json:"target_url" db:"target_url"`
	IsTargetAchieved bool    `json:"is_target_achieved" db:"is_target_achieved"`

	// Related Keywords
	RelatedKeywords RelatedKeywords `json:"related_keywords" db:"related_keywords"`

	// Trend Data
	TrendData TrendData `json:"trend_data" db:"trend_data"`

	// Competitor Analysis
	CompetitorData CompetitorData `json:"competitor_data" db:"competitor_data"`

	// Status and Notes
	Status string  `json:"status" db:"status"`
	Notes  *string `json:"notes" db:"notes"`

	// Metadata
	CreatedBy *uint     `json:"created_by" db:"created_by"`
	CreatedAt time.Time `json:"created_at" db:"created_at"`
	UpdatedAt time.Time `json:"updated_at" db:"updated_at"`
}

// SERPFeatures contains SERP features for a keyword
type SERPFeatures []SERPFeature

// SERPFeature represents a single SERP feature
type SERPFeature struct {
	Type        string `json:"type"`
	Present     bool   `json:"present"`
	Position    int    `json:"position,omitempty"`
	Domain      string `json:"domain,omitempty"`
	URL         string `json:"url,omitempty"`
	Title       string `json:"title,omitempty"`
	Description string `json:"description,omitempty"`
}

// Value implements the driver.Valuer interface
func (s SERPFeatures) Value() (driver.Value, error) {
	if s == nil {
		return "[]", nil
	}
	return json.Marshal(s)
}

// Scan implements the sql.Scanner interface
func (s *SERPFeatures) Scan(value interface{}) error {
	if value == nil {
		*s = make(SERPFeatures, 0)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, s)
}

// KeywordTags contains tags for a keyword
type KeywordTags []string

// Value implements the driver.Valuer interface
func (k KeywordTags) Value() (driver.Value, error) {
	if k == nil {
		return "[]", nil
	}
	return json.Marshal(k)
}

// Scan implements the sql.Scanner interface
func (k *KeywordTags) Scan(value interface{}) error {
	if value == nil {
		*k = make(KeywordTags, 0)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, k)
}

// RelatedKeywords contains related keywords
type RelatedKeywords []RelatedKeyword

// RelatedKeyword represents a related keyword
type RelatedKeyword struct {
	Keyword      string  `json:"keyword"`
	Relevance    float64 `json:"relevance"`
	SearchVolume uint    `json:"search_volume"`
	Difficulty   float64 `json:"difficulty"`
	Type         string  `json:"type"` // synonym, long_tail, question, etc.
}

// Value implements the driver.Valuer interface
func (r RelatedKeywords) Value() (driver.Value, error) {
	if r == nil {
		return "[]", nil
	}
	return json.Marshal(r)
}

// Scan implements the sql.Scanner interface
func (r *RelatedKeywords) Scan(value interface{}) error {
	if value == nil {
		*r = make(RelatedKeywords, 0)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, r)
}

// TrendData contains trend data for a keyword
type TrendData []TrendDataPoint

// TrendDataPoint represents a single trend data point
type TrendDataPoint struct {
	Date         time.Time `json:"date"`
	SearchVolume uint      `json:"search_volume"`
	Rank         uint      `json:"rank,omitempty"`
	Competition  float64   `json:"competition,omitempty"`
	CPC          float64   `json:"cpc,omitempty"`
}

// Value implements the driver.Valuer interface
func (t TrendData) Value() (driver.Value, error) {
	if t == nil {
		return "[]", nil
	}
	return json.Marshal(t)
}

// Scan implements the sql.Scanner interface
func (t *TrendData) Scan(value interface{}) error {
	if value == nil {
		*t = make(TrendData, 0)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, t)
}

// CompetitorData contains competitor analysis data
type CompetitorData []CompetitorKeyword

// CompetitorKeyword represents competitor data for a keyword
type CompetitorKeyword struct {
	Domain       string    `json:"domain"`
	URL          string    `json:"url"`
	Rank         uint      `json:"rank"`
	Title        string    `json:"title"`
	Description  string    `json:"description"`
	TrafficShare float64   `json:"traffic_share"`
	LastSeen     time.Time `json:"last_seen"`
}

// Value implements the driver.Valuer interface
func (c CompetitorData) Value() (driver.Value, error) {
	if c == nil {
		return "[]", nil
	}
	return json.Marshal(c)
}

// Scan implements the sql.Scanner interface
func (c *CompetitorData) Scan(value interface{}) error {
	if value == nil {
		*c = make(CompetitorData, 0)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return errors.New("type assertion to []byte failed")
	}

	return json.Unmarshal(bytes, c)
}

// CreateKeywordRequest represents the request to create a keyword
type CreateKeywordRequest struct {
	WebsiteID uint    `json:"website_id" validate:"required"`
	Keyword   string  `json:"keyword" validate:"required,min=1,max=255"`
	Language  string  `json:"language" validate:"required,len=2"`
	Country   string  `json:"country" validate:"required,len=2"`
	Location  *string `json:"location"`
	Device    string  `json:"device" validate:"required,oneof=desktop mobile tablet"`

	// Tracking Configuration
	TrackingEnabled   *bool   `json:"tracking_enabled"`
	TrackingFrequency *string `json:"tracking_frequency" validate:"omitempty,oneof=daily weekly monthly"`

	// Grouping and Categorization
	KeywordGroupID *uint        `json:"keyword_group_id"`
	Tags           *KeywordTags `json:"tags"`
	Category       *string      `json:"category"`
	Intent         *string      `json:"intent" validate:"omitempty,oneof=informational navigational transactional commercial"`

	// Target and Goals
	TargetRank *uint   `json:"target_rank" validate:"omitempty,min=1,max=100"`
	TargetURL  *string `json:"target_url" validate:"omitempty,url"`

	// Notes
	Notes *string `json:"notes"`
}

// UpdateKeywordRequest represents the request to update a keyword
type UpdateKeywordRequest struct {
	Language *string `json:"language" validate:"omitempty,len=2"`
	Country  *string `json:"country" validate:"omitempty,len=2"`
	Location *string `json:"location"`
	Device   *string `json:"device" validate:"omitempty,oneof=desktop mobile tablet"`

	// Tracking Configuration
	TrackingEnabled   *bool   `json:"tracking_enabled"`
	TrackingFrequency *string `json:"tracking_frequency" validate:"omitempty,oneof=daily weekly monthly"`

	// Grouping and Categorization
	KeywordGroupID *uint        `json:"keyword_group_id"`
	Tags           *KeywordTags `json:"tags"`
	Category       *string      `json:"category"`
	Intent         *string      `json:"intent" validate:"omitempty,oneof=informational navigational transactional commercial"`

	// Target and Goals
	TargetRank *uint   `json:"target_rank" validate:"omitempty,min=1,max=100"`
	TargetURL  *string `json:"target_url" validate:"omitempty,url"`

	// Status and Notes
	Status *string `json:"status" validate:"omitempty,oneof=active inactive paused deleted"`
	Notes  *string `json:"notes"`
}

// KeywordResponse represents the response for keyword operations
type KeywordResponse struct {
	*Keyword
	RankHistory        []RankHistoryPoint   `json:"rank_history,omitempty"`
	VolumeHistory      []VolumeHistoryPoint `json:"volume_history,omitempty"`
	CompetitorAnalysis *CompetitorAnalysis  `json:"competitor_analysis,omitempty"`
	Suggestions        []KeywordSuggestion  `json:"suggestions,omitempty"`
}

// RankHistoryPoint represents a point in ranking history
type RankHistoryPoint struct {
	Date time.Time `json:"date"`
	Rank uint      `json:"rank"`
	URL  string    `json:"url"`
}

// VolumeHistoryPoint represents a point in volume history
type VolumeHistoryPoint struct {
	Date   time.Time `json:"date"`
	Volume uint      `json:"volume"`
	Trend  float64   `json:"trend"`
}

// CompetitorAnalysis contains competitor analysis results
type CompetitorAnalysis struct {
	TopCompetitors   []CompetitorKeyword `json:"top_competitors"`
	MarketShare      float64             `json:"market_share"`
	CompetitionLevel string              `json:"competition_level"`
	Opportunities    []Opportunity       `json:"opportunities"`
	Threats          []Threat            `json:"threats"`
}

// Opportunity represents an SEO opportunity
type Opportunity struct {
	Type        string  `json:"type"`
	Description string  `json:"description"`
	Impact      string  `json:"impact"`
	Difficulty  string  `json:"difficulty"`
	Score       float64 `json:"score"`
}

// Threat represents an SEO threat
type Threat struct {
	Type        string  `json:"type"`
	Description string  `json:"description"`
	Severity    string  `json:"severity"`
	Score       float64 `json:"score"`
}

// KeywordSuggestion represents a keyword suggestion
type KeywordSuggestion struct {
	Keyword      string  `json:"keyword"`
	SearchVolume uint    `json:"search_volume"`
	Difficulty   float64 `json:"difficulty"`
	Opportunity  float64 `json:"opportunity"`
	Relevance    float64 `json:"relevance"`
	Type         string  `json:"type"`
}

// KeywordFilter represents filters for keyword queries
type KeywordFilter struct {
	WebsiteID         *uint      `json:"website_id"`
	KeywordGroupID    *uint      `json:"keyword_group_id"`
	Language          *string    `json:"language"`
	Country           *string    `json:"country"`
	Device            *string    `json:"device"`
	Status            *string    `json:"status"`
	TrackingEnabled   *bool      `json:"tracking_enabled"`
	Intent            *string    `json:"intent"`
	Category          *string    `json:"category"`
	MinRank           *uint      `json:"min_rank"`
	MaxRank           *uint      `json:"max_rank"`
	MinSearchVolume   *uint      `json:"min_search_volume"`
	MaxSearchVolume   *uint      `json:"max_search_volume"`
	MinDifficulty     *float64   `json:"min_difficulty"`
	MaxDifficulty     *float64   `json:"max_difficulty"`
	IsTargetAchieved  *bool      `json:"is_target_achieved"`
	CreatedBy         *uint      `json:"created_by"`
	CreatedAfter      *time.Time `json:"created_after"`
	CreatedBefore     *time.Time `json:"created_before"`
	LastTrackedAfter  *time.Time `json:"last_tracked_after"`
	LastTrackedBefore *time.Time `json:"last_tracked_before"`
}

// KeywordGroup represents a group of keywords
type KeywordGroup struct {
	ID           uint      `json:"id" db:"id"`
	WebsiteID    uint      `json:"website_id" db:"website_id"`
	TenantID     uint      `json:"tenant_id" db:"tenant_id"`
	Name         string    `json:"name" db:"name"`
	Description  *string   `json:"description" db:"description"`
	Color        *string   `json:"color" db:"color"`
	Icon         *string   `json:"icon" db:"icon"`
	KeywordCount uint      `json:"keyword_count" db:"keyword_count"`
	Status       string    `json:"status" db:"status"`
	CreatedBy    *uint     `json:"created_by" db:"created_by"`
	CreatedAt    time.Time `json:"created_at" db:"created_at"`
	UpdatedAt    time.Time `json:"updated_at" db:"updated_at"`
}

// CreateKeywordGroupRequest represents the request to create a keyword group
type CreateKeywordGroupRequest struct {
	WebsiteID   uint    `json:"website_id" validate:"required"`
	Name        string  `json:"name" validate:"required,min=1,max=255"`
	Description *string `json:"description"`
	Color       *string `json:"color"`
	Icon        *string `json:"icon"`
}

// UpdateKeywordGroupRequest represents the request to update a keyword group
type UpdateKeywordGroupRequest struct {
	Name        *string `json:"name" validate:"omitempty,min=1,max=255"`
	Description *string `json:"description"`
	Color       *string `json:"color"`
	Icon        *string `json:"icon"`
	Status      *string `json:"status" validate:"omitempty,oneof=active inactive deleted"`
}

// TableName returns the table name for the Keyword model
func (Keyword) TableName() string {
	return "seo_keywords"
}

// Validate validates the Keyword model
func (k *Keyword) Validate() error {
	if k.WebsiteID == 0 {
		return errors.New("website_id is required")
	}

	if k.TenantID == 0 {
		return errors.New("tenant_id is required")
	}

	if k.Keyword == "" {
		return errors.New("keyword is required")
	}

	if k.Language == "" {
		return errors.New("language is required")
	}

	if k.Country == "" {
		return errors.New("country is required")
	}

	if k.Device == "" {
		return errors.New("device is required")
	}

	return nil
}

// IsActive returns true if the keyword is active
func (k *Keyword) IsActive() bool {
	return k.Status == KeywordStatusActive
}

// IsTracking returns true if tracking is enabled
func (k *Keyword) IsTracking() bool {
	return k.TrackingEnabled
}

// NeedsTracking returns true if the keyword needs tracking
func (k *Keyword) NeedsTracking() bool {
	if !k.TrackingEnabled {
		return false
	}

	if k.NextTracking == nil {
		return true
	}

	return time.Now().After(*k.NextTracking)
}

// GetRankChange returns the rank change from previous check
func (k *Keyword) GetRankChange() int {
	if k.CurrentRank == nil || k.PreviousRank == nil {
		return 0
	}

	// Lower rank number is better, so positive change means improvement
	return int(*k.PreviousRank) - int(*k.CurrentRank)
}

// HasAchievedTarget returns true if the target rank is achieved
func (k *Keyword) HasAchievedTarget() bool {
	if k.TargetRank == nil || k.CurrentRank == nil {
		return false
	}

	return *k.CurrentRank <= *k.TargetRank
}

// GetOpportunityScore calculates the opportunity score for the keyword
func (k *Keyword) GetOpportunityScore() float64 {
	if k.Opportunity != nil {
		return *k.Opportunity
	}

	// Calculate based on search volume, difficulty, and current rank
	var score float64

	if k.SearchVolume != nil {
		score += float64(*k.SearchVolume) * 0.4
	}

	if k.Difficulty != nil {
		score += (100 - *k.Difficulty) * 0.3
	}

	if k.CurrentRank != nil {
		score += (100 - float64(*k.CurrentRank)) * 0.3
	}

	return score
}

// Constants for keyword
const (
	KeywordStatusActive   = "active"
	KeywordStatusInactive = "inactive"
	KeywordStatusPaused   = "paused"
	KeywordStatusDeleted  = "deleted"

	KeywordIntentInformational = "informational"
	KeywordIntentNavigational  = "navigational"
	KeywordIntentTransactional = "transactional"
	KeywordIntentCommercial    = "commercial"

	KeywordDeviceDesktop = "desktop"
	KeywordDeviceMobile  = "mobile"
	KeywordDeviceTablet  = "tablet"

	TrackingFrequencyDaily   = "daily"
	TrackingFrequencyWeekly  = "weekly"
	TrackingFrequencyMonthly = "monthly"

	CompetitionLevelLow    = "low"
	CompetitionLevelMedium = "medium"
	CompetitionLevelHigh   = "high"

	SERPFeatureTypeSnippet        = "snippet"
	SERPFeatureTypeLocalPack      = "local_pack"
	SERPFeatureTypeKnowledgePanel = "knowledge_panel"
	SERPFeatureTypeImagePack      = "image_pack"
	SERPFeatureTypeVideoPack      = "video_pack"
	SERPFeatureTypeNews           = "news"
	SERPFeatureTypeShopping       = "shopping"
	SERPFeatureTypeAds            = "ads"

	RelatedKeywordTypeSynonym    = "synonym"
	RelatedKeywordTypeLongTail   = "long_tail"
	RelatedKeywordTypeQuestion   = "question"
	RelatedKeywordTypeComparison = "comparison"
	RelatedKeywordTypeModifier   = "modifier"
)
