package services

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/repositories"
	pkgcontext "github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// SEOAuditService handles SEO audit operations
type SEOAuditService struct {
	auditRepo repositories.SEOAuditRepository
	metaRepo  repositories.SEOMetaRepository
	logger    *logrus.Logger
	analyzer  SEOAnalyzer
}

// NewSEOAuditService creates a new SEO audit service
func NewSEOAuditService(
	auditRepo repositories.SEOAuditRepository,
	metaRepo repositories.SEOMetaRepository,
	logger *logrus.Logger,
	analyzer SEOAnalyzer,
) *SEOAuditService {
	return &SEOAuditService{
		auditRepo: auditRepo,
		metaRepo:  metaRepo,
		logger:    logger,
		analyzer:  analyzer,
	}
}

// CreateAudit creates a new SEO audit
func (s *SEOAuditService) CreateAudit(ctx context.Context, websiteID uint, auditType string) (*models.SEOAudit, error) {
	tenantCtx, err := pkgcontext.FromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("tenant context is required: %w", err)
	}

	audit := &models.SEOAudit{
		TenantID:       tenantCtx.GetScope(),
		WebsiteID:      websiteID,
		AuditType:      models.AuditType(auditType),
		AuditName:      fmt.Sprintf("Manual Audit - %s", time.Now().Format("2006-01-02 15:04")),
		Status:         models.AuditStatusRunning,
		AuditStartedAt: time.Now(),
		AuditTool:      models.AuditToolInternal,
		OverallScore:   0,
		CrawlDepth:     5,
		MaxPages:       100,
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	if err = s.auditRepo.Create(ctx, audit); err != nil {
		return nil, fmt.Errorf("failed to create audit: %w", err)
	}

	// Start audit process in background
	go s.runAudit(context.Background(), audit)

	return audit, nil
}

// GetAudit retrieves an audit by ID
func (s *SEOAuditService) GetAudit(ctx context.Context, id uint) (*models.SEOAudit, error) {
	tenantCtx, err := pkgcontext.FromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("tenant context is required: %w", err)
	}

	audit, err := s.auditRepo.GetByID(ctx, tenantCtx.GetScope(), id)
	if err != nil {
		return nil, fmt.Errorf("failed to get audit: %w", err)
	}

	return audit, nil
}

// ListAudits lists audits with filtering
func (s *SEOAuditService) ListAudits(ctx context.Context, websiteID uint, limit, offset int) ([]*models.SEOAudit, *pagination.Cursor, error) {
	tenantCtx, err := pkgcontext.FromContext(ctx)
	if err != nil {
		return nil, nil, fmt.Errorf("tenant context is required: %w", err)
	}

	filter := &models.SEOAuditFilter{
		WebsiteID: &websiteID,
	}

	cursor := &pagination.Cursor{}
	if offset > 0 {
		cursor.ID = int64(offset)
	}

	audits, nextCursor, err := s.auditRepo.List(ctx, tenantCtx.GetScope(), filter, cursor)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to list audits: %w", err)
	}

	return audits, nextCursor, nil
}

// GetLatestAudit retrieves the latest audit for a website
func (s *SEOAuditService) GetLatestAudit(ctx context.Context, websiteID uint) (*models.SEOAudit, error) {
	tenantCtx, err := pkgcontext.FromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("tenant context is required: %w", err)
	}

	audit, err := s.auditRepo.GetLatestByWebsiteID(ctx, tenantCtx.GetScope(), websiteID)
	if err != nil {
		return nil, fmt.Errorf("failed to get latest audit: %w", err)
	}

	return audit, nil
}

// DeleteAudit deletes an audit
func (s *SEOAuditService) DeleteAudit(ctx context.Context, id uint) error {
	tenantCtx, err := pkgcontext.FromContext(ctx)
	if err != nil {
		return fmt.Errorf("tenant context is required: %w", err)
	}

	err = s.auditRepo.Delete(ctx, tenantCtx.GetScope(), id)
	if err != nil {
		return fmt.Errorf("failed to delete audit: %w", err)
	}

	return nil
}

// RunManualAudit runs a manual audit for a website
func (s *SEOAuditService) RunManualAudit(ctx context.Context, websiteID uint) (*models.SEOAudit, error) {
	tenantCtx, err := pkgcontext.FromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("tenant context is required: %w", err)
	}

	audit := &models.SEOAudit{
		TenantID:       tenantCtx.GetScope(),
		WebsiteID:      websiteID,
		AuditType:      models.AuditTypeFull,
		AuditName:      fmt.Sprintf("Manual Audit - %s", time.Now().Format("2006-01-02 15:04")),
		Status:         models.AuditStatusRunning,
		AuditTool:      models.AuditToolInternal,
		OverallScore:   0,
		CrawlDepth:     5,
		MaxPages:       100,
		AuditStartedAt: time.Now(),
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	if err = s.auditRepo.Create(ctx, audit); err != nil {
		return nil, fmt.Errorf("failed to create manual audit: %w", err)
	}

	// Run audit immediately
	err = s.runAudit(ctx, audit)
	if err != nil {
		s.logger.WithError(err).Error("Manual audit failed")
		audit.Status = "failed"
		errorMsg := err.Error()
		audit.ErrorMessage = &errorMsg
		s.auditRepo.Update(ctx, audit.TenantID, audit.ID, audit)
		return audit, fmt.Errorf("audit failed: %w", err)
	}

	return audit, nil
}

// ScheduleAudit schedules a recurring audit
func (s *SEOAuditService) ScheduleAudit(ctx context.Context, websiteID uint, frequency string) error {
	tenantCtx, err := pkgcontext.FromContext(ctx)
	if err != nil {
		return fmt.Errorf("tenant context is required: %w", err)
	}

	// This would integrate with a job scheduler
	// For now, we'll just log the schedule
	s.logger.WithFields(logrus.Fields{
		"tenant_id":  tenantCtx.GetScope(),
		"website_id": websiteID,
		"frequency":  frequency,
	}).Info("Audit scheduled")

	return nil
}

// runAudit runs the actual audit process
func (s *SEOAuditService) runAudit(ctx context.Context, audit *models.SEOAudit) error {
	defer func() {
		if r := recover(); r != nil {
			s.logger.WithField("panic", r).Error("Audit panicked")
			audit.Status = models.AuditStatusFailed
			errorMsg := fmt.Sprintf("Audit panicked: %v", r)
			audit.ErrorMessage = &errorMsg
			now := time.Now()
			audit.AuditCompletedAt = &now
			s.auditRepo.Update(ctx, audit.TenantID, audit.ID, audit)
		}
	}()

	// Get all SEO meta for this website
	filter := &models.SEOMetaFilter{
		WebsiteID: &audit.WebsiteID,
		Status:    stringPtr("active"),
	}

	seoMetas, _, err := s.metaRepo.List(ctx, audit.TenantID, filter, nil)
	if err != nil {
		return fmt.Errorf("failed to get SEO meta for audit: %w", err)
	}

	if len(seoMetas) == 0 {
		audit.Status = models.AuditStatusCompleted
		now := time.Now()
		audit.AuditCompletedAt = &now
		audit.AuditResults = models.AuditResults{
			"message": "No SEO meta found for this website",
		}
		s.auditRepo.Update(ctx, audit.TenantID, audit.ID, audit)
		return nil
	}

	// Initialize audit results
	results := &AuditResults{
		TotalPages:           len(seoMetas),
		AuditedPages:         0,
		Issues:               make([]AuditIssue, 0),
		Opportunities:        make([]AuditOpportunity, 0),
		TechnicalIssues:      make([]AuditIssue, 0),
		ContentIssues:        make([]AuditIssue, 0),
		PerformanceIssues:    make([]AuditIssue, 0),
		ScoreDistribution:    make(map[string]int),
		KeywordAnalysis:      make(map[string]int),
		AverageScore:         0.0,
		BestPerformingPages:  make([]PageScore, 0),
		WorstPerformingPages: make([]PageScore, 0),
	}

	var totalScore float64
	pageScores := make([]PageScore, 0)

	// Analyze each page
	for _, seoMeta := range seoMetas {
		analysis, err := s.analyzer.AnalyzeSEOMeta(ctx, seoMeta)
		if err != nil {
			s.logger.WithError(err).WithField("page_id", seoMeta.ID).Warn("Failed to analyze page")
			continue
		}

		results.AuditedPages++
		totalScore += analysis.OverallScore

		// Track page score
		pageScores = append(pageScores, PageScore{
			PageID:  seoMeta.ID,
			PageURL: seoMeta.PageURL,
			Score:   analysis.OverallScore,
		})

		// Categorize score
		scoreCategory := s.categorizeScore(analysis.OverallScore)
		results.ScoreDistribution[scoreCategory]++

		// Collect issues
		s.collectIssues(seoMeta, analysis, results)

		// Collect opportunities
		s.collectOpportunities(seoMeta, analysis, results)

		// Analyze keywords
		if seoMeta.FocusKeyword != nil {
			keyword := strings.ToLower(*seoMeta.FocusKeyword)
			results.KeywordAnalysis[keyword]++
		}
	}

	// Calculate average score
	if results.AuditedPages > 0 {
		results.AverageScore = totalScore / float64(results.AuditedPages)
	}

	// Sort pages by score
	s.sortPageScores(pageScores)

	// Get best and worst performing pages
	if len(pageScores) > 0 {
		// Best performing (top 5)
		bestCount := 5
		if len(pageScores) < bestCount {
			bestCount = len(pageScores)
		}
		results.BestPerformingPages = pageScores[:bestCount]

		// Worst performing (bottom 5)
		worstCount := 5
		if len(pageScores) < worstCount {
			worstCount = len(pageScores)
		}
		results.WorstPerformingPages = pageScores[len(pageScores)-worstCount:]
	}

	// Run additional technical audits
	s.runTechnicalAudits(ctx, audit.TenantID, audit.WebsiteID, results)

	// Update audit with results
	audit.Status = models.AuditStatusCompleted
	now := time.Now()
	audit.AuditCompletedAt = &now
	audit.AuditResults = convertToAuditResults(results)
	audit.OverallScore = results.AverageScore
	audit.IssuesFound = uint(len(results.Issues))

	err = s.auditRepo.Update(ctx, audit.TenantID, audit.ID, audit)
	if err != nil {
		return fmt.Errorf("failed to update audit with results: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"audit_id":    audit.ID,
		"website_id":  audit.WebsiteID,
		"total_pages": results.TotalPages,
		"avg_score":   results.AverageScore,
	}).Info("Audit completed successfully")

	return nil
}

// collectIssues collects issues from SEO analysis
func (s *SEOAuditService) collectIssues(seoMeta *models.SEOMeta, analysis *models.SEOAnalysis, results *AuditResults) {
	// Title issues
	if analysis.TitleAnalysis != nil {
		for _, issue := range analysis.TitleAnalysis.Issues {
			auditIssue := AuditIssue{
				Type:        "title",
				Severity:    s.determineSeverity(issue),
				Title:       "Title Issue",
				Description: issue,
				PageURL:     seoMeta.PageURL,
				PageID:      seoMeta.ID,
				Category:    "content",
			}
			results.Issues = append(results.Issues, auditIssue)
			results.ContentIssues = append(results.ContentIssues, auditIssue)
		}
	}

	// Description issues
	if analysis.DescriptionAnalysis != nil {
		for _, issue := range analysis.DescriptionAnalysis.Issues {
			auditIssue := AuditIssue{
				Type:        "description",
				Severity:    s.determineSeverity(issue),
				Title:       "Description Issue",
				Description: issue,
				PageURL:     seoMeta.PageURL,
				PageID:      seoMeta.ID,
				Category:    "content",
			}
			results.Issues = append(results.Issues, auditIssue)
			results.ContentIssues = append(results.ContentIssues, auditIssue)
		}
	}

	// Technical issues
	if analysis.TechnicalAnalysis != nil {
		for _, issue := range analysis.TechnicalAnalysis.Issues {
			auditIssue := AuditIssue{
				Type:        "technical",
				Severity:    s.determineSeverity(issue),
				Title:       "Technical Issue",
				Description: issue,
				PageURL:     seoMeta.PageURL,
				PageID:      seoMeta.ID,
				Category:    "technical",
			}
			results.Issues = append(results.Issues, auditIssue)
			results.TechnicalIssues = append(results.TechnicalIssues, auditIssue)
		}
	}
}

// collectOpportunities collects optimization opportunities
func (s *SEOAuditService) collectOpportunities(seoMeta *models.SEOMeta, analysis *models.SEOAnalysis, results *AuditResults) {
	// Collect recommendations as opportunities
	for _, recommendation := range analysis.Recommendations {
		opportunity := AuditOpportunity{
			Type:        "optimization",
			Title:       "SEO Optimization",
			Description: recommendation,
			Impact:      "medium",
			Effort:      "low",
			Score:       analysis.OverallScore,
			PageURL:     seoMeta.PageURL,
			PageID:      seoMeta.ID,
		}
		results.Opportunities = append(results.Opportunities, opportunity)
	}

	// Add specific opportunities based on analysis
	if analysis.TitleAnalysis != nil {
		for _, suggestion := range analysis.TitleAnalysis.Suggestions {
			opportunity := AuditOpportunity{
				Type:        "title_optimization",
				Title:       "Title Optimization",
				Description: suggestion,
				Impact:      "high",
				Effort:      "low",
				Score:       float64(analysis.TitleAnalysis.Score),
				PageURL:     seoMeta.PageURL,
				PageID:      seoMeta.ID,
			}
			results.Opportunities = append(results.Opportunities, opportunity)
		}
	}

	if analysis.DescriptionAnalysis != nil {
		for _, suggestion := range analysis.DescriptionAnalysis.Suggestions {
			opportunity := AuditOpportunity{
				Type:        "description_optimization",
				Title:       "Description Optimization",
				Description: suggestion,
				Impact:      "medium",
				Effort:      "low",
				Score:       float64(analysis.DescriptionAnalysis.Score),
				PageURL:     seoMeta.PageURL,
				PageID:      seoMeta.ID,
			}
			results.Opportunities = append(results.Opportunities, opportunity)
		}
	}
}

// runTechnicalAudits runs additional technical audits
func (s *SEOAuditService) runTechnicalAudits(ctx context.Context, tenantID, websiteID uint, results *AuditResults) {
	// Check for duplicate titles
	duplicateTitles, err := s.metaRepo.GetDuplicateMetaTitles(ctx, tenantID, websiteID)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to check duplicate titles")
	} else {
		for _, duplicate := range duplicateTitles {
			issue := AuditIssue{
				Type:        "duplicate_title",
				Severity:    "high",
				Title:       "Duplicate Meta Title",
				Description: fmt.Sprintf("Title '%s' is used on %d pages", duplicate.MetaValue, duplicate.Count),
				Category:    "technical",
				Count:       duplicate.Count,
			}
			results.Issues = append(results.Issues, issue)
			results.TechnicalIssues = append(results.TechnicalIssues, issue)
		}
	}

	// Check for duplicate descriptions
	duplicateDescriptions, err := s.metaRepo.GetDuplicateMetaDescriptions(ctx, tenantID, websiteID)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to check duplicate descriptions")
	} else {
		for _, duplicate := range duplicateDescriptions {
			issue := AuditIssue{
				Type:        "duplicate_description",
				Severity:    "medium",
				Title:       "Duplicate Meta Description",
				Description: fmt.Sprintf("Description '%s' is used on %d pages", duplicate.MetaValue, duplicate.Count),
				Category:    "technical",
				Count:       duplicate.Count,
			}
			results.Issues = append(results.Issues, issue)
			results.TechnicalIssues = append(results.TechnicalIssues, issue)
		}
	}

	// Check for missing titles
	missingTitles, err := s.metaRepo.GetMissingMetaTitles(ctx, tenantID, websiteID)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to check missing titles")
	} else {
		if len(missingTitles) > 0 {
			issue := AuditIssue{
				Type:        "missing_title",
				Severity:    "critical",
				Title:       "Missing Meta Titles",
				Description: fmt.Sprintf("%d pages are missing meta titles", len(missingTitles)),
				Category:    "technical",
				Count:       len(missingTitles),
			}
			results.Issues = append(results.Issues, issue)
			results.TechnicalIssues = append(results.TechnicalIssues, issue)
		}
	}

	// Check for missing descriptions
	missingDescriptions, err := s.metaRepo.GetMissingMetaDescriptions(ctx, tenantID, websiteID)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to check missing descriptions")
	} else {
		if len(missingDescriptions) > 0 {
			issue := AuditIssue{
				Type:        "missing_description",
				Severity:    "high",
				Title:       "Missing Meta Descriptions",
				Description: fmt.Sprintf("%d pages are missing meta descriptions", len(missingDescriptions)),
				Category:    "technical",
				Count:       len(missingDescriptions),
			}
			results.Issues = append(results.Issues, issue)
			results.TechnicalIssues = append(results.TechnicalIssues, issue)
		}
	}
}

// categorizeScore categorizes a score into a range
func (s *SEOAuditService) categorizeScore(score float64) string {
	if score >= 90 {
		return "excellent"
	} else if score >= 70 {
		return "good"
	} else if score >= 50 {
		return "average"
	} else if score >= 30 {
		return "poor"
	} else {
		return "critical"
	}
}

// determineSeverity determines the severity of an issue
func (s *SEOAuditService) determineSeverity(issue string) string {
	issue = strings.ToLower(issue)

	if strings.Contains(issue, "missing") {
		return "critical"
	} else if strings.Contains(issue, "too short") || strings.Contains(issue, "too long") {
		return "high"
	} else if strings.Contains(issue, "duplicate") {
		return "high"
	} else {
		return "medium"
	}
}

// sortPageScores sorts page scores in descending order
func (s *SEOAuditService) sortPageScores(scores []PageScore) {
	for i := 0; i < len(scores); i++ {
		for j := i + 1; j < len(scores); j++ {
			if scores[i].Score < scores[j].Score {
				scores[i], scores[j] = scores[j], scores[i]
			}
		}
	}
}

// Helper types for audit results
type AuditResults struct {
	TotalPages           int                `json:"total_pages"`
	AuditedPages         int                `json:"audited_pages"`
	Issues               []AuditIssue       `json:"issues"`
	Opportunities        []AuditOpportunity `json:"opportunities"`
	TechnicalIssues      []AuditIssue       `json:"technical_issues"`
	ContentIssues        []AuditIssue       `json:"content_issues"`
	PerformanceIssues    []AuditIssue       `json:"performance_issues"`
	ScoreDistribution    map[string]int     `json:"score_distribution"`
	KeywordAnalysis      map[string]int     `json:"keyword_analysis"`
	AverageScore         float64            `json:"average_score"`
	BestPerformingPages  []PageScore        `json:"best_performing_pages"`
	WorstPerformingPages []PageScore        `json:"worst_performing_pages"`
}

type AuditIssue struct {
	Type        string `json:"type"`
	Severity    string `json:"severity"`
	Title       string `json:"title"`
	Description string `json:"description"`
	PageURL     string `json:"page_url,omitempty"`
	PageID      uint   `json:"page_id,omitempty"`
	Category    string `json:"category"`
	Count       int    `json:"count,omitempty"`
}

type AuditOpportunity struct {
	Type        string  `json:"type"`
	Title       string  `json:"title"`
	Description string  `json:"description"`
	Impact      string  `json:"impact"`
	Effort      string  `json:"effort"`
	Score       float64 `json:"score"`
	PageURL     string  `json:"page_url,omitempty"`
	PageID      uint    `json:"page_id,omitempty"`
}

type PageScore struct {
	PageID  uint    `json:"page_id"`
	PageURL string  `json:"page_url"`
	Score   float64 `json:"score"`
}

// Helper functions
func timePtr(t time.Time) *time.Time {
	return &t
}

func stringPtr(s string) *string {
	return &s
}

// convertToAuditResults converts AuditResults to models.AuditResults
func convertToAuditResults(results *AuditResults) models.AuditResults {
	data := make(map[string]interface{})
	data["average_score"] = results.AverageScore
	data["total_pages"] = results.TotalPages
	data["audited_pages"] = results.AuditedPages
	data["issues"] = results.Issues
	data["opportunities"] = results.Opportunities
	data["technical_issues"] = results.TechnicalIssues
	data["content_issues"] = results.ContentIssues
	data["performance_issues"] = results.PerformanceIssues
	data["score_distribution"] = results.ScoreDistribution
	data["keyword_analysis"] = results.KeywordAnalysis
	data["best_performing_pages"] = results.BestPerformingPages
	data["worst_performing_pages"] = results.WorstPerformingPages
	return models.AuditResults(data)
}
