package services

import (
	"context"
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"unicode/utf8"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
)

// SEOValidator defines the interface for SEO validation
type SEOValidator interface {
	ValidateSEOMeta(ctx context.Context, seoMeta *models.SEOMeta) (*models.SEOValidationResult, error)
	ValidateTitle(title string) *models.ValidationResult
	ValidateDescription(description string) *models.ValidationResult
	ValidateKeywords(keywords string) *models.ValidationResult
	ValidateCanonicalURL(canonicalURL string) *models.ValidationResult
	ValidateOpenGraphTags(seoMeta *models.SEOMeta) *models.ValidationResult
	ValidateTwitterCardTags(seoMeta *models.SEOMeta) *models.ValidationResult
	ValidateStructuredData(schemaData models.SchemaData) *models.ValidationResult
	ValidateRobotsTags(robotsTags string) *models.ValidationResult
	ValidateImageOptimization(imageURL string) *models.ValidationResult
}

// seoValidator implements SEOValidator
type seoValidator struct{}

// NewSEOValidator creates a new SEO validator
func NewSEOValidator() SEOValidator {
	return &seoValidator{}
}

// ValidateSEOMeta performs comprehensive SEO validation
func (v *seoValidator) ValidateSEOMeta(ctx context.Context, seoMeta *models.SEOMeta) (*models.SEOValidationResult, error) {
	result := &models.SEOValidationResult{
		IsValid:     true,
		Score:       100.0,
		Errors:      make([]models.ValidationError, 0),
		Warnings:    make([]models.ValidationWarning, 0),
		Suggestions: make([]models.ValidationSuggestion, 0),
	}

	var totalTests int
	var passedTests int

	// Validate title
	if seoMeta.MetaTitle != nil {
		titleResult := v.ValidateTitle(*seoMeta.MetaTitle)
		v.mergeValidationResult(result, titleResult, "title")
		totalTests++
		if titleResult.IsValid {
			passedTests++
		}
	} else {
		result.Errors = append(result.Errors, models.ValidationError{
			Field:    "meta_title",
			Code:     "MISSING_TITLE",
			Message:  "Meta title is missing",
			Severity: "critical",
		})
		totalTests++
	}

	// Validate description
	if seoMeta.MetaDescription != nil {
		descResult := v.ValidateDescription(*seoMeta.MetaDescription)
		v.mergeValidationResult(result, descResult, "description")
		totalTests++
		if descResult.IsValid {
			passedTests++
		}
	} else {
		result.Errors = append(result.Errors, models.ValidationError{
			Field:    "meta_description",
			Code:     "MISSING_DESCRIPTION",
			Message:  "Meta description is missing",
			Severity: "high",
		})
		totalTests++
	}

	// Validate keywords
	if seoMeta.MetaKeywords != nil {
		keywordsResult := v.ValidateKeywords(*seoMeta.MetaKeywords)
		v.mergeValidationResult(result, keywordsResult, "keywords")
		totalTests++
		if keywordsResult.IsValid {
			passedTests++
		}
	}

	// Validate canonical URL
	if seoMeta.CanonicalURL != nil {
		canonicalResult := v.ValidateCanonicalURL(*seoMeta.CanonicalURL)
		v.mergeValidationResult(result, canonicalResult, "canonical")
		totalTests++
		if canonicalResult.IsValid {
			passedTests++
		}
	}

	// Validate Open Graph tags
	ogResult := v.ValidateOpenGraphTags(seoMeta)
	v.mergeValidationResult(result, ogResult, "open_graph")
	totalTests++
	if ogResult.IsValid {
		passedTests++
	}

	// Validate Twitter Card tags
	twitterResult := v.ValidateTwitterCardTags(seoMeta)
	v.mergeValidationResult(result, twitterResult, "twitter_card")
	totalTests++
	if twitterResult.IsValid {
		passedTests++
	}

	// Validate structured data
	if len(seoMeta.SchemaData) > 0 {
		schemaResult := v.ValidateStructuredData(seoMeta.SchemaData)
		v.mergeValidationResult(result, schemaResult, "structured_data")
		totalTests++
		if schemaResult.IsValid {
			passedTests++
		}
	}

	// Validate robots tags
	robotsResult := v.ValidateRobotsTags(seoMeta.MetaRobots)
	v.mergeValidationResult(result, robotsResult, "robots")
	totalTests++
	if robotsResult.IsValid {
		passedTests++
	}

	// Validate image optimization
	if seoMeta.OGImage != nil {
		imageResult := v.ValidateImageOptimization(*seoMeta.OGImage)
		v.mergeValidationResult(result, imageResult, "image")
		totalTests++
		if imageResult.IsValid {
			passedTests++
		}
	}

	// Calculate overall score
	if totalTests > 0 {
		result.Score = (float64(passedTests) / float64(totalTests)) * 100
	}

	// Determine overall validity
	result.IsValid = len(result.Errors) == 0

	// Add general suggestions
	v.addGeneralSuggestions(result, seoMeta)

	return result, nil
}

// ValidateTitle validates meta title
func (v *seoValidator) ValidateTitle(title string) *models.ValidationResult {
	result := &models.ValidationResult{
		IsValid:     true,
		Errors:      make([]models.ValidationError, 0),
		Warnings:    make([]models.ValidationWarning, 0),
		Suggestions: make([]models.ValidationSuggestion, 0),
	}

	if title == "" {
		result.IsValid = false
		result.Errors = append(result.Errors, models.ValidationError{
			Field:    "title",
			Code:     "EMPTY_TITLE",
			Message:  "Title cannot be empty",
			Severity: "critical",
		})
		return result
	}

	length := utf8.RuneCountInString(title)

	// Check length
	if length < 30 {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "title",
			Code:    "SHORT_TITLE",
			Message: fmt.Sprintf("Title is too short (%d characters). Recommended: 30-60 characters", length),
		})
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "title",
			Type:        "optimization",
			Description: "Consider adding more descriptive words to reach 30-60 characters",
			Impact:      "medium",
		})
	} else if length > 60 {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "title",
			Code:    "LONG_TITLE",
			Message: fmt.Sprintf("Title is too long (%d characters). Recommended: 30-60 characters", length),
		})
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "title",
			Type:        "optimization",
			Description: "Consider shortening the title to 60 characters or less",
			Impact:      "high",
		})
	}

	// Check for duplicate words
	words := strings.Fields(strings.ToLower(title))
	wordCount := make(map[string]int)
	for _, word := range words {
		if len(word) > 3 { // Only count meaningful words
			wordCount[word]++
		}
	}

	duplicateWords := 0
	for _, count := range wordCount {
		if count > 1 {
			duplicateWords++
		}
	}

	if duplicateWords > 2 {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "title",
			Code:    "DUPLICATE_WORDS",
			Message: "Title contains multiple duplicate words",
		})
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "title",
			Type:        "content",
			Description: "Reduce repetitive words to improve readability",
			Impact:      "low",
		})
	}

	// Check for special characters
	if strings.Contains(title, "|") || strings.Contains(title, "-") || strings.Contains(title, "–") {
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "title",
			Type:        "formatting",
			Description: "Good use of separators for brand/category separation",
			Impact:      "positive",
		})
	}

	// Check for ALL CAPS
	if title == strings.ToUpper(title) && length > 10 {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "title",
			Code:    "ALL_CAPS",
			Message: "Title is in all capital letters",
		})
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "title",
			Type:        "formatting",
			Description: "Use proper capitalization for better readability",
			Impact:      "medium",
		})
	}

	return result
}

// ValidateDescription validates meta description
func (v *seoValidator) ValidateDescription(description string) *models.ValidationResult {
	result := &models.ValidationResult{
		IsValid:     true,
		Errors:      make([]models.ValidationError, 0),
		Warnings:    make([]models.ValidationWarning, 0),
		Suggestions: make([]models.ValidationSuggestion, 0),
	}

	if description == "" {
		result.IsValid = false
		result.Errors = append(result.Errors, models.ValidationError{
			Field:    "description",
			Code:     "EMPTY_DESCRIPTION",
			Message:  "Description cannot be empty",
			Severity: "high",
		})
		return result
	}

	length := utf8.RuneCountInString(description)

	// Check length
	if length < 120 {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "description",
			Code:    "SHORT_DESCRIPTION",
			Message: fmt.Sprintf("Description is too short (%d characters). Recommended: 120-160 characters", length),
		})
	} else if length > 160 {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "description",
			Code:    "LONG_DESCRIPTION",
			Message: fmt.Sprintf("Description is too long (%d characters). Recommended: 120-160 characters", length),
		})
	}

	// Check for call-to-action
	ctaWords := []string{"learn", "discover", "find", "get", "buy", "download", "try", "start", "explore", "read"}
	hasCallToAction := false
	lowerDescription := strings.ToLower(description)

	for _, cta := range ctaWords {
		if strings.Contains(lowerDescription, cta) {
			hasCallToAction = true
			break
		}
	}

	if !hasCallToAction {
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "description",
			Type:        "content",
			Description: "Consider adding a call-to-action to make the description more compelling",
			Impact:      "medium",
		})
	}

	// Check for duplicate sentences
	sentences := regexp.MustCompile(`[.!?]+`).Split(description, -1)
	if len(sentences) > 2 {
		sentenceMap := make(map[string]int)
		for _, sentence := range sentences {
			cleaned := strings.TrimSpace(sentence)
			if len(cleaned) > 10 {
				sentenceMap[strings.ToLower(cleaned)]++
			}
		}

		for _, count := range sentenceMap {
			if count > 1 {
				result.Warnings = append(result.Warnings, models.ValidationWarning{
					Field:   "description",
					Code:    "DUPLICATE_SENTENCES",
					Message: "Description contains duplicate sentences",
				})
				break
			}
		}
	}

	return result
}

// ValidateKeywords validates meta keywords
func (v *seoValidator) ValidateKeywords(keywords string) *models.ValidationResult {
	result := &models.ValidationResult{
		IsValid:     true,
		Errors:      make([]models.ValidationError, 0),
		Warnings:    make([]models.ValidationWarning, 0),
		Suggestions: make([]models.ValidationSuggestion, 0),
	}

	if keywords == "" {
		return result // Keywords are optional
	}

	keywordList := strings.Split(keywords, ",")
	cleanKeywords := make([]string, 0)

	for _, keyword := range keywordList {
		cleaned := strings.TrimSpace(keyword)
		if cleaned != "" {
			cleanKeywords = append(cleanKeywords, cleaned)
		}
	}

	// Check keyword count
	if len(cleanKeywords) > 10 {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "keywords",
			Code:    "TOO_MANY_KEYWORDS",
			Message: fmt.Sprintf("Too many keywords (%d). Consider limiting to 5-10 relevant keywords", len(cleanKeywords)),
		})
	} else if len(cleanKeywords) < 3 {
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "keywords",
			Type:        "content",
			Description: "Consider adding more relevant keywords (3-10 recommended)",
			Impact:      "low",
		})
	}

	// Check for keyword stuffing
	keywordMap := make(map[string]int)
	for _, keyword := range cleanKeywords {
		keywordMap[strings.ToLower(keyword)]++
	}

	for keyword, count := range keywordMap {
		if count > 1 {
			result.Warnings = append(result.Warnings, models.ValidationWarning{
				Field:   "keywords",
				Code:    "DUPLICATE_KEYWORDS",
				Message: fmt.Sprintf("Keyword '%s' appears %d times", keyword, count),
			})
		}
	}

	// Check keyword length
	for _, keyword := range cleanKeywords {
		if utf8.RuneCountInString(keyword) > 50 {
			result.Warnings = append(result.Warnings, models.ValidationWarning{
				Field:   "keywords",
				Code:    "LONG_KEYWORD",
				Message: fmt.Sprintf("Keyword '%s' is too long", keyword),
			})
		}
	}

	return result
}

// ValidateCanonicalURL validates canonical URL
func (v *seoValidator) ValidateCanonicalURL(canonicalURL string) *models.ValidationResult {
	result := &models.ValidationResult{
		IsValid:     true,
		Errors:      make([]models.ValidationError, 0),
		Warnings:    make([]models.ValidationWarning, 0),
		Suggestions: make([]models.ValidationSuggestion, 0),
	}

	if canonicalURL == "" {
		return result // Canonical URL is optional
	}

	// Validate URL format
	parsedURL, err := url.Parse(canonicalURL)
	if err != nil {
		result.IsValid = false
		result.Errors = append(result.Errors, models.ValidationError{
			Field:    "canonical_url",
			Code:     "INVALID_URL",
			Message:  "Canonical URL is not a valid URL",
			Severity: "high",
		})
		return result
	}

	// Check for HTTPS
	if parsedURL.Scheme != "https" {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "canonical_url",
			Code:    "NON_HTTPS",
			Message: "Canonical URL should use HTTPS",
		})
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "canonical_url",
			Type:        "security",
			Description: "Use HTTPS for better SEO and security",
			Impact:      "medium",
		})
	}

	// Check for trailing slash consistency
	if strings.HasSuffix(canonicalURL, "/") && parsedURL.Path != "/" {
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "canonical_url",
			Type:        "formatting",
			Description: "Consider removing trailing slash for consistency",
			Impact:      "low",
		})
	}

	// Check for URL parameters
	if parsedURL.RawQuery != "" {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "canonical_url",
			Code:    "URL_PARAMETERS",
			Message: "Canonical URL contains parameters",
		})
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "canonical_url",
			Type:        "optimization",
			Description: "Consider using clean URLs without parameters for canonical URLs",
			Impact:      "medium",
		})
	}

	return result
}

// ValidateOpenGraphTags validates Open Graph tags
func (v *seoValidator) ValidateOpenGraphTags(seoMeta *models.SEOMeta) *models.ValidationResult {
	result := &models.ValidationResult{
		IsValid:     true,
		Errors:      make([]models.ValidationError, 0),
		Warnings:    make([]models.ValidationWarning, 0),
		Suggestions: make([]models.ValidationSuggestion, 0),
	}

	// Check required OG tags
	if seoMeta.OGTitle == nil || *seoMeta.OGTitle == "" {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "og_title",
			Code:    "MISSING_OG_TITLE",
			Message: "Open Graph title is missing",
		})
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "og_title",
			Type:        "social",
			Description: "Add Open Graph title for better social media sharing",
			Impact:      "medium",
		})
	}

	if seoMeta.OGDescription == nil || *seoMeta.OGDescription == "" {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "og_description",
			Code:    "MISSING_OG_DESCRIPTION",
			Message: "Open Graph description is missing",
		})
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "og_description",
			Type:        "social",
			Description: "Add Open Graph description for better social media sharing",
			Impact:      "medium",
		})
	}

	if seoMeta.OGImage == nil || *seoMeta.OGImage == "" {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "og_image",
			Code:    "MISSING_OG_IMAGE",
			Message: "Open Graph image is missing",
		})
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "og_image",
			Type:        "social",
			Description: "Add Open Graph image for better social media sharing",
			Impact:      "high",
		})
	} else {
		// Validate image URL
		if _, err := url.Parse(*seoMeta.OGImage); err != nil {
			result.Errors = append(result.Errors, models.ValidationError{
				Field:    "og_image",
				Code:     "INVALID_OG_IMAGE_URL",
				Message:  "Open Graph image URL is invalid",
				Severity: "medium",
			})
		}
	}

	// Check OG type
	if seoMeta.OGType == "" {
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "og_type",
			Type:        "social",
			Description: "Add Open Graph type (website, article, etc.) for better categorization",
			Impact:      "low",
		})
	}

	return result
}

// ValidateTwitterCardTags validates Twitter Card tags
func (v *seoValidator) ValidateTwitterCardTags(seoMeta *models.SEOMeta) *models.ValidationResult {
	result := &models.ValidationResult{
		IsValid:     true,
		Errors:      make([]models.ValidationError, 0),
		Warnings:    make([]models.ValidationWarning, 0),
		Suggestions: make([]models.ValidationSuggestion, 0),
	}

	// Check Twitter Card type
	if seoMeta.TwitterCard == "" {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "twitter_card",
			Code:    "MISSING_TWITTER_CARD",
			Message: "Twitter Card type is missing",
		})
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "twitter_card",
			Type:        "social",
			Description: "Add Twitter Card type (summary, summary_large_image) for better Twitter sharing",
			Impact:      "medium",
		})
	} else {
		validCardTypes := []string{"summary", "summary_large_image", "app", "player"}
		isValid := false
		for _, validType := range validCardTypes {
			if seoMeta.TwitterCard == validType {
				isValid = true
				break
			}
		}
		if !isValid {
			result.Errors = append(result.Errors, models.ValidationError{
				Field:    "twitter_card",
				Code:     "INVALID_TWITTER_CARD_TYPE",
				Message:  fmt.Sprintf("Invalid Twitter Card type: %s", seoMeta.TwitterCard),
				Severity: "medium",
			})
		}
	}

	// Check Twitter title
	if seoMeta.TwitterTitle == nil || *seoMeta.TwitterTitle == "" {
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "twitter_title",
			Type:        "social",
			Description: "Add Twitter title for better Twitter sharing",
			Impact:      "medium",
		})
	}

	// Check Twitter description
	if seoMeta.TwitterDescription == nil || *seoMeta.TwitterDescription == "" {
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "twitter_description",
			Type:        "social",
			Description: "Add Twitter description for better Twitter sharing",
			Impact:      "medium",
		})
	}

	// Check Twitter image
	if seoMeta.TwitterImage == nil || *seoMeta.TwitterImage == "" {
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "twitter_image",
			Type:        "social",
			Description: "Add Twitter image for better Twitter sharing",
			Impact:      "high",
		})
	}

	return result
}

// ValidateStructuredData validates structured data
func (v *seoValidator) ValidateStructuredData(schemaData models.SchemaData) *models.ValidationResult {
	result := &models.ValidationResult{
		IsValid:     true,
		Errors:      make([]models.ValidationError, 0),
		Warnings:    make([]models.ValidationWarning, 0),
		Suggestions: make([]models.ValidationSuggestion, 0),
	}

	if len(schemaData) == 0 {
		return result
	}

	// Check for required @context
	if context, exists := schemaData["@context"]; !exists || context != "https://schema.org" {
		result.Errors = append(result.Errors, models.ValidationError{
			Field:    "schema_data",
			Code:     "MISSING_CONTEXT",
			Message:  "@context is missing or invalid in structured data",
			Severity: "high",
		})
	}

	// Check for @type
	if schemaType, exists := schemaData["@type"]; !exists || schemaType == "" {
		result.Errors = append(result.Errors, models.ValidationError{
			Field:    "schema_data",
			Code:     "MISSING_TYPE",
			Message:  "@type is missing in structured data",
			Severity: "high",
		})
	} else {
		// Validate common schema types
		validTypes := []string{"Article", "WebPage", "Organization", "Person", "Product", "LocalBusiness", "Event", "Recipe"}
		isValidType := false
		for _, validType := range validTypes {
			if schemaType == validType {
				isValidType = true
				break
			}
		}
		if !isValidType {
			result.Warnings = append(result.Warnings, models.ValidationWarning{
				Field:   "schema_data",
				Code:    "UNCOMMON_SCHEMA_TYPE",
				Message: fmt.Sprintf("Schema type '%s' is not commonly used", schemaType),
			})
		}
	}

	// Check for required properties based on type
	if schemaType, exists := schemaData["@type"]; exists {
		switch schemaType {
		case "Article":
			v.validateArticleSchema(schemaData, result)
		case "Organization":
			v.validateOrganizationSchema(schemaData, result)
		case "Product":
			v.validateProductSchema(schemaData, result)
		}
	}

	return result
}

// ValidateRobotsTags validates robots meta tags
func (v *seoValidator) ValidateRobotsTags(robotsTags string) *models.ValidationResult {
	result := &models.ValidationResult{
		IsValid:     true,
		Errors:      make([]models.ValidationError, 0),
		Warnings:    make([]models.ValidationWarning, 0),
		Suggestions: make([]models.ValidationSuggestion, 0),
	}

	if robotsTags == "" {
		robotsTags = "index,follow" // Default
	}

	// Parse robots directives
	directives := strings.Split(strings.ToLower(robotsTags), ",")
	for i, directive := range directives {
		directives[i] = strings.TrimSpace(directive)
	}

	// Check for conflicting directives
	hasIndex := false
	hasNoIndex := false
	hasFollow := false
	hasNoFollow := false

	validDirectives := map[string]bool{
		"index": true, "noindex": true, "follow": true, "nofollow": true,
		"none": true, "all": true, "noarchive": true, "nocache": true,
		"nosnippet": true, "noimageindex": true, "notranslate": true,
	}

	for _, directive := range directives {
		if !validDirectives[directive] {
			result.Warnings = append(result.Warnings, models.ValidationWarning{
				Field:   "robots",
				Code:    "UNKNOWN_DIRECTIVE",
				Message: fmt.Sprintf("Unknown robots directive: %s", directive),
			})
		}

		switch directive {
		case "index":
			hasIndex = true
		case "noindex":
			hasNoIndex = true
		case "follow":
			hasFollow = true
		case "nofollow":
			hasNoFollow = true
		case "none":
			hasNoIndex = true
			hasNoFollow = true
		case "all":
			hasIndex = true
			hasFollow = true
		}
	}

	// Check for conflicts
	if hasIndex && hasNoIndex {
		result.Errors = append(result.Errors, models.ValidationError{
			Field:    "robots",
			Code:     "CONFLICTING_INDEX_DIRECTIVES",
			Message:  "Conflicting index directives: both 'index' and 'noindex' specified",
			Severity: "high",
		})
	}

	if hasFollow && hasNoFollow {
		result.Errors = append(result.Errors, models.ValidationError{
			Field:    "robots",
			Code:     "CONFLICTING_FOLLOW_DIRECTIVES",
			Message:  "Conflicting follow directives: both 'follow' and 'nofollow' specified",
			Severity: "high",
		})
	}

	// Check for SEO-unfriendly settings
	if hasNoIndex {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "robots",
			Code:    "NOINDEX_SET",
			Message: "Page is set to noindex - it will not be indexed by search engines",
		})
	}

	return result
}

// ValidateImageOptimization validates image optimization
func (v *seoValidator) ValidateImageOptimization(imageURL string) *models.ValidationResult {
	result := &models.ValidationResult{
		IsValid:     true,
		Errors:      make([]models.ValidationError, 0),
		Warnings:    make([]models.ValidationWarning, 0),
		Suggestions: make([]models.ValidationSuggestion, 0),
	}

	if imageURL == "" {
		return result
	}

	// Validate URL format
	parsedURL, err := url.Parse(imageURL)
	if err != nil {
		result.IsValid = false
		result.Errors = append(result.Errors, models.ValidationError{
			Field:    "image_url",
			Code:     "INVALID_IMAGE_URL",
			Message:  "Image URL is not valid",
			Severity: "medium",
		})
		return result
	}

	// Check for HTTPS
	if parsedURL.Scheme != "https" {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "image_url",
			Code:    "NON_HTTPS_IMAGE",
			Message: "Image URL should use HTTPS",
		})
	}

	// Check file extension
	path := strings.ToLower(parsedURL.Path)
	validExtensions := []string{".jpg", ".jpeg", ".png", ".webp", ".gif", ".svg"}
	hasValidExtension := false

	for _, ext := range validExtensions {
		if strings.HasSuffix(path, ext) {
			hasValidExtension = true
			break
		}
	}

	if !hasValidExtension {
		result.Warnings = append(result.Warnings, models.ValidationWarning{
			Field:   "image_url",
			Code:    "UNKNOWN_IMAGE_FORMAT",
			Message: "Image format may not be optimized for web",
		})
	}

	// Suggest modern formats
	if strings.HasSuffix(path, ".jpg") || strings.HasSuffix(path, ".jpeg") || strings.HasSuffix(path, ".png") {
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "image_url",
			Type:        "optimization",
			Description: "Consider using WebP format for better compression and performance",
			Impact:      "medium",
		})
	}

	return result
}

// Helper methods

func (v *seoValidator) mergeValidationResult(target *models.SEOValidationResult, source *models.ValidationResult, context string) {
	if !source.IsValid {
		target.IsValid = false
	}

	for _, err := range source.Errors {
		err.Context = context
		target.Errors = append(target.Errors, err)
	}

	for _, warning := range source.Warnings {
		warning.Context = context
		target.Warnings = append(target.Warnings, warning)
	}

	for _, suggestion := range source.Suggestions {
		suggestion.Context = context
		target.Suggestions = append(target.Suggestions, suggestion)
	}
}

func (v *seoValidator) addGeneralSuggestions(result *models.SEOValidationResult, seoMeta *models.SEOMeta) {
	// Focus keyword usage
	if seoMeta.FocusKeyword == nil || *seoMeta.FocusKeyword == "" {
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "focus_keyword",
			Type:        "seo",
			Description: "Add a focus keyword to optimize content around specific terms",
			Impact:      "medium",
		})
	}

	// Schema markup
	if len(seoMeta.SchemaData) == 0 {
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "schema_data",
			Type:        "technical",
			Description: "Add structured data markup for better search engine understanding",
			Impact:      "high",
		})
	}

	// Alternative text for images
	if seoMeta.OGImage != nil && *seoMeta.OGImage != "" {
		result.Suggestions = append(result.Suggestions, models.ValidationSuggestion{
			Field:       "image_alt",
			Type:        "accessibility",
			Description: "Ensure all images have descriptive alt text for accessibility and SEO",
			Impact:      "medium",
		})
	}
}

func (v *seoValidator) validateArticleSchema(schemaData models.SchemaData, result *models.ValidationResult) {
	requiredFields := []string{"headline", "author", "datePublished"}

	for _, field := range requiredFields {
		if _, exists := schemaData[field]; !exists {
			result.Warnings = append(result.Warnings, models.ValidationWarning{
				Field:   "schema_data",
				Code:    "MISSING_ARTICLE_FIELD",
				Message: fmt.Sprintf("Article schema missing recommended field: %s", field),
			})
		}
	}
}

func (v *seoValidator) validateOrganizationSchema(schemaData models.SchemaData, result *models.ValidationResult) {
	requiredFields := []string{"name", "url"}

	for _, field := range requiredFields {
		if _, exists := schemaData[field]; !exists {
			result.Warnings = append(result.Warnings, models.ValidationWarning{
				Field:   "schema_data",
				Code:    "MISSING_ORGANIZATION_FIELD",
				Message: fmt.Sprintf("Organization schema missing recommended field: %s", field),
			})
		}
	}
}

func (v *seoValidator) validateProductSchema(schemaData models.SchemaData, result *models.ValidationResult) {
	requiredFields := []string{"name", "description", "offers"}

	for _, field := range requiredFields {
		if _, exists := schemaData[field]; !exists {
			result.Warnings = append(result.Warnings, models.ValidationWarning{
				Field:   "schema_data",
				Code:    "MISSING_PRODUCT_FIELD",
				Message: fmt.Sprintf("Product schema missing recommended field: %s", field),
			})
		}
	}
}
