package services

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// SEORedirectService defines the interface for SEO redirect operations
type SEORedirectService interface {
	// Basic CRUD operations
	Create(ctx context.Context, req *models.SEORedirectCreateRequest) (*models.SEORedirectResponse, error)
	GetByID(ctx context.Context, id uint) (*models.SEORedirectResponse, error)
	Update(ctx context.Context, id uint, req *models.SEORedirectUpdateRequest) (*models.SEORedirectResponse, error)
	Delete(ctx context.Context, id uint) error
	SoftDelete(ctx context.Context, id uint) error

	// List and query operations
	List(ctx context.Context, req *models.SEORedirectListRequest) ([]*models.SEORedirectResponse, int64, error)
	ListWithCursor(ctx context.Context, websiteID, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) (*dto.SEORedirectListResponse, error)
	GetBySourceURL(ctx context.Context, websiteID, tenantID uint, sourceURL string) (*models.SEORedirectResponse, error)
	GetActiveRedirects(ctx context.Context, websiteID, tenantID uint) ([]*models.SEORedirectResponse, error)

	// Redirect processing operations
	ProcessRedirect(ctx context.Context, websiteID, tenantID uint, path, ip, userAgent, referrer string) (*models.SEORedirectResponse, error)
	FindRedirectForPath(ctx context.Context, websiteID, tenantID uint, path string) (*models.SEORedirectResponse, error)

	// Analytics and statistics
	GetStats(ctx context.Context, websiteID, tenantID uint) (*models.SEORedirectStatsResponse, error)
	GetTopRedirects(ctx context.Context, websiteID, tenantID uint, limit int) ([]*models.SEORedirectResponse, error)
	GetRecentHits(ctx context.Context, websiteID, tenantID uint, hours int) ([]*models.SEORedirectResponse, error)

	// Bulk operations
	BulkCreate(ctx context.Context, redirects []*models.SEORedirectCreateRequest) ([]*models.SEORedirectResponse, error)
	BulkUpdate(ctx context.Context, updates map[uint]*models.SEORedirectUpdateRequest) ([]*models.SEORedirectResponse, error)
	BulkDelete(ctx context.Context, ids []uint) error

	// Validation and utility operations
	ValidateRedirect(ctx context.Context, req *models.SEORedirectCreateRequest) error
	TestRedirect(ctx context.Context, id uint, testPath string) (string, error)

	// Maintenance operations
	CleanupExpiredRedirects(ctx context.Context) (int, error)
	ArchiveOldRedirects(ctx context.Context, days int) (int, error)
}

// seoRedirectService implements the SEORedirectService interface
type seoRedirectService struct {
	repo   repositories.SEORedirectRepository
	logger utils.Logger
}

// NewSEORedirectService creates a new SEO redirect service
func NewSEORedirectService(
	repo repositories.SEORedirectRepository,
	logger utils.Logger,
) SEORedirectService {
	return &seoRedirectService{
		repo:   repo,
		logger: logger,
	}
}

// Create creates a new SEO redirect
func (s *seoRedirectService) Create(ctx context.Context, req *models.SEORedirectCreateRequest) (*models.SEORedirectResponse, error) {
	// Validate the request
	if err := s.ValidateRedirect(ctx, req); err != nil {
		return nil, err
	}

	// Check for existing redirect with same source URL
	existing, err := s.repo.GetBySourceURL(ctx, req.WebsiteID, req.TenantID, req.SourceURL)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("redirect with source URL '%s' already exists", req.SourceURL)
	}

	// Create the redirect model
	redirect := &models.SEORedirect{
		WebsiteID: req.WebsiteID,
		TenantID:  req.TenantID,

		SourceURL:       req.SourceURL,
		SourcePath:      req.SourcePath,
		DestinationURL:  req.DestinationURL,
		DestinationPath: req.DestinationPath,

		RedirectType:       req.RedirectType,
		RedirectMatch:      req.RedirectMatch,
		RegexPattern:       req.RegexPattern,
		ReplacementPattern: req.ReplacementPattern,

		Conditions:          req.Conditions,
		QueryStringHandling: req.QueryStringHandling,

		SEOReason: req.SEOReason,
		Notes:     req.Notes,
		CreatedBy: req.CreatedBy,

		ActiveFrom:  req.ActiveFrom,
		ActiveUntil: req.ActiveUntil,

		Status: req.Status,
	}

	// Set defaults
	if redirect.RedirectType == "" {
		redirect.RedirectType = models.RedirectType301
	}
	if redirect.RedirectMatch == "" {
		redirect.RedirectMatch = models.RedirectMatchExact
	}
	if redirect.QueryStringHandling == "" {
		redirect.QueryStringHandling = models.QueryStringPreserve
	}
	if redirect.Status == "" {
		redirect.Status = models.RedirectStatusActive
	}

	// Create the redirect
	if err := s.repo.Create(ctx, redirect); err != nil {
		s.logger.Error("Failed to create SEO redirect", "error", err)
		return nil, fmt.Errorf("failed to create redirect: %w", err)
	}

	s.logger.Info("Created SEO redirect", "id", redirect.ID, "source", redirect.SourceURL)
	return redirect.ToResponse(), nil
}

// GetByID retrieves an SEO redirect by ID
func (s *seoRedirectService) GetByID(ctx context.Context, id uint) (*models.SEORedirectResponse, error) {
	redirect, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get redirect: %w", err)
	}

	return redirect.ToResponse(), nil
}

// Update updates an existing SEO redirect
func (s *seoRedirectService) Update(ctx context.Context, id uint, req *models.SEORedirectUpdateRequest) (*models.SEORedirectResponse, error) {
	// Get existing redirect
	redirect, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get redirect: %w", err)
	}

	// Update fields
	if req.SourceURL != nil {
		redirect.SourceURL = *req.SourceURL
	}
	if req.SourcePath != nil {
		redirect.SourcePath = *req.SourcePath
	}
	if req.DestinationURL != nil {
		redirect.DestinationURL = *req.DestinationURL
	}
	if req.DestinationPath != nil {
		redirect.DestinationPath = req.DestinationPath
	}
	if req.RedirectType != nil {
		redirect.RedirectType = *req.RedirectType
	}
	if req.RedirectMatch != nil {
		redirect.RedirectMatch = *req.RedirectMatch
	}
	if req.RegexPattern != nil {
		redirect.RegexPattern = req.RegexPattern
	}
	if req.ReplacementPattern != nil {
		redirect.ReplacementPattern = req.ReplacementPattern
	}
	if req.Conditions != nil {
		redirect.Conditions = req.Conditions
	}
	if req.QueryStringHandling != nil {
		redirect.QueryStringHandling = *req.QueryStringHandling
	}
	if req.SEOReason != nil {
		redirect.SEOReason = req.SEOReason
	}
	if req.Notes != nil {
		redirect.Notes = req.Notes
	}
	if req.UpdatedBy != nil {
		redirect.UpdatedBy = req.UpdatedBy
	}
	if req.ActiveFrom != nil {
		redirect.ActiveFrom = req.ActiveFrom
	}
	if req.ActiveUntil != nil {
		redirect.ActiveUntil = req.ActiveUntil
	}
	if req.Status != nil {
		redirect.Status = *req.Status
	}

	// Update the redirect
	if err := s.repo.Update(ctx, redirect); err != nil {
		s.logger.Error("Failed to update SEO redirect", "error", err, "id", id)
		return nil, fmt.Errorf("failed to update redirect: %w", err)
	}

	s.logger.Info("Updated SEO redirect", "id", id)
	return redirect.ToResponse(), nil
}

// Delete permanently deletes an SEO redirect
func (s *seoRedirectService) Delete(ctx context.Context, id uint) error {
	if err := s.repo.Delete(ctx, id); err != nil {
		s.logger.Error("Failed to delete SEO redirect", "error", err, "id", id)
		return fmt.Errorf("failed to delete redirect: %w", err)
	}

	s.logger.Info("Deleted SEO redirect", "id", id)
	return nil
}

// SoftDelete marks an SEO redirect as deleted
func (s *seoRedirectService) SoftDelete(ctx context.Context, id uint) error {
	if err := s.repo.SoftDelete(ctx, id); err != nil {
		s.logger.Error("Failed to soft delete SEO redirect", "error", err, "id", id)
		return fmt.Errorf("failed to soft delete redirect: %w", err)
	}

	s.logger.Info("Soft deleted SEO redirect", "id", id)
	return nil
}

// List retrieves SEO redirects with pagination and filters
func (s *seoRedirectService) List(ctx context.Context, req *models.SEORedirectListRequest) ([]*models.SEORedirectResponse, int64, error) {
	// Set defaults
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	// Build filters
	filters := make(map[string]interface{})
	if req.Status != nil {
		filters["status"] = *req.Status
	}
	if req.RedirectType != nil {
		filters["redirect_type"] = *req.RedirectType
	}
	if req.SourceURL != nil {
		filters["source_url"] = *req.SourceURL
	}
	if req.CreatedBy != nil {
		filters["created_by"] = *req.CreatedBy
	}
	if req.SortBy != nil {
		filters["sort_by"] = *req.SortBy
	}
	if req.SortOrder != nil {
		filters["sort_order"] = *req.SortOrder
	}

	// Get redirects
	redirects, total, err := s.repo.List(ctx, req.WebsiteID, req.TenantID, req.Page, req.PageSize, filters)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list redirects: %w", err)
	}

	// Convert to response format
	responses := make([]*models.SEORedirectResponse, len(redirects))
	for i, redirect := range redirects {
		responses[i] = redirect.ToResponse()
	}

	return responses, total, nil
}

// ListWithCursor retrieves SEO redirects using cursor-based pagination
func (s *seoRedirectService) ListWithCursor(ctx context.Context, websiteID, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) (*dto.SEORedirectListResponse, error) {
	redirects, paginationResp, err := s.repo.ListWithCursor(ctx, websiteID, tenantID, req, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to list redirects with cursor: %w", err)
	}

	// Convert redirects to response DTOs
	redirectResponses := make([]dto.SEORedirectResponse, len(redirects))
	for i, redirect := range redirects {
		redirectResponses[i] = s.redirectToResponseDTO(*redirect)
	}

	return &dto.SEORedirectListResponse{
		Redirects:  redirectResponses,
		Pagination: paginationResp,
	}, nil
}

// GetBySourceURL retrieves an SEO redirect by source URL
func (s *seoRedirectService) GetBySourceURL(ctx context.Context, websiteID, tenantID uint, sourceURL string) (*models.SEORedirectResponse, error) {
	redirect, err := s.repo.GetBySourceURL(ctx, websiteID, tenantID, sourceURL)
	if err != nil {
		return nil, fmt.Errorf("failed to get redirect by source URL: %w", err)
	}

	return redirect.ToResponse(), nil
}

// GetActiveRedirects retrieves all active redirects for a website
func (s *seoRedirectService) GetActiveRedirects(ctx context.Context, websiteID, tenantID uint) ([]*models.SEORedirectResponse, error) {
	redirects, err := s.repo.GetActiveRedirects(ctx, websiteID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get active redirects: %w", err)
	}

	responses := make([]*models.SEORedirectResponse, len(redirects))
	for i, redirect := range redirects {
		responses[i] = redirect.ToResponse()
	}

	return responses, nil
}

// ProcessRedirect processes a redirect and increments hit count
func (s *seoRedirectService) ProcessRedirect(ctx context.Context, websiteID, tenantID uint, path, ip, userAgent, referrer string) (*models.SEORedirectResponse, error) {
	// Find matching redirect
	redirect, err := s.repo.FindMatchingRedirect(ctx, websiteID, tenantID, path)
	if err != nil {
		return nil, fmt.Errorf("no matching redirect found: %w", err)
	}

	// Check if redirect is active
	if !redirect.IsActive() {
		return nil, fmt.Errorf("redirect is not active")
	}

	// Increment hit count
	if err := s.repo.IncrementHit(ctx, redirect.ID, ip, userAgent, referrer); err != nil {
		s.logger.Error("Failed to increment redirect hit count", "error", err, "id", redirect.ID)
		// Don't return error, still process redirect
	}

	s.logger.Info("Processed redirect", "id", redirect.ID, "path", path, "destination", redirect.DestinationURL)
	return redirect.ToResponse(), nil
}

// FindRedirectForPath finds a redirect for a given path
func (s *seoRedirectService) FindRedirectForPath(ctx context.Context, websiteID, tenantID uint, path string) (*models.SEORedirectResponse, error) {
	redirect, err := s.repo.FindMatchingRedirect(ctx, websiteID, tenantID, path)
	if err != nil {
		return nil, fmt.Errorf("no matching redirect found: %w", err)
	}

	return redirect.ToResponse(), nil
}

// GetStats retrieves redirect statistics
func (s *seoRedirectService) GetStats(ctx context.Context, websiteID, tenantID uint) (*models.SEORedirectStatsResponse, error) {
	stats, err := s.repo.GetStats(ctx, websiteID, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get redirect stats: %w", err)
	}

	return stats, nil
}

// GetTopRedirects retrieves top redirects by hit count
func (s *seoRedirectService) GetTopRedirects(ctx context.Context, websiteID, tenantID uint, limit int) ([]*models.SEORedirectResponse, error) {
	if limit <= 0 {
		limit = 10
	}

	redirects, err := s.repo.GetTopRedirectsByHits(ctx, websiteID, tenantID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get top redirects: %w", err)
	}

	responses := make([]*models.SEORedirectResponse, len(redirects))
	for i, redirect := range redirects {
		responses[i] = redirect.ToResponse()
	}

	return responses, nil
}

// GetRecentHits retrieves redirects with recent hits
func (s *seoRedirectService) GetRecentHits(ctx context.Context, websiteID, tenantID uint, hours int) ([]*models.SEORedirectResponse, error) {
	if hours <= 0 {
		hours = 24
	}

	since := time.Now().Add(-time.Duration(hours) * time.Hour)
	redirects, err := s.repo.GetRecentHits(ctx, websiteID, tenantID, since)
	if err != nil {
		return nil, fmt.Errorf("failed to get recent hits: %w", err)
	}

	responses := make([]*models.SEORedirectResponse, len(redirects))
	for i, redirect := range redirects {
		responses[i] = redirect.ToResponse()
	}

	return responses, nil
}

// BulkCreate creates multiple redirects
func (s *seoRedirectService) BulkCreate(ctx context.Context, requests []*models.SEORedirectCreateRequest) ([]*models.SEORedirectResponse, error) {
	redirects := make([]*models.SEORedirect, len(requests))

	for i, req := range requests {
		// Validate each request
		if err := s.ValidateRedirect(ctx, req); err != nil {
			return nil, fmt.Errorf("validation failed for redirect %d: %w", i, err)
		}

		// Create redirect model
		redirect := &models.SEORedirect{
			WebsiteID: req.WebsiteID,
			TenantID:  req.TenantID,

			SourceURL:       req.SourceURL,
			SourcePath:      req.SourcePath,
			DestinationURL:  req.DestinationURL,
			DestinationPath: req.DestinationPath,

			RedirectType:       req.RedirectType,
			RedirectMatch:      req.RedirectMatch,
			RegexPattern:       req.RegexPattern,
			ReplacementPattern: req.ReplacementPattern,

			Conditions:          req.Conditions,
			QueryStringHandling: req.QueryStringHandling,

			SEOReason: req.SEOReason,
			Notes:     req.Notes,
			CreatedBy: req.CreatedBy,

			ActiveFrom:  req.ActiveFrom,
			ActiveUntil: req.ActiveUntil,

			Status: req.Status,
		}

		// Set defaults
		if redirect.RedirectType == "" {
			redirect.RedirectType = models.RedirectType301
		}
		if redirect.RedirectMatch == "" {
			redirect.RedirectMatch = models.RedirectMatchExact
		}
		if redirect.QueryStringHandling == "" {
			redirect.QueryStringHandling = models.QueryStringPreserve
		}
		if redirect.Status == "" {
			redirect.Status = models.RedirectStatusActive
		}

		redirects[i] = redirect
	}

	// Bulk create
	if err := s.repo.BulkCreate(ctx, redirects); err != nil {
		s.logger.Error("Failed to bulk create SEO redirects", "error", err)
		return nil, fmt.Errorf("failed to bulk create redirects: %w", err)
	}

	// Convert to responses
	responses := make([]*models.SEORedirectResponse, len(redirects))
	for i, redirect := range redirects {
		responses[i] = redirect.ToResponse()
	}

	s.logger.Info("Bulk created SEO redirects", "count", len(redirects))
	return responses, nil
}

// BulkUpdate updates multiple redirects
func (s *seoRedirectService) BulkUpdate(ctx context.Context, updates map[uint]*models.SEORedirectUpdateRequest) ([]*models.SEORedirectResponse, error) {
	var redirects []*models.SEORedirect

	for id, req := range updates {
		// Get existing redirect
		redirect, err := s.repo.GetByID(ctx, id)
		if err != nil {
			return nil, fmt.Errorf("failed to get redirect %d: %w", id, err)
		}

		// Update fields
		if req.SourceURL != nil {
			redirect.SourceURL = *req.SourceURL
		}
		if req.SourcePath != nil {
			redirect.SourcePath = *req.SourcePath
		}
		if req.DestinationURL != nil {
			redirect.DestinationURL = *req.DestinationURL
		}
		if req.DestinationPath != nil {
			redirect.DestinationPath = req.DestinationPath
		}
		if req.RedirectType != nil {
			redirect.RedirectType = *req.RedirectType
		}
		if req.RedirectMatch != nil {
			redirect.RedirectMatch = *req.RedirectMatch
		}
		if req.RegexPattern != nil {
			redirect.RegexPattern = req.RegexPattern
		}
		if req.ReplacementPattern != nil {
			redirect.ReplacementPattern = req.ReplacementPattern
		}
		if req.Conditions != nil {
			redirect.Conditions = req.Conditions
		}
		if req.QueryStringHandling != nil {
			redirect.QueryStringHandling = *req.QueryStringHandling
		}
		if req.SEOReason != nil {
			redirect.SEOReason = req.SEOReason
		}
		if req.Notes != nil {
			redirect.Notes = req.Notes
		}
		if req.UpdatedBy != nil {
			redirect.UpdatedBy = req.UpdatedBy
		}
		if req.ActiveFrom != nil {
			redirect.ActiveFrom = req.ActiveFrom
		}
		if req.ActiveUntil != nil {
			redirect.ActiveUntil = req.ActiveUntil
		}
		if req.Status != nil {
			redirect.Status = *req.Status
		}

		redirects = append(redirects, redirect)
	}

	// Bulk update
	if err := s.repo.BulkUpdate(ctx, redirects); err != nil {
		s.logger.Error("Failed to bulk update SEO redirects", "error", err)
		return nil, fmt.Errorf("failed to bulk update redirects: %w", err)
	}

	// Convert to responses
	responses := make([]*models.SEORedirectResponse, len(redirects))
	for i, redirect := range redirects {
		responses[i] = redirect.ToResponse()
	}

	s.logger.Info("Bulk updated SEO redirects", "count", len(redirects))
	return responses, nil
}

// BulkDelete deletes multiple redirects
func (s *seoRedirectService) BulkDelete(ctx context.Context, ids []uint) error {
	if err := s.repo.BulkDelete(ctx, ids); err != nil {
		s.logger.Error("Failed to bulk delete SEO redirects", "error", err)
		return fmt.Errorf("failed to bulk delete redirects: %w", err)
	}

	s.logger.Info("Bulk deleted SEO redirects", "count", len(ids))
	return nil
}

// ValidateRedirect validates a redirect request
func (s *seoRedirectService) ValidateRedirect(ctx context.Context, req *models.SEORedirectCreateRequest) error {
	// Check required fields
	if req.WebsiteID == 0 {
		return fmt.Errorf("website_id is required")
	}
	if req.TenantID == 0 {
		return fmt.Errorf("tenant_id is required")
	}
	if req.SourceURL == "" {
		return fmt.Errorf("source_url is required")
	}
	if req.SourcePath == "" {
		return fmt.Errorf("source_path is required")
	}
	if req.DestinationURL == "" {
		return fmt.Errorf("destination_url is required")
	}

	// Validate redirect type
	if req.RedirectType != "" {
		validTypes := []models.RedirectType{
			models.RedirectType301,
			models.RedirectType302,
			models.RedirectType303,
			models.RedirectType307,
			models.RedirectType308,
		}
		valid := false
		for _, validType := range validTypes {
			if req.RedirectType == validType {
				valid = true
				break
			}
		}
		if !valid {
			return fmt.Errorf("invalid redirect_type: %s", req.RedirectType)
		}
	}

	// Validate redirect match
	if req.RedirectMatch != "" {
		validMatches := []models.RedirectMatch{
			models.RedirectMatchExact,
			models.RedirectMatchRegex,
			models.RedirectMatchWildcard,
		}
		valid := false
		for _, validMatch := range validMatches {
			if req.RedirectMatch == validMatch {
				valid = true
				break
			}
		}
		if !valid {
			return fmt.Errorf("invalid redirect_match: %s", req.RedirectMatch)
		}
	}

	// Validate regex pattern if using regex match
	if req.RedirectMatch == models.RedirectMatchRegex {
		if req.RegexPattern == nil || *req.RegexPattern == "" {
			return fmt.Errorf("regex_pattern is required when using regex matching")
		}

		// Test regex pattern
		if _, err := regexp.Compile(*req.RegexPattern); err != nil {
			return fmt.Errorf("invalid regex_pattern: %w", err)
		}
	}

	// Validate URLs
	if req.SourceURL == req.DestinationURL {
		return fmt.Errorf("source_url and destination_url cannot be the same")
	}

	// Validate active dates
	if req.ActiveFrom != nil && req.ActiveUntil != nil {
		if req.ActiveFrom.After(*req.ActiveUntil) {
			return fmt.Errorf("active_from cannot be after active_until")
		}
	}

	return nil
}

// TestRedirect tests a redirect against a test path
func (s *seoRedirectService) TestRedirect(ctx context.Context, id uint, testPath string) (string, error) {
	redirect, err := s.repo.GetByID(ctx, id)
	if err != nil {
		return "", fmt.Errorf("failed to get redirect: %w", err)
	}

	// Test based on match type
	switch redirect.RedirectMatch {
	case models.RedirectMatchExact:
		if redirect.SourcePath == testPath {
			return redirect.DestinationURL, nil
		}
		return "", fmt.Errorf("no exact match")

	case models.RedirectMatchRegex:
		if redirect.RegexPattern == nil {
			return "", fmt.Errorf("regex pattern not set")
		}

		regex, err := regexp.Compile(*redirect.RegexPattern)
		if err != nil {
			return "", fmt.Errorf("invalid regex pattern: %w", err)
		}

		if regex.MatchString(testPath) {
			destination := redirect.DestinationURL
			if redirect.ReplacementPattern != nil {
				destination = regex.ReplaceAllString(testPath, *redirect.ReplacementPattern)
			}
			return destination, nil
		}
		return "", fmt.Errorf("no regex match")

	case models.RedirectMatchWildcard:
		// Simple wildcard matching with * and ?
		pattern := strings.ReplaceAll(redirect.SourcePath, "*", ".*")
		pattern = strings.ReplaceAll(pattern, "?", ".")
		pattern = "^" + pattern + "$"

		regex, err := regexp.Compile(pattern)
		if err != nil {
			return "", fmt.Errorf("invalid wildcard pattern: %w", err)
		}

		if regex.MatchString(testPath) {
			return redirect.DestinationURL, nil
		}
		return "", fmt.Errorf("no wildcard match")
	}

	return "", fmt.Errorf("unknown redirect match type: %s", redirect.RedirectMatch)
}

// CleanupExpiredRedirects removes expired redirects
func (s *seoRedirectService) CleanupExpiredRedirects(ctx context.Context) (int, error) {
	err := s.repo.CleanupExpiredRedirects(ctx)
	if err != nil {
		s.logger.Error("Failed to cleanup expired redirects", "error", err)
		return 0, fmt.Errorf("failed to cleanup expired redirects: %w", err)
	}

	// TODO: Return actual count of cleaned up redirects
	count := 0
	s.logger.Info("Cleaned up expired redirects", "count", count)
	return count, nil
}

// ArchiveOldRedirects archives old redirects
func (s *seoRedirectService) ArchiveOldRedirects(ctx context.Context, days int) (int, error) {
	if days <= 0 {
		days = 90 // Default to 90 days
	}

	before := time.Now().AddDate(0, 0, -days)
	err := s.repo.ArchiveOldRedirects(ctx, before)
	if err != nil {
		s.logger.Error("Failed to archive old redirects", "error", err)
		return 0, fmt.Errorf("failed to archive old redirects: %w", err)
	}

	// TODO: Return actual count of archived redirects
	count := 0
	s.logger.Info("Archived old redirects", "count", count, "days", days)
	return count, nil
}

// redirectToResponseDTO converts models.SEORedirect to dto.SEORedirectResponse
func (s *seoRedirectService) redirectToResponseDTO(redirect models.SEORedirect) dto.SEORedirectResponse {
	return dto.SEORedirectResponse{
		ID:        redirect.ID,
		WebsiteID: redirect.WebsiteID,
		TenantID:  redirect.TenantID,

		SourceURL:       redirect.SourceURL,
		SourcePath:      redirect.SourcePath,
		DestinationURL:  redirect.DestinationURL,
		DestinationPath: redirect.DestinationPath,

		RedirectType:       redirect.RedirectType,
		RedirectMatch:      redirect.RedirectMatch,
		RegexPattern:       redirect.RegexPattern,
		ReplacementPattern: redirect.ReplacementPattern,

		Conditions:          redirect.Conditions,
		QueryStringHandling: redirect.QueryStringHandling,

		HitCount:         redirect.HitCount,
		LastHitAt:        redirect.LastHitAt,
		LastHitIP:        redirect.LastHitIP,
		LastHitUserAgent: redirect.LastHitUserAgent,
		LastHitReferrer:  redirect.LastHitReferrer,

		SEOReason: redirect.SEOReason,
		Notes:     redirect.Notes,
		CreatedBy: redirect.CreatedBy,
		UpdatedBy: redirect.UpdatedBy,

		ActiveFrom:  redirect.ActiveFrom,
		ActiveUntil: redirect.ActiveUntil,

		Status:    redirect.Status,
		CreatedAt: redirect.CreatedAt,
		UpdatedAt: redirect.UpdatedAt,
	}
}
