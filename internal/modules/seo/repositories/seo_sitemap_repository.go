package repositories

import (
	"context"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
	"gorm.io/gorm"
)

// SEOSitemapRepository defines the interface for SEO sitemap operations
type SEOSitemapRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, sitemap *models.SEOSitemap) error
	GetByID(ctx context.Context, id uint) (*models.SEOSitemap, error)
	Update(ctx context.Context, sitemap *models.SEOSitemap) error
	Delete(ctx context.Context, id uint) error

	// Query operations
	List(ctx context.Context, websiteID, tenantID uint, page, pageSize int, filters map[string]interface{}) ([]*models.SEOSitemap, int64, error)
	GetByWebsite(ctx context.Context, websiteID, tenantID uint) ([]*models.SEOSitemap, error)
	GetByType(ctx context.Context, websiteID, tenantID uint, sitemapType models.SitemapType) ([]*models.SEOSitemap, error)
	GetByTypeAndWebsite(ctx context.Context, websiteID, tenantID uint, sitemapType models.SitemapType) (*models.SEOSitemap, error)
	GetByFilename(ctx context.Context, websiteID, tenantID uint, filename string) (*models.SEOSitemap, error)

	// Statistics operations
	GetStats(ctx context.Context, websiteID, tenantID uint) (*models.SEOSitemapStatsResponse, error)

	// Maintenance operations
	CleanupOldSitemaps(ctx context.Context, before time.Time) (int, error)
}

// seoSitemapRepository implements the SEOSitemapRepository interface
type seoSitemapRepository struct {
	db *gorm.DB
}

// NewSEOSitemapRepository creates a new SEO sitemap repository
func NewSEOSitemapRepository(db *gorm.DB) SEOSitemapRepository {
	return &seoSitemapRepository{db: db}
}

// Create creates a new SEO sitemap
func (r *seoSitemapRepository) Create(ctx context.Context, sitemap *models.SEOSitemap) error {
	return r.db.WithContext(ctx).Create(sitemap).Error
}

// GetByID retrieves an SEO sitemap by ID
func (r *seoSitemapRepository) GetByID(ctx context.Context, id uint) (*models.SEOSitemap, error) {
	var sitemap models.SEOSitemap
	err := r.db.WithContext(ctx).
		Preload("Website").
		Preload("Tenant").
		First(&sitemap, id).Error
	if err != nil {
		return nil, err
	}
	return &sitemap, nil
}

// Update updates an existing SEO sitemap
func (r *seoSitemapRepository) Update(ctx context.Context, sitemap *models.SEOSitemap) error {
	return r.db.WithContext(ctx).Save(sitemap).Error
}

// Delete permanently deletes an SEO sitemap
func (r *seoSitemapRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.SEOSitemap{}, id).Error
}

// List retrieves SEO sitemaps with pagination and filters
func (r *seoSitemapRepository) List(ctx context.Context, websiteID, tenantID uint, page, pageSize int, filters map[string]interface{}) ([]*models.SEOSitemap, int64, error) {
	var sitemaps []*models.SEOSitemap
	var total int64

	query := r.db.WithContext(ctx).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("status != ?", models.SitemapStatusDeleted)

	// Apply filters
	if sitemapType, ok := filters["type"]; ok {
		query = query.Where("type = ?", sitemapType)
	}
	if status, ok := filters["status"]; ok {
		query = query.Where("status = ?", status)
	}
	if name, ok := filters["name"]; ok {
		query = query.Where("name LIKE ?", fmt.Sprintf("%%%s%%", name))
	}
	if from, ok := filters["created_from"]; ok {
		query = query.Where("created_at >= ?", from)
	}
	if to, ok := filters["created_to"]; ok {
		query = query.Where("created_at <= ?", to)
	}

	// Count total records
	err := query.Model(&models.SEOSitemap{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (page - 1) * pageSize
	query = query.Offset(offset).Limit(pageSize)

	// Apply sorting
	if sortBy, ok := filters["sort_by"]; ok {
		sortOrder := "desc"
		if order, ok := filters["sort_order"]; ok {
			sortOrder = order.(string)
		}
		query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))
	} else {
		query = query.Order("created_at desc")
	}

	err = query.
		Preload("Website").
		Preload("Tenant").
		Find(&sitemaps).Error

	return sitemaps, total, err
}

// GetByWebsite retrieves all sitemaps for a website
func (r *seoSitemapRepository) GetByWebsite(ctx context.Context, websiteID, tenantID uint) ([]*models.SEOSitemap, error) {
	var sitemaps []*models.SEOSitemap
	err := r.db.WithContext(ctx).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("status != ?", models.SitemapStatusDeleted).
		Order("created_at desc").
		Find(&sitemaps).Error

	return sitemaps, err
}

// GetByType retrieves sitemaps by type
func (r *seoSitemapRepository) GetByType(ctx context.Context, websiteID, tenantID uint, sitemapType models.SitemapType) ([]*models.SEOSitemap, error) {
	var sitemaps []*models.SEOSitemap
	err := r.db.WithContext(ctx).
		Where("website_id = ? AND tenant_id = ? AND type = ?", websiteID, tenantID, sitemapType).
		Where("status != ?", models.SitemapStatusDeleted).
		Order("created_at desc").
		Find(&sitemaps).Error

	return sitemaps, err
}

// GetByTypeAndWebsite retrieves a sitemap by type and website
func (r *seoSitemapRepository) GetByTypeAndWebsite(ctx context.Context, websiteID, tenantID uint, sitemapType models.SitemapType) (*models.SEOSitemap, error) {
	var sitemap models.SEOSitemap
	err := r.db.WithContext(ctx).
		Where("website_id = ? AND tenant_id = ? AND type = ?", websiteID, tenantID, sitemapType).
		Where("status != ?", models.SitemapStatusDeleted).
		Order("created_at desc").
		First(&sitemap).Error
	if err != nil {
		return nil, err
	}
	return &sitemap, nil
}

// GetByFilename retrieves a sitemap by filename
func (r *seoSitemapRepository) GetByFilename(ctx context.Context, websiteID, tenantID uint, filename string) (*models.SEOSitemap, error) {
	var sitemap models.SEOSitemap
	err := r.db.WithContext(ctx).
		Where("website_id = ? AND tenant_id = ? AND filename = ?", websiteID, tenantID, filename).
		Where("status != ?", models.SitemapStatusDeleted).
		First(&sitemap).Error
	if err != nil {
		return nil, err
	}
	return &sitemap, nil
}

// GetStats retrieves sitemap statistics
func (r *seoSitemapRepository) GetStats(ctx context.Context, websiteID, tenantID uint) (*models.SEOSitemapStatsResponse, error) {
	var stats models.SEOSitemapStatsResponse
	var count int64

	// Count total sitemaps
	err := r.db.WithContext(ctx).
		Model(&models.SEOSitemap{}).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("status != ?", models.SitemapStatusDeleted).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	stats.TotalSitemaps = int(count)

	// Count active sitemaps
	err = r.db.WithContext(ctx).
		Model(&models.SEOSitemap{}).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("status = ?", models.SitemapStatusActive).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	stats.ActiveSitemaps = int(count)

	// Count pending sitemaps
	err = r.db.WithContext(ctx).
		Model(&models.SEOSitemap{}).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("status = ?", models.SitemapStatusPending).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	stats.PendingSitemaps = int(count)

	// Count error sitemaps
	err = r.db.WithContext(ctx).
		Model(&models.SEOSitemap{}).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("status = ?", models.SitemapStatusError).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	stats.ErrorSitemaps = int(count)

	// Sum total URLs
	err = r.db.WithContext(ctx).
		Model(&models.SEOSitemap{}).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("status != ?", models.SitemapStatusDeleted).
		Select("COALESCE(SUM(url_count), 0)").
		Scan(&stats.TotalURLs).Error
	if err != nil {
		return nil, err
	}

	// Sum total file size
	err = r.db.WithContext(ctx).
		Model(&models.SEOSitemap{}).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("status != ?", models.SitemapStatusDeleted).
		Select("COALESCE(SUM(file_size), 0)").
		Scan(&stats.TotalFileSize).Error
	if err != nil {
		return nil, err
	}

	// Count compressed sitemaps
	err = r.db.WithContext(ctx).
		Model(&models.SEOSitemap{}).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("compressed = ?", true).
		Where("status != ?", models.SitemapStatusDeleted).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	stats.CompressedSitemaps = int(count)

	return &stats, nil
}

// CleanupOldSitemaps removes old sitemaps
func (r *seoSitemapRepository) CleanupOldSitemaps(ctx context.Context, before time.Time) (int, error) {
	result := r.db.WithContext(ctx).
		Where("created_at < ?", before).
		Where("status = ?", models.SitemapStatusDeleted).
		Delete(&models.SEOSitemap{})

	return int(result.RowsAffected), result.Error
}
