package repositories

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// SEOMetaRepository defines the interface for SEO meta repository
type SEOMetaRepository interface {
	Create(ctx context.Context, seoMeta *models.SEOMeta) error
	GetByID(ctx context.Context, tenantID, id uint) (*models.SEOMeta, error)
	GetByPageURL(ctx context.Context, tenantID uint, websiteID uint, pageURL string) (*models.SEOMeta, error)
	GetByPageIdentifier(ctx context.Context, tenantID uint, websiteID uint, pageType string, pageID uint) (*models.SEOMeta, error)
	Update(ctx context.Context, tenantID, id uint, seoMeta *models.SEOMeta) error
	Delete(ctx context.Context, tenantID, id uint) error
	List(ctx context.Context, tenantID uint, filter *models.SEOMetaFilter, cursor *pagination.Cursor) ([]*models.SEOMeta, *pagination.Cursor, error)
	GetWebsiteMetaCount(ctx context.Context, tenantID, websiteID uint) (int, error)
	GetMetaByStatus(ctx context.Context, tenantID uint, status string) ([]*models.SEOMeta, error)
	BulkUpdateSEOScores(ctx context.Context, tenantID uint, scores map[uint]float64) error
	GetDuplicateMetaTitles(ctx context.Context, tenantID, websiteID uint) ([]models.DuplicateMetaResult, error)
	GetDuplicateMetaDescriptions(ctx context.Context, tenantID, websiteID uint) ([]models.DuplicateMetaResult, error)
	GetMissingMetaTitles(ctx context.Context, tenantID, websiteID uint) ([]*models.SEOMeta, error)
	GetMissingMetaDescriptions(ctx context.Context, tenantID, websiteID uint) ([]*models.SEOMeta, error)
	GetPagesByChangeFrequency(ctx context.Context, tenantID, websiteID uint, frequency string) ([]*models.SEOMeta, error)
	UpdateLastModified(ctx context.Context, tenantID, id uint) error
}

// mysqlSEOMetaRepository implements SEOMetaRepository using MySQL
type mysqlSEOMetaRepository struct {
	db *sql.DB
}

// NewSEOMetaRepository creates a new SEO meta repository
func NewSEOMetaRepository(db *sql.DB) SEOMetaRepository {
	return &mysqlSEOMetaRepository{db: db}
}

// Create creates a new SEO meta record
func (r *mysqlSEOMetaRepository) Create(ctx context.Context, seoMeta *models.SEOMeta) error {
	query := `
		INSERT INTO seo_meta (
			tenant_id, website_id, page_type, page_id, page_url, page_path,
			meta_title, meta_description, meta_keywords, meta_robots, canonical_url,
			og_title, og_description, og_image, og_type, og_url, og_site_name, og_locale,
			twitter_card, twitter_title, twitter_description, twitter_image, twitter_creator, twitter_site,
			schema_type, schema_data, additional_meta, focus_keyword, seo_score, readability_score,
			is_indexed, is_sitemap_included, priority, change_frequency, status
		) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	result, err := r.db.ExecContext(ctx, query,
		seoMeta.TenantID, seoMeta.WebsiteID, seoMeta.PageType, seoMeta.PageID, seoMeta.PageURL, seoMeta.PagePath,
		seoMeta.MetaTitle, seoMeta.MetaDescription, seoMeta.MetaKeywords, seoMeta.MetaRobots, seoMeta.CanonicalURL,
		seoMeta.OGTitle, seoMeta.OGDescription, seoMeta.OGImage, seoMeta.OGType, seoMeta.OGURL, seoMeta.OGSiteName, seoMeta.OGLocale,
		seoMeta.TwitterCard, seoMeta.TwitterTitle, seoMeta.TwitterDescription, seoMeta.TwitterImage, seoMeta.TwitterCreator, seoMeta.TwitterSite,
		seoMeta.SchemaType, seoMeta.SchemaData, seoMeta.AdditionalMeta, seoMeta.FocusKeyword, seoMeta.SEOScore, seoMeta.ReadabilityScore,
		seoMeta.IsIndexed, seoMeta.IsSitemapIncluded, seoMeta.Priority, seoMeta.ChangeFrequency, seoMeta.Status,
	)
	if err != nil {
		return fmt.Errorf("failed to create SEO meta: %w", err)
	}

	id, err := result.LastInsertId()
	if err != nil {
		return fmt.Errorf("failed to get last insert ID: %w", err)
	}

	seoMeta.ID = uint(id)
	return nil
}

// GetByID retrieves SEO meta by ID
func (r *mysqlSEOMetaRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.SEOMeta, error) {
	query := `
		SELECT id, tenant_id, website_id, page_type, page_id, page_url, page_path,
			meta_title, meta_description, meta_keywords, meta_robots, canonical_url,
			og_title, og_description, og_image, og_type, og_url, og_site_name, og_locale,
			twitter_card, twitter_title, twitter_description, twitter_image, twitter_creator, twitter_site,
			schema_type, schema_data, additional_meta, focus_keyword, seo_score, readability_score,
			is_indexed, is_sitemap_included, priority, change_frequency, status, created_at, updated_at
		FROM seo_meta
		WHERE tenant_id = ? AND id = ? AND status != 'deleted'
	`

	seoMeta := &models.SEOMeta{}
	err := r.db.QueryRowContext(ctx, query, tenantID, id).Scan(
		&seoMeta.ID, &seoMeta.TenantID, &seoMeta.WebsiteID, &seoMeta.PageType, &seoMeta.PageID, &seoMeta.PageURL, &seoMeta.PagePath,
		&seoMeta.MetaTitle, &seoMeta.MetaDescription, &seoMeta.MetaKeywords, &seoMeta.MetaRobots, &seoMeta.CanonicalURL,
		&seoMeta.OGTitle, &seoMeta.OGDescription, &seoMeta.OGImage, &seoMeta.OGType, &seoMeta.OGURL, &seoMeta.OGSiteName, &seoMeta.OGLocale,
		&seoMeta.TwitterCard, &seoMeta.TwitterTitle, &seoMeta.TwitterDescription, &seoMeta.TwitterImage, &seoMeta.TwitterCreator, &seoMeta.TwitterSite,
		&seoMeta.SchemaType, &seoMeta.SchemaData, &seoMeta.AdditionalMeta, &seoMeta.FocusKeyword, &seoMeta.SEOScore, &seoMeta.ReadabilityScore,
		&seoMeta.IsIndexed, &seoMeta.IsSitemapIncluded, &seoMeta.Priority, &seoMeta.ChangeFrequency, &seoMeta.Status, &seoMeta.CreatedAt, &seoMeta.UpdatedAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrSEOMetaNotFound
		}
		return nil, fmt.Errorf("failed to get SEO meta: %w", err)
	}

	return seoMeta, nil
}

// GetByPageURL retrieves SEO meta by page URL
func (r *mysqlSEOMetaRepository) GetByPageURL(ctx context.Context, tenantID uint, websiteID uint, pageURL string) (*models.SEOMeta, error) {
	query := `
		SELECT id, tenant_id, website_id, page_type, page_id, page_url, page_path,
			meta_title, meta_description, meta_keywords, meta_robots, canonical_url,
			og_title, og_description, og_image, og_type, og_url, og_site_name, og_locale,
			twitter_card, twitter_title, twitter_description, twitter_image, twitter_creator, twitter_site,
			schema_type, schema_data, additional_meta, focus_keyword, seo_score, readability_score,
			is_indexed, is_sitemap_included, priority, change_frequency, status, created_at, updated_at
		FROM seo_meta
		WHERE tenant_id = ? AND website_id = ? AND page_url = ? AND status != 'deleted'
	`

	seoMeta := &models.SEOMeta{}
	err := r.db.QueryRowContext(ctx, query, tenantID, websiteID, pageURL).Scan(
		&seoMeta.ID, &seoMeta.TenantID, &seoMeta.WebsiteID, &seoMeta.PageType, &seoMeta.PageID, &seoMeta.PageURL, &seoMeta.PagePath,
		&seoMeta.MetaTitle, &seoMeta.MetaDescription, &seoMeta.MetaKeywords, &seoMeta.MetaRobots, &seoMeta.CanonicalURL,
		&seoMeta.OGTitle, &seoMeta.OGDescription, &seoMeta.OGImage, &seoMeta.OGType, &seoMeta.OGURL, &seoMeta.OGSiteName, &seoMeta.OGLocale,
		&seoMeta.TwitterCard, &seoMeta.TwitterTitle, &seoMeta.TwitterDescription, &seoMeta.TwitterImage, &seoMeta.TwitterCreator, &seoMeta.TwitterSite,
		&seoMeta.SchemaType, &seoMeta.SchemaData, &seoMeta.AdditionalMeta, &seoMeta.FocusKeyword, &seoMeta.SEOScore, &seoMeta.ReadabilityScore,
		&seoMeta.IsIndexed, &seoMeta.IsSitemapIncluded, &seoMeta.Priority, &seoMeta.ChangeFrequency, &seoMeta.Status, &seoMeta.CreatedAt, &seoMeta.UpdatedAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrSEOMetaNotFound
		}
		return nil, fmt.Errorf("failed to get SEO meta by URL: %w", err)
	}

	return seoMeta, nil
}

// GetByPageIdentifier retrieves SEO meta by page type and ID
func (r *mysqlSEOMetaRepository) GetByPageIdentifier(ctx context.Context, tenantID uint, websiteID uint, pageType string, pageID uint) (*models.SEOMeta, error) {
	query := `
		SELECT id, tenant_id, website_id, page_type, page_id, page_url, page_path,
			meta_title, meta_description, meta_keywords, meta_robots, canonical_url,
			og_title, og_description, og_image, og_type, og_url, og_site_name, og_locale,
			twitter_card, twitter_title, twitter_description, twitter_image, twitter_creator, twitter_site,
			schema_type, schema_data, additional_meta, focus_keyword, seo_score, readability_score,
			is_indexed, is_sitemap_included, priority, change_frequency, status, created_at, updated_at
		FROM seo_meta
		WHERE tenant_id = ? AND website_id = ? AND page_type = ? AND page_id = ? AND status != 'deleted'
	`

	seoMeta := &models.SEOMeta{}
	err := r.db.QueryRowContext(ctx, query, tenantID, websiteID, pageType, pageID).Scan(
		&seoMeta.ID, &seoMeta.TenantID, &seoMeta.WebsiteID, &seoMeta.PageType, &seoMeta.PageID, &seoMeta.PageURL, &seoMeta.PagePath,
		&seoMeta.MetaTitle, &seoMeta.MetaDescription, &seoMeta.MetaKeywords, &seoMeta.MetaRobots, &seoMeta.CanonicalURL,
		&seoMeta.OGTitle, &seoMeta.OGDescription, &seoMeta.OGImage, &seoMeta.OGType, &seoMeta.OGURL, &seoMeta.OGSiteName, &seoMeta.OGLocale,
		&seoMeta.TwitterCard, &seoMeta.TwitterTitle, &seoMeta.TwitterDescription, &seoMeta.TwitterImage, &seoMeta.TwitterCreator, &seoMeta.TwitterSite,
		&seoMeta.SchemaType, &seoMeta.SchemaData, &seoMeta.AdditionalMeta, &seoMeta.FocusKeyword, &seoMeta.SEOScore, &seoMeta.ReadabilityScore,
		&seoMeta.IsIndexed, &seoMeta.IsSitemapIncluded, &seoMeta.Priority, &seoMeta.ChangeFrequency, &seoMeta.Status, &seoMeta.CreatedAt, &seoMeta.UpdatedAt,
	)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, ErrSEOMetaNotFound
		}
		return nil, fmt.Errorf("failed to get SEO meta by page identifier: %w", err)
	}

	return seoMeta, nil
}

// Update updates SEO meta record
func (r *mysqlSEOMetaRepository) Update(ctx context.Context, tenantID, id uint, seoMeta *models.SEOMeta) error {
	query := `
		UPDATE seo_meta SET
			meta_title = ?, meta_description = ?, meta_keywords = ?, meta_robots = ?, canonical_url = ?,
			og_title = ?, og_description = ?, og_image = ?, og_type = ?, og_url = ?, og_site_name = ?, og_locale = ?,
			twitter_card = ?, twitter_title = ?, twitter_description = ?, twitter_image = ?, twitter_creator = ?, twitter_site = ?,
			schema_type = ?, schema_data = ?, additional_meta = ?, focus_keyword = ?, seo_score = ?, readability_score = ?,
			is_indexed = ?, is_sitemap_included = ?, priority = ?, change_frequency = ?, status = ?, updated_at = CURRENT_TIMESTAMP
		WHERE tenant_id = ? AND id = ?
	`

	result, err := r.db.ExecContext(ctx, query,
		seoMeta.MetaTitle, seoMeta.MetaDescription, seoMeta.MetaKeywords, seoMeta.MetaRobots, seoMeta.CanonicalURL,
		seoMeta.OGTitle, seoMeta.OGDescription, seoMeta.OGImage, seoMeta.OGType, seoMeta.OGURL, seoMeta.OGSiteName, seoMeta.OGLocale,
		seoMeta.TwitterCard, seoMeta.TwitterTitle, seoMeta.TwitterDescription, seoMeta.TwitterImage, seoMeta.TwitterCreator, seoMeta.TwitterSite,
		seoMeta.SchemaType, seoMeta.SchemaData, seoMeta.AdditionalMeta, seoMeta.FocusKeyword, seoMeta.SEOScore, seoMeta.ReadabilityScore,
		seoMeta.IsIndexed, seoMeta.IsSitemapIncluded, seoMeta.Priority, seoMeta.ChangeFrequency, seoMeta.Status,
		tenantID, id,
	)
	if err != nil {
		return fmt.Errorf("failed to update SEO meta: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return ErrSEOMetaNotFound
	}

	return nil
}

// Delete soft deletes SEO meta record
func (r *mysqlSEOMetaRepository) Delete(ctx context.Context, tenantID, id uint) error {
	query := `UPDATE seo_meta SET status = 'deleted', updated_at = CURRENT_TIMESTAMP WHERE tenant_id = ? AND id = ?`

	result, err := r.db.ExecContext(ctx, query, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to delete SEO meta: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return ErrSEOMetaNotFound
	}

	return nil
}

// List retrieves SEO meta records with filters and pagination
func (r *mysqlSEOMetaRepository) List(ctx context.Context, tenantID uint, filter *models.SEOMetaFilter, cursor *pagination.Cursor) ([]*models.SEOMeta, *pagination.Cursor, error) {
	var conditions []string
	var args []interface{}

	// Base condition for tenant
	conditions = append(conditions, "tenant_id = ?")
	args = append(args, tenantID)

	// Filter conditions
	if filter != nil {
		if filter.WebsiteID != nil {
			conditions = append(conditions, "website_id = ?")
			args = append(args, *filter.WebsiteID)
		}

		if filter.PageType != nil {
			conditions = append(conditions, "page_type = ?")
			args = append(args, *filter.PageType)
		}

		if filter.Status != nil {
			conditions = append(conditions, "status = ?")
			args = append(args, *filter.Status)
		} else {
			conditions = append(conditions, "status != 'deleted'")
		}

		if filter.IsIndexed != nil {
			conditions = append(conditions, "is_indexed = ?")
			args = append(args, *filter.IsIndexed)
		}

		if filter.IsSitemapIncluded != nil {
			conditions = append(conditions, "is_sitemap_included = ?")
			args = append(args, *filter.IsSitemapIncluded)
		}

		if filter.FocusKeyword != nil {
			conditions = append(conditions, "focus_keyword LIKE ?")
			args = append(args, "%"+*filter.FocusKeyword+"%")
		}

		if filter.MinSEOScore != nil {
			conditions = append(conditions, "seo_score >= ?")
			args = append(args, *filter.MinSEOScore)
		}

		if filter.MaxSEOScore != nil {
			conditions = append(conditions, "seo_score <= ?")
			args = append(args, *filter.MaxSEOScore)
		}

		if filter.CreatedAfter != nil {
			conditions = append(conditions, "created_at >= ?")
			args = append(args, *filter.CreatedAfter)
		}

		if filter.CreatedBefore != nil {
			conditions = append(conditions, "created_at <= ?")
			args = append(args, *filter.CreatedBefore)
		}
	} else {
		conditions = append(conditions, "status != 'deleted'")
	}

	// Cursor pagination
	if cursor != nil && cursor.ID > 0 {
		conditions = append(conditions, "id > ?")
		args = append(args, cursor.ID)
	}

	// Build query
	whereClause := strings.Join(conditions, " AND ")
	query := fmt.Sprintf(`
		SELECT id, tenant_id, website_id, page_type, page_id, page_url, page_path,
			meta_title, meta_description, meta_keywords, meta_robots, canonical_url,
			og_title, og_description, og_image, og_type, og_url, og_site_name, og_locale,
			twitter_card, twitter_title, twitter_description, twitter_image, twitter_creator, twitter_site,
			schema_type, schema_data, additional_meta, focus_keyword, seo_score, readability_score,
			is_indexed, is_sitemap_included, priority, change_frequency, status, created_at, updated_at
		FROM seo_meta
		WHERE %s
		ORDER BY id ASC
		LIMIT ?
	`, whereClause)

	limit := 20
	args = append(args, limit+1) // Get one extra to check if there are more results

	rows, err := r.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to list SEO meta: %w", err)
	}
	defer rows.Close()

	var seoMetas []*models.SEOMeta
	for rows.Next() {
		seoMeta := &models.SEOMeta{}
		err := rows.Scan(
			&seoMeta.ID, &seoMeta.TenantID, &seoMeta.WebsiteID, &seoMeta.PageType, &seoMeta.PageID, &seoMeta.PageURL, &seoMeta.PagePath,
			&seoMeta.MetaTitle, &seoMeta.MetaDescription, &seoMeta.MetaKeywords, &seoMeta.MetaRobots, &seoMeta.CanonicalURL,
			&seoMeta.OGTitle, &seoMeta.OGDescription, &seoMeta.OGImage, &seoMeta.OGType, &seoMeta.OGURL, &seoMeta.OGSiteName, &seoMeta.OGLocale,
			&seoMeta.TwitterCard, &seoMeta.TwitterTitle, &seoMeta.TwitterDescription, &seoMeta.TwitterImage, &seoMeta.TwitterCreator, &seoMeta.TwitterSite,
			&seoMeta.SchemaType, &seoMeta.SchemaData, &seoMeta.AdditionalMeta, &seoMeta.FocusKeyword, &seoMeta.SEOScore, &seoMeta.ReadabilityScore,
			&seoMeta.IsIndexed, &seoMeta.IsSitemapIncluded, &seoMeta.Priority, &seoMeta.ChangeFrequency, &seoMeta.Status, &seoMeta.CreatedAt, &seoMeta.UpdatedAt,
		)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to scan SEO meta: %w", err)
		}
		seoMetas = append(seoMetas, seoMeta)
	}

	// Check for more results
	hasMore := len(seoMetas) > limit
	if hasMore {
		seoMetas = seoMetas[:limit]
	}

	// Create next cursor
	var nextCursor *pagination.Cursor
	if hasMore && len(seoMetas) > 0 {
		lastMeta := seoMetas[len(seoMetas)-1]
		nextCursor = &pagination.Cursor{
			ID:   int64(lastMeta.ID),
			Time: lastMeta.CreatedAt,
		}
	}

	return seoMetas, nextCursor, nil
}

// GetWebsiteMetaCount returns the count of SEO meta records for a website
func (r *mysqlSEOMetaRepository) GetWebsiteMetaCount(ctx context.Context, tenantID, websiteID uint) (int, error) {
	query := `SELECT COUNT(*) FROM seo_meta WHERE tenant_id = ? AND website_id = ? AND status != 'deleted'`

	var count int
	err := r.db.QueryRowContext(ctx, query, tenantID, websiteID).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("failed to get website meta count: %w", err)
	}

	return count, nil
}

// GetMetaByStatus returns SEO meta records by status
func (r *mysqlSEOMetaRepository) GetMetaByStatus(ctx context.Context, tenantID uint, status string) ([]*models.SEOMeta, error) {
	query := `
		SELECT id, tenant_id, website_id, page_type, page_id, page_url, page_path,
			meta_title, meta_description, meta_keywords, meta_robots, canonical_url,
			og_title, og_description, og_image, og_type, og_url, og_site_name, og_locale,
			twitter_card, twitter_title, twitter_description, twitter_image, twitter_creator, twitter_site,
			schema_type, schema_data, additional_meta, focus_keyword, seo_score, readability_score,
			is_indexed, is_sitemap_included, priority, change_frequency, status, created_at, updated_at
		FROM seo_meta
		WHERE tenant_id = ? AND status = ?
		ORDER BY created_at DESC
	`

	rows, err := r.db.QueryContext(ctx, query, tenantID, status)
	if err != nil {
		return nil, fmt.Errorf("failed to get meta by status: %w", err)
	}
	defer rows.Close()

	var seoMetas []*models.SEOMeta
	for rows.Next() {
		seoMeta := &models.SEOMeta{}
		err := rows.Scan(
			&seoMeta.ID, &seoMeta.TenantID, &seoMeta.WebsiteID, &seoMeta.PageType, &seoMeta.PageID, &seoMeta.PageURL, &seoMeta.PagePath,
			&seoMeta.MetaTitle, &seoMeta.MetaDescription, &seoMeta.MetaKeywords, &seoMeta.MetaRobots, &seoMeta.CanonicalURL,
			&seoMeta.OGTitle, &seoMeta.OGDescription, &seoMeta.OGImage, &seoMeta.OGType, &seoMeta.OGURL, &seoMeta.OGSiteName, &seoMeta.OGLocale,
			&seoMeta.TwitterCard, &seoMeta.TwitterTitle, &seoMeta.TwitterDescription, &seoMeta.TwitterImage, &seoMeta.TwitterCreator, &seoMeta.TwitterSite,
			&seoMeta.SchemaType, &seoMeta.SchemaData, &seoMeta.AdditionalMeta, &seoMeta.FocusKeyword, &seoMeta.SEOScore, &seoMeta.ReadabilityScore,
			&seoMeta.IsIndexed, &seoMeta.IsSitemapIncluded, &seoMeta.Priority, &seoMeta.ChangeFrequency, &seoMeta.Status, &seoMeta.CreatedAt, &seoMeta.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan SEO meta: %w", err)
		}
		seoMetas = append(seoMetas, seoMeta)
	}

	return seoMetas, nil
}

// BulkUpdateSEOScores updates SEO scores for multiple records
func (r *mysqlSEOMetaRepository) BulkUpdateSEOScores(ctx context.Context, tenantID uint, scores map[uint]float64) error {
	if len(scores) == 0 {
		return nil
	}

	tx, err := r.db.BeginTx(ctx, nil)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer tx.Rollback()

	query := `UPDATE seo_meta SET seo_score = ?, updated_at = CURRENT_TIMESTAMP WHERE tenant_id = ? AND id = ?`
	stmt, err := tx.PrepareContext(ctx, query)
	if err != nil {
		return fmt.Errorf("failed to prepare statement: %w", err)
	}
	defer stmt.Close()

	for id, score := range scores {
		_, err := stmt.ExecContext(ctx, score, tenantID, id)
		if err != nil {
			return fmt.Errorf("failed to update SEO score for ID %d: %w", id, err)
		}
	}

	return tx.Commit()
}

// GetDuplicateMetaTitles returns duplicate meta titles
func (r *mysqlSEOMetaRepository) GetDuplicateMetaTitles(ctx context.Context, tenantID, websiteID uint) ([]models.DuplicateMetaResult, error) {
	query := `
		SELECT meta_title, COUNT(*) as count, GROUP_CONCAT(page_url) as urls
		FROM seo_meta
		WHERE tenant_id = ? AND website_id = ? AND meta_title IS NOT NULL AND meta_title != '' AND status != 'deleted'
		GROUP BY meta_title
		HAVING COUNT(*) > 1
		ORDER BY count DESC
	`

	rows, err := r.db.QueryContext(ctx, query, tenantID, websiteID)
	if err != nil {
		return nil, fmt.Errorf("failed to get duplicate meta titles: %w", err)
	}
	defer rows.Close()

	var results []models.DuplicateMetaResult
	for rows.Next() {
		var result models.DuplicateMetaResult
		var urls string
		err := rows.Scan(&result.MetaValue, &result.Count, &urls)
		if err != nil {
			return nil, fmt.Errorf("failed to scan duplicate result: %w", err)
		}
		result.URLs = strings.Split(urls, ",")
		results = append(results, result)
	}

	return results, nil
}

// GetDuplicateMetaDescriptions returns duplicate meta descriptions
func (r *mysqlSEOMetaRepository) GetDuplicateMetaDescriptions(ctx context.Context, tenantID, websiteID uint) ([]models.DuplicateMetaResult, error) {
	query := `
		SELECT meta_description, COUNT(*) as count, GROUP_CONCAT(page_url) as urls
		FROM seo_meta
		WHERE tenant_id = ? AND website_id = ? AND meta_description IS NOT NULL AND meta_description != '' AND status != 'deleted'
		GROUP BY meta_description
		HAVING COUNT(*) > 1
		ORDER BY count DESC
	`

	rows, err := r.db.QueryContext(ctx, query, tenantID, websiteID)
	if err != nil {
		return nil, fmt.Errorf("failed to get duplicate meta descriptions: %w", err)
	}
	defer rows.Close()

	var results []models.DuplicateMetaResult
	for rows.Next() {
		var result models.DuplicateMetaResult
		var urls string
		err := rows.Scan(&result.MetaValue, &result.Count, &urls)
		if err != nil {
			return nil, fmt.Errorf("failed to scan duplicate result: %w", err)
		}
		result.URLs = strings.Split(urls, ",")
		results = append(results, result)
	}

	return results, nil
}

// GetMissingMetaTitles returns pages with missing meta titles
func (r *mysqlSEOMetaRepository) GetMissingMetaTitles(ctx context.Context, tenantID, websiteID uint) ([]*models.SEOMeta, error) {
	query := `
		SELECT id, tenant_id, website_id, page_type, page_id, page_url, page_path,
			meta_title, meta_description, meta_keywords, meta_robots, canonical_url,
			og_title, og_description, og_image, og_type, og_url, og_site_name, og_locale,
			twitter_card, twitter_title, twitter_description, twitter_image, twitter_creator, twitter_site,
			schema_type, schema_data, additional_meta, focus_keyword, seo_score, readability_score,
			is_indexed, is_sitemap_included, priority, change_frequency, status, created_at, updated_at
		FROM seo_meta
		WHERE tenant_id = ? AND website_id = ? AND (meta_title IS NULL OR meta_title = '') AND status != 'deleted'
		ORDER BY created_at DESC
	`

	rows, err := r.db.QueryContext(ctx, query, tenantID, websiteID)
	if err != nil {
		return nil, fmt.Errorf("failed to get missing meta titles: %w", err)
	}
	defer rows.Close()

	var seoMetas []*models.SEOMeta
	for rows.Next() {
		seoMeta := &models.SEOMeta{}
		err := rows.Scan(
			&seoMeta.ID, &seoMeta.TenantID, &seoMeta.WebsiteID, &seoMeta.PageType, &seoMeta.PageID, &seoMeta.PageURL, &seoMeta.PagePath,
			&seoMeta.MetaTitle, &seoMeta.MetaDescription, &seoMeta.MetaKeywords, &seoMeta.MetaRobots, &seoMeta.CanonicalURL,
			&seoMeta.OGTitle, &seoMeta.OGDescription, &seoMeta.OGImage, &seoMeta.OGType, &seoMeta.OGURL, &seoMeta.OGSiteName, &seoMeta.OGLocale,
			&seoMeta.TwitterCard, &seoMeta.TwitterTitle, &seoMeta.TwitterDescription, &seoMeta.TwitterImage, &seoMeta.TwitterCreator, &seoMeta.TwitterSite,
			&seoMeta.SchemaType, &seoMeta.SchemaData, &seoMeta.AdditionalMeta, &seoMeta.FocusKeyword, &seoMeta.SEOScore, &seoMeta.ReadabilityScore,
			&seoMeta.IsIndexed, &seoMeta.IsSitemapIncluded, &seoMeta.Priority, &seoMeta.ChangeFrequency, &seoMeta.Status, &seoMeta.CreatedAt, &seoMeta.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan SEO meta: %w", err)
		}
		seoMetas = append(seoMetas, seoMeta)
	}

	return seoMetas, nil
}

// GetMissingMetaDescriptions returns pages with missing meta descriptions
func (r *mysqlSEOMetaRepository) GetMissingMetaDescriptions(ctx context.Context, tenantID, websiteID uint) ([]*models.SEOMeta, error) {
	query := `
		SELECT id, tenant_id, website_id, page_type, page_id, page_url, page_path,
			meta_title, meta_description, meta_keywords, meta_robots, canonical_url,
			og_title, og_description, og_image, og_type, og_url, og_site_name, og_locale,
			twitter_card, twitter_title, twitter_description, twitter_image, twitter_creator, twitter_site,
			schema_type, schema_data, additional_meta, focus_keyword, seo_score, readability_score,
			is_indexed, is_sitemap_included, priority, change_frequency, status, created_at, updated_at
		FROM seo_meta
		WHERE tenant_id = ? AND website_id = ? AND (meta_description IS NULL OR meta_description = '') AND status != 'deleted'
		ORDER BY created_at DESC
	`

	rows, err := r.db.QueryContext(ctx, query, tenantID, websiteID)
	if err != nil {
		return nil, fmt.Errorf("failed to get missing meta descriptions: %w", err)
	}
	defer rows.Close()

	var seoMetas []*models.SEOMeta
	for rows.Next() {
		seoMeta := &models.SEOMeta{}
		err := rows.Scan(
			&seoMeta.ID, &seoMeta.TenantID, &seoMeta.WebsiteID, &seoMeta.PageType, &seoMeta.PageID, &seoMeta.PageURL, &seoMeta.PagePath,
			&seoMeta.MetaTitle, &seoMeta.MetaDescription, &seoMeta.MetaKeywords, &seoMeta.MetaRobots, &seoMeta.CanonicalURL,
			&seoMeta.OGTitle, &seoMeta.OGDescription, &seoMeta.OGImage, &seoMeta.OGType, &seoMeta.OGURL, &seoMeta.OGSiteName, &seoMeta.OGLocale,
			&seoMeta.TwitterCard, &seoMeta.TwitterTitle, &seoMeta.TwitterDescription, &seoMeta.TwitterImage, &seoMeta.TwitterCreator, &seoMeta.TwitterSite,
			&seoMeta.SchemaType, &seoMeta.SchemaData, &seoMeta.AdditionalMeta, &seoMeta.FocusKeyword, &seoMeta.SEOScore, &seoMeta.ReadabilityScore,
			&seoMeta.IsIndexed, &seoMeta.IsSitemapIncluded, &seoMeta.Priority, &seoMeta.ChangeFrequency, &seoMeta.Status, &seoMeta.CreatedAt, &seoMeta.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan SEO meta: %w", err)
		}
		seoMetas = append(seoMetas, seoMeta)
	}

	return seoMetas, nil
}

// GetPagesByChangeFrequency returns pages by change frequency
func (r *mysqlSEOMetaRepository) GetPagesByChangeFrequency(ctx context.Context, tenantID, websiteID uint, frequency string) ([]*models.SEOMeta, error) {
	query := `
		SELECT id, tenant_id, website_id, page_type, page_id, page_url, page_path,
			meta_title, meta_description, meta_keywords, meta_robots, canonical_url,
			og_title, og_description, og_image, og_type, og_url, og_site_name, og_locale,
			twitter_card, twitter_title, twitter_description, twitter_image, twitter_creator, twitter_site,
			schema_type, schema_data, additional_meta, focus_keyword, seo_score, readability_score,
			is_indexed, is_sitemap_included, priority, change_frequency, status, created_at, updated_at
		FROM seo_meta
		WHERE tenant_id = ? AND website_id = ? AND change_frequency = ? AND status != 'deleted'
		ORDER BY priority DESC, created_at DESC
	`

	rows, err := r.db.QueryContext(ctx, query, tenantID, websiteID, frequency)
	if err != nil {
		return nil, fmt.Errorf("failed to get pages by change frequency: %w", err)
	}
	defer rows.Close()

	var seoMetas []*models.SEOMeta
	for rows.Next() {
		seoMeta := &models.SEOMeta{}
		err := rows.Scan(
			&seoMeta.ID, &seoMeta.TenantID, &seoMeta.WebsiteID, &seoMeta.PageType, &seoMeta.PageID, &seoMeta.PageURL, &seoMeta.PagePath,
			&seoMeta.MetaTitle, &seoMeta.MetaDescription, &seoMeta.MetaKeywords, &seoMeta.MetaRobots, &seoMeta.CanonicalURL,
			&seoMeta.OGTitle, &seoMeta.OGDescription, &seoMeta.OGImage, &seoMeta.OGType, &seoMeta.OGURL, &seoMeta.OGSiteName, &seoMeta.OGLocale,
			&seoMeta.TwitterCard, &seoMeta.TwitterTitle, &seoMeta.TwitterDescription, &seoMeta.TwitterImage, &seoMeta.TwitterCreator, &seoMeta.TwitterSite,
			&seoMeta.SchemaType, &seoMeta.SchemaData, &seoMeta.AdditionalMeta, &seoMeta.FocusKeyword, &seoMeta.SEOScore, &seoMeta.ReadabilityScore,
			&seoMeta.IsIndexed, &seoMeta.IsSitemapIncluded, &seoMeta.Priority, &seoMeta.ChangeFrequency, &seoMeta.Status, &seoMeta.CreatedAt, &seoMeta.UpdatedAt,
		)
		if err != nil {
			return nil, fmt.Errorf("failed to scan SEO meta: %w", err)
		}
		seoMetas = append(seoMetas, seoMeta)
	}

	return seoMetas, nil
}

// UpdateLastModified updates the last modified time for SEO meta
func (r *mysqlSEOMetaRepository) UpdateLastModified(ctx context.Context, tenantID, id uint) error {
	query := `UPDATE seo_meta SET updated_at = CURRENT_TIMESTAMP WHERE tenant_id = ? AND id = ?`

	result, err := r.db.ExecContext(ctx, query, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to update last modified: %w", err)
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return fmt.Errorf("failed to get rows affected: %w", err)
	}

	if rowsAffected == 0 {
		return ErrSEOMetaNotFound
	}

	return nil
}

// DuplicateMetaResult represents duplicate meta analysis result
type DuplicateMetaResult struct {
	Value string   `json:"value"`
	Count int      `json:"count"`
	URLs  []string `json:"urls"`
}

// Repository errors
var (
	ErrSEOMetaNotFound = fmt.Errorf("SEO meta not found")
)
