package repositories

import (
	"context"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"gorm.io/gorm"
)

// SEORedirectRepository defines the interface for SEO redirect operations
type SEORedirectRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, redirect *models.SEORedirect) error
	GetByID(ctx context.Context, id uint) (*models.SEORedirect, error)
	Update(ctx context.Context, redirect *models.SEORedirect) error
	Delete(ctx context.Context, id uint) error
	SoftDelete(ctx context.Context, id uint) error

	// Query operations
	List(ctx context.Context, websiteID, tenantID uint, page, pageSize int, filters map[string]interface{}) ([]*models.SEORedirect, int64, error)
	ListWithCursor(ctx context.Context, websiteID, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]*models.SEORedirect, *pagination.CursorResponse, error)
	GetBySourceURL(ctx context.Context, websiteID, tenantID uint, sourceURL string) (*models.SEORedirect, error)
	GetBySourcePath(ctx context.Context, websiteID, tenantID uint, sourcePath string) (*models.SEORedirect, error)
	GetActiveRedirects(ctx context.Context, websiteID, tenantID uint) ([]*models.SEORedirect, error)

	// Redirect matching operations
	FindMatchingRedirect(ctx context.Context, websiteID, tenantID uint, path string) (*models.SEORedirect, error)
	FindExactMatch(ctx context.Context, websiteID, tenantID uint, path string) (*models.SEORedirect, error)
	FindRegexMatch(ctx context.Context, websiteID, tenantID uint, path string) (*models.SEORedirect, error)
	FindWildcardMatch(ctx context.Context, websiteID, tenantID uint, path string) (*models.SEORedirect, error)

	// Hit tracking operations
	IncrementHit(ctx context.Context, id uint, ip, userAgent, referrer string) error
	GetTopRedirectsByHits(ctx context.Context, websiteID, tenantID uint, limit int) ([]*models.SEORedirect, error)
	GetRecentHits(ctx context.Context, websiteID, tenantID uint, since time.Time) ([]*models.SEORedirect, error)

	// Statistics operations
	GetStats(ctx context.Context, websiteID, tenantID uint) (*models.SEORedirectStatsResponse, error)
	GetStatsByDateRange(ctx context.Context, websiteID, tenantID uint, from, to time.Time) (*models.SEORedirectStatsResponse, error)

	// Bulk operations
	BulkCreate(ctx context.Context, redirects []*models.SEORedirect) error
	BulkUpdate(ctx context.Context, redirects []*models.SEORedirect) error
	BulkDelete(ctx context.Context, ids []uint) error

	// Maintenance operations
	CleanupExpiredRedirects(ctx context.Context) error
	ArchiveOldRedirects(ctx context.Context, before time.Time) error
}

// seoRedirectRepository implements the SEORedirectRepository interface
type seoRedirectRepository struct {
	db *gorm.DB
}

// NewSEORedirectRepository creates a new SEO redirect repository
func NewSEORedirectRepository(db *gorm.DB) SEORedirectRepository {
	return &seoRedirectRepository{db: db}
}

// Create creates a new SEO redirect
func (r *seoRedirectRepository) Create(ctx context.Context, redirect *models.SEORedirect) error {
	return r.db.WithContext(ctx).Create(redirect).Error
}

// GetByID retrieves an SEO redirect by ID
func (r *seoRedirectRepository) GetByID(ctx context.Context, id uint) (*models.SEORedirect, error) {
	var redirect models.SEORedirect
	err := r.db.WithContext(ctx).
		Preload("Website").
		Preload("Tenant").
		Preload("Creator").
		Preload("Updater").
		First(&redirect, id).Error
	if err != nil {
		return nil, err
	}
	return &redirect, nil
}

// Update updates an existing SEO redirect
func (r *seoRedirectRepository) Update(ctx context.Context, redirect *models.SEORedirect) error {
	return r.db.WithContext(ctx).Save(redirect).Error
}

// Delete permanently deletes an SEO redirect
func (r *seoRedirectRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.SEORedirect{}, id).Error
}

// SoftDelete marks an SEO redirect as deleted
func (r *seoRedirectRepository) SoftDelete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.SEORedirect{}).
		Where("id = ?", id).
		Update("status", models.RedirectStatusDeleted).Error
}

// List retrieves SEO redirects with pagination and filters
func (r *seoRedirectRepository) List(ctx context.Context, websiteID, tenantID uint, page, pageSize int, filters map[string]interface{}) ([]*models.SEORedirect, int64, error) {
	var redirects []*models.SEORedirect
	var total int64

	query := r.db.WithContext(ctx).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("status != ?", models.RedirectStatusDeleted)

	// Apply filters
	if status, ok := filters["status"]; ok {
		query = query.Where("status = ?", status)
	}
	if redirectType, ok := filters["redirect_type"]; ok {
		query = query.Where("redirect_type = ?", redirectType)
	}
	if sourceURL, ok := filters["source_url"]; ok {
		query = query.Where("source_url LIKE ?", fmt.Sprintf("%%%s%%", sourceURL))
	}
	if createdBy, ok := filters["created_by"]; ok {
		query = query.Where("created_by = ?", createdBy)
	}
	if from, ok := filters["created_from"]; ok {
		query = query.Where("created_at >= ?", from)
	}
	if to, ok := filters["created_to"]; ok {
		query = query.Where("created_at <= ?", to)
	}

	// Count total records
	err := query.Model(&models.SEORedirect{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (page - 1) * pageSize
	query = query.Offset(offset).Limit(pageSize)

	// Apply sorting
	if sortBy, ok := filters["sort_by"]; ok {
		sortOrder := "desc"
		if order, ok := filters["sort_order"]; ok {
			sortOrder = order.(string)
		}
		query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))
	} else {
		query = query.Order("created_at desc")
	}

	err = query.
		Preload("Website").
		Preload("Tenant").
		Preload("Creator").
		Preload("Updater").
		Find(&redirects).Error

	return redirects, total, err
}

// ListWithCursor retrieves SEO redirects with cursor-based pagination
func (r *seoRedirectRepository) ListWithCursor(ctx context.Context, websiteID, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]*models.SEORedirect, *pagination.CursorResponse, error) {
	var redirects []*models.SEORedirect

	query := r.db.WithContext(ctx).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("status != ?", models.RedirectStatusDeleted)

	// Apply filters
	if status, ok := filters["status"]; ok {
		query = query.Where("status = ?", status)
	}
	if redirectType, ok := filters["redirect_type"]; ok {
		query = query.Where("redirect_type = ?", redirectType)
	}
	if sourceURL, ok := filters["source_url"]; ok {
		query = query.Where("source_url LIKE ?", fmt.Sprintf("%%%s%%", sourceURL))
	}
	if search, ok := filters["search"].(string); ok && search != "" {
		query = query.Where("(source_url LIKE ? OR destination_url LIKE ? OR notes LIKE ?)",
			fmt.Sprintf("%%%s%%", search),
			fmt.Sprintf("%%%s%%", search),
			fmt.Sprintf("%%%s%%", search))
	}
	if createdBy, ok := filters["created_by"]; ok {
		query = query.Where("created_by = ?", createdBy)
	}
	if from, ok := filters["date_from"]; ok {
		query = query.Where("created_at >= ?", from)
	}
	if to, ok := filters["date_to"]; ok {
		query = query.Where("created_at <= ?", to)
	}

	// Apply sorting
	sortBy := "created_at"
	if sortByFilter, ok := filters["sort_by"].(string); ok && sortByFilter != "" {
		sortBy = sortByFilter
	}
	sortOrder := "desc"
	if sortOrderFilter, ok := filters["sort_order"].(string); ok && sortOrderFilter != "" {
		sortOrder = sortOrderFilter
	}

	// Parse cursor for pagination
	var cursorID uint
	var cursorTime time.Time
	if req.Cursor != "" {
		if parsed, err := pagination.ParseCursor(req.Cursor); err == nil {
			cursorID = parsed.ID
			cursorTime = parsed.CreatedAt
		}
	}

	// Apply cursor-based pagination
	if cursorID > 0 {
		if sortBy == "created_at" {
			if sortOrder == "desc" {
				query = query.Where("created_at < ?", cursorTime)
			} else {
				query = query.Where("created_at > ?", cursorTime)
			}
		} else {
			// For other sort fields, use ID as fallback
			if sortOrder == "desc" {
				query = query.Where("id < ?", cursorID)
			} else {
				query = query.Where("id > ?", cursorID)
			}
		}
	}

	// Add one extra to check if there are more results
	limit := req.Limit + 1
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder)).Limit(limit)

	// Execute query with preloads
	err := query.
		Preload("Website").
		Preload("Tenant").
		Preload("Creator").
		Preload("Updater").
		Find(&redirects).Error

	if err != nil {
		return nil, nil, err
	}

	// Check if there are more results
	hasNext := len(redirects) > req.Limit
	if hasNext {
		redirects = redirects[:req.Limit] // Remove the extra item
	}

	// Build pagination response
	var nextCursor string
	if hasNext && len(redirects) > 0 {
		lastRedirect := redirects[len(redirects)-1]
		nextCursor, _ = pagination.EncodeCursor(lastRedirect.ID, lastRedirect.CreatedAt)
	}

	paginationResp := &pagination.CursorResponse{
		NextCursor: nextCursor,
		HasNext:    hasNext,
		Limit:      req.Limit,
	}

	return redirects, paginationResp, nil
}

// GetBySourceURL retrieves an SEO redirect by source URL
func (r *seoRedirectRepository) GetBySourceURL(ctx context.Context, websiteID, tenantID uint, sourceURL string) (*models.SEORedirect, error) {
	var redirect models.SEORedirect
	err := r.db.WithContext(ctx).
		Where("website_id = ? AND tenant_id = ? AND source_url = ?", websiteID, tenantID, sourceURL).
		Where("status != ?", models.RedirectStatusDeleted).
		First(&redirect).Error
	if err != nil {
		return nil, err
	}
	return &redirect, nil
}

// GetBySourcePath retrieves an SEO redirect by source path
func (r *seoRedirectRepository) GetBySourcePath(ctx context.Context, websiteID, tenantID uint, sourcePath string) (*models.SEORedirect, error) {
	var redirect models.SEORedirect
	err := r.db.WithContext(ctx).
		Where("website_id = ? AND tenant_id = ? AND source_path = ?", websiteID, tenantID, sourcePath).
		Where("status != ?", models.RedirectStatusDeleted).
		First(&redirect).Error
	if err != nil {
		return nil, err
	}
	return &redirect, nil
}

// GetActiveRedirects retrieves all active redirects for a website
func (r *seoRedirectRepository) GetActiveRedirects(ctx context.Context, websiteID, tenantID uint) ([]*models.SEORedirect, error) {
	var redirects []*models.SEORedirect
	now := time.Now()

	err := r.db.WithContext(ctx).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("status = ?", models.RedirectStatusActive).
		Where("(active_from IS NULL OR active_from <= ?)", now).
		Where("(active_until IS NULL OR active_until >= ?)", now).
		Order("hit_count desc").
		Find(&redirects).Error

	return redirects, err
}

// FindMatchingRedirect finds a matching redirect for a given path
func (r *seoRedirectRepository) FindMatchingRedirect(ctx context.Context, websiteID, tenantID uint, path string) (*models.SEORedirect, error) {
	// Try exact match first
	if redirect, err := r.FindExactMatch(ctx, websiteID, tenantID, path); err == nil {
		return redirect, nil
	}

	// Try regex match
	if redirect, err := r.FindRegexMatch(ctx, websiteID, tenantID, path); err == nil {
		return redirect, nil
	}

	// Try wildcard match
	if redirect, err := r.FindWildcardMatch(ctx, websiteID, tenantID, path); err == nil {
		return redirect, nil
	}

	return nil, gorm.ErrRecordNotFound
}

// FindExactMatch finds an exact match redirect
func (r *seoRedirectRepository) FindExactMatch(ctx context.Context, websiteID, tenantID uint, path string) (*models.SEORedirect, error) {
	var redirect models.SEORedirect
	now := time.Now()

	err := r.db.WithContext(ctx).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("source_path = ?", path).
		Where("redirect_match = ?", models.RedirectMatchExact).
		Where("status = ?", models.RedirectStatusActive).
		Where("(active_from IS NULL OR active_from <= ?)", now).
		Where("(active_until IS NULL OR active_until >= ?)", now).
		Order("created_at desc").
		First(&redirect).Error

	if err != nil {
		return nil, err
	}
	return &redirect, nil
}

// FindRegexMatch finds a regex match redirect
func (r *seoRedirectRepository) FindRegexMatch(ctx context.Context, websiteID, tenantID uint, path string) (*models.SEORedirect, error) {
	var redirects []*models.SEORedirect
	now := time.Now()

	err := r.db.WithContext(ctx).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("redirect_match = ?", models.RedirectMatchRegex).
		Where("status = ?", models.RedirectStatusActive).
		Where("(active_from IS NULL OR active_from <= ?)", now).
		Where("(active_until IS NULL OR active_until >= ?)", now).
		Where("regex_pattern IS NOT NULL").
		Order("created_at desc").
		Find(&redirects).Error

	if err != nil {
		return nil, err
	}

	// TODO: Implement regex matching logic
	// For now, return first match (this should be replaced with proper regex matching)
	if len(redirects) > 0 {
		return redirects[0], nil
	}

	return nil, gorm.ErrRecordNotFound
}

// FindWildcardMatch finds a wildcard match redirect
func (r *seoRedirectRepository) FindWildcardMatch(ctx context.Context, websiteID, tenantID uint, path string) (*models.SEORedirect, error) {
	var redirects []*models.SEORedirect
	now := time.Now()

	err := r.db.WithContext(ctx).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("redirect_match = ?", models.RedirectMatchWildcard).
		Where("status = ?", models.RedirectStatusActive).
		Where("(active_from IS NULL OR active_from <= ?)", now).
		Where("(active_until IS NULL OR active_until >= ?)", now).
		Order("created_at desc").
		Find(&redirects).Error

	if err != nil {
		return nil, err
	}

	// TODO: Implement wildcard matching logic
	// For now, return first match (this should be replaced with proper wildcard matching)
	if len(redirects) > 0 {
		return redirects[0], nil
	}

	return nil, gorm.ErrRecordNotFound
}

// IncrementHit increments the hit count for a redirect
func (r *seoRedirectRepository) IncrementHit(ctx context.Context, id uint, ip, userAgent, referrer string) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&models.SEORedirect{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"hit_count":           gorm.Expr("hit_count + 1"),
			"last_hit_at":         now,
			"last_hit_ip":         ip,
			"last_hit_user_agent": userAgent,
			"last_hit_referrer":   referrer,
		}).Error
}

// GetTopRedirectsByHits retrieves top redirects by hit count
func (r *seoRedirectRepository) GetTopRedirectsByHits(ctx context.Context, websiteID, tenantID uint, limit int) ([]*models.SEORedirect, error) {
	var redirects []*models.SEORedirect
	err := r.db.WithContext(ctx).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("status = ?", models.RedirectStatusActive).
		Order("hit_count desc").
		Limit(limit).
		Find(&redirects).Error

	return redirects, err
}

// GetRecentHits retrieves redirects with recent hits
func (r *seoRedirectRepository) GetRecentHits(ctx context.Context, websiteID, tenantID uint, since time.Time) ([]*models.SEORedirect, error) {
	var redirects []*models.SEORedirect
	err := r.db.WithContext(ctx).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("last_hit_at >= ?", since).
		Order("last_hit_at desc").
		Find(&redirects).Error

	return redirects, err
}

// GetStats retrieves redirect statistics
func (r *seoRedirectRepository) GetStats(ctx context.Context, websiteID, tenantID uint) (*models.SEORedirectStatsResponse, error) {
	var stats models.SEORedirectStatsResponse
	var count int64

	// Count total redirects
	err := r.db.WithContext(ctx).
		Model(&models.SEORedirect{}).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("status != ?", models.RedirectStatusDeleted).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	stats.TotalRedirects = int(count)

	// Count active redirects
	err = r.db.WithContext(ctx).
		Model(&models.SEORedirect{}).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("status = ?", models.RedirectStatusActive).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	stats.ActiveRedirects = int(count)

	// Count inactive redirects
	err = r.db.WithContext(ctx).
		Model(&models.SEORedirect{}).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("status = ?", models.RedirectStatusInactive).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	stats.InactiveRedirects = int(count)

	// Count expired redirects
	err = r.db.WithContext(ctx).
		Model(&models.SEORedirect{}).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("status = ?", models.RedirectStatusExpired).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	stats.ExpiredRedirects = int(count)

	// Sum total hits
	err = r.db.WithContext(ctx).
		Model(&models.SEORedirect{}).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("status != ?", models.RedirectStatusDeleted).
		Select("COALESCE(SUM(hit_count), 0)").
		Scan(&stats.TotalHits).Error
	if err != nil {
		return nil, err
	}

	// Count 301 redirects
	err = r.db.WithContext(ctx).
		Model(&models.SEORedirect{}).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("redirect_type = ?", models.RedirectType301).
		Where("status != ?", models.RedirectStatusDeleted).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	stats.Top301Redirects = int(count)

	// Count 302 redirects
	err = r.db.WithContext(ctx).
		Model(&models.SEORedirect{}).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("redirect_type = ?", models.RedirectType302).
		Where("status != ?", models.RedirectStatusDeleted).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	stats.Top302Redirects = int(count)

	// Count recent hits (last 7 days)
	since := time.Now().AddDate(0, 0, -7)
	err = r.db.WithContext(ctx).
		Model(&models.SEORedirect{}).
		Where("website_id = ? AND tenant_id = ?", websiteID, tenantID).
		Where("last_hit_at >= ?", since).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	stats.RecentHits = int(count)

	return &stats, nil
}

// GetStatsByDateRange retrieves redirect statistics for a date range
func (r *seoRedirectRepository) GetStatsByDateRange(ctx context.Context, websiteID, tenantID uint, from, to time.Time) (*models.SEORedirectStatsResponse, error) {
	// This would implement date range statistics
	// For now, return basic stats
	return r.GetStats(ctx, websiteID, tenantID)
}

// BulkCreate creates multiple redirects
func (r *seoRedirectRepository) BulkCreate(ctx context.Context, redirects []*models.SEORedirect) error {
	return r.db.WithContext(ctx).Create(redirects).Error
}

// BulkUpdate updates multiple redirects
func (r *seoRedirectRepository) BulkUpdate(ctx context.Context, redirects []*models.SEORedirect) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, redirect := range redirects {
			if err := tx.Save(redirect).Error; err != nil {
				return err
			}
		}
		return nil
	})
}

// BulkDelete deletes multiple redirects
func (r *seoRedirectRepository) BulkDelete(ctx context.Context, ids []uint) error {
	return r.db.WithContext(ctx).Delete(&models.SEORedirect{}, ids).Error
}

// CleanupExpiredRedirects removes expired redirects
func (r *seoRedirectRepository) CleanupExpiredRedirects(ctx context.Context) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&models.SEORedirect{}).
		Where("active_until < ?", now).
		Where("status = ?", models.RedirectStatusActive).
		Update("status", models.RedirectStatusExpired).Error
}

// ArchiveOldRedirects archives old redirects
func (r *seoRedirectRepository) ArchiveOldRedirects(ctx context.Context, before time.Time) error {
	return r.db.WithContext(ctx).
		Model(&models.SEORedirect{}).
		Where("created_at < ?", before).
		Where("status = ?", models.RedirectStatusInactive).
		Update("status", models.RedirectStatusDeleted).Error
}
