package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// SEOSitemapHandler handles SEO sitemap HTTP requests
type SEOSitemapHandler struct {
	service services.SEOSitemapService
	logger  utils.Logger
}

// NewSEOSitemapHandler creates a new SEO sitemap handler
func NewSEOSitemapHandler(service services.SEOSitemapService, logger utils.Logger) *SEOSitemapHandler {
	return &SEOSitemapHandler{
		service: service,
		logger:  logger,
	}
}

// CreateSitemap creates a new SEO sitemap
func (h *SEOSitemapHandler) CreateSitemap(c *gin.Context) {
	var req models.SEOSitemapCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", "error", err)
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	sitemap, err := h.service.Create(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to create sitemap", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"success": true,
		"data":    sitemap,
	})
}

// GetSitemap retrieves an SEO sitemap by ID
func (h *SEOSitemapHandler) GetSitemap(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid sitemap ID"})
		return
	}

	sitemap, err := h.service.GetByID(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.Error("Failed to get sitemap", "error", err, "id", id)
		c.JSON(http.StatusNotFound, gin.H{"error": "Sitemap not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    sitemap,
	})
}

// UpdateSitemap updates an existing SEO sitemap
func (h *SEOSitemapHandler) UpdateSitemap(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid sitemap ID"})
		return
	}

	var req models.SEOSitemapUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Error("Failed to bind request", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	sitemap, err := h.service.Update(c.Request.Context(), uint(id), &req)
	if err != nil {
		h.logger.Error("Failed to update sitemap", "error", err, "id", id)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    sitemap,
	})
}

// DeleteSitemap deletes an SEO sitemap
func (h *SEOSitemapHandler) DeleteSitemap(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid sitemap ID"})
		return
	}

	err = h.service.Delete(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.Error("Failed to delete sitemap", "error", err, "id", id)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Sitemap deleted successfully",
	})
}

// ListSitemaps lists SEO sitemaps with pagination and filters
func (h *SEOSitemapHandler) ListSitemaps(c *gin.Context) {
	var req models.SEOSitemapListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		h.logger.Error("Failed to bind query parameters", "error", err)
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Set defaults if not provided
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}

	sitemaps, total, err := h.service.List(c.Request.Context(), &req)
	if err != nil {
		h.logger.Error("Failed to list sitemaps", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"sitemaps": sitemaps,
			"pagination": gin.H{
				"page":        req.Page,
				"page_size":   req.PageSize,
				"total":       total,
				"total_pages": (total + int64(req.PageSize) - 1) / int64(req.PageSize),
			},
		},
	})
}

// GenerateSitemap generates a sitemap for a specific type
func (h *SEOSitemapHandler) GenerateSitemap(c *gin.Context) {
	websiteID, err := strconv.ParseUint(c.Param("website_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid website ID"})
		return
	}

	tenantID, err := strconv.ParseUint(c.Param("tenant_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tenant ID"})
		return
	}

	sitemapType := models.SitemapType(c.Param("type"))
	if sitemapType == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Sitemap type is required"})
		return
	}

	sitemap, err := h.service.GenerateSitemap(c.Request.Context(), uint(websiteID), uint(tenantID), sitemapType)
	if err != nil {
		h.logger.Error("Failed to generate sitemap", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    sitemap,
	})
}

// GenerateAllSitemaps generates all types of sitemaps
func (h *SEOSitemapHandler) GenerateAllSitemaps(c *gin.Context) {
	websiteID, err := strconv.ParseUint(c.Param("website_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid website ID"})
		return
	}

	tenantID, err := strconv.ParseUint(c.Param("tenant_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tenant ID"})
		return
	}

	sitemaps, err := h.service.GenerateAllSitemaps(c.Request.Context(), uint(websiteID), uint(tenantID))
	if err != nil {
		h.logger.Error("Failed to generate all sitemaps", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"sitemaps": sitemaps,
			"count":    len(sitemaps),
		},
	})
}

// RefreshSitemap refreshes an existing sitemap
func (h *SEOSitemapHandler) RefreshSitemap(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid sitemap ID"})
		return
	}

	sitemap, err := h.service.RefreshSitemap(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.Error("Failed to refresh sitemap", "error", err, "id", id)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    sitemap,
	})
}

// GetSitemapXML returns the XML content of a sitemap
func (h *SEOSitemapHandler) GetSitemapXML(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid sitemap ID"})
		return
	}

	xmlContent, err := h.service.GetSitemapXML(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.Error("Failed to get sitemap XML", "error", err, "id", id)
		c.JSON(http.StatusNotFound, gin.H{"error": "Sitemap not found"})
		return
	}

	c.Header("Content-Type", "application/xml")
	c.String(http.StatusOK, xmlContent)
}

// GetSitemapIndex returns the sitemap index XML
func (h *SEOSitemapHandler) GetSitemapIndex(c *gin.Context) {
	websiteID, err := strconv.ParseUint(c.Param("website_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid website ID"})
		return
	}

	tenantID, err := strconv.ParseUint(c.Param("tenant_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tenant ID"})
		return
	}

	xmlContent, err := h.service.GetSitemapIndexXML(c.Request.Context(), uint(websiteID), uint(tenantID))
	if err != nil {
		h.logger.Error("Failed to get sitemap index XML", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.Header("Content-Type", "application/xml")
	c.String(http.StatusOK, xmlContent)
}

// ServeSitemap serves a sitemap by filename
func (h *SEOSitemapHandler) ServeSitemap(c *gin.Context) {
	websiteID, err := strconv.ParseUint(c.Param("website_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid website ID"})
		return
	}

	tenantID, err := strconv.ParseUint(c.Param("tenant_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tenant ID"})
		return
	}

	filename := c.Param("filename")
	if filename == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Filename is required"})
		return
	}

	xmlContent, err := h.service.ServeSitemap(c.Request.Context(), uint(websiteID), uint(tenantID), filename)
	if err != nil {
		h.logger.Error("Failed to serve sitemap", "error", err)
		c.JSON(http.StatusNotFound, gin.H{"error": "Sitemap not found"})
		return
	}

	c.Header("Content-Type", "application/xml")
	c.String(http.StatusOK, xmlContent)
}

// GetSitemapStats retrieves sitemap statistics
func (h *SEOSitemapHandler) GetSitemapStats(c *gin.Context) {
	websiteID, err := strconv.ParseUint(c.Param("website_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid website ID"})
		return
	}

	tenantID, err := strconv.ParseUint(c.Param("tenant_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tenant ID"})
		return
	}

	stats, err := h.service.GetStats(c.Request.Context(), uint(websiteID), uint(tenantID))
	if err != nil {
		h.logger.Error("Failed to get sitemap stats", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// ValidateSitemap validates a sitemap
func (h *SEOSitemapHandler) ValidateSitemap(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid sitemap ID"})
		return
	}

	err = h.service.ValidateSitemap(c.Request.Context(), uint(id))
	if err != nil {
		h.logger.Error("Sitemap validation failed", "error", err, "id", id)
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Sitemap is valid",
	})
}

// CleanupOldSitemaps removes old sitemaps
func (h *SEOSitemapHandler) CleanupOldSitemaps(c *gin.Context) {
	days := 30
	if daysStr := c.Query("days"); daysStr != "" {
		if parsedDays, err := strconv.Atoi(daysStr); err == nil && parsedDays > 0 {
			days = parsedDays
		}
	}

	count, err := h.service.CleanupOldSitemaps(c.Request.Context(), days)
	if err != nil {
		h.logger.Error("Failed to cleanup old sitemaps", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Old sitemaps cleaned up successfully",
		"count":   count,
	})
}

// CompressSitemaps compresses large sitemaps
func (h *SEOSitemapHandler) CompressSitemaps(c *gin.Context) {
	websiteID, err := strconv.ParseUint(c.Param("website_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid website ID"})
		return
	}

	tenantID, err := strconv.ParseUint(c.Param("tenant_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid tenant ID"})
		return
	}

	err = h.service.CompressSitemaps(c.Request.Context(), uint(websiteID), uint(tenantID))
	if err != nil {
		h.logger.Error("Failed to compress sitemaps", "error", err)
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Sitemaps compressed successfully",
	})
}
