package seo

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/seo/handlers"
)

// RegisterRoutes registers all SEO module routes
func RegisterRoutes(router *gin.RouterGroup, redirectHandler *handlers.SEORedirectHandler, sitemapHandler *handlers.SEOSitemapHandler, metaHandler *handlers.SEOMetaHandler) {
	// SEO API group
	seoGroup := router.Group("/seo")
	{
		// Redirect routes
		redirectGroup := seoGroup.Group("/redirects")
		{
			// Basic CRUD operations
			redirectGroup.POST("", redirectHandler.CreateRedirect)
			redirectGroup.GET("/:id", redirectHandler.GetRedirect)
			redirectGroup.PUT("/:id", redirectHandler.UpdateRedirect)
			redirectGroup.DELETE("/:id", redirectHandler.DeleteRedirect)
			redirectGroup.GET("", redirectHandler.ListRedirects)

			// Redirect processing and testing
			redirectGroup.POST("/process/:website_id/:tenant_id", redirectHandler.ProcessRedirect)
			redirectGroup.GET("/:id/test", redirectHandler.TestRedirect)

			// Analytics and statistics
			redirectGroup.GET("/stats/:website_id/:tenant_id", redirectHandler.GetRedirectStats)
			redirectGroup.GET("/top/:website_id/:tenant_id", redirectHandler.GetTopRedirects)

			// Bulk operations
			redirectGroup.POST("/bulk", redirectHandler.BulkCreateRedirects)
			redirectGroup.DELETE("/bulk", redirectHandler.BulkDeleteRedirects)

			// Maintenance operations
			redirectGroup.POST("/cleanup", redirectHandler.CleanupExpiredRedirects)
		}

		// Sitemap routes
		sitemapGroup := seoGroup.Group("/sitemaps")
		{
			// Basic CRUD operations
			sitemapGroup.POST("", sitemapHandler.CreateSitemap)
			sitemapGroup.GET("/:id", sitemapHandler.GetSitemap)
			sitemapGroup.PUT("/:id", sitemapHandler.UpdateSitemap)
			sitemapGroup.DELETE("/:id", sitemapHandler.DeleteSitemap)
			sitemapGroup.GET("", sitemapHandler.ListSitemaps)

			// Sitemap generation
			sitemapGroup.POST("/generate/:website_id/:tenant_id/:type", sitemapHandler.GenerateSitemap)
			sitemapGroup.POST("/generate-all/:website_id/:tenant_id", sitemapHandler.GenerateAllSitemaps)
			sitemapGroup.POST("/:id/refresh", sitemapHandler.RefreshSitemap)

			// XML serving
			sitemapGroup.GET("/:id/xml", sitemapHandler.GetSitemapXML)
			sitemapGroup.GET("/index/:website_id/:tenant_id", sitemapHandler.GetSitemapIndex)
			sitemapGroup.GET("/serve/:website_id/:tenant_id/:filename", sitemapHandler.ServeSitemap)

			// Statistics and validation
			sitemapGroup.GET("/stats/:website_id/:tenant_id", sitemapHandler.GetSitemapStats)
			sitemapGroup.POST("/:id/validate", sitemapHandler.ValidateSitemap)

			// Maintenance operations
			sitemapGroup.POST("/cleanup", sitemapHandler.CleanupOldSitemaps)
			sitemapGroup.POST("/compress/:website_id/:tenant_id", sitemapHandler.CompressSitemaps)
		}

		// Meta routes
		metaGroup := seoGroup.Group("/meta")
		{
			// Basic CRUD operations
			metaGroup.POST("", metaHandler.CreateMeta)
			metaGroup.GET("/:id", metaHandler.GetMeta)
			metaGroup.PUT("/:id", metaHandler.UpdateMeta)
			metaGroup.DELETE("/:id", metaHandler.DeleteMeta)
			metaGroup.GET("", metaHandler.ListMeta)

			// Query by page
			metaGroup.GET("/by-page", metaHandler.GetMetaByPage)

			// Bulk operations
			metaGroup.POST("/bulk", metaHandler.BulkCreateMeta)

			// Analysis and validation
			metaGroup.POST("/:id/analyze", metaHandler.AnalyzeMeta)
			metaGroup.POST("/:id/validate", metaHandler.ValidateMeta)

			// Meta tag generation
			metaGroup.GET("/:id/tags", metaHandler.GenerateMetaTags)
		}
	}

	// Public sitemap routes (for search engines)
	publicGroup := router.Group("/public")
	{
		// Direct sitemap access for search engines
		publicGroup.GET("/sitemap/:website_id/:tenant_id/sitemap.xml", sitemapHandler.GetSitemapIndex)
		publicGroup.GET("/sitemap/:website_id/:tenant_id/:filename", sitemapHandler.ServeSitemap)
	}
}
