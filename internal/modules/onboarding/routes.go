package onboarding

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/config"
	authMysql "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories/mysql"
	authServices "github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/handlers"
	onboardingrepos "github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/repositories"
	onboardingmysql "github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/services"
	tenantRepos "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/repositories"
	tenantServices "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/services"
	userRepos "github.com/tranthanhloi/wn-api-v3/internal/modules/user/repositories/mysql"
	userServices "github.com/tranthanhloi/wn-api-v3/internal/modules/user/services"
	httpmiddleware "github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
	"gorm.io/gorm"
)

// RegisterRoutes registers all onboarding module routes
func RegisterRoutes(router *gin.RouterGroup, db *gorm.DB, v validator.Validator, logger utils.Logger) {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		logger.WithError(err).Error("Failed to load config")
		return
	}

	// Initialize repositories
	progressRepo := onboardingmysql.NewOnboardingProgressRepository(db, logger)
	tenantRepo := tenantRepos.NewMySQLTenantRepository(db)
	planRepo := tenantRepos.NewMySQLTenantPlanRepository(db)
	tenantMembershipRepo := userRepos.NewTenantMembershipRepository(db, logger)

	// Initialize JWT service dependencies
	tokenBlacklistRepo := authMysql.NewTokenBlacklistRepository(db)
	tokenBlacklistService := authServices.NewTokenBlacklistService(tokenBlacklistRepo, nil)

	// Create JWT service
	jwtService, err := authServices.NewJWTService(cfg, tokenBlacklistService, tenantMembershipRepo)
	if err != nil {
		logger.WithError(err).Error("Failed to create JWT service")
		return
	}

	// Initialize services
	onboardingService := services.NewOnboardingService(
		progressRepo,
		logger,
		&services.OnboardingConfig{
			AutoStart:           true,
			RequiredSteps:       []string{"create_tenant", "create_website"},
			OptionalSteps:       []string{},
			DefaultTimeout:      7 * 24 * time.Hour, // 7 days
			AllowSkip:           false,
			EnableNotifications: true,
		},
	)
	tenantService := tenantServices.NewTenantService(tenantRepo, planRepo)
	tenantMembershipService := userServices.NewTenantMembershipService(tenantMembershipRepo, logger)

	// Initialize handlers
	onboardingHandler := handlers.NewOnboardingHandler(
		onboardingService,
		tenantService,
		tenantMembershipService,
		v,
		logger,
	)

	// Create onboarding routes group
	onboardingRoutes := router.Group("/onboarding")
	onboardingRoutes.Use(httpmiddleware.EnhancedCORSMiddleware())
	onboardingRoutes.Use(httpmiddleware.SecurityHeadersMiddleware())
	onboardingRoutes.Use(httpmiddleware.AuthRateLimitingMiddleware(logger))
	onboardingRoutes.Use(httpmiddleware.ValidateJSONContentType())
	onboardingRoutes.Use(httpmiddleware.TenantContextMiddleware())
	onboardingRoutes.Use(httpmiddleware.RequestLoggingMiddleware(logger))

	// Public onboarding endpoints (require authentication but not specific roles)
	publicRoutes := onboardingRoutes.Group("")
	publicRoutes.Use(httpmiddleware.JWTAuthMiddleware(jwtService))
	publicRoutes.Use(httpmiddleware.RequireAuthentication())
	{
		// Essential onboarding endpoints
		publicRoutes.PUT("/step", onboardingHandler.UpdateStep)
		publicRoutes.POST("/complete", onboardingHandler.CompleteOnboarding)
		publicRoutes.GET("/step", onboardingHandler.GetCurrentStep)

		// Organization management
		publicRoutes.POST("/create-organization", onboardingHandler.CreateOrganization)
		publicRoutes.GET("/my/organization", onboardingHandler.GetMyOrganization)
		publicRoutes.POST("/my/organization", onboardingHandler.CreateMyOrganization)
		publicRoutes.GET("/my/website", onboardingHandler.GetMyWebsite)
		publicRoutes.POST("/my/website", onboardingHandler.CreateMyWebsite)
	}

	// Admin onboarding endpoints (require admin role)
	adminRoutes := onboardingRoutes.Group("/admin")
	adminRoutes.Use(httpmiddleware.RequireAdminRole())
	{
		// Administrative endpoints
		adminRoutes.GET("/progress", onboardingHandler.ListProgress)
		adminRoutes.GET("/stats", onboardingHandler.GetStats)
	}

	// Health check for onboarding module
	onboardingRoutes.GET("/health", func(c *gin.Context) {
		httpresponse.Success(c.Writer, gin.H{
			"module":  "onboarding",
			"status":  "healthy",
			"version": "1.0.0",
			"checks": gin.H{
				"database":           db != nil,
				"progress_repo":      progressRepo != nil,
				"onboarding_service": onboardingService != nil,
			},
		})
	})
}

// GetOnboardingService creates and returns an onboarding service instance
// This function can be used by other modules that need to interact with onboarding
func GetOnboardingService(db *gorm.DB, logger utils.Logger) services.OnboardingService {
	progressRepo := onboardingmysql.NewOnboardingProgressRepository(db, logger)

	return services.NewOnboardingService(
		progressRepo,
		logger,
		&services.OnboardingConfig{
			AutoStart:           true,
			RequiredSteps:       []string{"create_tenant", "create_website"},
			OptionalSteps:       []string{},
			DefaultTimeout:      7 * 24 * time.Hour, // 7 days
			AllowSkip:           false,
			EnableNotifications: true,
		},
	)
}

// GetOnboardingRepository creates and returns an onboarding progress repository instance
// This function can be used by other modules that need direct repository access
func GetOnboardingRepository(db *gorm.DB, logger utils.Logger) onboardingrepos.OnboardingProgressRepository {
	return onboardingmysql.NewOnboardingProgressRepository(db, logger)
}
