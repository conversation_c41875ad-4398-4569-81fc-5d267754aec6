package services

import (
	"context"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/onboarding/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// OnboardingService handles onboarding business logic
type OnboardingService interface {
	// Essential progress management
	UpdateStep(ctx context.Context, req *dto.UpdateOnboardingStepRequest) (*dto.UpdateOnboardingStepResponse, error)
	CompleteOnboarding(ctx context.Context, req *dto.CompleteOnboardingRequest) (*dto.CompleteOnboardingResponse, error)
	GetCurrentStep(ctx context.Context, userID uint) (string, error)
	GetCurrentStepWithStatus(ctx context.Context, userID uint) (string, string, error)

	// Auto-creation for email verification
	CreateOnboardingForUser(ctx context.Context, userID uint) error

	// Admin functionality
	ListProgress(ctx context.Context, req *dto.ListOnboardingProgressRequest) (*dto.ListOnboardingProgressResponse, error)
	GetStats(ctx context.Context) (*dto.OnboardingStatsResponse, error)

	// Internal utilities
	IsOnboardingCompleted(ctx context.Context, userID uint) (bool, error)
}

type onboardingService struct {
	progressRepo repositories.OnboardingProgressRepository
	logger       utils.Logger
	config       *OnboardingConfig
}

// OnboardingConfig holds onboarding configuration
type OnboardingConfig struct {
	AutoStart           bool          `json:"auto_start"`
	RequiredSteps       []string      `json:"required_steps"`
	OptionalSteps       []string      `json:"optional_steps"`
	DefaultTimeout      time.Duration `json:"default_timeout"`
	AllowSkip           bool          `json:"allow_skip"`
	EnableNotifications bool          `json:"enable_notifications"`
}

// NewOnboardingService creates a new onboarding service
func NewOnboardingService(
	progressRepo repositories.OnboardingProgressRepository,
	logger utils.Logger,
	config *OnboardingConfig,
) OnboardingService {
	if config == nil {
		config = &OnboardingConfig{
			AutoStart:           true,
			RequiredSteps:       []string{string(models.OnboardingStepCreateTenant), string(models.OnboardingStepCreateWebsite)},
			OptionalSteps:       []string{},
			DefaultTimeout:      7 * 24 * time.Hour, // 7 days
			AllowSkip:           false,
			EnableNotifications: true,
		}
	}

	return &onboardingService{
		progressRepo: progressRepo,
		logger:       logger,
		config:       config,
	}
}

// CreateOnboardingForUser creates an onboarding record for a user (called during email verification)
func (s *onboardingService) CreateOnboardingForUser(ctx context.Context, userID uint) error {
	// Check if onboarding already exists
	exists, err := s.progressRepo.Exists(ctx, userID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to check if onboarding exists")
		return fmt.Errorf("failed to check onboarding existence: %w", err)
	}

	if exists {
		s.logger.WithFields(map[string]interface{}{
			"user_id": userID,
		}).Info("Onboarding record already exists for user")
		return nil // Already exists, no need to create
	}

	// Create new onboarding progress record
	progress := &models.OnboardingProgress{
		UserID: userID,
		Status: models.OnboardingStatusPending,
		Step:   models.OnboardingStepCreateTenant,
		Metadata: models.OnboardingMetadata{
			"created_via":     "email_verification",
			"required_steps":  s.config.RequiredSteps,
			"optional_steps":  s.config.OptionalSteps,
			"steps_completed": []string{},
		},
	}

	if err := s.progressRepo.Create(ctx, progress); err != nil {
		s.logger.WithError(err).Error("Failed to create onboarding progress")
		return fmt.Errorf("failed to create onboarding progress: %w", err)
	}

	s.logger.WithFields(map[string]interface{}{
		"user_id": userID,
		"status":  progress.Status,
		"step":    progress.Step,
	}).Info("Onboarding record created for user")

	return nil
}

// UpdateStep updates the current step of onboarding
func (s *onboardingService) UpdateStep(ctx context.Context, req *dto.UpdateOnboardingStepRequest) (*dto.UpdateOnboardingStepResponse, error) {
	// Check if onboarding progress exists
	exists, err := s.progressRepo.Exists(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to check onboarding existence: %w", err)
	}

	// If no onboarding progress exists, create it first
	if !exists {
		s.logger.WithFields(map[string]interface{}{
			"user_id": req.UserID,
		}).Info("Creating onboarding progress before updating step")
		
		if err := s.CreateOnboardingForUser(ctx, req.UserID); err != nil {
			return nil, fmt.Errorf("failed to create onboarding progress: %w", err)
		}
	}

	progress, err := s.progressRepo.GetByUserID(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get onboarding progress: %w", err)
	}

	if progress.IsCompleted() {
		return nil, fmt.Errorf("onboarding already completed")
	}

	// Automatically start onboarding if it's pending
	if progress.IsPending() {
		progress.MarkAsStarted()
	}

	// Update step
	switch req.Step {
	case string(models.OnboardingStepCreateTenant):
		progress.Step = models.OnboardingStepCreateTenant
	case string(models.OnboardingStepCreateWebsite):
		progress.Step = models.OnboardingStepCreateWebsite
	case string(models.OnboardingStepCompleted):
		progress.MarkAsCompleted()
	default:
		return nil, fmt.Errorf("invalid step: %s", req.Step)
	}

	// Update metadata if provided
	if req.Metadata != nil {
		for key, value := range req.Metadata {
			progress.SetMetadata(key, value)
		}
	}

	// Add step completion tracking
	if req.Step != "completed" {
		stepsCompleted, _ := progress.GetMetadata("steps_completed")
		if stepsCompleted == nil {
			stepsCompleted = []string{}
		}
		if steps, ok := stepsCompleted.([]interface{}); ok {
			// Convert to string slice and add current step if not already present
			stepStrings := make([]string, 0, len(steps)+1)
			for _, step := range steps {
				if stepStr, ok := step.(string); ok {
					stepStrings = append(stepStrings, stepStr)
				}
			}
			// Add current step if not already completed
			found := false
			for _, completedStep := range stepStrings {
				if completedStep == req.Step {
					found = true
					break
				}
			}
			if !found {
				stepStrings = append(stepStrings, req.Step)
			}
			progress.SetMetadata("steps_completed", stepStrings)
		}
	}

	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return nil, fmt.Errorf("failed to update onboarding step: %w", err)
	}

	return &dto.UpdateOnboardingStepResponse{
		ID:          progress.ID,
		UserID:      progress.UserID,
		Status:      progress.GetStatus(),
		Step:        progress.GetCurrentStep(),
		StartedAt:   progress.StartedAt,
		CompletedAt: progress.CompletedAt,
		Metadata:    progress.Metadata,
		UpdatedAt:   progress.UpdatedAt,
		Message:     fmt.Sprintf("Onboarding step updated to %s", req.Step),
	}, nil
}

// CompleteOnboarding marks the onboarding as completed
func (s *onboardingService) CompleteOnboarding(ctx context.Context, req *dto.CompleteOnboardingRequest) (*dto.CompleteOnboardingResponse, error) {
	// Check if onboarding progress exists
	exists, err := s.progressRepo.Exists(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to check onboarding existence: %w", err)
	}

	// If no onboarding progress exists, create it first
	if !exists {
		s.logger.WithFields(map[string]interface{}{
			"user_id": req.UserID,
		}).Info("Creating onboarding progress before completing")
		
		if err := s.CreateOnboardingForUser(ctx, req.UserID); err != nil {
			return nil, fmt.Errorf("failed to create onboarding progress: %w", err)
		}
	}

	progress, err := s.progressRepo.GetByUserID(ctx, req.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get onboarding progress: %w", err)
	}

	if progress.IsCompleted() {
		return nil, fmt.Errorf("onboarding already completed")
	}

	// Mark as completed
	progress.MarkAsCompleted()

	// Update metadata if provided
	if req.Metadata != nil {
		for key, value := range req.Metadata {
			progress.SetMetadata(key, value)
		}
	}

	// Add completion metadata
	progress.SetMetadata("completed_at", time.Now().Format(time.RFC3339))
	progress.SetMetadata("completion_method", "manual")

	if err := s.progressRepo.Update(ctx, progress); err != nil {
		return nil, fmt.Errorf("failed to complete onboarding: %w", err)
	}

	return &dto.CompleteOnboardingResponse{
		ID:          progress.ID,
		UserID:      progress.UserID,
		Status:      progress.GetStatus(),
		Step:        progress.GetCurrentStep(),
		StartedAt:   progress.StartedAt,
		CompletedAt: *progress.CompletedAt,
		Metadata:    progress.Metadata,
		Message:     "Onboarding completed successfully",
	}, nil
}

// ListProgress lists onboarding progress with filters
func (s *onboardingService) ListProgress(ctx context.Context, req *dto.ListOnboardingProgressRequest) (*dto.ListOnboardingProgressResponse, error) {
	filters := &repositories.OnboardingProgressFilters{
		Limit:  req.Limit,
		Offset: req.Offset,
	}

	if req.Status != "" {
		status := models.OnboardingStatus(req.Status)
		filters.Status = &status
	}

	if req.Step != "" {
		step := models.OnboardingStep(req.Step)
		filters.Step = &step
	}

	// Set default limit if not provided
	if filters.Limit == 0 {
		filters.Limit = 10
	}

	progressList, err := s.progressRepo.List(ctx, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to list onboarding progress: %w", err)
	}

	total, err := s.progressRepo.Count(ctx, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to count onboarding progress: %w", err)
	}

	// Convert to DTOs
	progressDTOs := make([]dto.GetOnboardingProgressResponse, len(progressList))
	for i, progress := range progressList {
		progressDTOs[i] = dto.GetOnboardingProgressResponse{
			ID:          progress.ID,
			UserID:      progress.UserID,
			Status:      progress.GetStatus(),
			Step:        progress.GetCurrentStep(),
			StartedAt:   progress.StartedAt,
			CompletedAt: progress.CompletedAt,
			Metadata:    progress.Metadata,
			CreatedAt:   progress.CreatedAt,
			UpdatedAt:   progress.UpdatedAt,
		}
	}

	return &dto.ListOnboardingProgressResponse{
		Progress: progressDTOs,
		Total:    total,
		Limit:    req.Limit,
		Offset:   req.Offset,
	}, nil
}

// GetStats retrieves onboarding statistics
func (s *onboardingService) GetStats(ctx context.Context) (*dto.OnboardingStatsResponse, error) {
	stats, err := s.progressRepo.GetStats(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get onboarding stats: %w", err)
	}

	return &dto.OnboardingStatsResponse{
		TotalUsers:      stats.TotalUsers,
		PendingUsers:    stats.PendingUsers,
		ProcessingUsers: stats.ProcessingUsers,
		CompletedUsers:  stats.CompletedUsers,
		CompletionRate:  stats.CompletionRate,
	}, nil
}

// IsOnboardingCompleted checks if onboarding is completed for a user
func (s *onboardingService) IsOnboardingCompleted(ctx context.Context, userID uint) (bool, error) {
	exists, err := s.progressRepo.Exists(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to check onboarding existence: %w", err)
	}

	if !exists {
		return false, nil // No onboarding record means not completed
	}

	progress, err := s.progressRepo.GetByUserID(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to get onboarding progress: %w", err)
	}

	return progress.IsCompleted(), nil
}

// GetCurrentStep retrieves the current step for a user
func (s *onboardingService) GetCurrentStep(ctx context.Context, userID uint) (string, error) {
	exists, err := s.progressRepo.Exists(ctx, userID)
	if err != nil {
		return "", fmt.Errorf("failed to check onboarding existence: %w", err)
	}

	if !exists {
		return string(models.OnboardingStepCreateTenant), nil // Default first step
	}

	progress, err := s.progressRepo.GetByUserID(ctx, userID)
	if err != nil {
		return "", fmt.Errorf("failed to get onboarding progress: %w", err)
	}

	return progress.GetCurrentStep(), nil
}

// GetCurrentStepWithStatus retrieves the current step and status for a user
func (s *onboardingService) GetCurrentStepWithStatus(ctx context.Context, userID uint) (string, string, error) {
	exists, err := s.progressRepo.Exists(ctx, userID)
	if err != nil {
		return "", "", fmt.Errorf("failed to check onboarding existence: %w", err)
	}

	if !exists {
		return string(models.OnboardingStepCreateTenant), string(models.OnboardingStatusPending), nil // Default first step and status
	}

	progress, err := s.progressRepo.GetByUserID(ctx, userID)
	if err != nil {
		return "", "", fmt.Errorf("failed to get onboarding progress: %w", err)
	}

	return progress.GetCurrentStep(), progress.GetStatus(), nil
}
