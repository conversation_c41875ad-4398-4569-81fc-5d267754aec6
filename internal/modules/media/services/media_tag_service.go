package services

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/context"
)

// MediaTagService handles media tagging operations
type MediaTagService struct {
	tagRepo     repositories.MediaTagRepository
	fileTagRepo repositories.MediaFileTagRepository
	fileRepo    repositories.MediaFileRepository
	logger      *logrus.Logger
}

// NewMediaTagService creates a new media tag service
func NewMediaTagService(
	tagRepo repositories.MediaTagRepository,
	fileTagRepo repositories.MediaFileTagRepository,
	fileRepo repositories.MediaFileRepository,
	logger *logrus.Logger,
) *MediaTagService {
	return &MediaTagService{
		tagRepo:     tagRepo,
		fileTagRepo: fileTagRepo,
		fileRepo:    fileRepo,
		logger:      logger,
	}
}

// CreateTag creates a new media tag
func (s *MediaTagService) CreateTag(ctx context.Context, req *models.CreateTagRequest) (*models.MediaTag, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Validate request
	if req.Name == "" {
		return nil, fmt.Errorf("tag name is required")
	}

	// Normalize tag name
	normalizedName := strings.ToLower(strings.TrimSpace(req.Name))

	// Check if tag already exists
	existing, err := s.tagRepo.GetByName(ctx, tenantCtx.TenantID, normalizedName)
	if err == nil && existing != nil {
		return existing, nil // Return existing tag instead of error
	}

	// Create new tag
	tag := &models.MediaTag{
		TenantID:    tenantCtx.TenantID,
		Name:        normalizedName,
		DisplayName: req.DisplayName,
		Description: req.Description,
		Color:       req.Color,
		UsageCount:  0,
		Status:      "active",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Set display name if not provided
	if tag.DisplayName == "" {
		tag.DisplayName = req.Name
	}

	err = s.tagRepo.Create(ctx, tag)
	if err != nil {
		return nil, fmt.Errorf("failed to create tag: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"tag_id":    tag.ID,
		"name":      tag.Name,
	}).Info("Media tag created successfully")

	return tag, nil
}

// GetTag retrieves a tag by ID
func (s *MediaTagService) GetTag(ctx context.Context, id uint) (*models.MediaTag, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	tag, err := s.tagRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get tag: %w", err)
	}

	return tag, nil
}

// GetTagByName retrieves a tag by name
func (s *MediaTagService) GetTagByName(ctx context.Context, name string) (*models.MediaTag, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	normalizedName := strings.ToLower(strings.TrimSpace(name))
	tag, err := s.tagRepo.GetByName(ctx, tenantCtx.TenantID, normalizedName)
	if err != nil {
		return nil, fmt.Errorf("failed to get tag: %w", err)
	}

	return tag, nil
}

// UpdateTag updates an existing tag
func (s *MediaTagService) UpdateTag(ctx context.Context, id uint, req *models.UpdateTagRequest) (*models.MediaTag, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	tag, err := s.tagRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get tag: %w", err)
	}

	// Update fields
	if req.Name != nil {
		normalizedName := strings.ToLower(strings.TrimSpace(*req.Name))

		// Check if new name already exists
		existing, err := s.tagRepo.GetByName(ctx, tenantCtx.TenantID, normalizedName)
		if err == nil && existing != nil && existing.ID != id {
			return nil, fmt.Errorf("tag with name '%s' already exists", normalizedName)
		}

		tag.Name = normalizedName
	}

	if req.DisplayName != nil {
		tag.DisplayName = *req.DisplayName
	}

	if req.Description != nil {
		tag.Description = req.Description
	}

	if req.Color != nil {
		tag.Color = req.Color
	}

	if req.Status != nil {
		tag.Status = *req.Status
	}

	tag.UpdatedAt = time.Now()

	err = s.tagRepo.Update(ctx, tag)
	if err != nil {
		return nil, fmt.Errorf("failed to update tag: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"tag_id":    id,
	}).Info("Media tag updated successfully")

	return tag, nil
}

// DeleteTag deletes a tag
func (s *MediaTagService) DeleteTag(ctx context.Context, id uint) error {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	tag, err := s.tagRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return fmt.Errorf("failed to get tag: %w", err)
	}

	// Remove all file associations
	err = s.fileTagRepo.DeleteByTagID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return fmt.Errorf("failed to remove tag associations: %w", err)
	}

	// Soft delete tag
	tag.Status = "deleted"
	tag.UpdatedAt = time.Now()

	err = s.tagRepo.Update(ctx, tag)
	if err != nil {
		return fmt.Errorf("failed to delete tag: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"tag_id":    id,
	}).Info("Media tag deleted successfully")

	return nil
}

// ListTags lists tags with filtering and pagination
func (s *MediaTagService) ListTags(ctx context.Context, filter *models.TagFilter, limit, offset int) ([]*models.MediaTag, int, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, 0, fmt.Errorf("tenant context is required")
	}

	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	tags, total, err := s.tagRepo.List(ctx, tenantCtx.TenantID, filter, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list tags: %w", err)
	}

	return tags, total, nil
}

// SearchTags searches tags by name or description
func (s *MediaTagService) SearchTags(ctx context.Context, query string, limit, offset int) ([]*models.MediaTag, int, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, 0, fmt.Errorf("tenant context is required")
	}

	if query == "" {
		return s.ListTags(ctx, nil, limit, offset)
	}

	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	tags, total, err := s.tagRepo.Search(ctx, tenantCtx.TenantID, query, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to search tags: %w", err)
	}

	return tags, total, nil
}

// GetPopularTags gets the most popular tags
func (s *MediaTagService) GetPopularTags(ctx context.Context, limit int) ([]*models.MediaTag, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	if limit <= 0 {
		limit = 10
	}

	tags, err := s.tagRepo.GetPopular(ctx, tenantCtx.TenantID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get popular tags: %w", err)
	}

	return tags, nil
}

// AddTagToFile adds a tag to a media file
func (s *MediaTagService) AddTagToFile(ctx context.Context, fileID uint, tagID uint) error {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	// Validate file exists
	_, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return fmt.Errorf("media file not found: %w", err)
	}

	// Validate tag exists
	tag, err := s.tagRepo.GetByID(ctx, tenantCtx.TenantID, tagID)
	if err != nil {
		return fmt.Errorf("tag not found: %w", err)
	}

	// Check if association already exists
	existing, err := s.fileTagRepo.GetByFileAndTag(ctx, tenantCtx.TenantID, fileID, tagID)
	if err == nil && existing != nil {
		return nil // Already exists, no error
	}

	// Create file-tag association
	fileTag := &models.MediaFileTag{
		TenantID:  tenantCtx.TenantID,
		FileID:    fileID,
		TagID:     tagID,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	err = s.fileTagRepo.Create(ctx, fileTag)
	if err != nil {
		return fmt.Errorf("failed to add tag to file: %w", err)
	}

	// Update tag usage count
	tag.UsageCount++
	tag.UpdatedAt = time.Now()
	err = s.tagRepo.Update(ctx, tag)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to update tag usage count")
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"file_id":   fileID,
		"tag_id":    tagID,
	}).Info("Tag added to file successfully")

	return nil
}

// RemoveTagFromFile removes a tag from a media file
func (s *MediaTagService) RemoveTagFromFile(ctx context.Context, fileID uint, tagID uint) error {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	// Remove file-tag association
	err := s.fileTagRepo.DeleteByFileAndTag(ctx, tenantCtx.TenantID, fileID, tagID)
	if err != nil {
		return fmt.Errorf("failed to remove tag from file: %w", err)
	}

	// Update tag usage count
	tag, err := s.tagRepo.GetByID(ctx, tenantCtx.TenantID, tagID)
	if err == nil {
		if tag.UsageCount > 0 {
			tag.UsageCount--
		}
		tag.UpdatedAt = time.Now()
		err = s.tagRepo.Update(ctx, tag)
		if err != nil {
			s.logger.WithError(err).Warn("Failed to update tag usage count")
		}
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"file_id":   fileID,
		"tag_id":    tagID,
	}).Info("Tag removed from file successfully")

	return nil
}

// AddTagsToFile adds multiple tags to a media file
func (s *MediaTagService) AddTagsToFile(ctx context.Context, fileID uint, tagNames []string) error {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	for _, tagName := range tagNames {
		// Get or create tag
		tag, err := s.GetOrCreateTag(ctx, tagName)
		if err != nil {
			s.logger.WithError(err).WithField("tag_name", tagName).Error("Failed to get or create tag")
			continue
		}

		// Add tag to file
		err = s.AddTagToFile(ctx, fileID, tag.ID)
		if err != nil {
			s.logger.WithError(err).WithFields(logrus.Fields{
				"file_id": fileID,
				"tag_id":  tag.ID,
			}).Error("Failed to add tag to file")
		}
	}

	return nil
}

// GetTagsForFile retrieves all tags for a media file
func (s *MediaTagService) GetTagsForFile(ctx context.Context, fileID uint) ([]*models.MediaTag, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	tags, err := s.tagRepo.GetByFileID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get tags for file: %w", err)
	}

	return tags, nil
}

// GetFilesForTag retrieves all files with a specific tag
func (s *MediaTagService) GetFilesForTag(ctx context.Context, tagID uint, limit, offset int) ([]*models.MediaFile, int, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, 0, fmt.Errorf("tenant context is required")
	}

	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	files, total, err := s.fileRepo.GetByTagID(ctx, tenantCtx.TenantID, tagID, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get files for tag: %w", err)
	}

	return files, total, nil
}

// GetOrCreateTag gets an existing tag or creates a new one
func (s *MediaTagService) GetOrCreateTag(ctx context.Context, tagName string) (*models.MediaTag, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	normalizedName := strings.ToLower(strings.TrimSpace(tagName))

	// Try to get existing tag
	tag, err := s.tagRepo.GetByName(ctx, tenantCtx.TenantID, normalizedName)
	if err == nil {
		return tag, nil
	}

	// Create new tag
	req := &models.CreateTagRequest{
		Name:        normalizedName,
		DisplayName: tagName,
	}

	return s.CreateTag(ctx, req)
}

// BulkCreateTags creates multiple tags at once
func (s *MediaTagService) BulkCreateTags(ctx context.Context, tagNames []string) ([]*models.MediaTag, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	var tags []*models.MediaTag

	for _, tagName := range tagNames {
		tag, err := s.GetOrCreateTag(ctx, tagName)
		if err != nil {
			s.logger.WithError(err).WithField("tag_name", tagName).Error("Failed to create tag")
			continue
		}

		tags = append(tags, tag)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"count":     len(tags),
	}).Info("Bulk tag creation completed")

	return tags, nil
}

// ReplaceTagsForFile replaces all tags for a media file
func (s *MediaTagService) ReplaceTagsForFile(ctx context.Context, fileID uint, tagNames []string) error {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	// Get existing tags
	existingTags, err := s.GetTagsForFile(ctx, fileID)
	if err != nil {
		return fmt.Errorf("failed to get existing tags: %w", err)
	}

	// Remove all existing tags
	for _, tag := range existingTags {
		err = s.RemoveTagFromFile(ctx, fileID, tag.ID)
		if err != nil {
			s.logger.WithError(err).WithField("tag_id", tag.ID).Error("Failed to remove existing tag")
		}
	}

	// Add new tags
	err = s.AddTagsToFile(ctx, fileID, tagNames)
	if err != nil {
		return fmt.Errorf("failed to add new tags: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"file_id":   fileID,
		"count":     len(tagNames),
	}).Info("Tags replaced for file successfully")

	return nil
}

// GetTagStatistics gets tag usage statistics
func (s *MediaTagService) GetTagStatistics(ctx context.Context) (*models.TagStatistics, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	stats, err := s.tagRepo.GetStatistics(ctx, tenantCtx.TenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get tag statistics: %w", err)
	}

	return stats, nil
}

// CleanupUnusedTags removes tags that are not associated with any files
func (s *MediaTagService) CleanupUnusedTags(ctx context.Context) (int, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return 0, fmt.Errorf("tenant context is required")
	}

	count, err := s.tagRepo.DeleteUnused(ctx, tenantCtx.TenantID)
	if err != nil {
		return 0, fmt.Errorf("failed to cleanup unused tags: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"count":     count,
	}).Info("Unused tags cleaned up successfully")

	return count, nil
}

// MergeTags merges multiple tags into one
func (s *MediaTagService) MergeTags(ctx context.Context, targetTagID uint, sourceTagIDs []uint) error {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	// Get target tag
	targetTag, err := s.tagRepo.GetByID(ctx, tenantCtx.TenantID, targetTagID)
	if err != nil {
		return fmt.Errorf("target tag not found: %w", err)
	}

	var totalUsage int
	for _, sourceTagID := range sourceTagIDs {
		// Get source tag
		sourceTag, err := s.tagRepo.GetByID(ctx, tenantCtx.TenantID, sourceTagID)
		if err != nil {
			s.logger.WithError(err).WithField("tag_id", sourceTagID).Error("Failed to get source tag")
			continue
		}

		// Move all file associations to target tag
		err = s.fileTagRepo.MoveAssociations(ctx, tenantCtx.TenantID, sourceTagID, targetTagID)
		if err != nil {
			s.logger.WithError(err).WithField("tag_id", sourceTagID).Error("Failed to move tag associations")
			continue
		}

		totalUsage += sourceTag.UsageCount

		// Delete source tag
		err = s.DeleteTag(ctx, sourceTagID)
		if err != nil {
			s.logger.WithError(err).WithField("tag_id", sourceTagID).Error("Failed to delete source tag")
		}
	}

	// Update target tag usage count
	targetTag.UsageCount += totalUsage
	targetTag.UpdatedAt = time.Now()
	err = s.tagRepo.Update(ctx, targetTag)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to update target tag usage count")
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":     tenantCtx.TenantID,
		"target_tag_id": targetTagID,
		"merged_count":  len(sourceTagIDs),
	}).Info("Tags merged successfully")

	return nil
}
