package services

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/context"
)

// SearchIndex represents a search index entry
type SearchIndex struct {
	FileID   uint                   `json:"file_id"`
	Content  string                 `json:"content"`
	Metadata map[string]interface{} `json:"metadata"`
	Tags     []string               `json:"tags"`
	Score    float64                `json:"score"`
}

// MediaSearchService handles media search with metadata indexing
type MediaSearchService struct {
	fileRepo      repositories.MediaFileRepository
	folderRepo    repositories.MediaFolderRepository
	tagRepo       repositories.MediaTagRepository
	fileTagRepo   repositories.MediaFileTagRepository
	searchIndexes map[uint]*SearchIndex // In-memory index for demo
	logger        *logrus.Logger
}

// NewMediaSearchService creates a new media search service
func NewMediaSearchService(
	fileRepo repositories.MediaFileRepository,
	folderRepo repositories.MediaFolderRepository,
	tagRepo repositories.MediaTagRepository,
	fileTagRepo repositories.MediaFileTagRepository,
	logger *logrus.Logger,
) *MediaSearchService {
	return &MediaSearchService{
		fileRepo:      fileRepo,
		folderRepo:    folderRepo,
		tagRepo:       tagRepo,
		fileTagRepo:   fileTagRepo,
		searchIndexes: make(map[uint]*SearchIndex),
		logger:        logger,
	}
}

// IndexFile indexes a media file for search
func (s *MediaSearchService) IndexFile(ctx context.Context, fileID uint) error {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	// Get file
	file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return fmt.Errorf("failed to get file: %w", err)
	}

	// Get file tags
	tags, err := s.tagRepo.GetByFileID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get file tags for indexing")
		tags = []*models.MediaTag{}
	}

	// Extract tag names
	tagNames := make([]string, len(tags))
	for i, tag := range tags {
		tagNames[i] = tag.Name
	}

	// Build search content
	content := s.buildSearchContent(file, tagNames)

	// Create search index
	index := &SearchIndex{
		FileID:   fileID,
		Content:  content,
		Metadata: file.Metadata,
		Tags:     tagNames,
		Score:    0,
	}

	// Store in memory index
	s.searchIndexes[fileID] = index

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"file_id":   fileID,
		"content":   content,
	}).Info("File indexed for search")

	return nil
}

// RemoveFromIndex removes a file from the search index
func (s *MediaSearchService) RemoveFromIndex(ctx context.Context, fileID uint) error {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	// Remove from memory index
	delete(s.searchIndexes, fileID)

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"file_id":   fileID,
	}).Info("File removed from search index")

	return nil
}

// Search searches for media files using various criteria
func (s *MediaSearchService) Search(ctx context.Context, req *models.SearchRequest) (*models.SearchResponse, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Validate request
	if req.Query == "" && len(req.Tags) == 0 && req.MediaType == "" {
		return nil, fmt.Errorf("at least one search criteria is required")
	}

	// Set default pagination
	if req.Limit <= 0 {
		req.Limit = 20
	}
	if req.Limit > 100 {
		req.Limit = 100
	}

	// Search in indexes
	results, err := s.searchInIndexes(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to search in indexes: %w", err)
	}

	// Get file details for results
	files, err := s.getFileDetails(ctx, results)
	if err != nil {
		return nil, fmt.Errorf("failed to get file details: %w", err)
	}

	// Build response
	response := &models.SearchResponse{
		Query:       req.Query,
		Results:     files,
		TotalFound:  len(results),
		TotalPages:  (len(results) + req.Limit - 1) / req.Limit,
		CurrentPage: (req.Offset / req.Limit) + 1,
		SearchTime:  time.Now(),
	}

	// Apply pagination
	start := req.Offset
	end := start + req.Limit
	if end > len(files) {
		end = len(files)
	}
	if start < len(files) {
		response.Results = files[start:end]
	} else {
		response.Results = []*models.MediaFile{}
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":     tenantCtx.TenantID,
		"query":         req.Query,
		"results_count": len(response.Results),
		"total_found":   response.TotalFound,
	}).Info("Search completed")

	return response, nil
}

// SearchSuggestions provides search suggestions based on query
func (s *MediaSearchService) SearchSuggestions(ctx context.Context, query string, limit int) ([]string, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	if limit <= 0 {
		limit = 5
	}

	// Get suggestions from tags
	tagSuggestions, err := s.tagRepo.Search(ctx, tenantCtx.TenantID, query, limit, 0)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get tag suggestions")
		tagSuggestions = []*models.MediaTag{}
	}

	// Get suggestions from filenames
	filenameSuggestions, err := s.getFilenameSuggestions(ctx, query, limit)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get filename suggestions")
		filenameSuggestions = []string{}
	}

	// Combine suggestions
	suggestions := make([]string, 0, limit)

	// Add tag suggestions
	for _, tag := range tagSuggestions {
		if len(suggestions) >= limit {
			break
		}
		suggestions = append(suggestions, tag.DisplayName)
	}

	// Add filename suggestions
	for _, suggestion := range filenameSuggestions {
		if len(suggestions) >= limit {
			break
		}
		suggestions = append(suggestions, suggestion)
	}

	return suggestions, nil
}

// SearchByMetadata searches files by metadata criteria
func (s *MediaSearchService) SearchByMetadata(ctx context.Context, criteria map[string]interface{}, limit, offset int) ([]*models.MediaFile, int, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, 0, fmt.Errorf("tenant context is required")
	}

	var matchingFiles []*models.MediaFile

	// Search in indexes
	for fileID, index := range s.searchIndexes {
		if s.matchesMetadataCriteria(index.Metadata, criteria) {
			file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, fileID)
			if err != nil {
				continue
			}
			matchingFiles = append(matchingFiles, file)
		}
	}

	// Sort by relevance (creation date for now)
	sort.Slice(matchingFiles, func(i, j int) bool {
		return matchingFiles[i].CreatedAt.After(matchingFiles[j].CreatedAt)
	})

	// Apply pagination
	total := len(matchingFiles)
	start := offset
	end := start + limit
	if end > total {
		end = total
	}

	var results []*models.MediaFile
	if start < total {
		results = matchingFiles[start:end]
	}

	return results, total, nil
}

// SearchSimilar finds similar files based on metadata and content
func (s *MediaSearchService) SearchSimilar(ctx context.Context, fileID uint, limit int) ([]*models.MediaFile, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get reference file
	referenceFile, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get reference file: %w", err)
	}

	// Get reference index
	referenceIndex, exists := s.searchIndexes[fileID]
	if !exists {
		return nil, fmt.Errorf("reference file not indexed")
	}

	// Find similar files
	var similarFiles []*models.MediaFile

	for indexFileID, index := range s.searchIndexes {
		if indexFileID == fileID {
			continue // Skip self
		}

		// Calculate similarity score
		score := s.calculateSimilarityScore(referenceIndex, index, referenceFile)
		if score > 0.3 { // Threshold for similarity
			file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, indexFileID)
			if err != nil {
				continue
			}
			similarFiles = append(similarFiles, file)
		}
	}

	// Sort by similarity (for now, just by file type and size)
	sort.Slice(similarFiles, func(i, j int) bool {
		return similarFiles[i].MediaType == referenceFile.MediaType
	})

	// Apply limit
	if limit > 0 && len(similarFiles) > limit {
		similarFiles = similarFiles[:limit]
	}

	return similarFiles, nil
}

// RebuildIndex rebuilds the entire search index
func (s *MediaSearchService) RebuildIndex(ctx context.Context) error {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	// Clear existing index
	s.searchIndexes = make(map[uint]*SearchIndex)

	// Get all active files
	filter := &models.MediaFileFilter{
		Status: stringPtr("active"),
	}

	files, _, err := s.fileRepo.List(ctx, tenantCtx.TenantID, filter, 10000, 0)
	if err != nil {
		return fmt.Errorf("failed to get files: %w", err)
	}

	// Index each file
	for _, file := range files {
		err := s.IndexFile(ctx, file.ID)
		if err != nil {
			s.logger.WithError(err).WithField("file_id", file.ID).Error("Failed to index file")
		}
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":     tenantCtx.TenantID,
		"files_count":   len(files),
		"indexed_count": len(s.searchIndexes),
	}).Info("Search index rebuilt")

	return nil
}

// GetSearchStatistics gets search and indexing statistics
func (s *MediaSearchService) GetSearchStatistics(ctx context.Context) (*models.SearchStatistics, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get total files
	stats, err := s.fileRepo.GetStatistics(ctx, tenantCtx.TenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file statistics: %w", err)
	}

	// Get indexed files count
	indexedCount := len(s.searchIndexes)

	searchStats := &models.SearchStatistics{
		TotalFiles:    stats.TotalFiles,
		IndexedFiles:  indexedCount,
		IndexingRatio: float64(indexedCount) / float64(stats.TotalFiles) * 100,
		LastIndexedAt: time.Now(),
		IndexSize:     s.calculateIndexSize(),
		GeneratedAt:   time.Now(),
	}

	return searchStats, nil
}

// Private helper methods

// buildSearchContent builds searchable content from file metadata
func (s *MediaSearchService) buildSearchContent(file *models.MediaFile, tags []string) string {
	var content []string

	// Add filename
	content = append(content, file.OriginalName)
	content = append(content, file.Filename)

	// Add description and alt text
	if file.Description != nil {
		content = append(content, *file.Description)
	}
	if file.AltText != nil {
		content = append(content, *file.AltText)
	}
	if file.Caption != nil {
		content = append(content, *file.Caption)
	}

	// Add media type
	content = append(content, file.MediaType)

	// Add tags
	content = append(content, tags...)

	// Add metadata values
	for key, value := range file.Metadata {
		if str, ok := value.(string); ok {
			content = append(content, fmt.Sprintf("%s:%s", key, str))
		}
	}

	return strings.Join(content, " ")
}

// searchInIndexes searches within the memory indexes
func (s *MediaSearchService) searchInIndexes(ctx context.Context, req *models.SearchRequest) ([]*SearchIndex, error) {
	var results []*SearchIndex

	query := strings.ToLower(req.Query)

	for _, index := range s.searchIndexes {
		score := 0.0

		// Text search
		if query != "" {
			if strings.Contains(strings.ToLower(index.Content), query) {
				score += 1.0
			}
		}

		// Tag search
		if len(req.Tags) > 0 {
			for _, reqTag := range req.Tags {
				for _, indexTag := range index.Tags {
					if strings.EqualFold(reqTag, indexTag) {
						score += 2.0
					}
				}
			}
		}

		// Media type filter
		if req.MediaType != "" {
			if mediaType, ok := index.Metadata["media_type"].(string); ok {
				if strings.EqualFold(req.MediaType, mediaType) {
					score += 0.5
				}
			}
		}

		// Date range filter
		if !req.DateFrom.IsZero() || !req.DateTo.IsZero() {
			if createdAt, ok := index.Metadata["created_at"].(time.Time); ok {
				if !req.DateFrom.IsZero() && createdAt.Before(req.DateFrom) {
					continue
				}
				if !req.DateTo.IsZero() && createdAt.After(req.DateTo) {
					continue
				}
				score += 0.1
			}
		}

		// Size range filter
		if req.MinSize > 0 || req.MaxSize > 0 {
			if size, ok := index.Metadata["file_size"].(int64); ok {
				if req.MinSize > 0 && size < req.MinSize {
					continue
				}
				if req.MaxSize > 0 && size > req.MaxSize {
					continue
				}
				score += 0.1
			}
		}

		if score > 0 {
			index.Score = score
			results = append(results, index)
		}
	}

	// Sort by score
	sort.Slice(results, func(i, j int) bool {
		return results[i].Score > results[j].Score
	})

	return results, nil
}

// getFileDetails retrieves file details for search results
func (s *MediaSearchService) getFileDetails(ctx context.Context, indexes []*SearchIndex) ([]*models.MediaFile, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	var files []*models.MediaFile

	for _, index := range indexes {
		file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, index.FileID)
		if err != nil {
			s.logger.WithError(err).WithField("file_id", index.FileID).Warn("Failed to get file details")
			continue
		}
		files = append(files, file)
	}

	return files, nil
}

// getFilenameSuggestions gets filename-based suggestions
func (s *MediaSearchService) getFilenameSuggestions(ctx context.Context, query string, limit int) ([]string, error) {
	var suggestions []string
	query = strings.ToLower(query)

	for _, index := range s.searchIndexes {
		content := strings.ToLower(index.Content)
		if strings.Contains(content, query) {
			// Extract relevant parts
			words := strings.Fields(content)
			for _, word := range words {
				if strings.Contains(word, query) && len(suggestions) < limit {
					suggestions = append(suggestions, word)
				}
			}
		}
	}

	return suggestions, nil
}

// matchesMetadataCriteria checks if metadata matches criteria
func (s *MediaSearchService) matchesMetadataCriteria(metadata map[string]interface{}, criteria map[string]interface{}) bool {
	for key, value := range criteria {
		metaValue, exists := metadata[key]
		if !exists {
			return false
		}

		// Simple equality check
		if fmt.Sprintf("%v", metaValue) != fmt.Sprintf("%v", value) {
			return false
		}
	}
	return true
}

// calculateSimilarityScore calculates similarity between two files
func (s *MediaSearchService) calculateSimilarityScore(ref *SearchIndex, other *SearchIndex, refFile *models.MediaFile) float64 {
	score := 0.0

	// Same media type
	if refMediaType, ok := ref.Metadata["media_type"].(string); ok {
		if otherMediaType, ok := other.Metadata["media_type"].(string); ok {
			if refMediaType == otherMediaType {
				score += 0.3
			}
		}
	}

	// Similar size
	if refSize, ok := ref.Metadata["file_size"].(int64); ok {
		if otherSize, ok := other.Metadata["file_size"].(int64); ok {
			sizeDiff := float64(abs(refSize-otherSize)) / float64(refSize)
			if sizeDiff < 0.2 {
				score += 0.2
			}
		}
	}

	// Common tags
	commonTags := 0
	for _, refTag := range ref.Tags {
		for _, otherTag := range other.Tags {
			if refTag == otherTag {
				commonTags++
			}
		}
	}
	if commonTags > 0 {
		score += float64(commonTags) * 0.1
	}

	return score
}

// calculateIndexSize calculates approximate index size
func (s *MediaSearchService) calculateIndexSize() int64 {
	size := int64(0)
	for _, index := range s.searchIndexes {
		size += int64(len(index.Content))
		size += int64(len(index.Tags) * 20) // Approximate tag size
	}
	return size
}

// Helper functions
func abs(x int64) int64 {
	if x < 0 {
		return -x
	}
	return x
}

func stringPtr(s string) *string {
	return &s
}
