package services

import (
	"context"
	"fmt"
	"io"
	"mime/multipart"
	"path/filepath"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories"
	pkgcontext "github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/storage"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// MediaFileService handles media file operations
type MediaFileService struct {
	fileRepo      repositories.MediaFileRepository
	folderRepo    repositories.MediaFolderRepository
	tagRepo       repositories.MediaTagRepository
	storageClient storage.StorageClient
	logger        *logrus.Logger
}

// NewMediaFileService creates a new media file service
func NewMediaFileService(
	fileRepo repositories.MediaFileRepository,
	folderRepo repositories.MediaFolderRepository,
	tagRepo repositories.MediaTagRepository,
	storageClient storage.StorageClient,
	logger *logrus.Logger,
) *MediaFileService {
	return &MediaFileService{
		fileRepo:      fileRepo,
		folderRepo:    folderRepo,
		tagRepo:       tagRepo,
		storageClient: storageClient,
		logger:        logger,
	}
}

// UploadFile uploads a file to storage and creates a media file record
func (s *MediaFileService) UploadFile(ctx context.Context, file *multipart.FileHeader, folderID *uint, tags []string) (*models.MediaFile, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Validate file
	if file == nil {
		return nil, fmt.Errorf("file is required")
	}

	// Get file info
	mediaType := utils.GetMediaType(file.Filename)
	mimeType := utils.GetMimeType(file.Filename)
	fileSize := file.Size

	// Validate file type and size
	if !s.isValidMediaType(mediaType) {
		return nil, fmt.Errorf("unsupported media type: %s", mediaType)
	}

	if !utils.ValidateFileSize(fileSize, mediaType) {
		return nil, fmt.Errorf("file size exceeds limit for type: %s", mediaType)
	}

	// Open file
	src, err := file.Open()
	if err != nil {
		return nil, fmt.Errorf("failed to open file: %w", err)
	}
	defer src.Close()

	// Generate unique filename
	originalName := file.Filename
	sanitizedName := utils.SanitizeFilename(originalName)
	uniqueFilename := utils.GenerateUniqueFilename(sanitizedName)

	// Upload to storage
	storagePath := fmt.Sprintf("media/%d/%s", tenantCtx.TenantID, uniqueFilename)
	uploadedFile, err := s.storageClient.Upload(ctx, storagePath, src, mimeType)
	if err != nil {
		return nil, fmt.Errorf("failed to upload file: %w", err)
	}

	// Create media file record
	mediaFile := &models.MediaFile{
		TenantID:     tenantCtx.TenantID,
		FolderID:     folderID,
		OriginalName: originalName,
		Filename:     uniqueFilename,
		StoragePath:  storagePath,
		MimeType:     mimeType,
		FileSize:     fileSize,
		MediaType:    string(mediaType),
		Status:       "active",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Set additional metadata
	mediaFile.URL = uploadedFile.URL
	mediaFile.CDNUrl = uploadedFile.CDNURL

	// Extract metadata based on media type
	if mediaType == utils.MediaTypeImage {
		metadata, err := s.extractImageMetadata(src)
		if err != nil {
			s.logger.WithError(err).Warn("Failed to extract image metadata")
		} else {
			mediaFile.Width = metadata.Width
			mediaFile.Height = metadata.Height
			mediaFile.Metadata = metadata.ToMap()
		}
	}

	// Save to database
	err = s.fileRepo.Create(ctx, mediaFile)
	if err != nil {
		// Clean up uploaded file
		s.storageClient.Delete(ctx, storagePath)
		return nil, fmt.Errorf("failed to create media file record: %w", err)
	}

	// Add tags if provided
	if len(tags) > 0 {
		err = s.addTagsToFile(ctx, mediaFile.ID, tags)
		if err != nil {
			s.logger.WithError(err).Warn("Failed to add tags to media file")
		}
	}

	// Generate thumbnails for images
	if mediaType == utils.MediaTypeImage {
		go s.generateThumbnails(context.Background(), mediaFile)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":  tenantCtx.TenantID,
		"file_id":    mediaFile.ID,
		"filename":   mediaFile.Filename,
		"size":       mediaFile.FileSize,
		"media_type": mediaFile.MediaType,
	}).Info("File uploaded successfully")

	return mediaFile, nil
}

// GetFile retrieves a media file by ID
func (s *MediaFileService) GetFile(ctx context.Context, id uint) (*models.MediaFile, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get media file: %w", err)
	}

	return file, nil
}

// UpdateFile updates a media file's metadata
func (s *MediaFileService) UpdateFile(ctx context.Context, id uint, req *models.UpdateMediaFileRequest) (*models.MediaFile, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get media file: %w", err)
	}

	// Update fields
	if req.OriginalName != nil {
		file.OriginalName = *req.OriginalName
	}
	if req.FolderID != nil {
		file.FolderID = req.FolderID
	}
	if req.AltText != nil {
		file.AltText = req.AltText
	}
	if req.Caption != nil {
		file.Caption = req.Caption
	}
	if req.Description != nil {
		file.Description = req.Description
	}
	if req.Status != nil {
		file.Status = *req.Status
	}

	file.UpdatedAt = time.Now()

	err = s.fileRepo.Update(ctx, file)
	if err != nil {
		return nil, fmt.Errorf("failed to update media file: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"file_id":   id,
	}).Info("Media file updated successfully")

	return file, nil
}

// DeleteFile deletes a media file
func (s *MediaFileService) DeleteFile(ctx context.Context, id uint) error {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return fmt.Errorf("failed to get media file: %w", err)
	}

	// Delete from storage
	err = s.storageClient.Delete(ctx, file.StoragePath)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to delete file from storage")
	}

	// Delete thumbnails
	if file.MediaType == string(utils.MediaTypeImage) {
		s.deleteThumbnails(ctx, file)
	}

	// Soft delete from database
	file.Status = "deleted"
	file.UpdatedAt = time.Now()

	err = s.fileRepo.Update(ctx, file)
	if err != nil {
		return fmt.Errorf("failed to delete media file: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"file_id":   id,
	}).Info("Media file deleted successfully")

	return nil
}

// ListFiles lists media files with filtering and pagination
func (s *MediaFileService) ListFiles(ctx context.Context, filter *models.MediaFileFilter, limit, offset int) ([]*models.MediaFile, int, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, 0, fmt.Errorf("tenant context is required")
	}

	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	files, total, err := s.fileRepo.List(ctx, tenantCtx.TenantID, filter, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list media files: %w", err)
	}

	return files, total, nil
}

// SearchFilesOld searches media files by name, description, or tags (deprecated - use SearchFiles with MediaFileSearchRequest)
func (s *MediaFileService) SearchFilesOld(ctx context.Context, query string, filter *models.MediaFileFilter, limit, offset int) ([]*models.MediaFile, int, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, 0, fmt.Errorf("tenant context is required")
	}

	if query == "" {
		return s.ListFiles(ctx, filter, limit, offset)
	}

	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	files, total, err := s.fileRepo.Search(ctx, tenantCtx.TenantID, query, filter, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to search media files: %w", err)
	}

	return files, total, nil
}

// SearchFiles searches media files using MediaFileSearchRequest (updated signature for handler compatibility)
func (s *MediaFileService) SearchFiles(ctx context.Context, req *dto.MediaFileSearchRequest) ([]models.MediaFile, int64, error) {
	// Use repository's Search method directly
	return s.fileRepo.Search(ctx, req)
}

// SearchFilesWithCursor searches media files using cursor-based pagination
func (s *MediaFileService) SearchFilesWithCursor(ctx context.Context, req *dto.MediaFileSearchRequest, cursorReq *pagination.CursorRequest) ([]models.MediaFile, *pagination.CursorResponse, error) {
	// Use repository's SearchWithCursor method
	return s.fileRepo.SearchWithCursor(ctx, req, cursorReq)
}

// MoveFile moves a file to a different folder
func (s *MediaFileService) MoveFile(ctx context.Context, id uint, folderID *uint) error {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return fmt.Errorf("failed to get media file: %w", err)
	}

	// Validate folder exists if provided
	if folderID != nil {
		_, err = s.folderRepo.GetByID(ctx, tenantCtx.TenantID, *folderID)
		if err != nil {
			return fmt.Errorf("target folder not found: %w", err)
		}
	}

	// Update folder
	file.FolderID = folderID
	file.UpdatedAt = time.Now()

	err = s.fileRepo.Update(ctx, file)
	if err != nil {
		return fmt.Errorf("failed to move media file: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"file_id":   id,
		"folder_id": folderID,
	}).Info("Media file moved successfully")

	return nil
}

// CopyFile creates a copy of a media file
func (s *MediaFileService) CopyFile(ctx context.Context, id uint, folderID *uint) (*models.MediaFile, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	original, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get original media file: %w", err)
	}

	// Download original file
	reader, err := s.storageClient.Download(ctx, original.StoragePath)
	if err != nil {
		return nil, fmt.Errorf("failed to download original file: %w", err)
	}
	defer reader.Close()

	// Generate new filename
	ext := filepath.Ext(original.Filename)
	baseName := strings.TrimSuffix(original.Filename, ext)
	newFilename := fmt.Sprintf("%s_copy%s", baseName, ext)
	uniqueFilename := utils.GenerateUniqueFilename(newFilename)

	// Upload copy
	newStoragePath := fmt.Sprintf("media/%d/%s", tenantCtx.TenantID, uniqueFilename)
	uploadedFile, err := s.storageClient.Upload(ctx, newStoragePath, reader, original.MimeType)
	if err != nil {
		return nil, fmt.Errorf("failed to upload file copy: %w", err)
	}

	// Create new media file record
	newFile := &models.MediaFile{
		TenantID:     tenantCtx.TenantID,
		FolderID:     folderID,
		OriginalName: fmt.Sprintf("Copy of %s", original.OriginalName),
		Filename:     uniqueFilename,
		StoragePath:  newStoragePath,
		MimeType:     original.MimeType,
		FileSize:     original.FileSize,
		MediaType:    original.MediaType,
		Width:        original.Width,
		Height:       original.Height,
		URL:          uploadedFile.URL,
		CDNUrl:       uploadedFile.CDNURL,
		AltText:      original.AltText,
		Caption:      original.Caption,
		Description:  original.Description,
		Metadata:     original.Metadata,
		Status:       "active",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	err = s.fileRepo.Create(ctx, newFile)
	if err != nil {
		// Clean up uploaded file
		s.storageClient.Delete(ctx, newStoragePath)
		return nil, fmt.Errorf("failed to create media file copy: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":   tenantCtx.TenantID,
		"original_id": id,
		"copy_id":     newFile.ID,
	}).Info("Media file copied successfully")

	return newFile, nil
}

// GetFileStatistics gets file statistics
func (s *MediaFileService) GetFileStatistics(ctx context.Context) (*models.MediaFileStatistics, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	stats, err := s.fileRepo.GetStatistics(ctx, tenantCtx.TenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file statistics: %w", err)
	}

	return stats, nil
}

// BulkDelete deletes multiple files
func (s *MediaFileService) BulkDelete(ctx context.Context, fileIDs []uint) error {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	for _, id := range fileIDs {
		err := s.DeleteFile(ctx, id)
		if err != nil {
			s.logger.WithError(err).WithField("file_id", id).Error("Failed to delete file in bulk operation")
		}
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"count":     len(fileIDs),
	}).Info("Bulk delete completed")

	return nil
}

// BulkMove moves multiple files to a folder
func (s *MediaFileService) BulkMove(ctx context.Context, fileIDs []uint, folderID *uint) error {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	for _, id := range fileIDs {
		err := s.MoveFile(ctx, id, folderID)
		if err != nil {
			s.logger.WithError(err).WithField("file_id", id).Error("Failed to move file in bulk operation")
		}
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"count":     len(fileIDs),
		"folder_id": folderID,
	}).Info("Bulk move completed")

	return nil
}

// Private helper methods

// isValidMediaType checks if media type is supported
func (s *MediaFileService) isValidMediaType(mediaType utils.MediaType) bool {
	supportedTypes := []utils.MediaType{
		utils.MediaTypeImage,
		utils.MediaTypeVideo,
		utils.MediaTypeAudio,
		utils.MediaTypeDocument,
	}

	for _, supportedType := range supportedTypes {
		if mediaType == supportedType {
			return true
		}
	}

	return false
}

// extractImageMetadata extracts metadata from image files
func (s *MediaFileService) extractImageMetadata(reader io.Reader) (*ImageMetadata, error) {
	// This would use image processing libraries to extract metadata
	// For now, return basic metadata
	metadata := &ImageMetadata{
		Width:  0,
		Height: 0,
		Format: "unknown",
	}

	return metadata, nil
}

// generateThumbnails generates thumbnails for images
func (s *MediaFileService) generateThumbnails(ctx context.Context, file *models.MediaFile) {
	// This would generate thumbnails using image processing
	// For now, just log the operation
	s.logger.WithFields(logrus.Fields{
		"file_id":  file.ID,
		"filename": file.Filename,
	}).Info("Generating thumbnails for image")
}

// deleteThumbnails deletes thumbnails for an image
func (s *MediaFileService) deleteThumbnails(ctx context.Context, file *models.MediaFile) {
	// This would delete generated thumbnails
	// For now, just log the operation
	s.logger.WithFields(logrus.Fields{
		"file_id":  file.ID,
		"filename": file.Filename,
	}).Info("Deleting thumbnails for image")
}

// addTagsToFile adds tags to a media file
func (s *MediaFileService) addTagsToFile(ctx context.Context, fileID uint, tags []string) error {
	// This would add tags to the file
	// For now, just log the operation
	s.logger.WithFields(logrus.Fields{
		"file_id": fileID,
		"tags":    tags,
	}).Info("Adding tags to media file")

	return nil
}

// Helper types
type ImageMetadata struct {
	Width  int    `json:"width"`
	Height int    `json:"height"`
	Format string `json:"format"`
}

func (m *ImageMetadata) ToMap() map[string]interface{} {
	return map[string]interface{}{
		"width":  m.Width,
		"height": m.Height,
		"format": m.Format,
	}
}
