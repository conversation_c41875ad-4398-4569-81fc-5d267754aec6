package services

import (
	"context"
	"fmt"
	"image"
	"image/jpeg"
	"image/png"
	"io"
	"path/filepath"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/context"
	"github.com/tranthanhloi/wn-api-v3/pkg/storage"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// OptimizationSettings defines optimization parameters
type OptimizationSettings struct {
	Quality          int     `json:"quality"`
	MaxWidth         int     `json:"max_width"`
	MaxHeight        int     `json:"max_height"`
	Format           string  `json:"format"`
	CompressionLevel int     `json:"compression_level"`
	Progressive      bool    `json:"progressive"`
	StripMetadata    bool    `json:"strip_metadata"`
	Sharpen          bool    `json:"sharpen"`
	Blur             float64 `json:"blur"`
}

// MediaOptimizationService handles image optimization and compression
type MediaOptimizationService struct {
	fileRepo      repositories.MediaFileRepository
	storageClient storage.StorageClient
	logger        *logrus.Logger
}

// NewMediaOptimizationService creates a new media optimization service
func NewMediaOptimizationService(
	fileRepo repositories.MediaFileRepository,
	storageClient storage.StorageClient,
	logger *logrus.Logger,
) *MediaOptimizationService {
	return &MediaOptimizationService{
		fileRepo:      fileRepo,
		storageClient: storageClient,
		logger:        logger,
	}
}

// OptimizeImage optimizes an image file
func (s *MediaOptimizationService) OptimizeImage(ctx context.Context, fileID uint, settings *OptimizationSettings) (*models.OptimizationResult, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get file
	file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file: %w", err)
	}

	// Check if file is an image
	if !utils.IsImageFile(file.Filename) {
		return nil, fmt.Errorf("file is not an image: %s", file.Filename)
	}

	// Use default settings if not provided
	if settings == nil {
		settings = s.getDefaultSettings()
	}

	// Download original file
	reader, err := s.storageClient.Download(ctx, file.StoragePath)
	if err != nil {
		return nil, fmt.Errorf("failed to download original file: %w", err)
	}
	defer reader.Close()

	// Optimize image
	optimizedReader, metadata, err := s.optimizeImageStream(reader, settings)
	if err != nil {
		return nil, fmt.Errorf("failed to optimize image: %w", err)
	}
	defer optimizedReader.Close()

	// Generate optimized filename
	optimizedFilename := s.generateOptimizedFilename(file.Filename, settings)
	optimizedPath := fmt.Sprintf("optimized/%d/%s", tenantCtx.TenantID, optimizedFilename)

	// Upload optimized file
	mimeType := utils.GetMimeType(optimizedFilename)
	uploadedFile, err := s.storageClient.Upload(ctx, optimizedPath, optimizedReader, mimeType)
	if err != nil {
		return nil, fmt.Errorf("failed to upload optimized file: %w", err)
	}

	// Create optimization result
	result := &models.OptimizationResult{
		FileID:          fileID,
		OriginalSize:    file.FileSize,
		OptimizedSize:   uploadedFile.Size,
		SizeReduction:   float64(file.FileSize-uploadedFile.Size) / float64(file.FileSize) * 100,
		OptimizedPath:   optimizedPath,
		OptimizedURL:    uploadedFile.URL,
		OptimizedCDNURL: uploadedFile.CDNURL,
		Settings:        settings,
		Metadata:        metadata,
		OptimizedAt:     time.Now(),
	}

	// Update file record if this is better than current
	if result.SizeReduction > 0 {
		file.OptimizedPath = &optimizedPath
		file.OptimizedSize = &uploadedFile.Size
		file.OptimizedURL = &uploadedFile.URL
		file.OptimizedAt = &result.OptimizedAt
		file.UpdatedAt = time.Now()

		err = s.fileRepo.Update(ctx, file)
		if err != nil {
			s.logger.WithError(err).Warn("Failed to update file with optimization info")
		}
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":      tenantCtx.TenantID,
		"file_id":        fileID,
		"original_size":  result.OriginalSize,
		"optimized_size": result.OptimizedSize,
		"size_reduction": result.SizeReduction,
	}).Info("Image optimized successfully")

	return result, nil
}

// BulkOptimizeImages optimizes multiple images
func (s *MediaOptimizationService) BulkOptimizeImages(ctx context.Context, fileIDs []uint, settings *OptimizationSettings) ([]*models.OptimizationResult, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	var results []*models.OptimizationResult

	for _, fileID := range fileIDs {
		result, err := s.OptimizeImage(ctx, fileID, settings)
		if err != nil {
			s.logger.WithError(err).WithField("file_id", fileID).Error("Failed to optimize image")
			continue
		}

		results = append(results, result)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":       tenantCtx.TenantID,
		"files_count":     len(fileIDs),
		"optimized_count": len(results),
	}).Info("Bulk image optimization completed")

	return results, nil
}

// OptimizeByFolder optimizes all images in a folder
func (s *MediaOptimizationService) OptimizeByFolder(ctx context.Context, folderID uint, settings *OptimizationSettings) ([]*models.OptimizationResult, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get all image files in folder
	filter := &models.MediaFileFilter{
		FolderID:  &folderID,
		MediaType: stringPtr("image"),
		Status:    stringPtr("active"),
	}

	files, _, err := s.fileRepo.List(ctx, tenantCtx.TenantID, filter, 1000, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get files: %w", err)
	}

	// Extract file IDs
	fileIDs := make([]uint, len(files))
	for i, file := range files {
		fileIDs[i] = file.ID
	}

	// Optimize images
	results, err := s.BulkOptimizeImages(ctx, fileIDs, settings)
	if err != nil {
		return nil, fmt.Errorf("failed to optimize images: %w", err)
	}

	return results, nil
}

// GetOptimizationSettings gets optimization settings for different use cases
func (s *MediaOptimizationService) GetOptimizationSettings(useCase string) *OptimizationSettings {
	switch useCase {
	case "web":
		return &OptimizationSettings{
			Quality:          85,
			MaxWidth:         1920,
			MaxHeight:        1080,
			Format:           "jpeg",
			CompressionLevel: 8,
			Progressive:      true,
			StripMetadata:    true,
			Sharpen:          false,
			Blur:             0,
		}
	case "mobile":
		return &OptimizationSettings{
			Quality:          75,
			MaxWidth:         800,
			MaxHeight:        600,
			Format:           "jpeg",
			CompressionLevel: 9,
			Progressive:      false,
			StripMetadata:    true,
			Sharpen:          true,
			Blur:             0,
		}
	case "thumbnail":
		return &OptimizationSettings{
			Quality:          70,
			MaxWidth:         300,
			MaxHeight:        300,
			Format:           "jpeg",
			CompressionLevel: 9,
			Progressive:      false,
			StripMetadata:    true,
			Sharpen:          true,
			Blur:             0,
		}
	case "print":
		return &OptimizationSettings{
			Quality:          95,
			MaxWidth:         3000,
			MaxHeight:        3000,
			Format:           "png",
			CompressionLevel: 6,
			Progressive:      false,
			StripMetadata:    false,
			Sharpen:          false,
			Blur:             0,
		}
	default:
		return s.getDefaultSettings()
	}
}

// ConvertFormat converts image to a different format
func (s *MediaOptimizationService) ConvertFormat(ctx context.Context, fileID uint, targetFormat string) (*models.ConversionResult, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get file
	file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file: %w", err)
	}

	// Check if file is an image
	if !utils.IsImageFile(file.Filename) {
		return nil, fmt.Errorf("file is not an image: %s", file.Filename)
	}

	// Download original file
	reader, err := s.storageClient.Download(ctx, file.StoragePath)
	if err != nil {
		return nil, fmt.Errorf("failed to download original file: %w", err)
	}
	defer reader.Close()

	// Convert image
	convertedReader, err := s.convertImageFormat(reader, targetFormat)
	if err != nil {
		return nil, fmt.Errorf("failed to convert image: %w", err)
	}
	defer convertedReader.Close()

	// Generate converted filename
	convertedFilename := s.generateConvertedFilename(file.Filename, targetFormat)
	convertedPath := fmt.Sprintf("converted/%d/%s", tenantCtx.TenantID, convertedFilename)

	// Upload converted file
	mimeType := utils.GetMimeType(convertedFilename)
	uploadedFile, err := s.storageClient.Upload(ctx, convertedPath, convertedReader, mimeType)
	if err != nil {
		return nil, fmt.Errorf("failed to upload converted file: %w", err)
	}

	// Create conversion result
	result := &models.ConversionResult{
		FileID:          fileID,
		OriginalFormat:  s.getImageFormat(file.Filename),
		TargetFormat:    targetFormat,
		OriginalSize:    file.FileSize,
		ConvertedSize:   uploadedFile.Size,
		ConvertedPath:   convertedPath,
		ConvertedURL:    uploadedFile.URL,
		ConvertedCDNURL: uploadedFile.CDNURL,
		ConvertedAt:     time.Now(),
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":       tenantCtx.TenantID,
		"file_id":         fileID,
		"original_format": result.OriginalFormat,
		"target_format":   targetFormat,
		"original_size":   result.OriginalSize,
		"converted_size":  result.ConvertedSize,
	}).Info("Image converted successfully")

	return result, nil
}

// GetOptimizationStatistics gets optimization statistics
func (s *MediaOptimizationService) GetOptimizationStatistics(ctx context.Context) (*models.OptimizationStatistics, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get files with optimization info
	filter := &models.MediaFileFilter{
		HasOptimized: boolPtr(true),
		Status:       stringPtr("active"),
	}

	files, _, err := s.fileRepo.List(ctx, tenantCtx.TenantID, filter, 1000, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get files: %w", err)
	}

	var totalOriginalSize int64
	var totalOptimizedSize int64
	var optimizedCount int

	for _, file := range files {
		if file.OptimizedSize != nil {
			totalOriginalSize += file.FileSize
			totalOptimizedSize += *file.OptimizedSize
			optimizedCount++
		}
	}

	var avgReduction float64
	if totalOriginalSize > 0 {
		avgReduction = float64(totalOriginalSize-totalOptimizedSize) / float64(totalOriginalSize) * 100
	}

	stats := &models.OptimizationStatistics{
		TotalFiles:         len(files),
		OptimizedFiles:     optimizedCount,
		TotalOriginalSize:  totalOriginalSize,
		TotalOptimizedSize: totalOptimizedSize,
		TotalSpaceSaved:    totalOriginalSize - totalOptimizedSize,
		AverageReduction:   avgReduction,
		GeneratedAt:        time.Now(),
	}

	return stats, nil
}

// Private helper methods

// optimizeImageStream optimizes an image stream
func (s *MediaOptimizationService) optimizeImageStream(reader io.Reader, settings *OptimizationSettings) (io.ReadCloser, map[string]interface{}, error) {
	// Decode image
	img, format, err := image.Decode(reader)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to decode image: %w", err)
	}

	// Apply optimizations
	optimizedImg := s.applyOptimizations(img, settings)

	// Encode image
	var buf strings.Builder
	metadata := make(map[string]interface{})

	switch settings.Format {
	case "jpeg", "jpg":
		err = jpeg.Encode(&buf, optimizedImg, &jpeg.Options{Quality: settings.Quality})
		metadata["format"] = "jpeg"
	case "png":
		err = png.Encode(&buf, optimizedImg)
		metadata["format"] = "png"
	default:
		// Use original format
		switch format {
		case "jpeg":
			err = jpeg.Encode(&buf, optimizedImg, &jpeg.Options{Quality: settings.Quality})
			metadata["format"] = "jpeg"
		case "png":
			err = png.Encode(&buf, optimizedImg)
			metadata["format"] = "png"
		default:
			err = jpeg.Encode(&buf, optimizedImg, &jpeg.Options{Quality: settings.Quality})
			metadata["format"] = "jpeg"
		}
	}

	if err != nil {
		return nil, nil, fmt.Errorf("failed to encode optimized image: %w", err)
	}

	metadata["quality"] = settings.Quality
	metadata["optimized_at"] = time.Now()

	return io.NopCloser(strings.NewReader(buf.String())), metadata, nil
}

// applyOptimizations applies various optimizations to an image
func (s *MediaOptimizationService) applyOptimizations(img image.Image, settings *OptimizationSettings) image.Image {
	// For now, return original image
	// In production, this would apply:
	// - Resize if dimensions exceed max
	// - Apply sharpening if enabled
	// - Apply blur if specified
	// - Strip metadata if enabled
	return img
}

// convertImageFormat converts an image to a different format
func (s *MediaOptimizationService) convertImageFormat(reader io.Reader, targetFormat string) (io.ReadCloser, error) {
	// Decode image
	img, _, err := image.Decode(reader)
	if err != nil {
		return nil, fmt.Errorf("failed to decode image: %w", err)
	}

	// Encode in target format
	var buf strings.Builder

	switch targetFormat {
	case "jpeg", "jpg":
		err = jpeg.Encode(&buf, img, &jpeg.Options{Quality: 85})
	case "png":
		err = png.Encode(&buf, img)
	default:
		return nil, fmt.Errorf("unsupported target format: %s", targetFormat)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to encode image: %w", err)
	}

	return io.NopCloser(strings.NewReader(buf.String())), nil
}

// getDefaultSettings returns default optimization settings
func (s *MediaOptimizationService) getDefaultSettings() *OptimizationSettings {
	return &OptimizationSettings{
		Quality:          85,
		MaxWidth:         1920,
		MaxHeight:        1080,
		Format:           "jpeg",
		CompressionLevel: 8,
		Progressive:      true,
		StripMetadata:    true,
		Sharpen:          false,
		Blur:             0,
	}
}

// generateOptimizedFilename generates filename for optimized image
func (s *MediaOptimizationService) generateOptimizedFilename(originalFilename string, settings *OptimizationSettings) string {
	ext := fmt.Sprintf(".%s", settings.Format)
	baseName := strings.TrimSuffix(originalFilename, filepath.Ext(originalFilename))
	return fmt.Sprintf("%s_optimized_%d%s", baseName, settings.Quality, ext)
}

// generateConvertedFilename generates filename for converted image
func (s *MediaOptimizationService) generateConvertedFilename(originalFilename, targetFormat string) string {
	ext := fmt.Sprintf(".%s", targetFormat)
	baseName := strings.TrimSuffix(originalFilename, filepath.Ext(originalFilename))
	return fmt.Sprintf("%s_converted%s", baseName, ext)
}

// getImageFormat extracts image format from filename
func (s *MediaOptimizationService) getImageFormat(filename string) string {
	ext := strings.ToLower(filepath.Ext(filename))
	switch ext {
	case ".jpg", ".jpeg":
		return "jpeg"
	case ".png":
		return "png"
	case ".gif":
		return "gif"
	case ".webp":
		return "webp"
	default:
		return "unknown"
	}
}

// Helper functions
func boolPtr(b bool) *bool {
	return &b
}

func stringPtr(s string) *string {
	return &s
}
