package services

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories"
	pkgcontext "github.com/tranthanhloi/wn-api-v3/pkg/context"
)

// CDNProvider interface for CDN operations
type CDNProvider interface {
	GetCDNURL(originalURL string) (string, error)
	InvalidateCache(paths []string) error
	GetCacheStatus(path string) (*models.CacheStatus, error)
	PurgeCache(paths []string) error
}

// MediaCDNService handles CDN integration and cache management
type MediaCDNService struct {
	cdnProviders  map[string]CDNProvider
	defaultCDN    string
	fileRepo      repositories.MediaFileRepository
	thumbnailRepo repositories.MediaThumbnailRepository
	logger        *logrus.Logger
}

// NewMediaCDNService creates a new media CDN service
func NewMediaCDNService(
	providers map[string]CDNProvider,
	defaultCDN string,
	fileRepo repositories.MediaFileRepository,
	thumbnailRepo repositories.MediaThumbnailRepository,
	logger *logrus.Logger,
) *MediaCDNService {
	return &MediaCDNService{
		cdnProviders:  providers,
		defaultCDN:    defaultCDN,
		fileRepo:      fileRepo,
		thumbnailRepo: thumbnailRepo,
		logger:        logger,
	}
}

// GenerateCDNURL generates a CDN URL for a media file
func (s *MediaCDNService) GenerateCDNURL(ctx context.Context, fileID uint, cdnProvider string) (string, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return "", fmt.Errorf("tenant context is required")
	}

	// Get file
	file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return "", fmt.Errorf("failed to get file: %w", err)
	}

	// Get CDN provider
	provider := s.getCDNProvider(cdnProvider)
	if provider == nil {
		return "", fmt.Errorf("CDN provider not found: %s", cdnProvider)
	}

	// Generate CDN URL
	cdnURL, err := provider.GetCDNURL(file.URL)
	if err != nil {
		return "", fmt.Errorf("failed to generate CDN URL: %w", err)
	}

	// Update file record with CDN URL
	file.CDNUrl = &cdnURL
	file.CDNProvider = &cdnProvider
	file.UpdatedAt = time.Now()

	err = s.fileRepo.Update(ctx, file)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to update file with CDN URL")
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":    tenantCtx.TenantID,
		"file_id":      fileID,
		"cdn_provider": cdnProvider,
		"cdn_url":      cdnURL,
	}).Info("CDN URL generated successfully")

	return cdnURL, nil
}

// InvalidateFileCache invalidates CDN cache for a file
func (s *MediaCDNService) InvalidateFileCache(ctx context.Context, fileID uint) error {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	// Get file
	file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return fmt.Errorf("failed to get file: %w", err)
	}

	// Get CDN provider
	providerName := s.defaultCDN
	if file.CDNProvider != nil {
		providerName = *file.CDNProvider
	}

	provider := s.getCDNProvider(providerName)
	if provider == nil {
		return fmt.Errorf("CDN provider not found: %s", providerName)
	}

	// Collect paths to invalidate
	paths := []string{file.StoragePath}

	// Add thumbnail paths if it's an image
	if file.MediaType == "image" {
		thumbnails, err := s.thumbnailRepo.GetByFileID(ctx, tenantCtx.TenantID, fileID)
		if err == nil {
			for _, thumbnail := range thumbnails {
				paths = append(paths, thumbnail.StoragePath)
			}
		}
	}

	// Invalidate cache
	err = provider.InvalidateCache(paths)
	if err != nil {
		return fmt.Errorf("failed to invalidate cache: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":    tenantCtx.TenantID,
		"file_id":      fileID,
		"cdn_provider": providerName,
		"paths_count":  len(paths),
	}).Info("Cache invalidated successfully")

	return nil
}

// PurgeFileCache purges CDN cache for a file
func (s *MediaCDNService) PurgeFileCache(ctx context.Context, fileID uint) error {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	// Get file
	file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return fmt.Errorf("failed to get file: %w", err)
	}

	// Get CDN provider
	providerName := s.defaultCDN
	if file.CDNProvider != nil {
		providerName = *file.CDNProvider
	}

	provider := s.getCDNProvider(providerName)
	if provider == nil {
		return fmt.Errorf("CDN provider not found: %s", providerName)
	}

	// Collect paths to purge
	paths := []string{file.StoragePath}

	// Add thumbnail paths if it's an image
	if file.MediaType == "image" {
		thumbnails, err := s.thumbnailRepo.GetByFileID(ctx, tenantCtx.TenantID, fileID)
		if err == nil {
			for _, thumbnail := range thumbnails {
				paths = append(paths, thumbnail.StoragePath)
			}
		}
	}

	// Purge cache
	err = provider.PurgeCache(paths)
	if err != nil {
		return fmt.Errorf("failed to purge cache: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":    tenantCtx.TenantID,
		"file_id":      fileID,
		"cdn_provider": providerName,
		"paths_count":  len(paths),
	}).Info("Cache purged successfully")

	return nil
}

// GetCacheStatus gets cache status for a file
func (s *MediaCDNService) GetCacheStatus(ctx context.Context, fileID uint) (*models.FileCacheStatus, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get file
	file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, fileID)
	if err != nil {
		return nil, fmt.Errorf("failed to get file: %w", err)
	}

	// Get CDN provider
	providerName := s.defaultCDN
	if file.CDNProvider != nil {
		providerName = *file.CDNProvider
	}

	provider := s.getCDNProvider(providerName)
	if provider == nil {
		return nil, fmt.Errorf("CDN provider not found: %s", providerName)
	}

	// Get cache status
	cacheStatus, err := provider.GetCacheStatus(file.StoragePath)
	if err != nil {
		return nil, fmt.Errorf("failed to get cache status: %w", err)
	}

	status := &models.FileCacheStatus{
		FileID:      fileID,
		Path:        file.StoragePath,
		CDNProvider: providerName,
		CacheStatus: cacheStatus,
		CheckedAt:   time.Now(),
	}

	return status, nil
}

// BulkInvalidateCache invalidates cache for multiple files
func (s *MediaCDNService) BulkInvalidateCache(ctx context.Context, fileIDs []uint) error {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	// Group files by CDN provider
	providerFiles := make(map[string][]uint)

	for _, fileID := range fileIDs {
		file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, fileID)
		if err != nil {
			s.logger.WithError(err).WithField("file_id", fileID).Warn("Failed to get file for cache invalidation")
			continue
		}

		providerName := s.defaultCDN
		if file.CDNProvider != nil {
			providerName = *file.CDNProvider
		}

		providerFiles[providerName] = append(providerFiles[providerName], fileID)
	}

	// Invalidate cache for each provider
	for providerName, files := range providerFiles {
		provider := s.getCDNProvider(providerName)
		if provider == nil {
			s.logger.WithField("cdn_provider", providerName).Warn("CDN provider not found")
			continue
		}

		var paths []string
		for _, fileID := range files {
			file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, fileID)
			if err != nil {
				continue
			}
			paths = append(paths, file.StoragePath)
		}

		err := provider.InvalidateCache(paths)
		if err != nil {
			s.logger.WithError(err).WithField("cdn_provider", providerName).Error("Failed to invalidate cache")
		}
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":   tenantCtx.TenantID,
		"files_count": len(fileIDs),
	}).Info("Bulk cache invalidation completed")

	return nil
}

// OptimizeDelivery optimizes content delivery for files
func (s *MediaCDNService) OptimizeDelivery(ctx context.Context, fileIDs []uint) error {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	for _, fileID := range fileIDs {
		file, err := s.fileRepo.GetByID(ctx, tenantCtx.TenantID, fileID)
		if err != nil {
			s.logger.WithError(err).WithField("file_id", fileID).Warn("Failed to get file for optimization")
			continue
		}

		// Generate CDN URL if not exists
		if file.CDNUrl == nil {
			cdnURL, err := s.GenerateCDNURL(ctx, fileID, s.defaultCDN)
			if err != nil {
				s.logger.WithError(err).WithField("file_id", fileID).Warn("Failed to generate CDN URL")
				continue
			}
			file.CDNUrl = &cdnURL
		}

		// Pre-warm cache for popular files
		if s.isPopularFile(file) {
			err = s.prewarmCache(ctx, file)
			if err != nil {
				s.logger.WithError(err).WithField("file_id", fileID).Warn("Failed to prewarm cache")
			}
		}
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":   tenantCtx.TenantID,
		"files_count": len(fileIDs),
	}).Info("Delivery optimization completed")

	return nil
}

// GetCDNStatistics gets CDN usage statistics
func (s *MediaCDNService) GetCDNStatistics(ctx context.Context) (*models.CDNStatistics, error) {
	tenantCtx := pkgcontext.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get files with CDN URLs
	filter := &models.MediaFileFilter{
		HasCDN: boolPtr(true),
		Status: stringPtr("active"),
	}

	files, totalFiles, err := s.fileRepo.List(ctx, tenantCtx.TenantID, filter, 1000, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get files: %w", err)
	}

	// Count by CDN provider
	providerCounts := make(map[string]int)
	var totalSize int64

	for _, file := range files {
		if file.CDNProvider != nil {
			providerCounts[*file.CDNProvider]++
		}
		totalSize += file.FileSize
	}

	stats := &models.CDNStatistics{
		TotalFiles:         totalFiles,
		TotalSize:          totalSize,
		ProviderCounts:     providerCounts,
		AvailableProviders: s.getAvailableProviders(),
		DefaultProvider:    s.defaultCDN,
		GeneratedAt:        time.Now(),
	}

	return stats, nil
}

// SetDefaultCDN sets the default CDN provider
func (s *MediaCDNService) SetDefaultCDN(ctx context.Context, providerName string) error {
	if s.getCDNProvider(providerName) == nil {
		return fmt.Errorf("CDN provider not found: %s", providerName)
	}

	s.defaultCDN = providerName

	s.logger.WithField("cdn_provider", providerName).Info("Default CDN provider updated")

	return nil
}

// Private helper methods

// getCDNProvider gets a CDN provider by name
func (s *MediaCDNService) getCDNProvider(name string) CDNProvider {
	if name == "" {
		name = s.defaultCDN
	}
	return s.cdnProviders[name]
}

// getAvailableProviders returns list of available CDN providers
func (s *MediaCDNService) getAvailableProviders() []string {
	providers := make([]string, 0, len(s.cdnProviders))
	for name := range s.cdnProviders {
		providers = append(providers, name)
	}
	return providers
}

// isPopularFile checks if a file is considered popular
func (s *MediaCDNService) isPopularFile(file *models.MediaFile) bool {
	// Simple heuristic: files uploaded recently or accessed frequently
	// This could be enhanced with actual access statistics
	return time.Since(file.CreatedAt) < 24*time.Hour
}

// prewarmCache preloads content into CDN cache
func (s *MediaCDNService) prewarmCache(ctx context.Context, file *models.MediaFile) error {
	// This would typically make HTTP requests to CDN URLs to warm the cache
	// Implementation depends on specific CDN provider capabilities

	s.logger.WithFields(logrus.Fields{
		"file_id": file.ID,
		"path":    file.StoragePath,
	}).Info("Cache prewarmed for file")

	return nil
}

// Helper functions
func boolPtr(b bool) *bool {
	return &b
}

func stringPtr(s string) *string {
	return &s
}
