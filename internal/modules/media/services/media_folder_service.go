package services

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/context"
)

// MediaFolderService handles media folder operations
type MediaFolderService struct {
	folderRepo repositories.MediaFolderRepository
	fileRepo   repositories.MediaFileRepository
	logger     *logrus.Logger
}

// NewMediaFolderService creates a new media folder service
func NewMediaFolderService(
	folderRepo repositories.MediaFolderRepository,
	fileRepo repositories.MediaFileRepository,
	logger *logrus.Logger,
) *MediaFolderService {
	return &MediaFolderService{
		folderRepo: folderRepo,
		fileRepo:   fileRepo,
		logger:     logger,
	}
}

// CreateFolder creates a new media folder
func (s *MediaFolderService) CreateFolder(ctx context.Context, req *models.CreateFolderRequest) (*models.MediaFolder, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Validate request
	if req.Name == "" {
		return nil, fmt.Errorf("folder name is required")
	}

	// Check if folder name already exists in the same parent
	existing, err := s.folderRepo.GetByNameAndParent(ctx, tenantCtx.TenantID, req.Name, req.ParentID)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("folder with name '%s' already exists in this location", req.Name)
	}

	// Validate parent folder if provided
	var parentPath string
	if req.ParentID != nil {
		parent, err := s.folderRepo.GetByID(ctx, tenantCtx.TenantID, *req.ParentID)
		if err != nil {
			return nil, fmt.Errorf("parent folder not found: %w", err)
		}
		parentPath = parent.Path
	}

	// Generate folder path
	folderPath := s.generateFolderPath(parentPath, req.Name)

	// Create folder
	folder := &models.MediaFolder{
		TenantID:    tenantCtx.TenantID,
		ParentID:    req.ParentID,
		Name:        req.Name,
		Path:        folderPath,
		Description: req.Description,
		Status:      "active",
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	err = s.folderRepo.Create(ctx, folder)
	if err != nil {
		return nil, fmt.Errorf("failed to create folder: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"folder_id": folder.ID,
		"name":      folder.Name,
		"path":      folder.Path,
	}).Info("Media folder created successfully")

	return folder, nil
}

// GetFolder retrieves a media folder by ID
func (s *MediaFolderService) GetFolder(ctx context.Context, id uint) (*models.MediaFolder, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	folder, err := s.folderRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get folder: %w", err)
	}

	return folder, nil
}

// GetFolderWithContents retrieves a folder with its contents (subfolders and files)
func (s *MediaFolderService) GetFolderWithContents(ctx context.Context, id uint) (*models.FolderWithContents, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get folder
	folder, err := s.folderRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get folder: %w", err)
	}

	// Get subfolders
	subfolders, err := s.folderRepo.GetChildren(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get subfolders: %w", err)
	}

	// Get files in folder
	filter := &models.MediaFileFilter{
		FolderID: &id,
		Status:   stringPtr("active"),
	}
	files, _, err := s.fileRepo.List(ctx, tenantCtx.TenantID, filter, 100, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get files: %w", err)
	}

	contents := &models.FolderWithContents{
		Folder:     folder,
		Subfolders: subfolders,
		Files:      files,
	}

	return contents, nil
}

// UpdateFolder updates an existing media folder
func (s *MediaFolderService) UpdateFolder(ctx context.Context, id uint, req *models.UpdateFolderRequest) (*models.MediaFolder, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	folder, err := s.folderRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get folder: %w", err)
	}

	// Update fields
	if req.Name != nil {
		// Check if name already exists in the same parent
		existing, err := s.folderRepo.GetByNameAndParent(ctx, tenantCtx.TenantID, *req.Name, folder.ParentID)
		if err == nil && existing != nil && existing.ID != id {
			return nil, fmt.Errorf("folder with name '%s' already exists in this location", *req.Name)
		}

		folder.Name = *req.Name
		// Update path if name changed
		folder.Path = s.generateFolderPath(s.getParentPath(ctx, folder.ParentID), *req.Name)
	}

	if req.Description != nil {
		folder.Description = req.Description
	}

	if req.Status != nil {
		folder.Status = *req.Status
	}

	folder.UpdatedAt = time.Now()

	err = s.folderRepo.Update(ctx, folder)
	if err != nil {
		return nil, fmt.Errorf("failed to update folder: %w", err)
	}

	// Update paths of all child folders if path changed
	if req.Name != nil {
		err = s.updateChildPaths(ctx, folder)
		if err != nil {
			s.logger.WithError(err).Warn("Failed to update child folder paths")
		}
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"folder_id": id,
	}).Info("Media folder updated successfully")

	return folder, nil
}

// DeleteFolder deletes a media folder
func (s *MediaFolderService) DeleteFolder(ctx context.Context, id uint, force bool) error {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	folder, err := s.folderRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return fmt.Errorf("failed to get folder: %w", err)
	}

	// Check if folder has contents
	if !force {
		hasContents, err := s.folderHasContents(ctx, tenantCtx.TenantID, id)
		if err != nil {
			return fmt.Errorf("failed to check folder contents: %w", err)
		}
		if hasContents {
			return fmt.Errorf("folder is not empty, use force=true to delete")
		}
	}

	// Delete contents if force is true
	if force {
		err = s.deleteAllContents(ctx, tenantCtx.TenantID, id)
		if err != nil {
			return fmt.Errorf("failed to delete folder contents: %w", err)
		}
	}

	// Soft delete folder
	folder.Status = "deleted"
	folder.UpdatedAt = time.Now()

	err = s.folderRepo.Update(ctx, folder)
	if err != nil {
		return fmt.Errorf("failed to delete folder: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id": tenantCtx.TenantID,
		"folder_id": id,
		"force":     force,
	}).Info("Media folder deleted successfully")

	return nil
}

// ListFolders lists media folders with filtering and pagination
func (s *MediaFolderService) ListFolders(ctx context.Context, filter *models.FolderFilter, limit, offset int) ([]*models.MediaFolder, int, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, 0, fmt.Errorf("tenant context is required")
	}

	if limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	folders, total, err := s.folderRepo.List(ctx, tenantCtx.TenantID, filter, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list folders: %w", err)
	}

	return folders, total, nil
}

// GetRootFolders gets root level folders
func (s *MediaFolderService) GetRootFolders(ctx context.Context) ([]*models.MediaFolder, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	folders, err := s.folderRepo.GetRootFolders(ctx, tenantCtx.TenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get root folders: %w", err)
	}

	return folders, nil
}

// GetFolderTree gets the complete folder tree structure
func (s *MediaFolderService) GetFolderTree(ctx context.Context) ([]*models.FolderTreeNode, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	// Get all folders
	filter := &models.FolderFilter{
		Status: stringPtr("active"),
	}
	folders, _, err := s.folderRepo.List(ctx, tenantCtx.TenantID, filter, 1000, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get folders: %w", err)
	}

	// Build tree structure
	tree := s.buildFolderTree(folders)

	return tree, nil
}

// MoveFolder moves a folder to a different parent
func (s *MediaFolderService) MoveFolder(ctx context.Context, id uint, newParentID *uint) error {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	folder, err := s.folderRepo.GetByID(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return fmt.Errorf("failed to get folder: %w", err)
	}

	// Validate new parent
	if newParentID != nil {
		// Check if new parent exists
		_, err = s.folderRepo.GetByID(ctx, tenantCtx.TenantID, *newParentID)
		if err != nil {
			return fmt.Errorf("target parent folder not found: %w", err)
		}

		// Check for circular reference
		if s.wouldCreateCircularReference(ctx, tenantCtx.TenantID, id, *newParentID) {
			return fmt.Errorf("cannot move folder: would create circular reference")
		}

		// Check if folder with same name already exists in new parent
		existing, err := s.folderRepo.GetByNameAndParent(ctx, tenantCtx.TenantID, folder.Name, newParentID)
		if err == nil && existing != nil {
			return fmt.Errorf("folder with name '%s' already exists in target location", folder.Name)
		}
	}

	// Update folder
	folder.ParentID = newParentID
	folder.Path = s.generateFolderPath(s.getParentPath(ctx, newParentID), folder.Name)
	folder.UpdatedAt = time.Now()

	err = s.folderRepo.Update(ctx, folder)
	if err != nil {
		return fmt.Errorf("failed to move folder: %w", err)
	}

	// Update paths of all child folders
	err = s.updateChildPaths(ctx, folder)
	if err != nil {
		s.logger.WithError(err).Warn("Failed to update child folder paths after move")
	}

	s.logger.WithFields(logrus.Fields{
		"tenant_id":     tenantCtx.TenantID,
		"folder_id":     id,
		"new_parent_id": newParentID,
	}).Info("Media folder moved successfully")

	return nil
}

// GetFolderStatistics gets folder statistics
func (s *MediaFolderService) GetFolderStatistics(ctx context.Context, id uint) (*models.FolderStatistics, error) {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return nil, fmt.Errorf("tenant context is required")
	}

	stats, err := s.folderRepo.GetStatistics(ctx, tenantCtx.TenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get folder statistics: %w", err)
	}

	return stats, nil
}

// Private helper methods

// generateFolderPath generates the full path for a folder
func (s *MediaFolderService) generateFolderPath(parentPath, name string) string {
	if parentPath == "" {
		return name
	}
	return parentPath + "/" + name
}

// getParentPath gets the path of the parent folder
func (s *MediaFolderService) getParentPath(ctx context.Context, parentID *uint) string {
	if parentID == nil {
		return ""
	}

	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return ""
	}

	parent, err := s.folderRepo.GetByID(ctx, tenantCtx.TenantID, *parentID)
	if err != nil {
		return ""
	}

	return parent.Path
}

// updateChildPaths updates the paths of all child folders
func (s *MediaFolderService) updateChildPaths(ctx context.Context, folder *models.MediaFolder) error {
	tenantCtx := context.GetTenantContext(ctx)
	if tenantCtx == nil {
		return fmt.Errorf("tenant context is required")
	}

	children, err := s.folderRepo.GetChildren(ctx, tenantCtx.TenantID, folder.ID)
	if err != nil {
		return err
	}

	for _, child := range children {
		child.Path = s.generateFolderPath(folder.Path, child.Name)
		child.UpdatedAt = time.Now()

		err = s.folderRepo.Update(ctx, child)
		if err != nil {
			s.logger.WithError(err).WithField("folder_id", child.ID).Error("Failed to update child folder path")
			continue
		}

		// Recursively update grandchildren
		err = s.updateChildPaths(ctx, child)
		if err != nil {
			s.logger.WithError(err).WithField("folder_id", child.ID).Error("Failed to update grandchild folder paths")
		}
	}

	return nil
}

// folderHasContents checks if a folder has subfolders or files
func (s *MediaFolderService) folderHasContents(ctx context.Context, tenantID, folderID uint) (bool, error) {
	// Check subfolders
	subfolders, err := s.folderRepo.GetChildren(ctx, tenantID, folderID)
	if err != nil {
		return false, err
	}
	if len(subfolders) > 0 {
		return true, nil
	}

	// Check files
	filter := &models.MediaFileFilter{
		FolderID: &folderID,
		Status:   stringPtr("active"),
	}
	files, _, err := s.fileRepo.List(ctx, tenantID, filter, 1, 0)
	if err != nil {
		return false, err
	}

	return len(files) > 0, nil
}

// deleteAllContents deletes all contents of a folder
func (s *MediaFolderService) deleteAllContents(ctx context.Context, tenantID, folderID uint) error {
	// Delete all subfolders
	children, err := s.folderRepo.GetChildren(ctx, tenantID, folderID)
	if err != nil {
		return err
	}

	for _, child := range children {
		err = s.DeleteFolder(ctx, child.ID, true)
		if err != nil {
			s.logger.WithError(err).WithField("folder_id", child.ID).Error("Failed to delete child folder")
		}
	}

	// Delete all files
	filter := &models.MediaFileFilter{
		FolderID: &folderID,
		Status:   stringPtr("active"),
	}
	files, _, err := s.fileRepo.List(ctx, tenantID, filter, 1000, 0)
	if err != nil {
		return err
	}

	for _, file := range files {
		file.Status = "deleted"
		file.UpdatedAt = time.Now()
		err = s.fileRepo.Update(ctx, file)
		if err != nil {
			s.logger.WithError(err).WithField("file_id", file.ID).Error("Failed to delete file")
		}
	}

	return nil
}

// wouldCreateCircularReference checks if moving a folder would create a circular reference
func (s *MediaFolderService) wouldCreateCircularReference(ctx context.Context, tenantID, folderID, newParentID uint) bool {
	// Check if newParentID is a descendant of folderID
	return s.isDescendant(ctx, tenantID, folderID, newParentID)
}

// isDescendant checks if candidateID is a descendant of ancestorID
func (s *MediaFolderService) isDescendant(ctx context.Context, tenantID, ancestorID, candidateID uint) bool {
	if ancestorID == candidateID {
		return true
	}

	candidate, err := s.folderRepo.GetByID(ctx, tenantID, candidateID)
	if err != nil || candidate.ParentID == nil {
		return false
	}

	return s.isDescendant(ctx, tenantID, ancestorID, *candidate.ParentID)
}

// buildFolderTree builds a hierarchical tree structure from flat folder list
func (s *MediaFolderService) buildFolderTree(folders []*models.MediaFolder) []*models.FolderTreeNode {
	// Create a map for quick lookup
	folderMap := make(map[uint]*models.MediaFolder)
	for _, folder := range folders {
		folderMap[folder.ID] = folder
	}

	// Build tree nodes
	nodeMap := make(map[uint]*models.FolderTreeNode)
	var rootNodes []*models.FolderTreeNode

	for _, folder := range folders {
		node := &models.FolderTreeNode{
			Folder:   folder,
			Children: make([]*models.FolderTreeNode, 0),
		}
		nodeMap[folder.ID] = node

		if folder.ParentID == nil {
			rootNodes = append(rootNodes, node)
		}
	}

	// Link parent-child relationships
	for _, folder := range folders {
		if folder.ParentID != nil {
			parentNode, exists := nodeMap[*folder.ParentID]
			if exists {
				childNode := nodeMap[folder.ID]
				parentNode.Children = append(parentNode.Children, childNode)
			}
		}
	}

	return rootNodes
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}
