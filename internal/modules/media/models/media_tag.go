package models

import (
	"time"

	"gorm.io/gorm"
)

// MediaTag represents a tag for categorizing media files
type MediaTag struct {
	ID        uint `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID  uint `json:"tenant_id" gorm:"not null;index"`
	WebsiteID uint `json:"website_id" gorm:"not null;index"`
	UserID    uint `json:"user_id" gorm:"not null;index"`

	// Tag information
	Name        string  `json:"name" gorm:"size:100;not null;index"`
	Slug        string  `json:"slug" gorm:"size:100;not null;index"`
	Description *string `json:"description,omitempty" gorm:"type:text"`

	// Tag appearance
	Color string  `json:"color" gorm:"size:7;default:'#3b82f6'"`
	Icon  *string `json:"icon,omitempty" gorm:"size:50"`

	// Tag categorization
	Category string  `json:"category" gorm:"size:50;default:'general'"`
	Type     TagType `json:"type" gorm:"type:enum('user','system','auto');not null;default:'user'"`

	// Usage statistics
	UsageCount      uint    `json:"usage_count" gorm:"default:0"`
	PopularityScore float64 `json:"popularity_score" gorm:"default:0.0"`

	// Tag settings
	IsFeatured bool                   `json:"is_featured" gorm:"default:false"`
	IsPrivate  bool                   `json:"is_private" gorm:"default:false"`
	Settings   map[string]interface{} `json:"settings" gorm:"type:json"`

	// Status and timestamps
	Status    TagStatus `json:"status" gorm:"type:enum('active','archived','deleted');not null;default:'active'"`
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Creator  User           `json:"creator,omitempty" gorm:"foreignKey:UserID"`
	Tenant   Tenant         `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	Website  Website        `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
	Files    []MediaFile    `json:"files,omitempty" gorm:"many2many:media_file_tags"`
	FileTags []MediaFileTag `json:"file_tags,omitempty" gorm:"foreignKey:TagID"`
}

// TagUsageStats represents tag usage statistics
type TagUsageStats struct {
	TagID     uint   `json:"tag_id"`
	TagName   string `json:"tag_name"`
	FileCount uint   `json:"file_count"`
}

// TagType defines the type of tag
// @Enum user,system,auto
type TagType string

const (
	TagTypeUser   TagType = "user"
	TagTypeSystem TagType = "system"
	TagTypeAuto   TagType = "auto"
)

// TagStatus defines the status of tag
// @Enum active,archived,deleted
type TagStatus string

const (
	TagStatusActive   TagStatus = "active"
	TagStatusArchived TagStatus = "archived"
	TagStatusDeleted  TagStatus = "deleted"
)

// TableName returns the table name for MediaTag
func (MediaTag) TableName() string {
	return "media_tags"
}

// BeforeCreate hook for GORM
func (mt *MediaTag) BeforeCreate(tx *gorm.DB) error {
	if mt.Settings == nil {
		mt.Settings = make(map[string]interface{})
	}
	return nil
}

// BeforeUpdate hook for GORM
func (mt *MediaTag) BeforeUpdate(tx *gorm.DB) error {
	if mt.Settings == nil {
		mt.Settings = make(map[string]interface{})
	}
	return nil
}

// IsSystemTag returns true if this is a system tag
func (mt *MediaTag) IsSystemTag() bool {
	return mt.Type == TagTypeSystem
}

// IsAutoTag returns true if this is an auto-generated tag
func (mt *MediaTag) IsAutoTag() bool {
	return mt.Type == TagTypeAuto
}

// IsUserTag returns true if this is a user-created tag
func (mt *MediaTag) IsUserTag() bool {
	return mt.Type == TagTypeUser
}

// CanEdit checks if a user can edit this tag
func (mt *MediaTag) CanEdit(userID uint) bool {
	if mt.Type == TagTypeSystem {
		return false
	}

	if mt.UserID == userID {
		return true
	}

	// Additional permission checks can be added here
	return false
}

// CanDelete checks if a user can delete this tag
func (mt *MediaTag) CanDelete(userID uint) bool {
	if mt.Type == TagTypeSystem {
		return false
	}

	if mt.UserID == userID {
		return true
	}

	// Additional permission checks can be added here
	return false
}
