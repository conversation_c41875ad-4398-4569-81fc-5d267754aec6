package models

import (
	"time"

	"gorm.io/gorm"
)

// MediaFile represents a media file in the system
type MediaFile struct {
	ID        uint  `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID  uint  `json:"tenant_id" gorm:"not null;index"`
	WebsiteID uint  `json:"website_id" gorm:"not null;index"`
	FolderID  *uint `json:"folder_id,omitempty" gorm:"index"`
	UserID    uint  `json:"user_id" gorm:"not null;index"`

	// File information
	Filename         string `json:"filename" gorm:"size:255;not null;index"`
	OriginalFilename string `json:"original_filename" gorm:"size:255;not null"`
	Slug             string `json:"slug" gorm:"size:255;not null;index"`
	MimeType         string `json:"mime_type" gorm:"size:100;not null;index"`
	FileSize         uint64 `json:"file_size" gorm:"not null"`
	FileHash         string `json:"file_hash" gorm:"size:64;not null;index"`

	// Storage information
	StorageType     StorageType `json:"storage_type" gorm:"type:enum('local','minio','s3','gcs');not null;default:'local'"`
	StoragePath     string      `json:"storage_path" gorm:"size:500;not null"`
	PublicURL       string      `json:"public_url" gorm:"size:500;not null"`
	StorageProvider *string     `json:"storage_provider,omitempty" gorm:"size:100"`

	// CDN information
	CDNUrl      *string `json:"cdn_url,omitempty" gorm:"size:500"`
	CDNProvider *string `json:"cdn_provider,omitempty" gorm:"size:100"`

	// Optimization information
	OptimizedPath *string    `json:"optimized_path,omitempty" gorm:"size:500"`
	OptimizedSize *int64     `json:"optimized_size,omitempty"`
	OptimizedURL  *string    `json:"optimized_url,omitempty" gorm:"size:500"`
	OptimizedAt   *time.Time `json:"optimized_at,omitempty"`

	// Media metadata
	Width    *uint                  `json:"width,omitempty"`
	Height   *uint                  `json:"height,omitempty"`
	Duration *uint                  `json:"duration,omitempty"` // in seconds for video/audio
	Metadata map[string]interface{} `json:"metadata" gorm:"type:json"`

	// File categorization
	FileType FileType `json:"file_type" gorm:"type:enum('image','video','audio','document','archive','other');not null"`
	Category string   `json:"category" gorm:"size:100;default:'general'"`

	// SEO and accessibility
	AltText     *string `json:"alt_text,omitempty" gorm:"size:255"`
	Title       *string `json:"title,omitempty" gorm:"size:255"`
	Description *string `json:"description,omitempty" gorm:"type:text"`

	// Access control
	Visibility        FileVisibility     `json:"visibility" gorm:"type:enum('public','private','shared');not null;default:'public'"`
	AccessPermissions []AccessPermission `json:"access_permissions" gorm:"type:json"`

	// Usage tracking
	ViewCount      uint       `json:"view_count" gorm:"default:0"`
	DownloadCount  uint       `json:"download_count" gorm:"default:0"`
	LastAccessedAt *time.Time `json:"last_accessed_at,omitempty"`

	// Processing status
	Status           FileStatus             `json:"status" gorm:"type:enum('uploading','processing','ready','error','deleted');not null;default:'uploading'"`
	ProcessingStatus map[string]interface{} `json:"processing_status" gorm:"type:json"`
	ErrorMessage     *string                `json:"error_message,omitempty" gorm:"type:text"`

	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Folder     *MediaFolder     `json:"folder,omitempty" gorm:"foreignKey:FolderID"`
	Uploader   User             `json:"uploader,omitempty" gorm:"foreignKey:UserID"`
	Tenant     Tenant           `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	Website    Website          `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
	Thumbnails []MediaThumbnail `json:"thumbnails,omitempty" gorm:"foreignKey:FileID"`
	Tags       []MediaTag       `json:"tags,omitempty" gorm:"many2many:media_file_tags"`
}

// StorageType defines the storage type for files
// @Enum local,minio,s3,gcs
type StorageType string

const (
	StorageTypeLocal StorageType = "local"
	StorageTypeMinio StorageType = "minio"
	StorageTypeS3    StorageType = "s3"
	StorageTypeGCS   StorageType = "gcs"
)

// FileType defines the type of file
// @Enum image,video,audio,document,archive,other
type FileType string

const (
	FileTypeImage    FileType = "image"
	FileTypeVideo    FileType = "video"
	FileTypeAudio    FileType = "audio"
	FileTypeDocument FileType = "document"
	FileTypeArchive  FileType = "archive"
	FileTypeOther    FileType = "other"
)

// FileVisibility defines the visibility of file
// @Enum public,private,shared
type FileVisibility string

const (
	FileVisibilityPublic  FileVisibility = "public"
	FileVisibilityPrivate FileVisibility = "private"
	FileVisibilityShared  FileVisibility = "shared"
)

// FileStatus defines the status of file processing
// @Enum uploading,processing,ready,error,deleted
type FileStatus string

const (
	FileStatusUploading  FileStatus = "uploading"
	FileStatusProcessing FileStatus = "processing"
	FileStatusReady      FileStatus = "ready"
	FileStatusError      FileStatus = "error"
	FileStatusDeleted    FileStatus = "deleted"
)

// TableName returns the table name for MediaFile
func (MediaFile) TableName() string {
	return "media_files"
}

// BeforeCreate hook for GORM
func (mf *MediaFile) BeforeCreate(tx *gorm.DB) error {
	if mf.Metadata == nil {
		mf.Metadata = make(map[string]interface{})
	}
	if mf.ProcessingStatus == nil {
		mf.ProcessingStatus = make(map[string]interface{})
	}
	return nil
}

// BeforeUpdate hook for GORM
func (mf *MediaFile) BeforeUpdate(tx *gorm.DB) error {
	if mf.Metadata == nil {
		mf.Metadata = make(map[string]interface{})
	}
	if mf.ProcessingStatus == nil {
		mf.ProcessingStatus = make(map[string]interface{})
	}
	return nil
}

// IsImage returns true if the file is an image
func (mf *MediaFile) IsImage() bool {
	return mf.FileType == FileTypeImage
}

// IsVideo returns true if the file is a video
func (mf *MediaFile) IsVideo() bool {
	return mf.FileType == FileTypeVideo
}

// IsAudio returns true if the file is an audio
func (mf *MediaFile) IsAudio() bool {
	return mf.FileType == FileTypeAudio
}

// IsDocument returns true if the file is a document
func (mf *MediaFile) IsDocument() bool {
	return mf.FileType == FileTypeDocument
}

// HasThumbnails returns true if the file can have thumbnails
func (mf *MediaFile) HasThumbnails() bool {
	return mf.FileType == FileTypeImage || mf.FileType == FileTypeVideo
}

// CanAccess checks if a user can access this file
func (mf *MediaFile) CanAccess(userID uint) bool {
	if mf.Visibility == FileVisibilityPublic {
		return true
	}

	if mf.UserID == userID {
		return true
	}

	// Check access permissions
	for _, perm := range mf.AccessPermissions {
		if perm.UserID == userID {
			return true
		}
	}

	return false
}

// GetFileExtension returns the file extension
func (mf *MediaFile) GetFileExtension() string {
	if len(mf.Filename) > 0 {
		for i := len(mf.Filename) - 1; i >= 0; i-- {
			if mf.Filename[i] == '.' {
				return mf.Filename[i:]
			}
		}
	}
	return ""
}
