package models

import (
	"time"

	"gorm.io/gorm"
)

// MediaFolder represents a media folder for organizing media files
type MediaFolder struct {
	ID        uint  `json:"id" gorm:"primaryKey;autoIncrement"`
	TenantID  uint  `json:"tenant_id" gorm:"not null;index"`
	WebsiteID uint  `json:"website_id" gorm:"not null;index"`
	ParentID  *uint `json:"parent_id,omitempty" gorm:"index"`
	UserID    uint  `json:"user_id" gorm:"not null;index"`

	// Folder information
	Name        string  `json:"name" gorm:"size:255;not null"`
	Slug        string  `json:"slug" gorm:"size:255;not null;index"`
	Path        string  `json:"path" gorm:"size:500;not null;index"`
	Description *string `json:"description,omitempty" gorm:"type:text"`

	// Folder type and settings
	Type  FolderType `json:"type" gorm:"type:enum('user','system','public','private','shared');not null;default:'user'"`
	Color string     `json:"color" gorm:"size:7;default:'#6b7280'"`
	Icon  string     `json:"icon" gorm:"size:50;default:'folder'"`

	// Access control
	Visibility        FolderVisibility   `json:"visibility" gorm:"type:enum('public','private','shared');not null;default:'public'"`
	AccessPermissions []AccessPermission `json:"access_permissions" gorm:"type:json"`

	// Nested set model for hierarchy
	Lft   uint `json:"lft" gorm:"not null;default:1"`
	Rgt   uint `json:"rgt" gorm:"not null;default:2"`
	Level uint `json:"level" gorm:"not null;default:0"`

	// Folder settings
	SortOrder uint                   `json:"sort_order" gorm:"default:0"`
	Settings  map[string]interface{} `json:"settings" gorm:"type:json"`

	// Usage statistics
	FileCount uint   `json:"file_count" gorm:"default:0"`
	TotalSize uint64 `json:"total_size" gorm:"default:0"`

	// Status and timestamps
	Status    FolderStatus `json:"status" gorm:"type:enum('active','archived','deleted');not null;default:'active'"`
	CreatedAt time.Time    `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time    `json:"updated_at" gorm:"autoUpdateTime"`

	// Relationships
	Parent   *MediaFolder  `json:"parent,omitempty" gorm:"foreignKey:ParentID"`
	Children []MediaFolder `json:"children,omitempty" gorm:"foreignKey:ParentID"`
	Files    []MediaFile   `json:"files,omitempty" gorm:"foreignKey:FolderID"`
	Creator  User          `json:"creator,omitempty" gorm:"foreignKey:UserID"`
	Tenant   Tenant        `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	Website  Website       `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
}

// FolderType defines the type of folder
type FolderType string

const (
	FolderTypeUser    FolderType = "user"
	FolderTypeSystem  FolderType = "system"
	FolderTypePublic  FolderType = "public"
	FolderTypePrivate FolderType = "private"
	FolderTypeShared  FolderType = "shared"
)

// FolderVisibility defines the visibility of folder
type FolderVisibility string

const (
	FolderVisibilityPublic  FolderVisibility = "public"
	FolderVisibilityPrivate FolderVisibility = "private"
	FolderVisibilityShared  FolderVisibility = "shared"
)

// FolderStatus defines the status of folder
type FolderStatus string

const (
	FolderStatusActive   FolderStatus = "active"
	FolderStatusArchived FolderStatus = "archived"
	FolderStatusDeleted  FolderStatus = "deleted"
)

// AccessPermission defines access permissions for folders
type AccessPermission struct {
	UserID     uint   `json:"user_id"`
	Permission string `json:"permission"` // read, write, delete
}

// TableName returns the table name for MediaFolder
func (MediaFolder) TableName() string {
	return "media_folders"
}

// BeforeCreate hook for GORM
func (mf *MediaFolder) BeforeCreate(tx *gorm.DB) error {
	if mf.Settings == nil {
		mf.Settings = make(map[string]interface{})
	}
	return nil
}

// BeforeUpdate hook for GORM
func (mf *MediaFolder) BeforeUpdate(tx *gorm.DB) error {
	if mf.Settings == nil {
		mf.Settings = make(map[string]interface{})
	}
	return nil
}

// IsRoot returns true if this is a root folder (no parent)
func (mf *MediaFolder) IsRoot() bool {
	return mf.ParentID == nil
}

// GetFullPath returns the full path of the folder
func (mf *MediaFolder) GetFullPath() string {
	return mf.Path
}

// CanAccess checks if a user can access this folder
func (mf *MediaFolder) CanAccess(userID uint) bool {
	if mf.Visibility == FolderVisibilityPublic {
		return true
	}

	if mf.UserID == userID {
		return true
	}

	// Check access permissions
	for _, perm := range mf.AccessPermissions {
		if perm.UserID == userID {
			return true
		}
	}

	return false
}
