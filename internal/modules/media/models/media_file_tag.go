package models

import (
	"time"

	"gorm.io/gorm"
)

// MediaFileTag represents the many-to-many relationship between files and tags
type MediaFileTag struct {
	ID     uint `json:"id" gorm:"primaryKey;autoIncrement"`
	FileID uint `json:"file_id" gorm:"not null;index"`
	TagID  uint `json:"tag_id" gorm:"not null;index"`
	UserID uint `json:"user_id" gorm:"not null;index"`

	// Tagging information
	TaggedAt        time.Time `json:"tagged_at" gorm:"autoCreateTime"`
	TagSource       TagSource `json:"tag_source" gorm:"type:enum('manual','auto','ai','import');not null;default:'manual'"`
	ConfidenceScore float64   `json:"confidence_score" gorm:"default:1.0"` // For AI-generated tags (0.0 to 1.0)

	// Tag metadata
	TagContext map[string]interface{} `json:"tag_context" gorm:"type:json"`

	// Relationships
	File   MediaFile `json:"file,omitempty" gorm:"foreignKey:FileID"`
	Tag    MediaTag  `json:"tag,omitempty" gorm:"foreignKey:TagID"`
	Tagger User      `json:"tagger,omitempty" gorm:"foreignKey:UserID"`
}

// TagSource defines how a tag was applied to a file
type TagSource string

const (
	TagSourceManual TagSource = "manual"
	TagSourceAuto   TagSource = "auto"
	TagSourceAI     TagSource = "ai"
	TagSourceImport TagSource = "import"
)

// TableName returns the table name for MediaFileTag
func (MediaFileTag) TableName() string {
	return "media_file_tags"
}

// BeforeCreate hook for GORM
func (mft *MediaFileTag) BeforeCreate(tx *gorm.DB) error {
	if mft.TagContext == nil {
		mft.TagContext = make(map[string]interface{})
	}
	return nil
}

// BeforeUpdate hook for GORM
func (mft *MediaFileTag) BeforeUpdate(tx *gorm.DB) error {
	if mft.TagContext == nil {
		mft.TagContext = make(map[string]interface{})
	}
	return nil
}

// IsManualTag returns true if this tag was manually applied
func (mft *MediaFileTag) IsManualTag() bool {
	return mft.TagSource == TagSourceManual
}

// IsAutoTag returns true if this tag was auto-generated
func (mft *MediaFileTag) IsAutoTag() bool {
	return mft.TagSource == TagSourceAuto || mft.TagSource == TagSourceAI
}

// IsAITag returns true if this tag was generated by AI
func (mft *MediaFileTag) IsAITag() bool {
	return mft.TagSource == TagSourceAI
}

// IsImportedTag returns true if this tag was imported
func (mft *MediaFileTag) IsImportedTag() bool {
	return mft.TagSource == TagSourceImport
}

// GetConfidenceLevel returns a string representation of confidence level
func (mft *MediaFileTag) GetConfidenceLevel() string {
	if mft.ConfidenceScore >= 0.9 {
		return "high"
	} else if mft.ConfidenceScore >= 0.7 {
		return "medium"
	} else if mft.ConfidenceScore >= 0.5 {
		return "low"
	}
	return "very_low"
}

// IsHighConfidence returns true if the confidence score is high
func (mft *MediaFileTag) IsHighConfidence() bool {
	return mft.ConfidenceScore >= 0.8
}
