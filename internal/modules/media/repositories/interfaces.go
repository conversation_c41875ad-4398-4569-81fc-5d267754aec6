package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// MediaFolderRepository defines the interface for media folder operations
type MediaFolderRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, folder *models.MediaFolder) error
	GetByID(ctx context.Context, id uint) (*models.MediaFolder, error)
	GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.MediaFolder, error)
	Update(ctx context.Context, folder *models.MediaFolder) error
	Delete(ctx context.Context, id uint) error
	SoftDelete(ctx context.Context, id uint) error

	// Folder hierarchy operations
	GetRootFolders(ctx context.Context, tenantID, websiteID uint) ([]models.MediaFolder, error)
	GetChildFolders(ctx context.Context, parentID uint) ([]models.MediaFolder, error)
	GetFolderPath(ctx context.Context, folderID uint) ([]models.MediaFolder, error)
	GetFolderTree(ctx context.Context, tenantID, websiteID uint) ([]models.MediaFolder, error)
	MoveFolderTo(ctx context.Context, folderID, newParentID uint) error

	// Search and filtering
	Search(ctx context.Context, req *dto.MediaFolderSearchRequest) ([]models.MediaFolder, int64, error)
	GetFoldersByUser(ctx context.Context, userID uint, page, pageSize int) ([]models.MediaFolder, int64, error)
	GetFoldersByStatus(ctx context.Context, tenantID, websiteID uint, status models.FolderStatus) ([]models.MediaFolder, error)

	// Statistics
	GetFolderStats(ctx context.Context, tenantID, websiteID uint) (*dto.MediaFolderStatsResponse, error)
	UpdateFolderStats(ctx context.Context, folderID uint) error

	// Bulk operations
	BulkDelete(ctx context.Context, folderIDs []uint) error
	BulkUpdateStatus(ctx context.Context, folderIDs []uint, status models.FolderStatus) error
}

// MediaFileRepository defines the interface for media file operations
type MediaFileRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, file *models.MediaFile) error
	GetByID(ctx context.Context, id uint) (*models.MediaFile, error)
	GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.MediaFile, error)
	GetByHash(ctx context.Context, tenantID, websiteID uint, hash string) (*models.MediaFile, error)
	Update(ctx context.Context, file *models.MediaFile) error
	Delete(ctx context.Context, id uint) error
	SoftDelete(ctx context.Context, id uint) error

	// File queries
	GetFilesByFolder(ctx context.Context, folderID uint, page, pageSize int) ([]models.MediaFile, int64, error)
	GetFilesByUser(ctx context.Context, userID uint, page, pageSize int) ([]models.MediaFile, int64, error)
	GetFilesByType(ctx context.Context, tenantID, websiteID uint, fileType models.FileType, page, pageSize int) ([]models.MediaFile, int64, error)
	GetFilesByStatus(ctx context.Context, tenantID, websiteID uint, status models.FileStatus) ([]models.MediaFile, error)

	// Search and filtering
	Search(ctx context.Context, req *dto.MediaFileSearchRequest) ([]models.MediaFile, int64, error)
	SearchWithCursor(ctx context.Context, req *dto.MediaFileSearchRequest, cursorReq *pagination.CursorRequest) ([]models.MediaFile, *pagination.CursorResponse, error)
	SearchByTags(ctx context.Context, tenantID, websiteID uint, tagIDs []uint, page, pageSize int) ([]models.MediaFile, int64, error)
	GetSimilarFiles(ctx context.Context, fileID uint, minSimilarity float64, limit int) ([]models.MediaFile, error)

	// File operations
	MoveFileTo(ctx context.Context, fileID, newFolderID uint) error
	IncrementViewCount(ctx context.Context, fileID uint) error
	IncrementDownloadCount(ctx context.Context, fileID uint) error
	UpdateLastAccessed(ctx context.Context, fileID uint) error

	// Statistics
	GetFileStats(ctx context.Context, tenantID, websiteID uint) (*dto.MediaFileStatsResponse, error)
	GetStorageUsage(ctx context.Context, tenantID, websiteID uint) (uint64, error)
	GetFileCountByType(ctx context.Context, tenantID, websiteID uint) (map[string]uint, error)

	// Bulk operations
	BulkDelete(ctx context.Context, fileIDs []uint) error
	BulkUpdateStatus(ctx context.Context, fileIDs []uint, status models.FileStatus) error
	BulkMoveFiles(ctx context.Context, fileIDs []uint, newFolderID uint) error
	BulkUpdateMetadata(ctx context.Context, fileIDs []uint, metadata map[string]interface{}) error

	// Duplicate detection
	GetDuplicateFiles(ctx context.Context, tenantID, websiteID uint) ([]models.MediaFile, error)
	GetFilesByHashes(ctx context.Context, tenantID, websiteID uint, hashes []string) ([]models.MediaFile, error)
}

// MediaThumbnailRepository defines the interface for media thumbnail operations
type MediaThumbnailRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, thumbnail *models.MediaThumbnail) error
	GetByID(ctx context.Context, id uint) (*models.MediaThumbnail, error)
	GetByFileAndSize(ctx context.Context, fileID uint, sizeName string) (*models.MediaThumbnail, error)
	Update(ctx context.Context, thumbnail *models.MediaThumbnail) error
	Delete(ctx context.Context, id uint) error

	// Thumbnail queries
	GetThumbnailsByFile(ctx context.Context, fileID uint) ([]models.MediaThumbnail, error)
	GetThumbnailsBySize(ctx context.Context, sizeName string, page, pageSize int) ([]models.MediaThumbnail, int64, error)
	GetThumbnailsByStatus(ctx context.Context, status models.ThumbnailStatus) ([]models.MediaThumbnail, error)

	// Thumbnail operations
	CreateThumbnailsForFile(ctx context.Context, fileID uint, sizes []dto.ThumbnailSizeRequest) error
	RegenerateThumbnails(ctx context.Context, fileID uint) error
	UpdateThumbnailStatus(ctx context.Context, thumbnailID uint, status models.ThumbnailStatus) error

	// Statistics
	GetThumbnailStats(ctx context.Context, tenantID, websiteID uint) (*dto.ThumbnailStatsResponse, error)
	GetThumbnailStorageUsage(ctx context.Context, tenantID, websiteID uint) (uint64, error)

	// Bulk operations
	BulkDelete(ctx context.Context, thumbnailIDs []uint) error
	BulkUpdateStatus(ctx context.Context, thumbnailIDs []uint, status models.ThumbnailStatus) error
	BulkRegenerateThumbnails(ctx context.Context, fileIDs []uint) error

	// Cleanup operations
	CleanupOrphanedThumbnails(ctx context.Context) error
	CleanupFailedThumbnails(ctx context.Context, olderThan time.Time) error
}

// MediaTagRepository defines the interface for media tag operations
type MediaTagRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, tag *models.MediaTag) error
	GetByID(ctx context.Context, id uint) (*models.MediaTag, error)
	GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.MediaTag, error)
	GetByName(ctx context.Context, tenantID, websiteID uint, name string) (*models.MediaTag, error)
	Update(ctx context.Context, tag *models.MediaTag) error
	Delete(ctx context.Context, id uint) error
	SoftDelete(ctx context.Context, id uint) error

	// Tag queries
	GetTagsByUser(ctx context.Context, userID uint, page, pageSize int) ([]models.MediaTag, int64, error)
	GetTagsByType(ctx context.Context, tenantID, websiteID uint, tagType models.TagType) ([]models.MediaTag, error)
	GetTagsByCategory(ctx context.Context, tenantID, websiteID uint, category string) ([]models.MediaTag, error)
	GetTagsByStatus(ctx context.Context, tenantID, websiteID uint, status models.TagStatus) ([]models.MediaTag, error)

	// Search and filtering
	Search(ctx context.Context, req *dto.MediaTagSearchRequest) ([]models.MediaTag, int64, error)
	SearchByName(ctx context.Context, tenantID, websiteID uint, query string, limit int) ([]models.MediaTag, error)
	GetPopularTags(ctx context.Context, tenantID, websiteID uint, limit int) ([]models.MediaTag, error)
	GetFeaturedTags(ctx context.Context, tenantID, websiteID uint) ([]models.MediaTag, error)

	// Tag usage
	IncrementUsageCount(ctx context.Context, tagID uint) error
	DecrementUsageCount(ctx context.Context, tagID uint) error
	UpdatePopularityScore(ctx context.Context, tagID uint, score float64) error
	GetTagUsageStats(ctx context.Context, tagID uint) (*models.TagUsageStats, error)

	// Statistics
	GetTagStats(ctx context.Context, tenantID, websiteID uint) (*dto.MediaTagStatsResponse, error)
	GetTagCloud(ctx context.Context, req *dto.MediaTagCloudRequest) (*dto.MediaTagCloudResponse, error)

	// Bulk operations
	BulkDelete(ctx context.Context, tagIDs []uint) error
	BulkUpdateStatus(ctx context.Context, tagIDs []uint, status models.TagStatus) error
	BulkUpdateCategory(ctx context.Context, tagIDs []uint, category string) error

	// Tag suggestions
	GetSuggestions(ctx context.Context, req *dto.MediaTagSuggestionsRequest) ([]models.MediaTag, error)
	GetAutoTagSuggestions(ctx context.Context, fileID uint, limit int) ([]models.MediaTag, error)
}

// MediaFileTagRepository defines the interface for media file tag relationship operations
type MediaFileTagRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, fileTag *models.MediaFileTag) error
	GetByID(ctx context.Context, id uint) (*models.MediaFileTag, error)
	GetByFileAndTag(ctx context.Context, fileID, tagID uint) (*models.MediaFileTag, error)
	Update(ctx context.Context, fileTag *models.MediaFileTag) error
	Delete(ctx context.Context, id uint) error
	DeleteByFileAndTag(ctx context.Context, fileID, tagID uint) error

	// File-tag relationships
	GetTagsByFile(ctx context.Context, fileID uint) ([]models.MediaFileTag, error)
	GetFilesByTag(ctx context.Context, tagID uint, page, pageSize int) ([]models.MediaFileTag, int64, error)
	GetFilesByTags(ctx context.Context, tagIDs []uint, page, pageSize int) ([]models.MediaFileTag, int64, error)

	// Tagging operations
	TagFile(ctx context.Context, req *dto.CreateFileTagRequest) error
	UntagFile(ctx context.Context, fileID, tagID uint) error
	ReplaceFileTags(ctx context.Context, fileID uint, tagIDs []uint, userID uint) error
	GetCommonTags(ctx context.Context, fileIDs []uint) ([]models.MediaTag, error)

	// Bulk operations
	BulkTagFiles(ctx context.Context, req *dto.BulkTagFilesRequest) (*dto.BulkTagFilesResponse, error)
	BulkUntagFiles(ctx context.Context, req *dto.BulkUntagFilesRequest) (*dto.BulkUntagFilesResponse, error)
	BulkDeleteFilesTags(ctx context.Context, fileIDs []uint) error
	BulkDeleteTagsFiles(ctx context.Context, tagIDs []uint) error

	// Auto-tagging
	AutoTagFile(ctx context.Context, fileID uint, minConfidence float64) error
	GetAutoTagSuggestions(ctx context.Context, fileID uint, limit int) ([]models.MediaTag, error)
	UpdateConfidenceScore(ctx context.Context, fileTagID uint, score float64) error

	// Analytics
	GetTaggingAnalytics(ctx context.Context, req *dto.FileTagAnalyticsRequest) (*dto.FileTagAnalyticsResponse, error)
	GetTaggingActivity(ctx context.Context, tenantID, websiteID uint, dateFrom, dateTo time.Time) ([]dto.TaggingActivityPoint, error)
	GetSimilarFiles(ctx context.Context, req *dto.SimilarFilesRequest) (*dto.SimilarFilesResponse, error)

	// Search and filtering
	SearchFilesByTags(ctx context.Context, tenantID, websiteID uint, tagNames []string, page, pageSize int) ([]models.MediaFile, int64, error)
	GetFilesByTagSource(ctx context.Context, tenantID, websiteID uint, source models.TagSource, page, pageSize int) ([]models.MediaFileTag, int64, error)
	GetFilesByConfidence(ctx context.Context, minConfidence, maxConfidence float64, page, pageSize int) ([]models.MediaFileTag, int64, error)

	// Cleanup operations
	CleanupOrphanedFileTags(ctx context.Context) error
	CleanupLowConfidenceTags(ctx context.Context, minConfidence float64) error
}

// RepositoryManager defines the interface for managing all media repositories
type RepositoryManager interface {
	// Repository accessors
	MediaFolder() MediaFolderRepository
	MediaFile() MediaFileRepository
	MediaThumbnail() MediaThumbnailRepository
	MediaTag() MediaTagRepository
	MediaFileTag() MediaFileTagRepository

	// Transaction management
	BeginTransaction(ctx context.Context) (context.Context, error)
	CommitTransaction(ctx context.Context) error
	RollbackTransaction(ctx context.Context) error

	// Health check
	Health(ctx context.Context) error
}

// Additional models for requests and responses
type MediaFolderSearchRequest struct {
	TenantID   uint                     `json:"tenant_id" validate:"required"`
	WebsiteID  uint                     `json:"website_id" validate:"required"`
	ParentID   *uint                    `json:"parent_id,omitempty"`
	Query      *string                  `json:"query,omitempty"`
	Type       *models.FolderType       `json:"type,omitempty"`
	Status     *models.FolderStatus     `json:"status,omitempty"`
	Visibility *models.FolderVisibility `json:"visibility,omitempty"`
	UserID     *uint                    `json:"user_id,omitempty"`
	SortBy     *string                  `json:"sort_by,omitempty"`
	SortOrder  *string                  `json:"sort_order,omitempty"`
	Page       int                      `json:"page,omitempty"`
	PageSize   int                      `json:"page_size,omitempty"`
}

type MediaFolderStats struct {
	TotalFolders        uint            `json:"total_folders"`
	RootFolders         uint            `json:"root_folders"`
	MaxDepth            uint            `json:"max_depth"`
	AverageDepth        float64         `json:"average_depth"`
	FoldersByType       map[string]uint `json:"folders_by_type"`
	FoldersByStatus     map[string]uint `json:"folders_by_status"`
	FoldersByVisibility map[string]uint `json:"folders_by_visibility"`
	TotalFiles          uint            `json:"total_files"`
	TotalSize           uint64          `json:"total_size"`
}

type TagUsageStats struct {
	TagID           uint    `json:"tag_id"`
	UsageCount      uint    `json:"usage_count"`
	PopularityScore float64 `json:"popularity_score"`
	FilesCount      uint    `json:"files_count"`
	RecentUsage     uint    `json:"recent_usage"`
	TrendingScore   float64 `json:"trending_score"`
}
