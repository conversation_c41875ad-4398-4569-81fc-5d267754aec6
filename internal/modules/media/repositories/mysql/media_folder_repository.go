package mysql

import (
	"context"
	"fmt"
	"strings"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/media/repositories"
)

// mediaFolderRepository implements the MediaFolderRepository interface
type mediaFolderRepository struct {
	db *gorm.DB
}

// NewMediaFolderRepository creates a new media folder repository
func NewMediaFolderRepository(db *gorm.DB) repositories.MediaFolderRepository {
	return &mediaFolderRepository{db: db}
}

// Create creates a new media folder
func (r *mediaFolderRepository) Create(ctx context.Context, folder *models.MediaFolder) error {
	if err := r.db.WithContext(ctx).Create(folder).Error; err != nil {
		return fmt.Errorf("failed to create media folder: %w", err)
	}
	return nil
}

// GetByID gets a media folder by ID
func (r *mediaFolderRepository) GetByID(ctx context.Context, id uint) (*models.MediaFolder, error) {
	var folder models.MediaFolder
	if err := r.db.WithContext(ctx).
		Where("id = ? AND status != ?", id, models.FolderStatusDeleted).
		First(&folder).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("media folder not found")
		}
		return nil, fmt.Errorf("failed to get media folder: %w", err)
	}
	return &folder, nil
}

// GetBySlug gets a media folder by slug
func (r *mediaFolderRepository) GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.MediaFolder, error) {
	var folder models.MediaFolder
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND slug = ? AND status != ?", tenantID, websiteID, slug, models.FolderStatusDeleted).
		First(&folder).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("media folder not found")
		}
		return nil, fmt.Errorf("failed to get media folder: %w", err)
	}
	return &folder, nil
}

// Update updates a media folder
func (r *mediaFolderRepository) Update(ctx context.Context, folder *models.MediaFolder) error {
	if err := r.db.WithContext(ctx).Save(folder).Error; err != nil {
		return fmt.Errorf("failed to update media folder: %w", err)
	}
	return nil
}

// Delete permanently deletes a media folder
func (r *mediaFolderRepository) Delete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Delete(&models.MediaFolder{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete media folder: %w", err)
	}
	return nil
}

// SoftDelete soft deletes a media folder
func (r *mediaFolderRepository) SoftDelete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).
		Model(&models.MediaFolder{}).
		Where("id = ?", id).
		Update("status", models.FolderStatusDeleted).Error; err != nil {
		return fmt.Errorf("failed to soft delete media folder: %w", err)
	}
	return nil
}

// GetRootFolders gets all root folders for a tenant/website
func (r *mediaFolderRepository) GetRootFolders(ctx context.Context, tenantID, websiteID uint) ([]models.MediaFolder, error) {
	var folders []models.MediaFolder
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND parent_id IS NULL AND status != ?", tenantID, websiteID, models.FolderStatusDeleted).
		Order("sort_order ASC, name ASC").
		Find(&folders).Error; err != nil {
		return nil, fmt.Errorf("failed to get root folders: %w", err)
	}
	return folders, nil
}

// GetChildFolders gets all child folders of a parent folder
func (r *mediaFolderRepository) GetChildFolders(ctx context.Context, parentID uint) ([]models.MediaFolder, error) {
	var folders []models.MediaFolder
	if err := r.db.WithContext(ctx).
		Where("parent_id = ? AND status != ?", parentID, models.FolderStatusDeleted).
		Order("sort_order ASC, name ASC").
		Find(&folders).Error; err != nil {
		return nil, fmt.Errorf("failed to get child folders: %w", err)
	}
	return folders, nil
}

// GetFolderPath gets the full path of folders from root to the specified folder
func (r *mediaFolderRepository) GetFolderPath(ctx context.Context, folderID uint) ([]models.MediaFolder, error) {
	var folders []models.MediaFolder

	// Get the folder first
	folder, err := r.GetByID(ctx, folderID)
	if err != nil {
		return nil, err
	}

	folders = append(folders, *folder)

	// Traverse up the hierarchy
	currentID := folder.ParentID
	for currentID != nil {
		parentFolder, err := r.GetByID(ctx, *currentID)
		if err != nil {
			break
		}
		folders = append([]models.MediaFolder{*parentFolder}, folders...)
		currentID = parentFolder.ParentID
	}

	return folders, nil
}

// GetFolderTree gets the complete folder tree for a tenant/website
func (r *mediaFolderRepository) GetFolderTree(ctx context.Context, tenantID, websiteID uint) ([]models.MediaFolder, error) {
	var folders []models.MediaFolder
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, models.FolderStatusDeleted).
		Order("lft ASC").
		Find(&folders).Error; err != nil {
		return nil, fmt.Errorf("failed to get folder tree: %w", err)
	}
	return folders, nil
}

// MoveFolderTo moves a folder to a new parent
func (r *mediaFolderRepository) MoveFolderTo(ctx context.Context, folderID, newParentID uint) error {
	// Start transaction
	tx := r.db.WithContext(ctx).Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Get the folder to move
	var folder models.MediaFolder
	if err := tx.First(&folder, folderID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to get folder to move: %w", err)
	}

	// Update parent ID
	if err := tx.Model(&folder).Update("parent_id", newParentID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update folder parent: %w", err)
	}

	// Update path (simplified - in production, you'd want to update the full path)
	if err := tx.Model(&folder).Update("path", fmt.Sprintf("/%d", folderID)).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to update folder path: %w", err)
	}

	return tx.Commit().Error
}

// Search searches for folders based on criteria
func (r *mediaFolderRepository) Search(ctx context.Context, req *repositories.MediaFolderSearchRequest) ([]models.MediaFolder, int64, error) {
	var folders []models.MediaFolder
	var total int64

	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status != ?", req.TenantID, req.WebsiteID, models.FolderStatusDeleted)

	// Add filters
	if req.ParentID != nil {
		query = query.Where("parent_id = ?", *req.ParentID)
	}
	if req.Query != nil && *req.Query != "" {
		query = query.Where("name LIKE ? OR description LIKE ?", "%"+*req.Query+"%", "%"+*req.Query+"%")
	}
	if req.Type != nil {
		query = query.Where("type = ?", *req.Type)
	}
	if req.Status != nil {
		query = query.Where("status = ?", *req.Status)
	}
	if req.Visibility != nil {
		query = query.Where("visibility = ?", *req.Visibility)
	}
	if req.UserID != nil {
		query = query.Where("user_id = ?", *req.UserID)
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count folders: %w", err)
	}

	// Add sorting
	sortBy := "name"
	if req.SortBy != nil && *req.SortBy != "" {
		sortBy = *req.SortBy
	}
	sortOrder := "ASC"
	if req.SortOrder != nil && strings.ToUpper(*req.SortOrder) == "DESC" {
		sortOrder = "DESC"
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Add pagination
	if req.Page > 0 && req.PageSize > 0 {
		offset := (req.Page - 1) * req.PageSize
		query = query.Offset(offset).Limit(req.PageSize)
	}

	if err := query.Find(&folders).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to search folders: %w", err)
	}

	return folders, total, nil
}

// GetFoldersByUser gets folders created by a specific user
func (r *mediaFolderRepository) GetFoldersByUser(ctx context.Context, userID uint, page, pageSize int) ([]models.MediaFolder, int64, error) {
	var folders []models.MediaFolder
	var total int64

	query := r.db.WithContext(ctx).
		Where("user_id = ? AND status != ?", userID, models.FolderStatusDeleted)

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count user folders: %w", err)
	}

	// Add pagination
	if page > 0 && pageSize > 0 {
		offset := (page - 1) * pageSize
		query = query.Offset(offset).Limit(pageSize)
	}

	if err := query.Order("created_at DESC").Find(&folders).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to get user folders: %w", err)
	}

	return folders, total, nil
}

// GetFoldersByStatus gets folders by status
func (r *mediaFolderRepository) GetFoldersByStatus(ctx context.Context, tenantID, websiteID uint, status models.FolderStatus) ([]models.MediaFolder, error) {
	var folders []models.MediaFolder
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status = ?", tenantID, websiteID, status).
		Order("created_at DESC").
		Find(&folders).Error; err != nil {
		return nil, fmt.Errorf("failed to get folders by status: %w", err)
	}
	return folders, nil
}

// GetFolderStats gets folder statistics
func (r *mediaFolderRepository) GetFolderStats(ctx context.Context, tenantID, websiteID uint) (*dto.MediaFolderStatsResponse, error) {
	var stats dto.MediaFolderStatsResponse

	// Get total folders
	var totalFolders int64
	if err := r.db.WithContext(ctx).
		Model(&models.MediaFolder{}).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, models.FolderStatusDeleted).
		Count(&totalFolders).Error; err != nil {
		return nil, fmt.Errorf("failed to count total folders: %w", err)
	}
	stats.TotalFolders = uint(totalFolders)

	// Get active folders
	var activeFolders int64
	if err := r.db.WithContext(ctx).
		Model(&models.MediaFolder{}).
		Where("tenant_id = ? AND website_id = ? AND status = ?", tenantID, websiteID, models.FolderStatusActive).
		Count(&activeFolders).Error; err != nil {
		return nil, fmt.Errorf("failed to count active folders: %w", err)
	}
	stats.ActiveFolders = uint(activeFolders)

	// Get folders by type
	var typeStats []struct {
		Type  models.FolderType `json:"type"`
		Count uint              `json:"count"`
	}
	if err := r.db.WithContext(ctx).
		Model(&models.MediaFolder{}).
		Select("type, COUNT(*) as count").
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, models.FolderStatusDeleted).
		Group("type").
		Find(&typeStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get folder type stats: %w", err)
	}

	stats.FoldersByType = make(map[string]uint)
	for _, stat := range typeStats {
		stats.FoldersByType[string(stat.Type)] = stat.Count
	}

	// Get folders by status
	var statusStats []struct {
		Status models.FolderStatus `json:"status"`
		Count  uint                `json:"count"`
	}
	if err := r.db.WithContext(ctx).
		Model(&models.MediaFolder{}).
		Select("status, COUNT(*) as count").
		Where("tenant_id = ? AND website_id = ?", tenantID, websiteID).
		Group("status").
		Find(&statusStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get folder status stats: %w", err)
	}

	stats.FoldersByStatus = make(map[string]uint)
	for _, stat := range statusStats {
		stats.FoldersByStatus[string(stat.Status)] = stat.Count
	}

	return &stats, nil
}

// UpdateFolderStats updates folder statistics
func (r *mediaFolderRepository) UpdateFolderStats(ctx context.Context, folderID uint) error {
	// Get file count and total size for the folder
	var fileCount int64
	var totalSize uint64

	if err := r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("folder_id = ? AND status != ?", folderID, models.FileStatusDeleted).
		Count(&fileCount).Error; err != nil {
		return fmt.Errorf("failed to count files: %w", err)
	}

	if err := r.db.WithContext(ctx).
		Model(&models.MediaFile{}).
		Where("folder_id = ? AND status != ?", folderID, models.FileStatusDeleted).
		Select("COALESCE(SUM(file_size), 0)").
		Scan(&totalSize).Error; err != nil {
		return fmt.Errorf("failed to sum file sizes: %w", err)
	}

	// Update folder stats
	if err := r.db.WithContext(ctx).
		Model(&models.MediaFolder{}).
		Where("id = ?", folderID).
		Updates(map[string]interface{}{
			"file_count": fileCount,
			"total_size": totalSize,
		}).Error; err != nil {
		return fmt.Errorf("failed to update folder stats: %w", err)
	}

	return nil
}

// BulkDelete bulk deletes folders
func (r *mediaFolderRepository) BulkDelete(ctx context.Context, folderIDs []uint) error {
	if len(folderIDs) == 0 {
		return nil
	}

	if err := r.db.WithContext(ctx).
		Where("id IN ?", folderIDs).
		Delete(&models.MediaFolder{}).Error; err != nil {
		return fmt.Errorf("failed to bulk delete folders: %w", err)
	}
	return nil
}

// BulkUpdateStatus bulk updates folder status
func (r *mediaFolderRepository) BulkUpdateStatus(ctx context.Context, folderIDs []uint, status models.FolderStatus) error {
	if len(folderIDs) == 0 {
		return nil
	}

	if err := r.db.WithContext(ctx).
		Model(&models.MediaFolder{}).
		Where("id IN ?", folderIDs).
		Update("status", status).Error; err != nil {
		return fmt.Errorf("failed to bulk update folder status: %w", err)
	}
	return nil
}
