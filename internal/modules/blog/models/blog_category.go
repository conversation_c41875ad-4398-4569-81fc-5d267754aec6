package models

import (
	"time"

	"gorm.io/gorm"
)

// BlogCategoryStatus represents the status of a blog category
// @Enum active,inactive,deleted
type BlogCategoryStatus string

const (
	BlogCategoryStatusActive   BlogCategoryStatus = "active"
	BlogCategoryStatusInactive BlogCategoryStatus = "inactive"
	BlogCategoryStatusDeleted  BlogCategoryStatus = "deleted"
)

// BlogCategory represents a blog category with hierarchical structure
type BlogCategory struct {
	ID        uint `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint `gorm:"not null;index" json:"tenant_id"`
	WebsiteID uint `gorm:"not null;index" json:"website_id"`

	// Basic Information
	Slug        string `gorm:"type:varchar(255);not null" json:"slug" validate:"required,min=1,max=255"`
	Name        string `gorm:"type:varchar(255);not null" json:"name" validate:"required,min=1,max=255"`
	Description string `gorm:"type:text" json:"description,omitempty"`

	// Hierarchical Structure (Nested Set Model)
	ParentID *uint `gorm:"index" json:"parent_id,omitempty"`
	Lft      uint  `gorm:"not null;default:1" json:"lft"`
	Rgt      uint  `gorm:"not null;default:2" json:"rgt"`
	Level    uint  `gorm:"not null;default:0" json:"level"`

	// Display Settings
	Color     string `gorm:"type:varchar(7);default:'#000000'" json:"color"`
	IsActive  bool   `gorm:"default:true" json:"is_active"`
	SortOrder uint   `gorm:"default:0" json:"sort_order"`

	// Status using enum for soft delete strategy
	Status BlogCategoryStatus `gorm:"type:varchar(20);not null;default:'active'" json:"status" validate:"oneof=active inactive deleted"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	// Relationships
	Parent   *BlogCategory  `gorm:"foreignKey:ParentID" json:"parent,omitempty"`
	Children []BlogCategory `gorm:"foreignKey:ParentID" json:"children,omitempty"`
}

// TableName returns the table name for the BlogCategory model
func (BlogCategory) TableName() string {
	return "blog_categories"
}

// BlogCategoryCreateRequest represents the request to create a blog category
type BlogCategoryCreateRequest struct {
	TenantID    uint   `json:"tenant_id" validate:"required,min=1"`
	WebsiteID   uint   `json:"website_id" validate:"required,min=1"`
	Slug        string `json:"slug" validate:"required,min=1,max=255"`
	Name        string `json:"name" validate:"required,min=1,max=255"`
	Description string `json:"description,omitempty" validate:"max=1000"`
	ParentID    *uint  `json:"parent_id,omitempty" validate:"omitempty,min=1"`
	Color       string `json:"color,omitempty" validate:"omitempty,hexcolor"`
	IsActive    bool   `json:"is_active"`
	SortOrder   uint   `json:"sort_order"`
}

// BlogCategoryUpdateRequest represents the request to update a blog category
type BlogCategoryUpdateRequest struct {
	Slug        string `json:"slug" validate:"required,min=1,max=255"`
	Name        string `json:"name" validate:"required,min=1,max=255"`
	Description string `json:"description,omitempty" validate:"max=1000"`
	ParentID    *uint  `json:"parent_id,omitempty" validate:"omitempty,min=1"`
	Color       string `json:"color,omitempty" validate:"omitempty,hexcolor"`
	IsActive    bool   `json:"is_active"`
	SortOrder   uint   `json:"sort_order"`
}

// BlogCategoryResponse represents the response when returning blog category data
type BlogCategoryResponse struct {
	ID          uint                   `json:"id"`
	TenantID    uint                   `json:"tenant_id"`
	WebsiteID   uint                   `json:"website_id"`
	Slug        string                 `json:"slug"`
	Name        string                 `json:"name"`
	Description string                 `json:"description,omitempty"`
	ParentID    *uint                  `json:"parent_id,omitempty"`
	Lft         uint                   `json:"lft"`
	Rgt         uint                   `json:"rgt"`
	Level       uint                   `json:"level"`
	Color       string                 `json:"color"`
	IsActive    bool                   `json:"is_active"`
	SortOrder   uint                   `json:"sort_order"`
	Status      string                 `json:"status"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Parent      *BlogCategoryResponse  `json:"parent,omitempty"`
	Children    []BlogCategoryResponse `json:"children,omitempty"`
}

// FromBlogCategory converts a BlogCategory model to BlogCategoryResponse
func (bcr *BlogCategoryResponse) FromBlogCategory(category *BlogCategory) {
	bcr.ID = category.ID
	bcr.TenantID = category.TenantID
	bcr.WebsiteID = category.WebsiteID
	bcr.Slug = category.Slug
	bcr.Name = category.Name
	bcr.Description = category.Description
	bcr.ParentID = category.ParentID
	bcr.Lft = category.Lft
	bcr.Rgt = category.Rgt
	bcr.Level = category.Level
	bcr.Color = category.Color
	bcr.IsActive = category.IsActive
	bcr.SortOrder = category.SortOrder
	bcr.Status = string(category.Status)
	bcr.CreatedAt = category.CreatedAt
	bcr.UpdatedAt = category.UpdatedAt

	// Convert relationships
	if category.Parent != nil {
		parentResponse := &BlogCategoryResponse{}
		parentResponse.FromBlogCategory(category.Parent)
		bcr.Parent = parentResponse
	}

	if len(category.Children) > 0 {
		bcr.Children = make([]BlogCategoryResponse, len(category.Children))
		for i, child := range category.Children {
			bcr.Children[i].FromBlogCategory(&child)
		}
	}
}

// BlogCategoryFilter represents filters for querying blog categories
type BlogCategoryFilter struct {
	TenantID  uint   `json:"tenant_id,omitempty"`
	WebsiteID uint   `json:"website_id,omitempty"`
	ParentID  *uint  `json:"parent_id,omitempty"`
	IsActive  *bool  `json:"is_active,omitempty"`
	Status    string `json:"status,omitempty"`
	Search    string `json:"search,omitempty"`
	Page      int    `json:"page,omitempty"`
	PageSize  int    `json:"page_size,omitempty"`
	SortBy    string `json:"sort_by,omitempty"`
	SortOrder string `json:"sort_order,omitempty"`
}
