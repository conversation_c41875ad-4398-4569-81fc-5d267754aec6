package models

import (
	"time"

	"gorm.io/gorm"
)

// BlogPostType represents the type of blog post
// @Enum post,page,announcement
type BlogPostType string

const (
	BlogPostTypePost         BlogPostType = "post"
	BlogPostTypePage         BlogPostType = "page"
	BlogPostTypeAnnouncement BlogPostType = "announcement"
)

// BlogPostStatus represents the status of a blog post
// @Enum draft,review,published,scheduled,archived,rejected,deleted
type BlogPostStatus string

const (
	BlogPostStatusDraft     BlogPostStatus = "draft"
	BlogPostStatusReview    BlogPostStatus = "review"
	BlogPostStatusPublished BlogPostStatus = "published"
	BlogPostStatusScheduled BlogPostStatus = "scheduled"
	BlogPostStatusArchived  BlogPostStatus = "archived"
	BlogPostStatusRejected  BlogPostStatus = "rejected"
	BlogPostStatusDeleted   BlogPostStatus = "deleted"
)

// BlogPost represents a blog post
type BlogPost struct {
	ID        uint `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint `gorm:"not null;index" json:"tenant_id"`
	WebsiteID uint `gorm:"not null;index" json:"website_id"`

	// Basic Information
	Slug    string `gorm:"type:varchar(255);not null" json:"slug" validate:"required,min=1,max=255"`
	Title   string `gorm:"type:varchar(255);not null" json:"title" validate:"required,min=1,max=255"`
	Content string `gorm:"type:longtext;not null" json:"content" validate:"required"`
	Excerpt string `gorm:"type:text" json:"excerpt,omitempty"`

	// Author and Category
	AuthorID   uint  `gorm:"not null;index" json:"author_id" validate:"required,min=1"`
	CategoryID *uint `gorm:"index" json:"category_id,omitempty" validate:"omitempty,min=1"`

	// Post Configuration
	Type          BlogPostType `gorm:"type:varchar(20);not null;default:'post'" json:"type" validate:"oneof=post page announcement"`
	IsFeatured    bool         `gorm:"default:false" json:"is_featured"`
	AllowComments bool         `gorm:"default:true" json:"allow_comments"`
	Password      string       `gorm:"type:varchar(255)" json:"password,omitempty"`
	FeaturedImage string       `gorm:"type:varchar(500)" json:"featured_image,omitempty"`

	// Statistics
	ViewCount uint `gorm:"default:0" json:"view_count"`

	// Publishing
	ScheduledAt *time.Time     `gorm:"index" json:"scheduled_at,omitempty"`
	PublishedAt *time.Time     `json:"published_at,omitempty"`
	Status      BlogPostStatus `gorm:"type:varchar(20);not null;default:'draft'" json:"status" validate:"oneof=draft review published scheduled archived rejected deleted"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	// Relationships
	Category *BlogCategory `gorm:"foreignKey:CategoryID" json:"category,omitempty"`
	Tags     []BlogTag     `gorm:"many2many:blog_post_tags;" json:"tags,omitempty"`
}

// TableName returns the table name for the BlogPost model
func (BlogPost) TableName() string {
	return "blog_posts"
}

// BlogPostCreateRequest represents the request to create a blog post
type BlogPostCreateRequest struct {
	TenantID      uint           `json:"tenant_id" validate:"required,min=1" example:"1"`
	WebsiteID     uint           `json:"website_id" validate:"required,min=1" example:"1"`
	Slug          string         `json:"slug" validate:"required,min=1,max=255" example:"my-first-blog-post"`
	Title         string         `json:"title" validate:"required,min=1,max=255" example:"My First Blog Post"`
	Content       string         `json:"content" validate:"required" example:"This is the comprehensive content of my first blog post. It covers programming concepts, best practices, and includes code examples..."`
	Excerpt       string         `json:"excerpt,omitempty" validate:"max=1000" example:"A brief summary of my first blog post covering programming fundamentals"`
	AuthorID      uint           `json:"author_id" validate:"required,min=1" example:"1"`
	CategoryID    *uint          `json:"category_id,omitempty" validate:"omitempty,min=1" example:"1"`
	Type          BlogPostType   `json:"type" validate:"oneof=post page announcement" example:"post"`
	IsFeatured    bool           `json:"is_featured" example:"false"`
	AllowComments bool           `json:"allow_comments" example:"true"`
	Password      string         `json:"password,omitempty" validate:"max=255" example:""`
	FeaturedImage string         `json:"featured_image,omitempty" validate:"omitempty,url" example:"https://example.com/images/featured-image.jpg"`
	ScheduledAt   *time.Time     `json:"scheduled_at,omitempty" example:"2025-01-20T15:00:00Z"`
	Status        BlogPostStatus `json:"status" validate:"oneof=draft review published scheduled" example:"draft"`
	TagIDs        []uint         `json:"tag_ids,omitempty" example:"1,2,3"`
}

// BlogPostUpdateRequest represents the request to update a blog post
type BlogPostUpdateRequest struct {
	Slug          string         `json:"slug" validate:"required,min=1,max=255"`
	Title         string         `json:"title" validate:"required,min=1,max=255"`
	Content       string         `json:"content" validate:"required"`
	Excerpt       string         `json:"excerpt,omitempty" validate:"max=1000"`
	CategoryID    *uint          `json:"category_id,omitempty" validate:"omitempty,min=1"`
	Type          BlogPostType   `json:"type" validate:"oneof=post page announcement"`
	IsFeatured    bool           `json:"is_featured"`
	AllowComments bool           `json:"allow_comments"`
	Password      string         `json:"password,omitempty" validate:"max=255"`
	FeaturedImage string         `json:"featured_image,omitempty" validate:"omitempty,url"`
	ScheduledAt   *time.Time     `json:"scheduled_at,omitempty"`
	Status        BlogPostStatus `json:"status" validate:"oneof=draft review published scheduled archived"`
	TagIDs        []uint         `json:"tag_ids,omitempty"`
}

// BlogPostResponse represents the response when returning blog post data
type BlogPostResponse struct {
	ID            uint                  `json:"id"`
	TenantID      uint                  `json:"tenant_id"`
	WebsiteID     uint                  `json:"website_id"`
	Slug          string                `json:"slug"`
	Title         string                `json:"title"`
	Content       string                `json:"content"`
	Excerpt       string                `json:"excerpt,omitempty"`
	AuthorID      uint                  `json:"author_id"`
	CategoryID    *uint                 `json:"category_id,omitempty"`
	Type          BlogPostType          `json:"type"`
	IsFeatured    bool                  `json:"is_featured"`
	AllowComments bool                  `json:"allow_comments"`
	FeaturedImage string                `json:"featured_image,omitempty"`
	ViewCount     uint                  `json:"view_count"`
	ScheduledAt   *time.Time            `json:"scheduled_at,omitempty"`
	PublishedAt   *time.Time            `json:"published_at,omitempty"`
	Status        BlogPostStatus        `json:"status"`
	CreatedAt     time.Time             `json:"created_at"`
	UpdatedAt     time.Time             `json:"updated_at"`
	Category      *BlogCategoryResponse `json:"category,omitempty"`
	Tags          []BlogTagResponse     `json:"tags,omitempty"`
}

// FromBlogPost converts a BlogPost model to BlogPostResponse
func (bpr *BlogPostResponse) FromBlogPost(post *BlogPost) {
	bpr.ID = post.ID
	bpr.TenantID = post.TenantID
	bpr.WebsiteID = post.WebsiteID
	bpr.Slug = post.Slug
	bpr.Title = post.Title
	bpr.Content = post.Content
	bpr.Excerpt = post.Excerpt
	bpr.AuthorID = post.AuthorID
	bpr.CategoryID = post.CategoryID
	bpr.Type = post.Type
	bpr.IsFeatured = post.IsFeatured
	bpr.AllowComments = post.AllowComments
	bpr.FeaturedImage = post.FeaturedImage
	bpr.ViewCount = post.ViewCount
	bpr.ScheduledAt = post.ScheduledAt
	bpr.PublishedAt = post.PublishedAt
	bpr.Status = post.Status
	bpr.CreatedAt = post.CreatedAt
	bpr.UpdatedAt = post.UpdatedAt

	// Convert relationships
	if post.Category != nil {
		categoryResponse := &BlogCategoryResponse{}
		categoryResponse.FromBlogCategory(post.Category)
		bpr.Category = categoryResponse
	}

	if len(post.Tags) > 0 {
		bpr.Tags = make([]BlogTagResponse, len(post.Tags))
		for i, tag := range post.Tags {
			bpr.Tags[i].FromBlogTag(&tag)
		}
	}
}

// BlogPostFilter represents filters for querying blog posts
type BlogPostFilter struct {
	TenantID      uint           `json:"tenant_id,omitempty"`
	WebsiteID     uint           `json:"website_id,omitempty"`
	AuthorID      uint           `json:"author_id,omitempty"`
	CategoryID    *uint          `json:"category_id,omitempty"`
	Type          BlogPostType   `json:"type,omitempty"`
	Status        BlogPostStatus `json:"status,omitempty"`
	IsFeatured    *bool          `json:"is_featured,omitempty"`
	AllowComments *bool          `json:"allow_comments,omitempty"`
	TagIDs        []uint         `json:"tag_ids,omitempty"`
	Search        string         `json:"search,omitempty"`
	DateFrom      *time.Time     `json:"date_from,omitempty"`
	DateTo        *time.Time     `json:"date_to,omitempty"`
	Page          int            `json:"page,omitempty"`
	PageSize      int            `json:"page_size,omitempty"`
	SortBy        string         `json:"sort_by,omitempty"`
	SortOrder     string         `json:"sort_order,omitempty"`
}

// BlogPostStats represents blog post statistics
type BlogPostStats struct {
	TotalPosts     int `json:"total_posts"`
	PublishedPosts int `json:"published_posts"`
	DraftPosts     int `json:"draft_posts"`
	ScheduledPosts int `json:"scheduled_posts"`
	FeaturedPosts  int `json:"featured_posts"`
	TotalViews     int `json:"total_views"`
}
