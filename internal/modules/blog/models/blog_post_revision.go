package models

import (
	"time"

	"gorm.io/gorm"
)

// BlogPostRevision represents a revision of a blog post
type BlogPostRevision struct {
	ID             uint `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID       uint `gorm:"not null;index" json:"tenant_id"`
	PostID         uint `gorm:"not null;index" json:"post_id"`
	UserID         uint `gorm:"not null;index" json:"user_id"`
	RevisionNumber uint `gorm:"not null" json:"revision_number"`

	// Content at time of revision
	Title           string `gorm:"type:varchar(255)" json:"title,omitempty"`
	Content         string `gorm:"type:longtext" json:"content,omitempty"`
	Excerpt         string `gorm:"type:text" json:"excerpt,omitempty"`
	MetaTitle       string `gorm:"type:varchar(255)" json:"meta_title,omitempty"`
	MetaDescription string `gorm:"type:text" json:"meta_description,omitempty"`

	// Revision metadata
	ChangesSummary string `gorm:"type:text" json:"changes_summary,omitempty"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at"`
	DeletedAt gorm.DeletedAt `gorm:"index" json:"-"`

	// Relationships
	Post *BlogPost `gorm:"foreignKey:PostID" json:"post,omitempty"`
}

// TableName returns the table name for the BlogPostRevision model
func (BlogPostRevision) TableName() string {
	return "blog_post_revisions"
}

// BlogPostRevisionCreateRequest represents the request to create a blog post revision
type BlogPostRevisionCreateRequest struct {
	TenantID        uint   `json:"tenant_id" validate:"required,min=1"`
	PostID          uint   `json:"post_id" validate:"required,min=1"`
	UserID          uint   `json:"user_id" validate:"required,min=1"`
	Title           string `json:"title,omitempty" validate:"max=255"`
	Content         string `json:"content,omitempty"`
	Excerpt         string `json:"excerpt,omitempty" validate:"max=1000"`
	MetaTitle       string `json:"meta_title,omitempty" validate:"max=255"`
	MetaDescription string `json:"meta_description,omitempty" validate:"max=1000"`
	ChangesSummary  string `json:"changes_summary,omitempty" validate:"max=1000"`
}

// BlogPostRevisionResponse represents the response when returning blog post revision data
type BlogPostRevisionResponse struct {
	ID              uint              `json:"id"`
	TenantID        uint              `json:"tenant_id"`
	PostID          uint              `json:"post_id"`
	UserID          uint              `json:"user_id"`
	RevisionNumber  uint              `json:"revision_number"`
	Title           string            `json:"title,omitempty"`
	Content         string            `json:"content,omitempty"`
	Excerpt         string            `json:"excerpt,omitempty"`
	MetaTitle       string            `json:"meta_title,omitempty"`
	MetaDescription string            `json:"meta_description,omitempty"`
	ChangesSummary  string            `json:"changes_summary,omitempty"`
	CreatedAt       time.Time         `json:"created_at"`
	Post            *BlogPostResponse `json:"post,omitempty"`
}

// FromBlogPostRevision converts a BlogPostRevision model to BlogPostRevisionResponse
func (bprr *BlogPostRevisionResponse) FromBlogPostRevision(revision *BlogPostRevision) {
	bprr.ID = revision.ID
	bprr.TenantID = revision.TenantID
	bprr.PostID = revision.PostID
	bprr.UserID = revision.UserID
	bprr.RevisionNumber = revision.RevisionNumber
	bprr.Title = revision.Title
	bprr.Content = revision.Content
	bprr.Excerpt = revision.Excerpt
	bprr.MetaTitle = revision.MetaTitle
	bprr.MetaDescription = revision.MetaDescription
	bprr.ChangesSummary = revision.ChangesSummary
	bprr.CreatedAt = revision.CreatedAt

	// Convert relationships
	if revision.Post != nil {
		postResponse := &BlogPostResponse{}
		postResponse.FromBlogPost(revision.Post)
		bprr.Post = postResponse
	}
}

// BlogPostRevisionFilter represents filters for querying blog post revisions
type BlogPostRevisionFilter struct {
	TenantID  uint       `json:"tenant_id,omitempty"`
	PostID    uint       `json:"post_id,omitempty"`
	UserID    uint       `json:"user_id,omitempty"`
	DateFrom  *time.Time `json:"date_from,omitempty"`
	DateTo    *time.Time `json:"date_to,omitempty"`
	Page      int        `json:"page,omitempty"`
	PageSize  int        `json:"page_size,omitempty"`
	SortBy    string     `json:"sort_by,omitempty"`
	SortOrder string     `json:"sort_order,omitempty"`
}

// BlogPostRevisionComparison represents a comparison between two revisions
type BlogPostRevisionComparison struct {
	FromRevision BlogPostRevisionResponse `json:"from_revision"`
	ToRevision   BlogPostRevisionResponse `json:"to_revision"`
	Changes      RevisionChanges          `json:"changes"`
}

// RevisionChanges represents the differences between revisions
type RevisionChanges struct {
	TitleChanged           bool   `json:"title_changed"`
	ContentChanged         bool   `json:"content_changed"`
	ExcerptChanged         bool   `json:"excerpt_changed"`
	MetaTitleChanged       bool   `json:"meta_title_changed"`
	MetaDescriptionChanged bool   `json:"meta_description_changed"`
	Summary                string `json:"summary"`
}

// BlogPostRevisionStats represents revision statistics
type BlogPostRevisionStats struct {
	TotalRevisions    int `json:"total_revisions"`
	RevisionsToday    int `json:"revisions_today"`
	RevisionsThisWeek int `json:"revisions_this_week"`
	ActiveAuthors     int `json:"active_authors"`
}
