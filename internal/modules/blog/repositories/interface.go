package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// BlogCategoryRepository defines the interface for blog category data access
type BlogCategoryRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, category *models.BlogCategory) error
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlogCategory, error)
	GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogCategory, error)
	Update(ctx context.Context, tenantID, id uint, category *models.BlogCategory) error
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlogCategoryFilter) ([]models.BlogCategory, int64, error)
	ListWithCursor(ctx context.Context, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogCategory, *pagination.CursorResponse, error)
	GetByParentID(ctx context.Context, tenantID, websiteID uint, parentID *uint) ([]models.BlogCategory, error)
	GetHierarchy(ctx context.Context, tenantID, websiteID uint) ([]models.BlogCategory, error)

	// Hierarchical operations (Nested Set Model)
	MoveCategory(ctx context.Context, tenantID, categoryID, newParentID uint) error
	GetDescendants(ctx context.Context, tenantID, categoryID uint) ([]models.BlogCategory, error)
	UpdatePositions(ctx context.Context, tenantID uint, positions []CategoryPosition) error
}

// BlogTagRepository defines the interface for blog tag data access
type BlogTagRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, tag *models.BlogTag) error
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlogTag, error)
	GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogTag, error)
	Update(ctx context.Context, tenantID, id uint, tag *models.BlogTag) error
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlogTagFilter) ([]models.BlogTag, int64, error)
	ListWithCursor(ctx context.Context, tenantID, websiteID uint, cursor *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogTag, *pagination.CursorResponse, error)
	GetByNames(ctx context.Context, tenantID, websiteID uint, names []string) ([]models.BlogTag, error)
	GetMostUsed(ctx context.Context, tenantID, websiteID uint, limit int) ([]models.BlogTag, error)
	GetSuggestions(ctx context.Context, tenantID, websiteID uint, query string, limit int) ([]models.BlogTag, error)

	// Usage operations
	IncrementUsage(ctx context.Context, tenantID, tagID uint) error
	DecrementUsage(ctx context.Context, tenantID, tagID uint) error
	UpdateUsageCount(ctx context.Context, tenantID, tagID uint, count uint) error
}

// BlogPostRepository defines the interface for blog post data access
type BlogPostRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, post *models.BlogPost) error
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlogPost, error)
	GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogPost, error)
	Update(ctx context.Context, tenantID, id uint, post *models.BlogPost) error
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlogPostFilter) ([]models.BlogPost, int64, error)
	ListWithCursor(ctx context.Context, tenantID, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogPost, *pagination.CursorResponse, error)
	GetPublished(ctx context.Context, tenantID, websiteID uint, limit, offset int) ([]models.BlogPost, int64, error)
	GetFeatured(ctx context.Context, tenantID, websiteID uint, limit int) ([]models.BlogPost, error)
	GetRelated(ctx context.Context, tenantID, postID uint, limit int) ([]models.BlogPost, error)

	// Tag operations
	AttachTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error
	DetachTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error
	SyncTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error
	GetPostTags(ctx context.Context, tenantID, postID uint) ([]models.BlogTag, error)

	// Statistics and analytics
	IncrementViewCount(ctx context.Context, tenantID, postID uint) error
	GetStats(ctx context.Context, tenantID, websiteID uint) (*models.BlogPostStats, error)
	GetPopular(ctx context.Context, tenantID, websiteID uint, days int, limit int) ([]models.BlogPost, error)

	// Publishing operations
	Publish(ctx context.Context, tenantID, postID uint) error
	Unpublish(ctx context.Context, tenantID, postID uint) error
	Schedule(ctx context.Context, tenantID, postID uint, scheduledAt *time.Time) error
	GetScheduled(ctx context.Context) ([]models.BlogPost, error)
}

// BlogPostScheduleRepository defines the interface for blog post scheduling
type BlogPostScheduleRepository interface {
	Create(ctx context.Context, schedule *models.BlogPostSchedule) error
	GetByPostID(ctx context.Context, tenantID, postID uint) (*models.BlogPostSchedule, error)
	Update(ctx context.Context, tenantID, id uint, schedule *models.BlogPostSchedule) error
	Delete(ctx context.Context, tenantID, id uint) error
	GetPendingSchedules(ctx context.Context) ([]models.BlogPostSchedule, error)
	MarkAsExecuted(ctx context.Context, tenantID, scheduleID uint) error
}

// BlogPostRevisionRepository defines the interface for blog post revisions
type BlogPostRevisionRepository interface {
	Create(ctx context.Context, revision *models.BlogPostRevision) error
	GetByPostID(ctx context.Context, tenantID, postID uint, limit, offset int) ([]models.BlogPostRevision, int64, error)
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlogPostRevision, error)
	Delete(ctx context.Context, tenantID, id uint) error
	DeleteOldRevisions(ctx context.Context, tenantID, postID uint, keepCount int) error
}

// HomepageBlockRepository defines the interface for homepage block data access
type HomepageBlockRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, block *models.HomepageBlock) error
	GetByID(ctx context.Context, tenantID, id uint) (*models.HomepageBlock, error)
	Update(ctx context.Context, tenantID, id uint, block *models.HomepageBlock) error
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.HomepageBlockFilter) ([]models.HomepageBlock, int64, error)
	GetByWebsite(ctx context.Context, tenantID, websiteID uint) ([]models.HomepageBlock, error)
	GetActiveBlocks(ctx context.Context, tenantID, websiteID uint) ([]models.HomepageBlock, error)

	// Block organization
	Reorder(ctx context.Context, tenantID uint, blockIDs []uint) error
	UpdateSortOrder(ctx context.Context, tenantID, blockID uint, sortOrder uint) error

	// Block status
	Activate(ctx context.Context, tenantID, blockID uint) error
	Deactivate(ctx context.Context, tenantID, blockID uint) error
}

// BlockTemplateRepository defines the interface for block template data access
type BlockTemplateRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, template *models.BlockTemplate) error
	GetByID(ctx context.Context, tenantID, id uint) (*models.BlockTemplate, error)
	Update(ctx context.Context, tenantID, id uint, template *models.BlockTemplate) error
	Delete(ctx context.Context, tenantID, id uint) error

	// List and filter operations
	List(ctx context.Context, filter *models.BlockTemplateFilter) ([]models.BlockTemplate, int64, error)
	GetPublicTemplates(ctx context.Context, blockType models.HomepageBlockType) ([]models.BlockTemplate, error)
	GetByTenant(ctx context.Context, tenantID uint) ([]models.BlockTemplate, error)
}

// CategoryPosition represents a category position for reordering
type CategoryPosition struct {
	ID       uint  `json:"id"`
	ParentID *uint `json:"parent_id"`
	Position uint  `json:"position"`
}
