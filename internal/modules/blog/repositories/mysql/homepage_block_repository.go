package mysql

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// homepageBlockRepository implements repositories.HomepageBlockRepository
type homepageBlockRepository struct {
	db     *gorm.DB
	logger utils.Logger
}

// NewHomepageBlockRepository creates a new homepage block repository
func NewHomepageBlockRepository(db *gorm.DB, logger utils.Logger) repositories.HomepageBlockRepository {
	return &homepageBlockRepository{
		db:     db,
		logger: logger,
	}
}

// Create creates a new homepage block
func (r *homepageBlockRepository) Create(ctx context.Context, block *models.HomepageBlock) error {
	r.logger.WithContext(ctx).Info("Creating homepage block", map[string]interface{}{
		"tenant_id":  block.TenantID,
		"website_id": block.WebsiteID,
		"block_type": block.BlockType,
		"title":      block.Title,
	})

	if err := r.db.WithContext(ctx).Create(block).Error; err != nil {
		r.logger.WithContext(ctx).WithError(err).Error("Failed to create homepage block")
		return fmt.Errorf("failed to create homepage block: %w", err)
	}

	r.logger.WithContext(ctx).Info("Successfully created homepage block", map[string]interface{}{
		"block_id": block.ID,
	})

	return nil
}

// GetByID retrieves a homepage block by ID
func (r *homepageBlockRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.HomepageBlock, error) {
	r.logger.WithContext(ctx).Info("Getting homepage block by ID", map[string]interface{}{
		"tenant_id": tenantID,
		"block_id":  id,
	})

	var block models.HomepageBlock
	if err := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ? AND status != ?", id, tenantID, "deleted").
		First(&block).Error; err != nil {
		r.logger.WithContext(ctx).WithError(err).Error("Failed to get homepage block")
		return nil, fmt.Errorf("failed to get homepage block: %w", err)
	}

	return &block, nil
}

// Update updates a homepage block
func (r *homepageBlockRepository) Update(ctx context.Context, tenantID, id uint, block *models.HomepageBlock) error {
	r.logger.WithContext(ctx).Info("Updating homepage block", map[string]interface{}{
		"tenant_id": tenantID,
		"block_id":  id,
	})

	result := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ? AND status != ?", id, tenantID, "deleted").
		Updates(block)

	if result.Error != nil {
		r.logger.WithContext(ctx).WithError(result.Error).Error("Failed to update homepage block")
		return fmt.Errorf("failed to update homepage block: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("homepage block not found")
	}

	r.logger.WithContext(ctx).Info("Successfully updated homepage block")
	return nil
}

// Delete soft deletes a homepage block
func (r *homepageBlockRepository) Delete(ctx context.Context, tenantID, id uint) error {
	r.logger.WithContext(ctx).Info("Deleting homepage block", map[string]interface{}{
		"tenant_id": tenantID,
		"block_id":  id,
	})

	result := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Update("status", "deleted")

	if result.Error != nil {
		r.logger.WithContext(ctx).WithError(result.Error).Error("Failed to delete homepage block")
		return fmt.Errorf("failed to delete homepage block: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("homepage block not found")
	}

	r.logger.WithContext(ctx).Info("Successfully deleted homepage block")
	return nil
}

// List retrieves homepage blocks with filtering and pagination
func (r *homepageBlockRepository) List(ctx context.Context, filter *models.HomepageBlockFilter) ([]models.HomepageBlock, int64, error) {
	r.logger.WithContext(ctx).Info("Listing homepage blocks", map[string]interface{}{
		"filter": filter,
	})

	query := r.db.WithContext(ctx).Where("status != ?", "deleted")

	// Apply filters
	if filter != nil {
		if filter.TenantID > 0 {
			query = query.Where("tenant_id = ?", filter.TenantID)
		}
		if filter.WebsiteID > 0 {
			query = query.Where("website_id = ?", filter.WebsiteID)
		}
		if filter.BlockType != "" {
			query = query.Where("block_type = ?", filter.BlockType)
		}
		if filter.IsActive != nil {
			query = query.Where("is_active = ?", *filter.IsActive)
		}
		if filter.Status != "" {
			query = query.Where("status = ?", filter.Status)
		}
	}

	// Count total records
	var total int64
	if err := query.Model(&models.HomepageBlock{}).Count(&total).Error; err != nil {
		r.logger.WithContext(ctx).WithError(err).Error("Failed to count homepage blocks")
		return nil, 0, fmt.Errorf("failed to count homepage blocks: %w", err)
	}

	// Apply sorting
	sortBy := "sort_order"
	sortOrder := "ASC"
	if filter != nil {
		if filter.SortBy != "" {
			sortBy = filter.SortBy
		}
		if filter.SortOrder != "" {
			sortOrder = filter.SortOrder
		}
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if filter != nil && filter.PageSize > 0 {
		offset := 0
		if filter.Page > 1 {
			offset = (filter.Page - 1) * filter.PageSize
		}
		query = query.Limit(filter.PageSize).Offset(offset)
	}

	var blocks []models.HomepageBlock
	if err := query.Find(&blocks).Error; err != nil {
		r.logger.WithContext(ctx).WithError(err).Error("Failed to list homepage blocks")
		return nil, 0, fmt.Errorf("failed to list homepage blocks: %w", err)
	}

	r.logger.WithContext(ctx).Info("Successfully listed homepage blocks", map[string]interface{}{
		"count": len(blocks),
		"total": total,
	})

	return blocks, total, nil
}

// GetByWebsite retrieves all homepage blocks for a website
func (r *homepageBlockRepository) GetByWebsite(ctx context.Context, tenantID, websiteID uint) ([]models.HomepageBlock, error) {
	r.logger.WithContext(ctx).Info("Getting homepage blocks by website", map[string]interface{}{
		"tenant_id":  tenantID,
		"website_id": websiteID,
	})

	var blocks []models.HomepageBlock
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, "deleted").
		Order("sort_order ASC").
		Find(&blocks).Error; err != nil {
		r.logger.WithContext(ctx).WithError(err).Error("Failed to get homepage blocks by website")
		return nil, fmt.Errorf("failed to get homepage blocks by website: %w", err)
	}

	return blocks, nil
}

// GetActiveBlocks retrieves all active homepage blocks for a website
func (r *homepageBlockRepository) GetActiveBlocks(ctx context.Context, tenantID, websiteID uint) ([]models.HomepageBlock, error) {
	r.logger.WithContext(ctx).Info("Getting active homepage blocks", map[string]interface{}{
		"tenant_id":  tenantID,
		"website_id": websiteID,
	})

	var blocks []models.HomepageBlock
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND is_active = ? AND status = ?", tenantID, websiteID, true, "active").
		Order("sort_order ASC").
		Find(&blocks).Error; err != nil {
		r.logger.WithContext(ctx).WithError(err).Error("Failed to get active homepage blocks")
		return nil, fmt.Errorf("failed to get active homepage blocks: %w", err)
	}

	return blocks, nil
}

// Reorder updates the sort order of multiple blocks
func (r *homepageBlockRepository) Reorder(ctx context.Context, tenantID uint, blockIDs []uint) error {
	r.logger.WithContext(ctx).Info("Reordering homepage blocks", map[string]interface{}{
		"tenant_id": tenantID,
		"block_ids": blockIDs,
	})

	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for i, blockID := range blockIDs {
			sortOrder := uint(i + 1)
			if err := tx.Where("id = ? AND tenant_id = ?", blockID, tenantID).
				Update("sort_order", sortOrder).Error; err != nil {
				return fmt.Errorf("failed to update sort order for block %d: %w", blockID, err)
			}
		}
		return nil
	})
}

// UpdateSortOrder updates the sort order of a specific block
func (r *homepageBlockRepository) UpdateSortOrder(ctx context.Context, tenantID, blockID uint, sortOrder uint) error {
	r.logger.WithContext(ctx).Info("Updating block sort order", map[string]interface{}{
		"tenant_id":  tenantID,
		"block_id":   blockID,
		"sort_order": sortOrder,
	})

	result := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ?", blockID, tenantID).
		Update("sort_order", sortOrder)

	if result.Error != nil {
		r.logger.WithContext(ctx).WithError(result.Error).Error("Failed to update block sort order")
		return fmt.Errorf("failed to update block sort order: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("homepage block not found")
	}

	return nil
}

// Activate activates a homepage block
func (r *homepageBlockRepository) Activate(ctx context.Context, tenantID, blockID uint) error {
	r.logger.WithContext(ctx).Info("Activating homepage block", map[string]interface{}{
		"tenant_id": tenantID,
		"block_id":  blockID,
	})

	result := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ?", blockID, tenantID).
		Update("is_active", true)

	if result.Error != nil {
		r.logger.WithContext(ctx).WithError(result.Error).Error("Failed to activate homepage block")
		return fmt.Errorf("failed to activate homepage block: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("homepage block not found")
	}

	return nil
}

// Deactivate deactivates a homepage block
func (r *homepageBlockRepository) Deactivate(ctx context.Context, tenantID, blockID uint) error {
	r.logger.WithContext(ctx).Info("Deactivating homepage block", map[string]interface{}{
		"tenant_id": tenantID,
		"block_id":  blockID,
	})

	result := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ?", blockID, tenantID).
		Update("is_active", false)

	if result.Error != nil {
		r.logger.WithContext(ctx).WithError(result.Error).Error("Failed to deactivate homepage block")
		return fmt.Errorf("failed to deactivate homepage block: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("homepage block not found")
	}

	return nil
}
