package mysql

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// blockTemplateRepository implements repositories.BlockTemplateRepository
type blockTemplateRepository struct {
	db     *gorm.DB
	logger utils.Logger
}

// NewBlockTemplateRepository creates a new block template repository
func NewBlockTemplateRepository(db *gorm.DB, logger utils.Logger) repositories.BlockTemplateRepository {
	return &blockTemplateRepository{
		db:     db,
		logger: logger,
	}
}

// Create creates a new block template
func (r *blockTemplateRepository) Create(ctx context.Context, template *models.BlockTemplate) error {
	r.logger.WithContext(ctx).Info("Creating block template", map[string]interface{}{
		"tenant_id":  template.TenantID,
		"block_type": template.BlockType,
		"name":       template.Name,
		"is_public":  template.IsPublic,
	})

	if err := r.db.WithContext(ctx).Create(template).Error; err != nil {
		r.logger.WithContext(ctx).WithError(err).Error("Failed to create block template")
		return fmt.Errorf("failed to create block template: %w", err)
	}

	r.logger.WithContext(ctx).Info("Successfully created block template", map[string]interface{}{
		"template_id": template.ID,
	})

	return nil
}

// GetByID retrieves a block template by ID
func (r *blockTemplateRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.BlockTemplate, error) {
	r.logger.WithContext(ctx).Info("Getting block template by ID", map[string]interface{}{
		"tenant_id":   tenantID,
		"template_id": id,
	})

	var template models.BlockTemplate
	query := r.db.WithContext(ctx).Where("id = ? AND status != ?", id, "deleted")

	// If tenantID is provided, include it in the query (for tenant-specific templates)
	// Public templates (tenant_id = 0) should be accessible to all tenants
	if tenantID > 0 {
		query = query.Where("(tenant_id = ? OR tenant_id = 0)", tenantID)
	}

	if err := query.First(&template).Error; err != nil {
		r.logger.WithContext(ctx).WithError(err).Error("Failed to get block template")
		return nil, fmt.Errorf("failed to get block template: %w", err)
	}

	return &template, nil
}

// Update updates a block template
func (r *blockTemplateRepository) Update(ctx context.Context, tenantID, id uint, template *models.BlockTemplate) error {
	r.logger.WithContext(ctx).Info("Updating block template", map[string]interface{}{
		"tenant_id":   tenantID,
		"template_id": id,
	})

	result := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ? AND status != ?", id, tenantID, "deleted").
		Updates(template)

	if result.Error != nil {
		r.logger.WithContext(ctx).WithError(result.Error).Error("Failed to update block template")
		return fmt.Errorf("failed to update block template: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("block template not found")
	}

	r.logger.WithContext(ctx).Info("Successfully updated block template")
	return nil
}

// Delete soft deletes a block template
func (r *blockTemplateRepository) Delete(ctx context.Context, tenantID, id uint) error {
	r.logger.WithContext(ctx).Info("Deleting block template", map[string]interface{}{
		"tenant_id":   tenantID,
		"template_id": id,
	})

	result := r.db.WithContext(ctx).
		Where("id = ? AND tenant_id = ?", id, tenantID).
		Update("status", "deleted")

	if result.Error != nil {
		r.logger.WithContext(ctx).WithError(result.Error).Error("Failed to delete block template")
		return fmt.Errorf("failed to delete block template: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("block template not found")
	}

	r.logger.WithContext(ctx).Info("Successfully deleted block template")
	return nil
}

// List retrieves block templates with filtering and pagination
func (r *blockTemplateRepository) List(ctx context.Context, filter *models.BlockTemplateFilter) ([]models.BlockTemplate, int64, error) {
	r.logger.WithContext(ctx).Info("Listing block templates", map[string]interface{}{
		"filter": filter,
	})

	query := r.db.WithContext(ctx).Where("status != ?", "deleted")

	// Apply filters
	if filter != nil {
		if filter.TenantID > 0 {
			// Include both tenant-specific and public templates
			query = query.Where("tenant_id = ? OR tenant_id = 0", filter.TenantID)
		}
		if filter.BlockType != "" {
			query = query.Where("block_type = ?", filter.BlockType)
		}
		if filter.IsPublic != nil {
			query = query.Where("is_public = ?", *filter.IsPublic)
		}
		if filter.Status != "" {
			query = query.Where("status = ?", filter.Status)
		}
		if filter.Search != "" {
			query = query.Where("name LIKE ? OR description LIKE ?", "%"+filter.Search+"%", "%"+filter.Search+"%")
		}
	}

	// Count total records
	var total int64
	if err := query.Model(&models.BlockTemplate{}).Count(&total).Error; err != nil {
		r.logger.WithContext(ctx).WithError(err).Error("Failed to count block templates")
		return nil, 0, fmt.Errorf("failed to count block templates: %w", err)
	}

	// Apply sorting
	sortBy := "created_at"
	sortOrder := "DESC"
	if filter != nil {
		if filter.SortBy != "" {
			sortBy = filter.SortBy
		}
		if filter.SortOrder != "" {
			sortOrder = filter.SortOrder
		}
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if filter != nil && filter.PageSize > 0 {
		offset := 0
		if filter.Page > 1 {
			offset = (filter.Page - 1) * filter.PageSize
		}
		query = query.Limit(filter.PageSize).Offset(offset)
	}

	var templates []models.BlockTemplate
	if err := query.Find(&templates).Error; err != nil {
		r.logger.WithContext(ctx).WithError(err).Error("Failed to list block templates")
		return nil, 0, fmt.Errorf("failed to list block templates: %w", err)
	}

	r.logger.WithContext(ctx).Info("Successfully listed block templates", map[string]interface{}{
		"count": len(templates),
		"total": total,
	})

	return templates, total, nil
}

// GetPublicTemplates retrieves public templates for a specific block type
func (r *blockTemplateRepository) GetPublicTemplates(ctx context.Context, blockType models.HomepageBlockType) ([]models.BlockTemplate, error) {
	r.logger.WithContext(ctx).Info("Getting public templates", map[string]interface{}{
		"block_type": blockType,
	})

	var templates []models.BlockTemplate
	query := r.db.WithContext(ctx).
		Where("is_public = ? AND status = ?", true, "active")

	if blockType != "" {
		query = query.Where("block_type = ?", blockType)
	}

	if err := query.Order("name ASC").Find(&templates).Error; err != nil {
		r.logger.WithContext(ctx).WithError(err).Error("Failed to get public templates")
		return nil, fmt.Errorf("failed to get public templates: %w", err)
	}

	r.logger.WithContext(ctx).Info("Successfully retrieved public templates", map[string]interface{}{
		"count": len(templates),
	})

	return templates, nil
}

// GetByTenant retrieves all templates for a specific tenant
func (r *blockTemplateRepository) GetByTenant(ctx context.Context, tenantID uint) ([]models.BlockTemplate, error) {
	r.logger.WithContext(ctx).Info("Getting templates by tenant", map[string]interface{}{
		"tenant_id": tenantID,
	})

	var templates []models.BlockTemplate
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status != ?", tenantID, "deleted").
		Order("name ASC").
		Find(&templates).Error; err != nil {
		r.logger.WithContext(ctx).WithError(err).Error("Failed to get templates by tenant")
		return nil, fmt.Errorf("failed to get templates by tenant: %w", err)
	}

	r.logger.WithContext(ctx).Info("Successfully retrieved templates by tenant", map[string]interface{}{
		"count": len(templates),
	})

	return templates, nil
}
