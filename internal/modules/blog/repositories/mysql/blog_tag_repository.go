package mysql

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// BlogTagRepository implements the blog tag repository interface using MySQL
type BlogTagRepository struct {
	db *gorm.DB
}

// NewBlogTagRepository creates a new instance of BlogTagRepository
func NewBlogTagRepository(db *gorm.DB) repositories.BlogTagRepository {
	return &BlogTagRepository{db: db}
}

// Create creates a new blog tag
func (r *BlogTagRepository) Create(ctx context.Context, tag *models.BlogTag) error {
	return r.db.WithContext(ctx).Create(tag).Error
}

// GetByID gets a blog tag by its ID and tenant ID
func (r *BlogTagRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.BlogTag, error) {
	var tag models.BlogTag
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, id, "deleted").
		First(&tag).Error

	if err != nil {
		return nil, err
	}
	return &tag, nil
}

// GetBySlug gets a blog tag by its slug, tenant ID, and website ID
func (r *BlogTagRepository) GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogTag, error) {
	var tag models.BlogTag
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND slug = ? AND status != ?", tenantID, websiteID, slug, "deleted").
		First(&tag).Error

	if err != nil {
		return nil, err
	}
	return &tag, nil
}

// Update updates a blog tag
func (r *BlogTagRepository) Update(ctx context.Context, tenantID, id uint, tag *models.BlogTag) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Updates(tag).Error
}

// Delete soft deletes a blog tag by setting status to 'deleted'
func (r *BlogTagRepository) Delete(ctx context.Context, tenantID, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.BlogTag{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Update("status", "deleted").Error
}

// List retrieves blog tags with filters and pagination
func (r *BlogTagRepository) List(ctx context.Context, filter *models.BlogTagFilter) ([]models.BlogTag, int64, error) {
	var tags []models.BlogTag
	var total int64

	query := r.db.WithContext(ctx).Model(&models.BlogTag{}).
		Where("status != ?", "deleted")

	// Apply filters
	if filter.TenantID != 0 {
		query = query.Where("tenant_id = ?", filter.TenantID)
	}
	if filter.WebsiteID != 0 {
		query = query.Where("website_id = ?", filter.WebsiteID)
	}
	if filter.IsActive != nil {
		query = query.Where("is_active = ?", *filter.IsActive)
	}
	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}
	if filter.Search != "" {
		searchTerm := "%" + filter.Search + "%"
		query = query.Where("name LIKE ? OR description LIKE ?", searchTerm, searchTerm)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	sortBy := "usage_count"
	if filter.SortBy != "" {
		sortBy = filter.SortBy
	}
	sortOrder := "DESC"
	if filter.SortOrder != "" {
		sortOrder = filter.SortOrder
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	// Execute query
	err := query.Find(&tags).Error
	return tags, total, err
}

// ListWithCursor lists blog tags with cursor-based pagination
func (r *BlogTagRepository) ListWithCursor(ctx context.Context, tenantID, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.BlogTag, *pagination.CursorResponse, error) {
	var tags []models.BlogTag
	query := r.db.WithContext(ctx).Model(&models.BlogTag{}).
		Where("tenant_id = ? AND website_id = ? AND status != ?", tenantID, websiteID, "deleted")

	// Apply filters
	if search, ok := filters["search"].(string); ok && search != "" {
		searchTerm := "%" + search + "%"
		query = query.Where("name LIKE ? OR description LIKE ?", searchTerm, searchTerm)
	}
	if isActive, ok := filters["is_active"].(*bool); ok && isActive != nil {
		query = query.Where("is_active = ?", *isActive)
	}
	if status, ok := filters["status"].(*models.BlogTagStatus); ok && status != nil {
		query = query.Where("status = ?", *status)
	}

	// Apply sorting
	sortBy := "name"
	if sortByFilter, ok := filters["sort_by"].(string); ok && sortByFilter != "" {
		sortBy = sortByFilter
	}
	sortOrder := "asc"
	if sortOrderFilter, ok := filters["sort_order"].(string); ok && sortOrderFilter != "" {
		sortOrder = sortOrderFilter
	}

	// Parse cursor for pagination
	var cursorID uint
	var cursorTime time.Time
	if req.Cursor != "" {
		if parsed, err := pagination.ParseCursor(req.Cursor); err == nil {
			cursorID = parsed.ID
			cursorTime = parsed.CreatedAt
		}
	}

	// Apply cursor-based pagination
	if cursorID > 0 {
		switch sortBy {
		case "created_at":
			if sortOrder == "desc" {
				query = query.Where("(created_at < ? OR (created_at = ? AND id < ?))", cursorTime, cursorTime, cursorID)
			} else {
				query = query.Where("(created_at > ? OR (created_at = ? AND id > ?))", cursorTime, cursorTime, cursorID)
			}
		case "updated_at":
			// For updated_at, we'd need to store the updated_at value in the cursor
			// For now, we'll use created_at as proxy
			if sortOrder == "desc" {
				query = query.Where("(updated_at < ? OR (updated_at = ? AND id < ?))", cursorTime, cursorTime, cursorID)
			} else {
				query = query.Where("(updated_at > ? OR (updated_at = ? AND id > ?))", cursorTime, cursorTime, cursorID)
			}
		case "name":
			// For name sorting, we use ID-based pagination for simplicity
			if sortOrder == "desc" {
				query = query.Where("id < ?", cursorID)
			} else {
				query = query.Where("id > ?", cursorID)
			}
		case "usage_count":
			// For usage_count sorting, similar approach
			if sortOrder == "desc" {
				query = query.Where("id < ?", cursorID)
			} else {
				query = query.Where("id > ?", cursorID)
			}
		default: // "id"
			if sortOrder == "desc" {
				query = query.Where("id < ?", cursorID)
			} else {
				query = query.Where("id > ?", cursorID)
			}
		}
	}

	// Apply sorting
	orderClause := fmt.Sprintf("%s %s", sortBy, sortOrder)
	if sortBy != "id" {
		// Add secondary sort by ID for consistent ordering
		orderClause += fmt.Sprintf(", id %s", sortOrder)
	}
	query = query.Order(orderClause)

	// Get limit + 1 to check if there are more records
	limit := req.GetLimitWithDefault()
	query = query.Limit(limit + 1)

	// Execute query
	if err := query.Find(&tags).Error; err != nil {
		return nil, nil, err
	}

	// Check if there are more records
	hasMore := len(tags) > limit
	if hasMore {
		// Remove the extra record
		tags = tags[:limit]
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: hasMore,
		HasMore: hasMore,
		Count:   len(tags),
		Limit:   limit,
	}

	// Generate next cursor if there are more items
	if hasMore && len(tags) > 0 {
		lastTag := tags[len(tags)-1]
		nextCursor, err := pagination.EncodeCursor(lastTag.ID, lastTag.CreatedAt)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to encode next cursor: %w", err)
		}
		response.NextCursor = nextCursor
	}

	return tags, response, nil
}

// GetByNames gets tags by their names for a specific tenant and website
func (r *BlogTagRepository) GetByNames(ctx context.Context, tenantID, websiteID uint, names []string) ([]models.BlogTag, error) {
	var tags []models.BlogTag
	if len(names) == 0 {
		return tags, nil
	}

	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND name IN ? AND status != ?", tenantID, websiteID, names, "deleted").
		Find(&tags).Error

	return tags, err
}

// GetMostUsed gets the most used tags for a website
func (r *BlogTagRepository) GetMostUsed(ctx context.Context, tenantID, websiteID uint, limit int) ([]models.BlogTag, error) {
	var tags []models.BlogTag
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status != ? AND is_active = ?", tenantID, websiteID, "deleted", true).
		Order("usage_count DESC").
		Limit(limit).
		Find(&tags).Error

	return tags, err
}

// GetSuggestions gets tag suggestions based on a query string
func (r *BlogTagRepository) GetSuggestions(ctx context.Context, tenantID, websiteID uint, query string, limit int) ([]models.BlogTag, error) {
	var tags []models.BlogTag
	if query == "" {
		return tags, nil
	}

	searchTerm := "%" + strings.ToLower(query) + "%"
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND LOWER(name) LIKE ? AND status != ? AND is_active = ?",
			tenantID, websiteID, searchTerm, "deleted", true).
		Order("usage_count DESC").
		Limit(limit).
		Find(&tags).Error

	return tags, err
}

// IncrementUsage increments the usage count of a tag
func (r *BlogTagRepository) IncrementUsage(ctx context.Context, tenantID, tagID uint) error {
	return r.db.WithContext(ctx).
		Model(&models.BlogTag{}).
		Where("tenant_id = ? AND id = ?", tenantID, tagID).
		Update("usage_count", gorm.Expr("usage_count + 1")).Error
}

// DecrementUsage decrements the usage count of a tag
func (r *BlogTagRepository) DecrementUsage(ctx context.Context, tenantID, tagID uint) error {
	return r.db.WithContext(ctx).
		Model(&models.BlogTag{}).
		Where("tenant_id = ? AND id = ? AND usage_count > 0", tenantID, tagID).
		Update("usage_count", gorm.Expr("usage_count - 1")).Error
}

// UpdateUsageCount updates the usage count of a tag to a specific value
func (r *BlogTagRepository) UpdateUsageCount(ctx context.Context, tenantID, tagID uint, count uint) error {
	return r.db.WithContext(ctx).
		Model(&models.BlogTag{}).
		Where("tenant_id = ? AND id = ?", tenantID, tagID).
		Update("usage_count", count).Error
}
