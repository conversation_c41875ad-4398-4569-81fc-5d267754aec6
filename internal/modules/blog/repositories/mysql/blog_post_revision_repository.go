package mysql

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
)

// BlogPostRevisionRepository implements the blog post revision repository interface using MySQL
type BlogPostRevisionRepository struct {
	db *gorm.DB
}

// NewBlogPostRevisionRepository creates a new instance of BlogPostRevisionRepository
func NewBlogPostRevisionRepository(db *gorm.DB) repositories.BlogPostRevisionRepository {
	return &BlogPostRevisionRepository{db: db}
}

// Create creates a new blog post revision
func (r *BlogPostRevisionRepository) Create(ctx context.Context, revision *models.BlogPostRevision) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Get the current highest revision number for this post
		var maxRevision uint
		tx.Model(&models.BlogPostRevision{}).
			Where("tenant_id = ? AND post_id = ?", revision.TenantID, revision.PostID).
			Select("COALESCE(MAX(revision_number), 0)").
			Scan(&maxRevision)

		// Set the next revision number
		revision.RevisionNumber = maxRevision + 1

		// Create the revision
		return tx.Create(revision).Error
	})
}

// GetByPostID gets blog post revisions by post ID with pagination
func (r *BlogPostRevisionRepository) GetByPostID(ctx context.Context, tenantID, postID uint, limit, offset int) ([]models.BlogPostRevision, int64, error) {
	var revisions []models.BlogPostRevision
	var total int64

	// Count total revisions
	if err := r.db.WithContext(ctx).Model(&models.BlogPostRevision{}).
		Where("tenant_id = ? AND post_id = ?", tenantID, postID).
		Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Get revisions with pagination
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND post_id = ?", tenantID, postID).
		Order("revision_number DESC").
		Offset(offset).
		Limit(limit).
		Preload("Post").
		Find(&revisions).Error

	return revisions, total, err
}

// GetByID gets a specific blog post revision by ID
func (r *BlogPostRevisionRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.BlogPostRevision, error) {
	var revision models.BlogPostRevision
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Preload("Post").
		First(&revision).Error

	if err != nil {
		return nil, err
	}
	return &revision, nil
}

// Delete deletes a blog post revision
func (r *BlogPostRevisionRepository) Delete(ctx context.Context, tenantID, id uint) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Delete(&models.BlogPostRevision{}).Error
}

// DeleteOldRevisions deletes old revisions keeping only the specified number
func (r *BlogPostRevisionRepository) DeleteOldRevisions(ctx context.Context, tenantID, postID uint, keepCount int) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Get revision IDs to delete (keep the latest keepCount revisions)
		var revisionIDs []uint
		err := tx.Model(&models.BlogPostRevision{}).
			Where("tenant_id = ? AND post_id = ?", tenantID, postID).
			Order("revision_number DESC").
			Offset(keepCount).
			Pluck("id", &revisionIDs).Error

		if err != nil {
			return err
		}

		if len(revisionIDs) > 0 {
			return tx.Where("id IN ?", revisionIDs).Delete(&models.BlogPostRevision{}).Error
		}

		return nil
	})
}

// List retrieves blog post revisions with filters and pagination
func (r *BlogPostRevisionRepository) List(ctx context.Context, filter *models.BlogPostRevisionFilter) ([]models.BlogPostRevision, int64, error) {
	var revisions []models.BlogPostRevision
	var total int64

	query := r.db.WithContext(ctx).Model(&models.BlogPostRevision{})

	// Apply filters
	if filter.TenantID != 0 {
		query = query.Where("tenant_id = ?", filter.TenantID)
	}
	if filter.PostID != 0 {
		query = query.Where("post_id = ?", filter.PostID)
	}
	if filter.UserID != 0 {
		query = query.Where("user_id = ?", filter.UserID)
	}
	if filter.DateFrom != nil {
		query = query.Where("created_at >= ?", *filter.DateFrom)
	}
	if filter.DateTo != nil {
		query = query.Where("created_at <= ?", *filter.DateTo)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	sortBy := "created_at"
	if filter.SortBy != "" {
		sortBy = filter.SortBy
	}
	sortOrder := "DESC"
	if filter.SortOrder != "" {
		sortOrder = filter.SortOrder
	}
	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	// Execute query with preloads
	err := query.
		Preload("Post").
		Find(&revisions).Error

	return revisions, total, err
}

// GetByRevisionNumber gets a specific revision by post ID and revision number
func (r *BlogPostRevisionRepository) GetByRevisionNumber(ctx context.Context, tenantID, postID uint, revisionNumber uint) (*models.BlogPostRevision, error) {
	var revision models.BlogPostRevision
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND post_id = ? AND revision_number = ?", tenantID, postID, revisionNumber).
		Preload("Post").
		First(&revision).Error

	if err != nil {
		return nil, err
	}
	return &revision, nil
}

// GetLatest gets the latest revision for a post
func (r *BlogPostRevisionRepository) GetLatest(ctx context.Context, tenantID, postID uint) (*models.BlogPostRevision, error) {
	var revision models.BlogPostRevision
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND post_id = ?", tenantID, postID).
		Order("revision_number DESC").
		Preload("Post").
		First(&revision).Error

	if err != nil {
		return nil, err
	}
	return &revision, nil
}

// CompareRevisions compares two revisions and returns the differences
func (r *BlogPostRevisionRepository) CompareRevisions(ctx context.Context, tenantID uint, fromRevisionID, toRevisionID uint) (*models.BlogPostRevisionComparison, error) {
	var fromRevision, toRevision models.BlogPostRevision

	// Get both revisions
	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, fromRevisionID).
		First(&fromRevision).Error; err != nil {
		return nil, err
	}

	if err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, toRevisionID).
		First(&toRevision).Error; err != nil {
		return nil, err
	}

	// Create comparison
	comparison := &models.BlogPostRevisionComparison{}
	comparison.FromRevision.FromBlogPostRevision(&fromRevision)
	comparison.ToRevision.FromBlogPostRevision(&toRevision)

	// Calculate changes
	changes := models.RevisionChanges{
		TitleChanged:           fromRevision.Title != toRevision.Title,
		ContentChanged:         fromRevision.Content != toRevision.Content,
		ExcerptChanged:         fromRevision.Excerpt != toRevision.Excerpt,
		MetaTitleChanged:       fromRevision.MetaTitle != toRevision.MetaTitle,
		MetaDescriptionChanged: fromRevision.MetaDescription != toRevision.MetaDescription,
	}

	// Generate summary
	changesCount := 0
	if changes.TitleChanged {
		changesCount++
	}
	if changes.ContentChanged {
		changesCount++
	}
	if changes.ExcerptChanged {
		changesCount++
	}
	if changes.MetaTitleChanged {
		changesCount++
	}
	if changes.MetaDescriptionChanged {
		changesCount++
	}

	if changesCount == 0 {
		changes.Summary = "No changes detected"
	} else {
		changes.Summary = fmt.Sprintf("%d field(s) changed", changesCount)
	}

	comparison.Changes = changes

	return comparison, nil
}

// GetStats gets revision statistics
func (r *BlogPostRevisionRepository) GetStats(ctx context.Context, tenantID uint) (*models.BlogPostRevisionStats, error) {
	var stats models.BlogPostRevisionStats

	// Total revisions
	r.db.WithContext(ctx).Model(&models.BlogPostRevision{}).
		Where("tenant_id = ?", tenantID).
		Count(&[]int64{int64(stats.TotalRevisions)}[0])

	// Revisions today
	r.db.WithContext(ctx).Model(&models.BlogPostRevision{}).
		Where("tenant_id = ? AND DATE(created_at) = CURDATE()", tenantID).
		Count(&[]int64{int64(stats.RevisionsToday)}[0])

	// Revisions this week
	r.db.WithContext(ctx).Model(&models.BlogPostRevision{}).
		Where("tenant_id = ? AND YEARWEEK(created_at) = YEARWEEK(NOW())", tenantID).
		Count(&[]int64{int64(stats.RevisionsThisWeek)}[0])

	// Active authors (distinct users who made revisions this week)
	r.db.WithContext(ctx).Model(&models.BlogPostRevision{}).
		Where("tenant_id = ? AND YEARWEEK(created_at) = YEARWEEK(NOW())", tenantID).
		Distinct("user_id").
		Count(&[]int64{int64(stats.ActiveAuthors)}[0])

	return &stats, nil
}
