package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	notificationModels "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	notificationServices "github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

// blogPostService implements BlogPostService interface
type blogPostService struct {
	repo                repositories.BlogPostRepository
	tagRepo             repositories.BlogTagRepository
	notificationService notificationServices.NotificationService
	logger              utils.Logger
}

// NewBlogPostService creates a new blog post service
func NewBlogPostService(
	repo repositories.BlogPostRepository,
	tagRepo repositories.BlogTagRepository,
	notificationService notificationServices.NotificationService,
	logger utils.Logger,
) BlogPostService {
	return &blogPostService{
		repo:                repo,
		tagRepo:             tagRepo,
		notificationService: notificationService,
		logger:              logger,
	}
}

// Create creates a new blog post
func (s *blogPostService) Create(ctx context.Context, req *models.BlogPostCreateRequest) (*models.BlogPostResponse, error) {
	// Check for duplicate slug within the same website
	existing, err := s.repo.GetBySlug(ctx, req.TenantID, req.WebsiteID, req.Slug)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("post with slug '%s' already exists", req.Slug)
	}

	// Create the post model
	post := &models.BlogPost{
		TenantID:      req.TenantID,
		WebsiteID:     req.WebsiteID,
		CategoryID:    req.CategoryID,
		AuthorID:      req.AuthorID,
		Slug:          req.Slug,
		Title:         req.Title,
		Content:       req.Content,
		Excerpt:       req.Excerpt,
		FeaturedImage: req.FeaturedImage,
		Type:          req.Type,
		IsFeatured:    req.IsFeatured,
		AllowComments: req.AllowComments,
		Password:      req.Password,
		ViewCount:     0,
		ScheduledAt:   req.ScheduledAt,
		Status:        models.BlogPostStatus(req.Status),
	}

	// Set default status if not provided
	if post.Status == "" {
		post.Status = "draft"
	}

	// Auto-publish if status is published
	if post.Status == "published" {
		now := time.Now()
		post.PublishedAt = &now
	}

	// Create the post
	err = s.repo.Create(ctx, post)
	if err != nil {
		return nil, fmt.Errorf("failed to create post: %w", err)
	}

	// Handle tags if provided
	if len(req.TagIDs) > 0 {
		err = s.repo.AttachTags(ctx, req.TenantID, post.ID, req.TagIDs)
		if err != nil {
			return nil, fmt.Errorf("failed to attach tags: %w", err)
		}

		// Increment usage count for tags
		for _, tagID := range req.TagIDs {
			s.tagRepo.IncrementUsage(ctx, req.TenantID, tagID)
		}
	}

	// Convert to response
	response := &models.BlogPostResponse{}
	response.FromBlogPost(post)
	return response, nil
}

// GetByID retrieves a blog post by ID
func (s *blogPostService) GetByID(ctx context.Context, tenantID, id uint) (*models.BlogPostResponse, error) {
	post, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("post not found")
		}
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	response := &models.BlogPostResponse{}
	response.FromBlogPost(post)
	return response, nil
}

// GetBySlug retrieves a blog post by slug
func (s *blogPostService) GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogPostResponse, error) {
	post, err := s.repo.GetBySlug(ctx, tenantID, websiteID, slug)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("post not found")
		}
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	response := &models.BlogPostResponse{}
	response.FromBlogPost(post)
	return response, nil
}

// Update updates a blog post
func (s *blogPostService) Update(ctx context.Context, tenantID, id uint, req *models.BlogPostUpdateRequest) (*models.BlogPostResponse, error) {
	// Get existing post
	existing, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("post not found")
		}
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	// Check for duplicate slug if slug is being changed
	if req.Slug != existing.Slug {
		existingBySlug, err := s.repo.GetBySlug(ctx, tenantID, existing.WebsiteID, req.Slug)
		if err == nil && existingBySlug != nil && existingBySlug.ID != existing.ID {
			return nil, fmt.Errorf("post with slug '%s' already exists", req.Slug)
		}
	}

	// Store old status for comparison
	oldStatus := existing.Status

	// Update the post
	existing.CategoryID = req.CategoryID
	existing.Slug = req.Slug
	existing.Title = req.Title
	existing.Content = req.Content
	existing.Excerpt = req.Excerpt
	existing.FeaturedImage = req.FeaturedImage
	existing.Type = req.Type
	existing.IsFeatured = req.IsFeatured
	existing.AllowComments = req.AllowComments
	existing.Password = req.Password
	existing.ScheduledAt = req.ScheduledAt
	existing.Status = models.BlogPostStatus(req.Status)

	// Handle publishing logic
	if oldStatus != "published" && req.Status == "published" {
		// Post is being published for the first time
		now := time.Now()
		existing.PublishedAt = &now
	} else if oldStatus == "published" && req.Status != "published" {
		// Post is being unpublished
		existing.PublishedAt = nil
	}

	err = s.repo.Update(ctx, tenantID, id, existing)
	if err != nil {
		return nil, fmt.Errorf("failed to update post: %w", err)
	}

	// Handle tags if provided
	if req.TagIDs != nil {
		// Get current tags to update usage counts
		currentTags, err := s.repo.GetPostTags(ctx, tenantID, id)
		if err == nil {
			// Decrement usage count for current tags
			for _, tag := range currentTags {
				s.tagRepo.DecrementUsage(ctx, tenantID, tag.ID)
			}
		}

		// Sync new tags
		err = s.repo.SyncTags(ctx, tenantID, id, req.TagIDs)
		if err != nil {
			return nil, fmt.Errorf("failed to sync tags: %w", err)
		}

		// Increment usage count for new tags
		for _, tagID := range req.TagIDs {
			s.tagRepo.IncrementUsage(ctx, tenantID, tagID)
		}
	}

	response := &models.BlogPostResponse{}
	response.FromBlogPost(existing)
	return response, nil
}

// Delete soft deletes a blog post
func (s *blogPostService) Delete(ctx context.Context, tenantID, id uint) error {
	// Check if post exists
	_, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("post not found")
		}
		return fmt.Errorf("failed to get post: %w", err)
	}

	// Get current tags to update usage counts
	currentTags, err := s.repo.GetPostTags(ctx, tenantID, id)
	if err == nil {
		// Decrement usage count for current tags
		for _, tag := range currentTags {
			s.tagRepo.DecrementUsage(ctx, tenantID, tag.ID)
		}
	}

	// Soft delete using repository delete method
	err = s.repo.Delete(ctx, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to delete post: %w", err)
	}

	return nil
}

// List retrieves a list of blog posts with filtering
func (s *blogPostService) List(ctx context.Context, filter *models.BlogPostFilter) ([]models.BlogPostResponse, int64, error) {
	posts, total, err := s.repo.List(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list posts: %w", err)
	}

	responses := make([]models.BlogPostResponse, len(posts))
	for i, post := range posts {
		responses[i].FromBlogPost(&post)
	}

	return responses, total, nil
}

// ListWithCursor retrieves blog posts using cursor-based pagination
func (s *blogPostService) ListWithCursor(ctx context.Context, tenantID, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) (*dto.BlogPostListResponse, error) {
	posts, paginationResp, err := s.repo.ListWithCursor(ctx, tenantID, websiteID, req, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to list posts with cursor: %w", err)
	}

	// Convert posts to response DTOs
	postResponses := make([]dto.BlogPostResponse, len(posts))
	for i, post := range posts {
		postResponses[i] = s.blogPostToResponseDTO(post)
	}

	return &dto.BlogPostListResponse{
		Posts:      postResponses,
		Pagination: paginationResp,
	}, nil
}

// GetPublished retrieves published posts
func (s *blogPostService) GetPublished(ctx context.Context, tenantID, websiteID uint, limit, offset int) ([]models.BlogPostResponse, int64, error) {
	posts, total, err := s.repo.GetPublished(ctx, tenantID, websiteID, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get published posts: %w", err)
	}

	responses := make([]models.BlogPostResponse, len(posts))
	for i, post := range posts {
		responses[i].FromBlogPost(&post)
	}

	return responses, total, nil
}

// GetFeatured retrieves featured posts
func (s *blogPostService) GetFeatured(ctx context.Context, tenantID, websiteID uint, limit int) ([]models.BlogPostResponse, error) {
	posts, err := s.repo.GetFeatured(ctx, tenantID, websiteID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get featured posts: %w", err)
	}

	responses := make([]models.BlogPostResponse, len(posts))
	for i, post := range posts {
		responses[i].FromBlogPost(&post)
	}

	return responses, nil
}

// GetRelated retrieves related posts
func (s *blogPostService) GetRelated(ctx context.Context, tenantID, postID uint, limit int) ([]models.BlogPostResponse, error) {
	posts, err := s.repo.GetRelated(ctx, tenantID, postID, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get related posts: %w", err)
	}

	responses := make([]models.BlogPostResponse, len(posts))
	for i, post := range posts {
		responses[i].FromBlogPost(&post)
	}

	return responses, nil
}

// Publish publishes a blog post
func (s *blogPostService) Publish(ctx context.Context, tenantID, postID uint) (*models.BlogPostResponse, error) {
	post, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("post not found")
		}
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	if post.Status == "published" {
		return nil, fmt.Errorf("post is already published")
	}

	err = s.repo.Publish(ctx, tenantID, postID)
	if err != nil {
		return nil, fmt.Errorf("failed to publish post: %w", err)
	}

	// Get updated post
	updatedPost, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated post: %w", err)
	}

	// Send post published notification
	if s.notificationService != nil {
		err := s.sendPostPublishedNotification(ctx, updatedPost)
		if err != nil {
			// Log error but don't fail publishing - notification is not critical
			s.logger.WithError(err).Error("Failed to send post published notification")
		} else {
			s.logger.WithField("post_id", updatedPost.ID).Info("Post published notification sent successfully")
		}
	}

	response := &models.BlogPostResponse{}
	response.FromBlogPost(updatedPost)
	return response, nil
}

// Unpublish unpublishes a blog post
func (s *blogPostService) Unpublish(ctx context.Context, tenantID, postID uint) (*models.BlogPostResponse, error) {
	post, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("post not found")
		}
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	if post.Status != "published" {
		return nil, fmt.Errorf("post is not published")
	}

	err = s.repo.Unpublish(ctx, tenantID, postID)
	if err != nil {
		return nil, fmt.Errorf("failed to unpublish post: %w", err)
	}

	// Get updated post
	updatedPost, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated post: %w", err)
	}

	response := &models.BlogPostResponse{}
	response.FromBlogPost(updatedPost)
	return response, nil
}

// Schedule schedules a blog post for publishing
func (s *blogPostService) Schedule(ctx context.Context, tenantID, postID uint, req *models.BlogPostScheduleCreateRequest) (*models.BlogPostResponse, error) {
	post, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("post not found")
		}
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	if post.Status == "published" {
		return nil, fmt.Errorf("cannot schedule already published post")
	}

	if req.ScheduledAt.Before(time.Now()) {
		return nil, fmt.Errorf("scheduled time must be in the future")
	}

	err = s.repo.Schedule(ctx, tenantID, postID, &req.ScheduledAt)
	if err != nil {
		return nil, fmt.Errorf("failed to schedule post: %w", err)
	}

	// Get updated post
	updatedPost, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated post: %w", err)
	}

	response := &models.BlogPostResponse{}
	response.FromBlogPost(updatedPost)
	return response, nil
}

// AttachTags attaches tags to a blog post
func (s *blogPostService) AttachTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error {
	// Check if post exists
	_, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("post not found")
		}
		return fmt.Errorf("failed to get post: %w", err)
	}

	err = s.repo.AttachTags(ctx, tenantID, postID, tagIDs)
	if err != nil {
		return fmt.Errorf("failed to attach tags: %w", err)
	}

	// Increment usage count for tags
	for _, tagID := range tagIDs {
		s.tagRepo.IncrementUsage(ctx, tenantID, tagID)
	}

	return nil
}

// DetachTags detaches tags from a blog post
func (s *blogPostService) DetachTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error {
	// Check if post exists
	_, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("post not found")
		}
		return fmt.Errorf("failed to get post: %w", err)
	}

	err = s.repo.DetachTags(ctx, tenantID, postID, tagIDs)
	if err != nil {
		return fmt.Errorf("failed to detach tags: %w", err)
	}

	// Decrement usage count for tags
	for _, tagID := range tagIDs {
		s.tagRepo.DecrementUsage(ctx, tenantID, tagID)
	}

	return nil
}

// SyncTags synchronizes tags for a blog post
func (s *blogPostService) SyncTags(ctx context.Context, tenantID, postID uint, tagIDs []uint) error {
	// Check if post exists
	_, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("post not found")
		}
		return fmt.Errorf("failed to get post: %w", err)
	}

	// Get current tags to update usage counts
	currentTags, err := s.repo.GetPostTags(ctx, tenantID, postID)
	if err == nil {
		// Decrement usage count for current tags
		for _, tag := range currentTags {
			s.tagRepo.DecrementUsage(ctx, tenantID, tag.ID)
		}
	}

	err = s.repo.SyncTags(ctx, tenantID, postID, tagIDs)
	if err != nil {
		return fmt.Errorf("failed to sync tags: %w", err)
	}

	// Increment usage count for new tags
	for _, tagID := range tagIDs {
		s.tagRepo.IncrementUsage(ctx, tenantID, tagID)
	}

	return nil
}

// IncrementViewCount increments the view count of a blog post
func (s *blogPostService) IncrementViewCount(ctx context.Context, tenantID, postID uint) error {
	err := s.repo.IncrementViewCount(ctx, tenantID, postID)
	if err != nil {
		return fmt.Errorf("failed to increment view count: %w", err)
	}
	return nil
}

// GetStats retrieves blog post statistics
func (s *blogPostService) GetStats(ctx context.Context, tenantID, websiteID uint) (*models.BlogPostStats, error) {
	stats, err := s.repo.GetStats(ctx, tenantID, websiteID)
	if err != nil {
		return nil, fmt.Errorf("failed to get blog post stats: %w", err)
	}
	return stats, nil
}

// GetPopular retrieves popular posts based on view count
func (s *blogPostService) GetPopular(ctx context.Context, tenantID, websiteID uint, days int, limit int) ([]models.BlogPostResponse, error) {
	posts, err := s.repo.GetPopular(ctx, tenantID, websiteID, days, limit)
	if err != nil {
		return nil, fmt.Errorf("failed to get popular posts: %w", err)
	}

	responses := make([]models.BlogPostResponse, len(posts))
	for i, post := range posts {
		responses[i].FromBlogPost(&post)
	}

	return responses, nil
}

// CreateRevision creates a revision for a blog post
func (s *blogPostService) CreateRevision(ctx context.Context, tenantID, postID, userID uint, changesSummary string) (*models.BlogPostRevisionResponse, error) {
	// Check if post exists
	post, err := s.repo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("post not found")
		}
		return nil, fmt.Errorf("failed to get post: %w", err)
	}

	// This would need a revision repository, but for now we'll return a mock response
	response := &models.BlogPostRevisionResponse{
		ID:             1, // Mock ID
		TenantID:       tenantID,
		PostID:         postID,
		UserID:         userID,
		Title:          post.Title,
		Content:        post.Content,
		Excerpt:        post.Excerpt,
		ChangesSummary: changesSummary,
		CreatedAt:      time.Now(),
	}

	return response, nil
}

// sendPostPublishedNotification sends a notification when a blog post is published
func (s *blogPostService) sendPostPublishedNotification(ctx context.Context, post *models.BlogPost) error {
	// Template data for post published notification
	templateData := map[string]interface{}{
		"post": map[string]interface{}{
			"title":   post.Title,
			"excerpt": post.Excerpt,
			"slug":    post.Slug,
		},
		"author": map[string]interface{}{
			"id": post.AuthorID,
		},
		"website": map[string]interface{}{
			"id": post.WebsiteID,
		},
		"published_at": time.Now().Format("2006-01-02 15:04:05"),
		"current_year": time.Now().Year(),
	}

	// Create notification request
	notificationReq := notificationModels.CreateNotificationRequest{
		Type:         "blog_post_published",
		Channel:      notificationModels.ChannelEmail,
		Subject:      fmt.Sprintf("📝 New Blog Post Published: %s", post.Title),
		TemplateID:   func() *uint { id := uint(3); return &id }(), // Use seeded blog_post_published template ID
		TemplateData: templateData,
		Recipients: []notificationModels.CreateRecipientRequest{
			{
				UserID:           &post.AuthorID,
				RecipientType:    notificationModels.RecipientTypeUser,
				RecipientAddress: "", // Will be resolved from user
			},
		},
		Priority: notificationModels.PriorityNormal,
		Metadata: map[string]interface{}{
			"source":     "blog_post_published",
			"post_id":    post.ID,
			"tenant_id":  post.TenantID,
			"website_id": post.WebsiteID,
			"author_id":  post.AuthorID,
		},
	}

	// Send notification via notification service
	_, err := s.notificationService.CreateNotification(post.TenantID, notificationReq)
	if err != nil {
		return fmt.Errorf("failed to create post published notification: %w", err)
	}

	return nil
}

// blogPostToResponseDTO converts BlogPost model to DTO BlogPostResponse
func (s *blogPostService) blogPostToResponseDTO(post models.BlogPost) dto.BlogPostResponse {
	// Convert category
	var category *dto.BlogCategoryResponse
	if post.Category != nil {
		category = &dto.BlogCategoryResponse{
			ID:          post.Category.ID,
			TenantID:    post.Category.TenantID,
			WebsiteID:   post.Category.WebsiteID,
			Name:        post.Category.Name,
			Slug:        post.Category.Slug,
			Description: post.Category.Description,
			CreatedAt:   post.Category.CreatedAt,
			UpdatedAt:   post.Category.UpdatedAt,
		}
	}

	// Convert tags
	tags := make([]dto.BlogTagResponse, len(post.Tags))
	for i, tag := range post.Tags {
		tags[i] = dto.BlogTagResponse{
			ID:        tag.ID,
			TenantID:  tag.TenantID,
			WebsiteID: tag.WebsiteID,
			Name:      tag.Name,
			Slug:      tag.Slug,
			CreatedAt: tag.CreatedAt,
			UpdatedAt: tag.UpdatedAt,
		}
	}

	return dto.BlogPostResponse{
		ID:            post.ID,
		TenantID:      post.TenantID,
		WebsiteID:     post.WebsiteID,
		Slug:          post.Slug,
		Title:         post.Title,
		Content:       post.Content,
		Excerpt:       post.Excerpt,
		AuthorID:      post.AuthorID,
		CategoryID:    post.CategoryID,
		Type:          post.Type,
		IsFeatured:    post.IsFeatured,
		AllowComments: post.AllowComments,
		FeaturedImage: post.FeaturedImage,
		ViewCount:     post.ViewCount,
		ScheduledAt:   post.ScheduledAt,
		PublishedAt:   post.PublishedAt,
		Status:        post.Status,
		CreatedAt:     post.CreatedAt,
		UpdatedAt:     post.UpdatedAt,
		Category:      category,
		Tags:          tags,
	}
}
