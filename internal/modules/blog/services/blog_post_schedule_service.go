package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"gorm.io/gorm"
)

// blogPostScheduleService implements BlogPostScheduleService interface
type blogPostScheduleService struct {
	repo     repositories.BlogPostScheduleRepository
	postRepo repositories.BlogPostRepository
}

// NewBlogPostScheduleService creates a new blog post schedule service
func NewBlogPostScheduleService(repo repositories.BlogPostScheduleRepository, postRepo repositories.BlogPostRepository) BlogPostScheduleService {
	return &blogPostScheduleService{
		repo:     repo,
		postRepo: postRepo,
	}
}

// Create creates a new blog post schedule
func (s *blogPostScheduleService) Create(ctx context.Context, req *models.BlogPostScheduleCreateRequest) (*models.BlogPostScheduleResponse, error) {
	// Validate that the post exists
	post, err := s.postRepo.GetByID(ctx, req.TenantID, req.PostID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("post not found")
		}
		return nil, fmt.Errorf("failed to validate post: %w", err)
	}

	// Validate that the scheduled time is in the future
	if req.ScheduledAt.Before(time.Now()) {
		return nil, fmt.Errorf("scheduled time must be in the future")
	}

	// Check if there's already a schedule for this post
	existing, err := s.repo.GetByPostID(ctx, req.TenantID, req.PostID)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("post already has a schedule")
	}

	// Create the schedule model
	schedule := &models.BlogPostSchedule{
		TenantID:    req.TenantID,
		PostID:      req.PostID,
		ScheduledAt: req.ScheduledAt,
		Status:      "pending",
	}

	// Create the schedule
	err = s.repo.Create(ctx, schedule)
	if err != nil {
		return nil, fmt.Errorf("failed to create schedule: %w", err)
	}

	// Update post status to scheduled
	post.Status = models.BlogPostStatusScheduled
	post.ScheduledAt = &req.ScheduledAt
	err = s.postRepo.Update(ctx, req.TenantID, req.PostID, post)
	if err != nil {
		return nil, fmt.Errorf("failed to update post status: %w", err)
	}

	// Convert to response
	response := &models.BlogPostScheduleResponse{}
	response.FromBlogPostSchedule(schedule)
	return response, nil
}

// GetByPostID retrieves a schedule by post ID
func (s *blogPostScheduleService) GetByPostID(ctx context.Context, tenantID, postID uint) (*models.BlogPostScheduleResponse, error) {
	schedule, err := s.repo.GetByPostID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("schedule not found")
		}
		return nil, fmt.Errorf("failed to get schedule: %w", err)
	}

	response := &models.BlogPostScheduleResponse{}
	response.FromBlogPostSchedule(schedule)
	return response, nil
}

// Update updates a blog post schedule
func (s *blogPostScheduleService) Update(ctx context.Context, tenantID, id uint, req *models.BlogPostScheduleUpdateRequest) (*models.BlogPostScheduleResponse, error) {
	// Get existing schedule by ID (we need to find it first by searching through schedules)
	// For now, we'll use a simple approach - this could be optimized with a GetByID method
	schedules, err := s.repo.GetPendingSchedules(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get schedules: %w", err)
	}

	var existing *models.BlogPostSchedule
	for _, schedule := range schedules {
		if schedule.ID == id && schedule.TenantID == tenantID {
			existing = &schedule
			break
		}
	}

	if existing == nil {
		return nil, fmt.Errorf("schedule not found")
	}

	// Validate that the scheduled time is in the future
	if req.ScheduledAt.Before(time.Now()) {
		return nil, fmt.Errorf("scheduled time must be in the future")
	}

	// Update the schedule
	existing.ScheduleType = req.ScheduleType
	existing.ScheduledAt = req.ScheduledAt
	existing.Timezone = req.Timezone
	existing.Status = req.Status

	err = s.repo.Update(ctx, tenantID, id, existing)
	if err != nil {
		return nil, fmt.Errorf("failed to update schedule: %w", err)
	}

	// Update post's scheduled time
	post, err := s.postRepo.GetByID(ctx, tenantID, existing.PostID)
	if err == nil {
		post.ScheduledAt = &req.ScheduledAt
		s.postRepo.Update(ctx, tenantID, existing.PostID, post)
	}

	response := &models.BlogPostScheduleResponse{}
	response.FromBlogPostSchedule(existing)
	return response, nil
}

// Delete removes a blog post schedule
func (s *blogPostScheduleService) Delete(ctx context.Context, tenantID, id uint) error {
	// Get the schedule to find the post ID
	schedule, err := s.repo.GetByPostID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("schedule not found")
		}
		return fmt.Errorf("failed to get schedule: %w", err)
	}

	// Delete the schedule
	err = s.repo.Delete(ctx, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to delete schedule: %w", err)
	}

	// Update post status back to draft
	post, err := s.postRepo.GetByID(ctx, tenantID, schedule.PostID)
	if err == nil {
		post.Status = models.BlogPostStatusDraft
		post.ScheduledAt = nil
		s.postRepo.Update(ctx, tenantID, schedule.PostID, post)
	}

	return nil
}

// List retrieves a list of blog post schedules with filtering
func (s *blogPostScheduleService) List(ctx context.Context, filter *models.BlogPostScheduleFilter) ([]models.BlogPostScheduleResponse, int64, error) {
	// Since repository doesn't have List method, we'll use GetPendingSchedules as a fallback
	// This should be improved in the repository implementation
	schedules, err := s.repo.GetPendingSchedules(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list schedules: %w", err)
	}

	// Filter schedules based on filter criteria
	var filteredSchedules []models.BlogPostSchedule
	for _, schedule := range schedules {
		if filter.TenantID > 0 && schedule.TenantID != filter.TenantID {
			continue
		}
		if filter.PostID > 0 && schedule.PostID != filter.PostID {
			continue
		}
		if filter.Status != "" && schedule.Status != filter.Status {
			continue
		}
		if filter.ScheduleType != "" && schedule.ScheduleType != filter.ScheduleType {
			continue
		}
		filteredSchedules = append(filteredSchedules, schedule)
	}

	responses := make([]models.BlogPostScheduleResponse, len(filteredSchedules))
	for i, schedule := range filteredSchedules {
		responses[i].FromBlogPostSchedule(&schedule)
	}

	return responses, int64(len(filteredSchedules)), nil
}

// ProcessPendingSchedules processes all pending schedules
func (s *blogPostScheduleService) ProcessPendingSchedules(ctx context.Context) error {
	// Get all pending schedules
	schedules, err := s.repo.GetPendingSchedules(ctx)
	if err != nil {
		return fmt.Errorf("failed to get pending schedules: %w", err)
	}

	// Log the number of pending schedules found
	fmt.Printf("📅 Found %d pending schedules to process\n", len(schedules))

	now := time.Now()
	processedCount := 0
	errorCount := 0

	for _, schedule := range schedules {
		// Check if it's time to publish
		if schedule.ScheduledAt.Before(now) || schedule.ScheduledAt.Equal(now) {
			// Get the post (remove unused variable)
			_, err := s.postRepo.GetByID(ctx, schedule.TenantID, schedule.PostID)
			if err != nil {
				// For now, just log the error since MarkAsFailed is not available
				fmt.Printf("Error: Failed to get post %d: %v\n", schedule.PostID, err)
				errorCount++
				continue
			}

			// Publish the post
			err = s.postRepo.Publish(ctx, schedule.TenantID, schedule.PostID)
			if err != nil {
				// For now, just log the error since MarkAsFailed is not available
				fmt.Printf("Error: Failed to publish post %d: %v\n", schedule.PostID, err)
				errorCount++
				continue
			}

			// Mark schedule as executed
			err = s.repo.MarkAsExecuted(ctx, schedule.TenantID, schedule.ID)
			if err != nil {
				// Log error but don't fail the process since post was published
				fmt.Printf("Warning: Failed to mark schedule %d as executed: %v\n", schedule.ID, err)
			}

			processedCount++
		}
	}

	if processedCount > 0 || errorCount > 0 {
		fmt.Printf("✅ Processed %d schedules successfully, %d failed\n", processedCount, errorCount)
	}

	if errorCount > 0 {
		return fmt.Errorf("processed %d schedules successfully, %d failed", processedCount, errorCount)
	}

	return nil
}

// MarkAsExecuted marks a schedule as executed
func (s *blogPostScheduleService) MarkAsExecuted(ctx context.Context, tenantID, scheduleID uint) error {
	err := s.repo.MarkAsExecuted(ctx, tenantID, scheduleID)
	if err != nil {
		return fmt.Errorf("failed to mark schedule as executed: %w", err)
	}
	return nil
}

// MarkAsFailed marks a schedule as failed
func (s *blogPostScheduleService) MarkAsFailed(ctx context.Context, tenantID, scheduleID uint, errorMessage string) error {
	// Since MarkAsFailed is not available in repository interface,
	// we'll implement a workaround by updating the schedule status
	// This should be improved when the repository interface is extended

	// For now, just log the error
	fmt.Printf("Schedule %d failed: %s\n", scheduleID, errorMessage)
	return nil
}
