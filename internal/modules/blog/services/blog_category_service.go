package services

import (
	"context"
	"errors"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"gorm.io/gorm"
)

// blogCategoryService implements BlogCategoryService interface
type blogCategoryService struct {
	repo repositories.BlogCategoryRepository
}

// NewBlogCategoryService creates a new blog category service
func NewBlogCategoryService(repo repositories.BlogCategoryRepository) BlogCategoryService {
	return &blogCategoryService{
		repo: repo,
	}
}

// <PERSON><PERSON> creates a new blog category
func (s *blogCategoryService) Create(ctx context.Context, req *models.BlogCategoryCreateRequest) (*models.BlogCategoryResponse, error) {
	// Validate parent category if specified
	if req.ParentID != nil {
		parent, err := s.repo.GetByID(ctx, req.TenantID, *req.ParentID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("parent category not found")
			}
			return nil, fmt.Errorf("failed to validate parent category: %w", err)
		}
		if parent.WebsiteID != req.WebsiteID {
			return nil, fmt.Errorf("parent category must belong to the same website")
		}
	}

	// Check for duplicate slug within the same website
	existing, err := s.repo.GetBySlug(ctx, req.TenantID, req.WebsiteID, req.Slug)
	if err == nil && existing != nil {
		return nil, fmt.Errorf("category with slug '%s' already exists", req.Slug)
	}

	// Create the category model
	category := &models.BlogCategory{
		TenantID:    req.TenantID,
		WebsiteID:   req.WebsiteID,
		Slug:        req.Slug,
		Name:        req.Name,
		Description: req.Description,
		ParentID:    req.ParentID,
		Color:       req.Color,
		IsActive:    req.IsActive,
		SortOrder:   req.SortOrder,
		Status:      "active",
	}

	// Set default color if not provided
	if category.Color == "" {
		category.Color = "#000000"
	}

	// Create the category
	err = s.repo.Create(ctx, category)
	if err != nil {
		return nil, fmt.Errorf("failed to create category: %w", err)
	}

	// Convert to response
	response := &models.BlogCategoryResponse{}
	response.FromBlogCategory(category)
	return response, nil
}

// GetByID retrieves a blog category by ID
func (s *blogCategoryService) GetByID(ctx context.Context, tenantID, id uint) (*models.BlogCategoryResponse, error) {
	category, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("category not found")
		}
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	response := &models.BlogCategoryResponse{}
	response.FromBlogCategory(category)
	return response, nil
}

// GetBySlug retrieves a blog category by slug
func (s *blogCategoryService) GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*models.BlogCategoryResponse, error) {
	category, err := s.repo.GetBySlug(ctx, tenantID, websiteID, slug)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("category not found")
		}
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	response := &models.BlogCategoryResponse{}
	response.FromBlogCategory(category)
	return response, nil
}

// Update updates a blog category
func (s *blogCategoryService) Update(ctx context.Context, tenantID, id uint, req *models.BlogCategoryUpdateRequest) (*models.BlogCategoryResponse, error) {
	// Get existing category
	existing, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("category not found")
		}
		return nil, fmt.Errorf("failed to get category: %w", err)
	}

	// Validate parent category if specified and changed
	if req.ParentID != nil && (existing.ParentID == nil || *req.ParentID != *existing.ParentID) {
		if *req.ParentID == existing.ID {
			return nil, fmt.Errorf("category cannot be its own parent")
		}

		parent, err := s.repo.GetByID(ctx, tenantID, *req.ParentID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("parent category not found")
			}
			return nil, fmt.Errorf("failed to validate parent category: %w", err)
		}
		if parent.WebsiteID != existing.WebsiteID {
			return nil, fmt.Errorf("parent category must belong to the same website")
		}

		// Check for circular reference
		if err := s.checkCircularReference(ctx, tenantID, existing.ID, *req.ParentID); err != nil {
			return nil, err
		}
	}

	// Check for duplicate slug if slug is being changed
	if req.Slug != existing.Slug {
		existingBySlug, err := s.repo.GetBySlug(ctx, tenantID, existing.WebsiteID, req.Slug)
		if err == nil && existingBySlug != nil && existingBySlug.ID != existing.ID {
			return nil, fmt.Errorf("category with slug '%s' already exists", req.Slug)
		}
	}

	// Update the category
	existing.Slug = req.Slug
	existing.Name = req.Name
	existing.Description = req.Description
	existing.ParentID = req.ParentID
	existing.Color = req.Color
	existing.IsActive = req.IsActive
	existing.SortOrder = req.SortOrder

	// Set default color if empty
	if existing.Color == "" {
		existing.Color = "#000000"
	}

	err = s.repo.Update(ctx, tenantID, id, existing)
	if err != nil {
		return nil, fmt.Errorf("failed to update category: %w", err)
	}

	response := &models.BlogCategoryResponse{}
	response.FromBlogCategory(existing)
	return response, nil
}

// Delete soft deletes a blog category
func (s *blogCategoryService) Delete(ctx context.Context, tenantID, id uint) error {
	// Check if category exists
	_, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("category not found")
		}
		return fmt.Errorf("failed to get category: %w", err)
	}

	// Check if category has children using GetDescendants
	children, err := s.repo.GetDescendants(ctx, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to check for children: %w", err)
	}
	if len(children) > 0 {
		return fmt.Errorf("cannot delete category with children")
	}

	// Soft delete using repository delete method
	err = s.repo.Delete(ctx, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to delete category: %w", err)
	}

	return nil
}

// List retrieves a list of blog categories with filtering
func (s *blogCategoryService) List(ctx context.Context, filter *models.BlogCategoryFilter) ([]models.BlogCategoryResponse, int64, error) {
	categories, total, err := s.repo.List(ctx, filter)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to list categories: %w", err)
	}

	responses := make([]models.BlogCategoryResponse, len(categories))
	for i, category := range categories {
		responses[i].FromBlogCategory(&category)
	}

	return responses, total, nil
}

// ListWithCursor retrieves categories with cursor-based pagination
func (s *blogCategoryService) ListWithCursor(ctx context.Context, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) (interface{}, error) {
	// Call repository method that should return categories and cursor response
	categories, cursorResponse, err := s.repo.ListWithCursor(ctx, tenantID, req, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to list categories with cursor: %w", err)
	}

	// Convert to response DTOs
	responses := make([]models.BlogCategoryResponse, len(categories))
	for i, category := range categories {
		responses[i].FromBlogCategory(&category)
	}

	// Build standardized cursor response
	return map[string]interface{}{
		"data":       responses,
		"pagination": cursorResponse,
	}, nil
}

// GetHierarchy retrieves the hierarchical structure of categories
func (s *blogCategoryService) GetHierarchy(ctx context.Context, tenantID, websiteID uint) ([]models.BlogCategoryResponse, error) {
	categories, err := s.repo.GetHierarchy(ctx, tenantID, websiteID)
	if err != nil {
		return nil, fmt.Errorf("failed to get category hierarchy: %w", err)
	}

	responses := make([]models.BlogCategoryResponse, len(categories))
	for i, category := range categories {
		responses[i].FromBlogCategory(&category)
	}

	return responses, nil
}

// MoveCategory moves a category to a new parent
func (s *blogCategoryService) MoveCategory(ctx context.Context, tenantID, categoryID, newParentID uint) error {
	// Get the category to move
	category, err := s.repo.GetByID(ctx, tenantID, categoryID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("category not found")
		}
		return fmt.Errorf("failed to get category: %w", err)
	}

	// Validate new parent
	if newParentID != 0 {
		parent, err := s.repo.GetByID(ctx, tenantID, newParentID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return fmt.Errorf("parent category not found")
			}
			return fmt.Errorf("failed to get parent category: %w", err)
		}
		if parent.WebsiteID != category.WebsiteID {
			return fmt.Errorf("parent category must belong to the same website")
		}

		// Check for circular reference
		if err := s.checkCircularReference(ctx, tenantID, categoryID, newParentID); err != nil {
			return err
		}
	}

	// Update parent
	if newParentID == 0 {
		category.ParentID = nil
	} else {
		category.ParentID = &newParentID
	}

	err = s.repo.Update(ctx, tenantID, categoryID, category)
	if err != nil {
		return fmt.Errorf("failed to move category: %w", err)
	}

	return nil
}

// UpdatePositions updates the sort order of multiple categories
func (s *blogCategoryService) UpdatePositions(ctx context.Context, tenantID uint, positions []CategoryPosition) error {
	for _, pos := range positions {
		category, err := s.repo.GetByID(ctx, tenantID, pos.ID)
		if err != nil {
			return fmt.Errorf("failed to get category %d: %w", pos.ID, err)
		}

		category.ParentID = pos.ParentID
		category.SortOrder = pos.Position

		err = s.repo.Update(ctx, tenantID, pos.ID, category)
		if err != nil {
			return fmt.Errorf("failed to update category %d position: %w", pos.ID, err)
		}
	}

	return nil
}

// checkCircularReference checks if creating the parent-child relationship would create a circular reference
func (s *blogCategoryService) checkCircularReference(ctx context.Context, tenantID, categoryID, newParentID uint) error {
	current := newParentID
	for current != 0 {
		if current == categoryID {
			return fmt.Errorf("circular reference detected: category cannot be ancestor of itself")
		}

		parent, err := s.repo.GetByID(ctx, tenantID, current)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				break
			}
			return fmt.Errorf("failed to check circular reference: %w", err)
		}

		if parent.ParentID == nil {
			break
		}
		current = *parent.ParentID
	}

	return nil
}
