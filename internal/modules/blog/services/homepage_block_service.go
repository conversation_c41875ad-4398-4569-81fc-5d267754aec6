package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// homepageBlockService implements HomepageBlockService interface
type homepageBlockService struct {
	blockRepo    repositories.HomepageBlockRepository
	templateRepo repositories.BlockTemplateRepository
	logger       utils.Logger
}

// NewHomepageBlockService creates a new homepage block service
func NewHomepageBlockService(
	blockRepo repositories.HomepageBlockRepository,
	templateRepo repositories.BlockTemplateRepository,
	logger utils.Logger,
) HomepageBlockService {
	return &homepageBlockService{
		blockRepo:    blockRepo,
		templateRepo: templateRepo,
		logger:       logger,
	}
}

// <PERSON>reate creates a new homepage block
func (s *homepageBlockService) Create(ctx context.Context, req *models.HomepageBlockCreateRequest) (*models.HomepageBlockResponse, error) {
	s.logger.WithContext(ctx).Info("Creating homepage block", map[string]interface{}{
		"tenant_id":  req.TenantID,
		"website_id": req.WebsiteID,
		"block_type": req.BlockType,
		"title":      req.Title,
	})

	// Validate request
	if err := s.validateCreateRequest(req); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to validate create request")
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Create domain model
	block := &models.HomepageBlock{
		TenantID:        req.TenantID,
		WebsiteID:       req.WebsiteID,
		BlockType:       req.BlockType,
		Title:           req.Title,
		Subtitle:        req.Subtitle,
		Configuration:   req.Configuration,
		Styling:         req.Styling,
		IsActive:        req.IsActive,
		SortOrder:       req.SortOrder,
		VisibilityRules: req.VisibilityRules,
		Status:          "active",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	// Create in repository
	if err := s.blockRepo.Create(ctx, block); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to create homepage block")
		return nil, fmt.Errorf("failed to create homepage block: %w", err)
	}

	// Convert to response
	response := &models.HomepageBlockResponse{}
	response.FromHomepageBlock(block)

	s.logger.WithContext(ctx).Info("Successfully created homepage block", map[string]interface{}{
		"block_id": block.ID,
	})

	return response, nil
}

// GetByID retrieves a homepage block by ID
func (s *homepageBlockService) GetByID(ctx context.Context, tenantID, id uint) (*models.HomepageBlockResponse, error) {
	s.logger.WithContext(ctx).Info("Getting homepage block by ID", map[string]interface{}{
		"tenant_id": tenantID,
		"block_id":  id,
	})

	// Validate input
	if tenantID == 0 || id == 0 {
		return nil, fmt.Errorf("tenant ID and block ID are required")
	}

	block, err := s.blockRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("homepage block not found")
		}
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get homepage block")
		return nil, fmt.Errorf("failed to get homepage block: %w", err)
	}

	// Convert to response
	response := &models.HomepageBlockResponse{}
	response.FromHomepageBlock(block)

	return response, nil
}

// Update updates a homepage block
func (s *homepageBlockService) Update(ctx context.Context, tenantID, id uint, req *models.HomepageBlockUpdateRequest) (*models.HomepageBlockResponse, error) {
	s.logger.WithContext(ctx).Info("Updating homepage block", map[string]interface{}{
		"tenant_id": tenantID,
		"block_id":  id,
	})

	// Validate input
	if tenantID == 0 || id == 0 {
		return nil, fmt.Errorf("tenant ID and block ID are required")
	}

	// Get existing block
	existingBlock, err := s.blockRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("homepage block not found")
		}
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get existing homepage block")
		return nil, fmt.Errorf("failed to get existing homepage block: %w", err)
	}

	// Update fields
	if req.Title != "" {
		existingBlock.Title = req.Title
	}
	if req.Subtitle != "" {
		existingBlock.Subtitle = req.Subtitle
	}
	if req.Configuration != nil {
		existingBlock.Configuration = req.Configuration
	}
	if req.Styling != nil {
		existingBlock.Styling = req.Styling
	}
	if req.VisibilityRules != nil {
		existingBlock.VisibilityRules = req.VisibilityRules
	}
	existingBlock.IsActive = req.IsActive
	existingBlock.SortOrder = req.SortOrder
	existingBlock.UpdatedAt = time.Now()

	// Update in repository
	if err := s.blockRepo.Update(ctx, tenantID, id, existingBlock); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to update homepage block")
		return nil, fmt.Errorf("failed to update homepage block: %w", err)
	}

	// Convert to response
	response := &models.HomepageBlockResponse{}
	response.FromHomepageBlock(existingBlock)

	s.logger.WithContext(ctx).Info("Successfully updated homepage block")
	return response, nil
}

// Delete deletes a homepage block
func (s *homepageBlockService) Delete(ctx context.Context, tenantID, id uint) error {
	s.logger.WithContext(ctx).Info("Deleting homepage block", map[string]interface{}{
		"tenant_id": tenantID,
		"block_id":  id,
	})

	// Validate input
	if tenantID == 0 || id == 0 {
		return fmt.Errorf("tenant ID and block ID are required")
	}

	// Check if block exists
	_, err := s.blockRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("homepage block not found")
		}
		s.logger.WithContext(ctx).WithError(err).Error("Failed to check existing homepage block")
		return fmt.Errorf("failed to check existing homepage block: %w", err)
	}

	// Delete block
	if err := s.blockRepo.Delete(ctx, tenantID, id); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to delete homepage block")
		return fmt.Errorf("failed to delete homepage block: %w", err)
	}

	s.logger.WithContext(ctx).Info("Successfully deleted homepage block")
	return nil
}

// List retrieves homepage blocks with filtering and pagination
func (s *homepageBlockService) List(ctx context.Context, filter *models.HomepageBlockFilter) ([]models.HomepageBlockResponse, int64, error) {
	s.logger.WithContext(ctx).Info("Listing homepage blocks", map[string]interface{}{
		"filter": filter,
	})

	blocks, total, err := s.blockRepo.List(ctx, filter)
	if err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to list homepage blocks")
		return nil, 0, fmt.Errorf("failed to list homepage blocks: %w", err)
	}

	// Convert to responses
	responses := make([]models.HomepageBlockResponse, len(blocks))
	for i, block := range blocks {
		responses[i].FromHomepageBlock(&block)
	}

	return responses, total, nil
}

// GetByWebsite retrieves all homepage blocks for a website
func (s *homepageBlockService) GetByWebsite(ctx context.Context, tenantID, websiteID uint) ([]models.HomepageBlockResponse, error) {
	s.logger.WithContext(ctx).Info("Getting homepage blocks by website", map[string]interface{}{
		"tenant_id":  tenantID,
		"website_id": websiteID,
	})

	// Validate input
	if tenantID == 0 || websiteID == 0 {
		return nil, fmt.Errorf("tenant ID and website ID are required")
	}

	blocks, err := s.blockRepo.GetByWebsite(ctx, tenantID, websiteID)
	if err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get homepage blocks by website")
		return nil, fmt.Errorf("failed to get homepage blocks by website: %w", err)
	}

	// Convert to responses
	responses := make([]models.HomepageBlockResponse, len(blocks))
	for i, block := range blocks {
		responses[i].FromHomepageBlock(&block)
	}

	return responses, nil
}

// GetActiveBlocks retrieves all active homepage blocks for a website
func (s *homepageBlockService) GetActiveBlocks(ctx context.Context, tenantID, websiteID uint) ([]models.HomepageBlockResponse, error) {
	s.logger.WithContext(ctx).Info("Getting active homepage blocks", map[string]interface{}{
		"tenant_id":  tenantID,
		"website_id": websiteID,
	})

	// Validate input
	if tenantID == 0 || websiteID == 0 {
		return nil, fmt.Errorf("tenant ID and website ID are required")
	}

	blocks, err := s.blockRepo.GetActiveBlocks(ctx, tenantID, websiteID)
	if err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get active homepage blocks")
		return nil, fmt.Errorf("failed to get active homepage blocks: %w", err)
	}

	// Convert to responses
	responses := make([]models.HomepageBlockResponse, len(blocks))
	for i, block := range blocks {
		responses[i].FromHomepageBlock(&block)
	}

	return responses, nil
}

// Reorder updates the sort order of multiple blocks
func (s *homepageBlockService) Reorder(ctx context.Context, tenantID uint, blockIDs []uint) error {
	s.logger.WithContext(ctx).Info("Reordering homepage blocks", map[string]interface{}{
		"tenant_id": tenantID,
		"block_ids": blockIDs,
	})

	// Validate input
	if tenantID == 0 {
		return fmt.Errorf("tenant ID is required")
	}
	if len(blockIDs) == 0 {
		return fmt.Errorf("block IDs are required")
	}

	// Reorder blocks
	if err := s.blockRepo.Reorder(ctx, tenantID, blockIDs); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to reorder homepage blocks")
		return fmt.Errorf("failed to reorder homepage blocks: %w", err)
	}

	s.logger.WithContext(ctx).Info("Successfully reordered homepage blocks")
	return nil
}

// UpdateSortOrder updates the sort order of a specific block
func (s *homepageBlockService) UpdateSortOrder(ctx context.Context, tenantID, blockID uint, sortOrder uint) error {
	s.logger.WithContext(ctx).Info("Updating block sort order", map[string]interface{}{
		"tenant_id":  tenantID,
		"block_id":   blockID,
		"sort_order": sortOrder,
	})

	// Validate input
	if tenantID == 0 || blockID == 0 {
		return fmt.Errorf("tenant ID and block ID are required")
	}

	// Update sort order
	if err := s.blockRepo.UpdateSortOrder(ctx, tenantID, blockID, sortOrder); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to update block sort order")
		return fmt.Errorf("failed to update block sort order: %w", err)
	}

	s.logger.WithContext(ctx).Info("Successfully updated block sort order")
	return nil
}

// Activate activates a homepage block
func (s *homepageBlockService) Activate(ctx context.Context, tenantID, blockID uint) error {
	s.logger.WithContext(ctx).Info("Activating homepage block", map[string]interface{}{
		"tenant_id": tenantID,
		"block_id":  blockID,
	})

	// Validate input
	if tenantID == 0 || blockID == 0 {
		return fmt.Errorf("tenant ID and block ID are required")
	}

	// Activate block
	if err := s.blockRepo.Activate(ctx, tenantID, blockID); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to activate homepage block")
		return fmt.Errorf("failed to activate homepage block: %w", err)
	}

	s.logger.WithContext(ctx).Info("Successfully activated homepage block")
	return nil
}

// Deactivate deactivates a homepage block
func (s *homepageBlockService) Deactivate(ctx context.Context, tenantID, blockID uint) error {
	s.logger.WithContext(ctx).Info("Deactivating homepage block", map[string]interface{}{
		"tenant_id": tenantID,
		"block_id":  blockID,
	})

	// Validate input
	if tenantID == 0 || blockID == 0 {
		return fmt.Errorf("tenant ID and block ID are required")
	}

	// Deactivate block
	if err := s.blockRepo.Deactivate(ctx, tenantID, blockID); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to deactivate homepage block")
		return fmt.Errorf("failed to deactivate homepage block: %w", err)
	}

	s.logger.WithContext(ctx).Info("Successfully deactivated homepage block")
	return nil
}

// CreateFromTemplate creates a new block from a template
func (s *homepageBlockService) CreateFromTemplate(ctx context.Context, tenantID, templateID, websiteID uint, customizations models.JSON) (*models.HomepageBlockResponse, error) {
	s.logger.WithContext(ctx).Info("Creating homepage block from template", map[string]interface{}{
		"tenant_id":   tenantID,
		"template_id": templateID,
		"website_id":  websiteID,
	})

	// Validate input
	if tenantID == 0 || templateID == 0 || websiteID == 0 {
		return nil, fmt.Errorf("tenant ID, template ID, and website ID are required")
	}

	// Get template
	template, err := s.templateRepo.GetByID(ctx, tenantID, templateID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("template not found")
		}
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get template")
		return nil, fmt.Errorf("failed to get template: %w", err)
	}

	// Merge template configuration with customizations
	configuration := template.Configuration
	if customizations != nil {
		// Merge customizations into template configuration
		for key, value := range customizations {
			configuration[key] = value
		}
	}

	// Create block from template
	block := &models.HomepageBlock{
		TenantID:      tenantID,
		WebsiteID:     websiteID,
		BlockType:     template.BlockType,
		Title:         template.Name,
		Subtitle:      template.Description,
		Configuration: configuration,
		IsActive:      true,
		SortOrder:     1,
		Status:        "active",
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// Create in repository
	if err := s.blockRepo.Create(ctx, block); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to create homepage block from template")
		return nil, fmt.Errorf("failed to create homepage block from template: %w", err)
	}

	// Convert to response
	response := &models.HomepageBlockResponse{}
	response.FromHomepageBlock(block)

	s.logger.WithContext(ctx).Info("Successfully created homepage block from template", map[string]interface{}{
		"block_id": block.ID,
	})

	return response, nil
}

// SaveAsTemplate saves a block as a template
func (s *homepageBlockService) SaveAsTemplate(ctx context.Context, tenantID, blockID uint, templateName, templateDescription string, isPublic bool) (*models.BlockTemplateResponse, error) {
	s.logger.WithContext(ctx).Info("Saving homepage block as template", map[string]interface{}{
		"tenant_id":     tenantID,
		"block_id":      blockID,
		"template_name": templateName,
		"is_public":     isPublic,
	})

	// Validate input
	if tenantID == 0 || blockID == 0 {
		return nil, fmt.Errorf("tenant ID and block ID are required")
	}
	if templateName == "" {
		return nil, fmt.Errorf("template name is required")
	}

	// Get block
	block, err := s.blockRepo.GetByID(ctx, tenantID, blockID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("homepage block not found")
		}
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get homepage block")
		return nil, fmt.Errorf("failed to get homepage block: %w", err)
	}

	// Create template from block
	template := &models.BlockTemplate{
		TenantID:      tenantID,
		BlockType:     block.BlockType,
		Name:          templateName,
		Description:   templateDescription,
		Configuration: block.Configuration,
		Styling:       block.Styling,
		IsPublic:      isPublic,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// Create in repository
	if err := s.templateRepo.Create(ctx, template); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to create template from homepage block")
		return nil, fmt.Errorf("failed to create template from homepage block: %w", err)
	}

	// Convert to response
	response := &models.BlockTemplateResponse{}
	response.FromBlockTemplate(template)

	s.logger.WithContext(ctx).Info("Successfully saved homepage block as template", map[string]interface{}{
		"template_id": template.ID,
	})

	return response, nil
}

// validateCreateRequest validates the create request
func (s *homepageBlockService) validateCreateRequest(req *models.HomepageBlockCreateRequest) error {
	if req.TenantID == 0 {
		return fmt.Errorf("tenant ID is required")
	}
	if req.WebsiteID == 0 {
		return fmt.Errorf("website ID is required")
	}
	if req.BlockType == "" {
		return fmt.Errorf("block type is required")
	}
	if req.Title == "" {
		return fmt.Errorf("title is required")
	}
	return nil
}
