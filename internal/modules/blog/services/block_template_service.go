package services

import (
	"context"
	"errors"
	"fmt"
	"time"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// blockTemplateService implements BlockTemplateService interface
type blockTemplateService struct {
	templateRepo repositories.BlockTemplateRepository
	logger       utils.Logger
}

// NewBlockTemplateService creates a new block template service
func NewBlockTemplateService(
	templateRepo repositories.BlockTemplateRepository,
	logger utils.Logger,
) BlockTemplateService {
	return &blockTemplateService{
		templateRepo: templateRepo,
		logger:       logger,
	}
}

// Create creates a new block template
func (s *blockTemplateService) Create(ctx context.Context, req *models.BlockTemplateCreateRequest) (*models.BlockTemplateResponse, error) {
	s.logger.WithContext(ctx).Info("Creating block template", map[string]interface{}{
		"tenant_id":  req.TenantID,
		"block_type": req.BlockType,
		"name":       req.Name,
		"is_public":  req.IsPublic,
	})

	// Validate request
	if err := s.validateCreateRequest(req); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to validate create request")
		return nil, fmt.Errorf("validation failed: %w", err)
	}

	// Create domain model
	template := &models.BlockTemplate{
		TenantID:      req.TenantID,
		BlockType:     req.BlockType,
		Name:          req.Name,
		Description:   req.Description,
		Configuration: req.Configuration,
		IsPublic:      req.IsPublic,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// Create in repository
	if err := s.templateRepo.Create(ctx, template); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to create block template")
		return nil, fmt.Errorf("failed to create block template: %w", err)
	}

	// Convert to response
	response := &models.BlockTemplateResponse{}
	response.FromBlockTemplate(template)

	s.logger.WithContext(ctx).Info("Successfully created block template", map[string]interface{}{
		"template_id": template.ID,
	})

	return response, nil
}

// GetByID retrieves a block template by ID
func (s *blockTemplateService) GetByID(ctx context.Context, tenantID, id uint) (*models.BlockTemplateResponse, error) {
	s.logger.WithContext(ctx).Info("Getting block template by ID", map[string]interface{}{
		"tenant_id":   tenantID,
		"template_id": id,
	})

	// Validate input
	if id == 0 {
		return nil, fmt.Errorf("template ID is required")
	}

	template, err := s.templateRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("block template not found")
		}
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get block template")
		return nil, fmt.Errorf("failed to get block template: %w", err)
	}

	// Convert to response
	response := &models.BlockTemplateResponse{}
	response.FromBlockTemplate(template)

	return response, nil
}

// Update updates a block template
func (s *blockTemplateService) Update(ctx context.Context, tenantID, id uint, req *models.BlockTemplateUpdateRequest) (*models.BlockTemplateResponse, error) {
	s.logger.WithContext(ctx).Info("Updating block template", map[string]interface{}{
		"tenant_id":   tenantID,
		"template_id": id,
	})

	// Validate input
	if tenantID == 0 || id == 0 {
		return nil, fmt.Errorf("tenant ID and template ID are required")
	}

	// Get existing template
	existingTemplate, err := s.templateRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("block template not found")
		}
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get existing block template")
		return nil, fmt.Errorf("failed to get existing block template: %w", err)
	}

	// Update fields
	if req.Name != "" {
		existingTemplate.Name = req.Name
	}
	if req.Description != "" {
		existingTemplate.Description = req.Description
	}
	if req.Configuration != nil {
		existingTemplate.Configuration = req.Configuration
	}
	existingTemplate.IsPublic = req.IsPublic
	existingTemplate.UpdatedAt = time.Now()

	// Update in repository
	if err := s.templateRepo.Update(ctx, tenantID, id, existingTemplate); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to update block template")
		return nil, fmt.Errorf("failed to update block template: %w", err)
	}

	// Convert to response
	response := &models.BlockTemplateResponse{}
	response.FromBlockTemplate(existingTemplate)

	s.logger.WithContext(ctx).Info("Successfully updated block template")
	return response, nil
}

// Delete deletes a block template
func (s *blockTemplateService) Delete(ctx context.Context, tenantID, id uint) error {
	s.logger.WithContext(ctx).Info("Deleting block template", map[string]interface{}{
		"tenant_id":   tenantID,
		"template_id": id,
	})

	// Validate input
	if tenantID == 0 || id == 0 {
		return fmt.Errorf("tenant ID and template ID are required")
	}

	// Check if template exists
	_, err := s.templateRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("block template not found")
		}
		s.logger.WithContext(ctx).WithError(err).Error("Failed to check existing block template")
		return fmt.Errorf("failed to check existing block template: %w", err)
	}

	// Delete template
	if err := s.templateRepo.Delete(ctx, tenantID, id); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to delete block template")
		return fmt.Errorf("failed to delete block template: %w", err)
	}

	s.logger.WithContext(ctx).Info("Successfully deleted block template")
	return nil
}

// List retrieves block templates with filtering and pagination
func (s *blockTemplateService) List(ctx context.Context, filter *models.BlockTemplateFilter) ([]models.BlockTemplateResponse, int64, error) {
	s.logger.WithContext(ctx).Info("Listing block templates", map[string]interface{}{
		"filter": filter,
	})

	templates, total, err := s.templateRepo.List(ctx, filter)
	if err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to list block templates")
		return nil, 0, fmt.Errorf("failed to list block templates: %w", err)
	}

	// Convert to responses
	responses := make([]models.BlockTemplateResponse, len(templates))
	for i, template := range templates {
		responses[i].FromBlockTemplate(&template)
	}

	return responses, total, nil
}

// GetPublicTemplates retrieves public templates for a specific block type
func (s *blockTemplateService) GetPublicTemplates(ctx context.Context, blockType models.HomepageBlockType) ([]models.BlockTemplateResponse, error) {
	s.logger.WithContext(ctx).Info("Getting public templates", map[string]interface{}{
		"block_type": blockType,
	})

	templates, err := s.templateRepo.GetPublicTemplates(ctx, blockType)
	if err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get public templates")
		return nil, fmt.Errorf("failed to get public templates: %w", err)
	}

	// Convert to responses
	responses := make([]models.BlockTemplateResponse, len(templates))
	for i, template := range templates {
		responses[i].FromBlockTemplate(&template)
	}

	return responses, nil
}

// GetByTenant retrieves all templates for a specific tenant
func (s *blockTemplateService) GetByTenant(ctx context.Context, tenantID uint) ([]models.BlockTemplateResponse, error) {
	s.logger.WithContext(ctx).Info("Getting templates by tenant", map[string]interface{}{
		"tenant_id": tenantID,
	})

	// Validate input
	if tenantID == 0 {
		return nil, fmt.Errorf("tenant ID is required")
	}

	templates, err := s.templateRepo.GetByTenant(ctx, tenantID)
	if err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get templates by tenant")
		return nil, fmt.Errorf("failed to get templates by tenant: %w", err)
	}

	// Convert to responses
	responses := make([]models.BlockTemplateResponse, len(templates))
	for i, template := range templates {
		responses[i].FromBlockTemplate(&template)
	}

	return responses, nil
}

// Clone creates a copy of an existing template
func (s *blockTemplateService) Clone(ctx context.Context, tenantID, templateID uint, newName string) (*models.BlockTemplateResponse, error) {
	s.logger.WithContext(ctx).Info("Cloning block template", map[string]interface{}{
		"tenant_id":   tenantID,
		"template_id": templateID,
		"new_name":    newName,
	})

	// Validate input
	if tenantID == 0 || templateID == 0 {
		return nil, fmt.Errorf("tenant ID and template ID are required")
	}
	if newName == "" {
		return nil, fmt.Errorf("new template name is required")
	}

	// Get original template
	originalTemplate, err := s.templateRepo.GetByID(ctx, tenantID, templateID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("original template not found")
		}
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get original template")
		return nil, fmt.Errorf("failed to get original template: %w", err)
	}

	// Create new template as a copy
	newTemplate := &models.BlockTemplate{
		TenantID:      tenantID,
		BlockType:     originalTemplate.BlockType,
		Name:          newName,
		Description:   fmt.Sprintf("Copy of %s", originalTemplate.Name),
		Configuration: originalTemplate.Configuration,
		IsPublic:      false, // Cloned templates are private by default
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// Create in repository
	if err := s.templateRepo.Create(ctx, newTemplate); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to create cloned template")
		return nil, fmt.Errorf("failed to create cloned template: %w", err)
	}

	// Convert to response
	response := &models.BlockTemplateResponse{}
	response.FromBlockTemplate(newTemplate)

	s.logger.WithContext(ctx).Info("Successfully cloned block template", map[string]interface{}{
		"new_template_id": newTemplate.ID,
	})

	return response, nil
}

// UpdateConfiguration updates the configuration of a template
func (s *blockTemplateService) UpdateConfiguration(ctx context.Context, tenantID, templateID uint, configuration models.JSON) (*models.BlockTemplateResponse, error) {
	s.logger.WithContext(ctx).Info("Updating template configuration", map[string]interface{}{
		"tenant_id":   tenantID,
		"template_id": templateID,
	})

	// Validate input
	if tenantID == 0 || templateID == 0 {
		return nil, fmt.Errorf("tenant ID and template ID are required")
	}
	if configuration == nil {
		return nil, fmt.Errorf("configuration is required")
	}

	// Get existing template
	existingTemplate, err := s.templateRepo.GetByID(ctx, tenantID, templateID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("block template not found")
		}
		s.logger.WithContext(ctx).WithError(err).Error("Failed to get existing block template")
		return nil, fmt.Errorf("failed to get existing block template: %w", err)
	}

	// Update configuration
	existingTemplate.Configuration = configuration
	existingTemplate.UpdatedAt = time.Now()

	// Update in repository
	if err := s.templateRepo.Update(ctx, tenantID, templateID, existingTemplate); err != nil {
		s.logger.WithContext(ctx).WithError(err).Error("Failed to update template configuration")
		return nil, fmt.Errorf("failed to update template configuration: %w", err)
	}

	// Convert to response
	response := &models.BlockTemplateResponse{}
	response.FromBlockTemplate(existingTemplate)

	s.logger.WithContext(ctx).Info("Successfully updated template configuration")
	return response, nil
}

// validateCreateRequest validates the create request
func (s *blockTemplateService) validateCreateRequest(req *models.BlockTemplateCreateRequest) error {
	if req.TenantID == 0 {
		return fmt.Errorf("tenant ID is required")
	}
	if req.BlockType == "" {
		return fmt.Errorf("block type is required")
	}
	if req.Name == "" {
		return fmt.Errorf("name is required")
	}
	if req.Configuration == nil {
		return fmt.Errorf("configuration is required")
	}
	return nil
}
