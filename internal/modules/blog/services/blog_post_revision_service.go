package services

import (
	"context"
	"errors"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/repositories"
	"gorm.io/gorm"
)

// blogPostRevisionService implements BlogPostRevisionService interface
type blogPostRevisionService struct {
	repo     repositories.BlogPostRevisionRepository
	postRepo repositories.BlogPostRepository
}

// NewBlogPostRevisionService creates a new blog post revision service
func NewBlogPostRevisionService(repo repositories.BlogPostRevisionRepository, postRepo repositories.BlogPostRepository) BlogPostRevisionService {
	return &blogPostRevisionService{
		repo:     repo,
		postRepo: postRepo,
	}
}

// Create creates a new blog post revision
func (s *blogPostRevisionService) Create(ctx context.Context, req *models.BlogPostRevisionCreateRequest) (*models.BlogPostRevisionResponse, error) {
	// Validate that the post exists
	post, err := s.postRepo.GetByID(ctx, req.TenantID, req.PostID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("post not found")
		}
		return nil, fmt.Errorf("failed to validate post: %w", err)
	}

	// Create the revision model
	revision := &models.BlogPostRevision{
		TenantID:       req.TenantID,
		PostID:         req.PostID,
		UserID:         req.UserID,
		Title:          req.Title,
		Content:        req.Content,
		Excerpt:        req.Excerpt,
		ChangesSummary: req.ChangesSummary,
	}

	// If no content provided, use current post content
	if revision.Title == "" {
		revision.Title = post.Title
	}
	if revision.Content == "" {
		revision.Content = post.Content
	}
	if revision.Excerpt == "" {
		revision.Excerpt = post.Excerpt
	}

	// Create the revision
	err = s.repo.Create(ctx, revision)
	if err != nil {
		return nil, fmt.Errorf("failed to create revision: %w", err)
	}

	// Convert to response
	response := &models.BlogPostRevisionResponse{}
	response.FromBlogPostRevision(revision)
	return response, nil
}

// GetByPostID retrieves revisions for a blog post
func (s *blogPostRevisionService) GetByPostID(ctx context.Context, tenantID, postID uint, limit, offset int) ([]models.BlogPostRevisionResponse, int64, error) {
	// Validate that the post exists
	_, err := s.postRepo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, 0, fmt.Errorf("post not found")
		}
		return nil, 0, fmt.Errorf("failed to validate post: %w", err)
	}

	revisions, total, err := s.repo.GetByPostID(ctx, tenantID, postID, limit, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get revisions: %w", err)
	}

	responses := make([]models.BlogPostRevisionResponse, len(revisions))
	for i, revision := range revisions {
		responses[i].FromBlogPostRevision(&revision)
	}

	return responses, total, nil
}

// GetByID retrieves a blog post revision by ID
func (s *blogPostRevisionService) GetByID(ctx context.Context, tenantID, id uint) (*models.BlogPostRevisionResponse, error) {
	revision, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, fmt.Errorf("revision not found")
		}
		return nil, fmt.Errorf("failed to get revision: %w", err)
	}

	response := &models.BlogPostRevisionResponse{}
	response.FromBlogPostRevision(revision)
	return response, nil
}

// Delete removes a blog post revision
func (s *blogPostRevisionService) Delete(ctx context.Context, tenantID, id uint) error {
	// Check if revision exists
	_, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("revision not found")
		}
		return fmt.Errorf("failed to get revision: %w", err)
	}

	// Delete the revision
	err = s.repo.Delete(ctx, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to delete revision: %w", err)
	}

	return nil
}

// List retrieves a list of blog post revisions with filtering
func (s *blogPostRevisionService) List(ctx context.Context, filter *models.BlogPostRevisionFilter) ([]models.BlogPostRevisionResponse, int64, error) {
	// Since repository doesn't have List method, we'll provide a basic implementation
	// This should be improved in the repository implementation

	if filter.PostID == 0 {
		return nil, 0, fmt.Errorf("post_id is required for listing revisions")
	}

	// Use GetByPostID with filter parameters
	limit := filter.PageSize
	if limit == 0 {
		limit = 20 // Default page size
	}

	offset := 0
	if filter.Page > 0 {
		offset = (filter.Page - 1) * limit
	}

	return s.GetByPostID(ctx, filter.TenantID, filter.PostID, limit, offset)
}

// GetLatest retrieves the latest revision for a blog post
func (s *blogPostRevisionService) GetLatest(ctx context.Context, tenantID, postID uint) (*models.BlogPostRevisionResponse, error) {
	revisions, _, err := s.GetByPostID(ctx, tenantID, postID, 1, 0)
	if err != nil {
		return nil, fmt.Errorf("failed to get latest revision: %w", err)
	}

	if len(revisions) == 0 {
		return nil, fmt.Errorf("no revisions found for post")
	}

	return &revisions[0], nil
}

// CompareRevisions compares two revisions
func (s *blogPostRevisionService) CompareRevisions(ctx context.Context, tenantID, fromRevisionID, toRevisionID uint) (*models.BlogPostRevisionComparison, error) {
	// Get both revisions
	fromRevision, err := s.GetByID(ctx, tenantID, fromRevisionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get from revision: %w", err)
	}

	toRevision, err := s.GetByID(ctx, tenantID, toRevisionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get to revision: %w", err)
	}

	// Ensure both revisions belong to the same post
	if fromRevision.PostID != toRevision.PostID {
		return nil, fmt.Errorf("revisions must belong to the same post")
	}

	// Create comparison
	comparison := &models.BlogPostRevisionComparison{
		FromRevision: *fromRevision,
		ToRevision:   *toRevision,
		Changes: models.RevisionChanges{
			TitleChanged:           fromRevision.Title != toRevision.Title,
			ContentChanged:         fromRevision.Content != toRevision.Content,
			ExcerptChanged:         fromRevision.Excerpt != toRevision.Excerpt,
			MetaTitleChanged:       fromRevision.MetaTitle != toRevision.MetaTitle,
			MetaDescriptionChanged: fromRevision.MetaDescription != toRevision.MetaDescription,
		},
	}

	// Calculate simple change summary
	var changeSummary []string
	if comparison.Changes.TitleChanged {
		changeSummary = append(changeSummary, "Title changed")
	}
	if comparison.Changes.ContentChanged {
		changeSummary = append(changeSummary, fmt.Sprintf("Content changed from %d to %d characters",
			len(fromRevision.Content), len(toRevision.Content)))
	}
	if comparison.Changes.ExcerptChanged {
		changeSummary = append(changeSummary, "Excerpt changed")
	}
	if comparison.Changes.MetaTitleChanged {
		changeSummary = append(changeSummary, "Meta title changed")
	}
	if comparison.Changes.MetaDescriptionChanged {
		changeSummary = append(changeSummary, "Meta description changed")
	}

	if len(changeSummary) > 0 {
		comparison.Changes.Summary = fmt.Sprintf("Changes: %s", fmt.Sprintf("%v", changeSummary))
	} else {
		comparison.Changes.Summary = "No changes detected"
	}

	return comparison, nil
}

// DeleteOldRevisions deletes old revisions, keeping only the specified count
func (s *blogPostRevisionService) DeleteOldRevisions(ctx context.Context, tenantID, postID uint, keepCount int) error {
	// Validate that the post exists
	_, err := s.postRepo.GetByID(ctx, tenantID, postID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return fmt.Errorf("post not found")
		}
		return fmt.Errorf("failed to validate post: %w", err)
	}

	// Use repository method to delete old revisions
	err = s.repo.DeleteOldRevisions(ctx, tenantID, postID, keepCount)
	if err != nil {
		return fmt.Errorf("failed to delete old revisions: %w", err)
	}

	return nil
}

// GetStats retrieves revision statistics
func (s *blogPostRevisionService) GetStats(ctx context.Context, tenantID uint) (*models.BlogPostRevisionStats, error) {
	// Since there's no GetStats method in repository, we'll provide a basic implementation
	// This should be improved in the repository implementation

	// For now, return basic stats structure
	stats := &models.BlogPostRevisionStats{
		TotalRevisions:    0,
		RevisionsToday:    0,
		RevisionsThisWeek: 0,
		ActiveAuthors: <AUTHORS>
	}

	return stats, nil
}
