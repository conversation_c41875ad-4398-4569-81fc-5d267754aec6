package handlers

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	httpresponse "github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

type BlogTagHandler struct {
	tagService services.BlogTagService
}

func NewBlogTagHandler(tagService services.BlogTagService) *BlogTagHandler {
	return &BlogTagHandler{
		tagService: tagService,
	}
}

// CreateTag creates a new blog tag
func (h *BlogTagHandler) CreateTag(c *gin.Context) {
	var req models.BlogTagCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request body")
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}
	req.TenantID = tenantID.(uint)

	tag, err := h.tagService.Create(c.Request.Context(), &req)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to create tag")
		return
	}

	httpresponse.Created(c.Writer, tag)
}

// GetTag retrieves a blog tag by ID
func (h *BlogTagHandler) GetTag(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid tag ID")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	tag, err := h.tagService.GetByID(c.Request.Context(), tenantID.(uint), uint(id))
	if err != nil {
		httpresponse.NotFound(c.Writer, "Tag not found")
		return
	}

	httpresponse.OK(c.Writer, tag)
}

// GetTagBySlug retrieves a blog tag by slug
func (h *BlogTagHandler) GetTagBySlug(c *gin.Context) {
	slug := c.Param("slug")
	websiteIDStr := c.Query("website_id")

	if websiteIDStr == "" {
		httpresponse.BadRequest(c.Writer, "Website ID is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid website ID")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	tag, err := h.tagService.GetBySlug(c.Request.Context(), tenantID.(uint), uint(websiteID), slug)
	if err != nil {
		httpresponse.NotFound(c.Writer, "Tag not found")
		return
	}

	httpresponse.OK(c.Writer, tag)
}

// UpdateTag updates a blog tag
func (h *BlogTagHandler) UpdateTag(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid tag ID")
		return
	}

	var req models.BlogTagUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid request body")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	tag, err := h.tagService.Update(c.Request.Context(), tenantID.(uint), uint(id), &req)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to update tag")
		return
	}

	httpresponse.OK(c.Writer, tag)
}

// DeleteTag deletes a blog tag
func (h *BlogTagHandler) DeleteTag(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid tag ID")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	err = h.tagService.Delete(c.Request.Context(), tenantID.(uint), uint(id))
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to delete tag")
		return
	}

	httpresponse.OK(c.Writer, nil)
}

// ListTags lists blog tags with filtering and cursor pagination
// @Summary      List blog tags with cursor pagination
// @Description  Get paginated list of blog tags with filters using cursor pagination
// @Tags         Blog Tags
// @Produce      json
// @Security     Bearer
// @Param        website_id query uint true "Website ID"
// @Param        search query string false "Search filter"
// @Param        is_active query bool false "Active status filter"
// @Param        status query string false "Status filter" Enums(active,inactive,deleted)
// @Param        cursor query string false "Pagination cursor"
// @Param        limit query int false "Items per page" default(20)
// @Param        sort_by query string false "Sort by field" Enums(id,name,created_at,updated_at,usage_count) default(name)
// @Param        sort_order query string false "Sort order" Enums(asc,desc) default(asc)
// @Param        use_page query bool false "Use page-based pagination" default(false)
// @Param        page query int false "Page number (when use_page=true)" default(1)
// @Param        page_size query int false "Page size (when use_page=true)" default(20)
// @Success      200 {object} dto.BlogTagListResponse
// @Failure      400 {object} httpresponse.Response "Invalid filter parameters"
// @Failure      401 {object} httpresponse.Response "Authentication required"
// @Failure      500 {object} httpresponse.Response "Failed to retrieve blog tags"
// @Router       /api/cms/v1/blog/tags [get]
func (h *BlogTagHandler) ListTags(c *gin.Context) {
	var filter dto.BlogTagFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid filter parameters", err.Error())
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	// Handle website_id parameter
	websiteIDStr := c.Query("website_id")
	if websiteIDStr == "" {
		httpresponse.BadRequest(c.Writer, "Website ID is required")
		return
	}
	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid website ID")
		return
	}

	// Create cursor request
	cursorReq := pagination.NewCursorRequest(filter.Cursor, filter.GetLimitWithDefault())

	// Check if page-based pagination is requested (for backward compatibility)
	if c.Query("use_page") == "true" {
		// Use traditional page-based pagination
		var modelFilter models.BlogTagFilter
		modelFilter.TenantID = tenantID.(uint)
		modelFilter.WebsiteID = uint(websiteID)
		modelFilter.Search = filter.Search
		modelFilter.IsActive = filter.IsActive
		if filter.Status != nil {
			modelFilter.Status = string(*filter.Status)
		}

		// Parse page-based parameters
		modelFilter.Page, _ = strconv.Atoi(c.DefaultQuery("page", "1"))
		modelFilter.PageSize, _ = strconv.Atoi(c.DefaultQuery("page_size", "20"))
		modelFilter.SortBy = c.DefaultQuery("sort_by", "name")
		modelFilter.SortOrder = c.DefaultQuery("sort_order", "asc")

		tags, total, err := h.tagService.List(c.Request.Context(), &modelFilter)
		if err != nil {
			httpresponse.InternalServerError(c.Writer, "Failed to list tags")
			return
		}

		// Build page-based response
		data := map[string]interface{}{
			"tags": tags,
			"meta": map[string]interface{}{
				"page":        modelFilter.Page,
				"page_size":   modelFilter.PageSize,
				"total":       int(total),
				"total_pages": (int(total) + modelFilter.PageSize - 1) / modelFilter.PageSize,
				"has_next":    modelFilter.Page < (int(total)+modelFilter.PageSize-1)/modelFilter.PageSize,
				"has_prev":    modelFilter.Page > 1,
			},
		}

		httpresponse.OK(c.Writer, data)
		return
	}

	// Use cursor-based pagination (default)
	response, err := h.tagService.ListWithCursor(c.Request.Context(), tenantID.(uint), uint(websiteID), cursorReq, map[string]interface{}{
		"search":     filter.Search,
		"is_active":  filter.IsActive,
		"status":     filter.Status,
		"sort_by":    filter.SortBy,
		"sort_order": filter.SortOrder,
	})
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to list tags with cursor")
		return
	}

	httpresponse.OK(c.Writer, response)
}

// GetMostUsedTags retrieves most used tags
func (h *BlogTagHandler) GetMostUsedTags(c *gin.Context) {
	websiteIDStr := c.Query("website_id")
	if websiteIDStr == "" {
		httpresponse.BadRequest(c.Writer, "Website ID is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid website ID")
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	tags, err := h.tagService.GetMostUsed(c.Request.Context(), tenantID.(uint), uint(websiteID), limit)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to get most used tags")
		return
	}

	httpresponse.OK(c.Writer, tags)
}

// GetTagSuggestions retrieves tag suggestions based on query
func (h *BlogTagHandler) GetTagSuggestions(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		httpresponse.BadRequest(c.Writer, "Query parameter 'q' is required")
		return
	}

	websiteIDStr := c.Query("website_id")
	if websiteIDStr == "" {
		httpresponse.BadRequest(c.Writer, "Website ID is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid website ID")
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	tags, err := h.tagService.GetSuggestions(c.Request.Context(), tenantID.(uint), uint(websiteID), query, limit)
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to get tag suggestions")
		return
	}

	httpresponse.OK(c.Writer, tags)
}

// GetTagStats retrieves tag statistics
func (h *BlogTagHandler) GetTagStats(c *gin.Context) {
	websiteIDStr := c.Query("website_id")
	if websiteIDStr == "" {
		httpresponse.BadRequest(c.Writer, "Website ID is required")
		return
	}

	websiteID, err := strconv.ParseUint(websiteIDStr, 10, 32)
	if err != nil {
		httpresponse.BadRequest(c.Writer, "Invalid website ID")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		httpresponse.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	stats, err := h.tagService.GetStats(c.Request.Context(), tenantID.(uint), uint(websiteID))
	if err != nil {
		httpresponse.InternalServerError(c.Writer, "Failed to get tag stats")
		return
	}

	httpresponse.OK(c.Writer, stats)
}
