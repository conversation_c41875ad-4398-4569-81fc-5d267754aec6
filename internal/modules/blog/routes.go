package blog

import (
	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/blog/services"
	seoServices "github.com/tranthanhloi/wn-api-v3/internal/modules/seo/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// RegisterBlogRoutes registers all blog module routes
func RegisterBlogRoutes(r *gin.RouterGroup, blogServices *services.BlogServices, seoMetaService seoServices.SEOMetaService, logger utils.Logger) {
	// Create handlers
	categoryHandler := handlers.NewBlogCategoryHandler(blogServices.CategoryService)
	postHandler := handlers.NewBlogPostHandler(blogServices.PostService)
	tagHandler := handlers.NewBlogTagHandler(blogServices.TagService)

	// Create SEO handler if SEO service is provided
	var blogPostSEOHandler *handlers.BlogPostSEOHandler
	if seoMetaService != nil {
		blogPostSEOHandler = handlers.NewBlogPostSEOHandler(seoMetaService, logger)
	}

	// Create blog routes group
	blog := r.Group("/blog")

	// Public read-only routes (no authentication required)
	publicBlog := blog.Group("")
	{
		// Public category routes
		publicCategories := publicBlog.Group("/categories")
		{
			publicCategories.GET("", categoryHandler.ListCategories)
			publicCategories.GET("/:id", categoryHandler.GetCategory)
			publicCategories.GET("/slug/:slug", categoryHandler.GetCategoryBySlug)
			publicCategories.GET("/hierarchy", categoryHandler.GetCategoryHierarchy)
		}
	}

	// Protected routes (require authentication)
	protectedBlog := blog.Group("")
	protectedBlog.Use(middleware.RequireAuthentication())
	{
		// Protected category routes
		categories := protectedBlog.Group("/categories")
		{
			categories.POST("", categoryHandler.CreateCategory)
			categories.PUT("/:id", categoryHandler.UpdateCategory)
			categories.DELETE("/:id", categoryHandler.DeleteCategory)
			categories.POST("/:id/move", categoryHandler.MoveCategory)
			categories.POST("/positions", categoryHandler.UpdateCategoryPositions)
		}

		// Public post routes (in publicBlog group above)
	}

	// Add public post routes
	publicPosts := publicBlog.Group("/posts")
	{
		publicPosts.GET("", postHandler.ListPosts)
		publicPosts.GET("/:id", postHandler.GetPost)
		publicPosts.GET("/slug/:slug", postHandler.GetPostBySlug)
		publicPosts.GET("/published", postHandler.GetPublishedPosts)
		publicPosts.GET("/featured", postHandler.GetFeaturedPosts)
		publicPosts.GET("/:id/related", postHandler.GetRelatedPosts)
		publicPosts.GET("/stats", postHandler.GetPostStats)
		publicPosts.GET("/popular", postHandler.GetPopularPosts)
	}

	// Continue with protected blog group
	protectedBlog2 := blog.Group("")
	protectedBlog2.Use(middleware.RequireAuthentication())
	{
		// Protected post routes
		posts := protectedBlog2.Group("/posts")
		{
			posts.POST("", postHandler.CreatePost)
			posts.PUT("/:id", postHandler.UpdatePost)
			posts.DELETE("/:id", postHandler.DeletePost)
			posts.POST("/:id/publish", postHandler.PublishPost)
			posts.POST("/:id/unpublish", postHandler.UnpublishPost)
			posts.POST("/:id/tags/attach", postHandler.AttachTags)
			posts.POST("/:id/tags/detach", postHandler.DetachTags)
			posts.POST("/:id/tags/sync", postHandler.SyncTags)

			// SEO convenience endpoints for blog posts
			if blogPostSEOHandler != nil {
				posts.POST("/:id/seo", blogPostSEOHandler.CreatePostSEO)            // Create SEO for post
				posts.GET("/:id/seo", blogPostSEOHandler.GetPostSEO)                // Get SEO for post
				posts.PUT("/:id/seo", blogPostSEOHandler.UpdatePostSEO)             // Update SEO for post
				posts.DELETE("/:id/seo", blogPostSEOHandler.DeletePostSEO)          // Delete SEO for post
				posts.POST("/:id/seo/analyze", blogPostSEOHandler.AnalyzePostSEO)   // Analyze post SEO
				posts.POST("/:id/seo/validate", blogPostSEOHandler.ValidatePostSEO) // Validate post SEO
				posts.GET("/:id/seo/tags", blogPostSEOHandler.GeneratePostMetaTags) // Generate meta tags
			}
		}

	}

	// Public tag routes
	publicTags := publicBlog.Group("/tags")
	{
		publicTags.GET("", tagHandler.ListTags)
		publicTags.GET("/:id", tagHandler.GetTag)
		publicTags.GET("/slug/:slug", tagHandler.GetTagBySlug)
		publicTags.GET("/most-used", tagHandler.GetMostUsedTags)
		publicTags.GET("/suggestions", tagHandler.GetTagSuggestions)
		publicTags.GET("/stats", tagHandler.GetTagStats)
	}

	// Protected tag routes
	protectedTags := protectedBlog2.Group("/tags")
	{
		protectedTags.POST("", tagHandler.CreateTag)
		protectedTags.PUT("/:id", tagHandler.UpdateTag)
		protectedTags.DELETE("/:id", tagHandler.DeleteTag)
	}
}
