package models

import (
	"encoding/json"
	"time"
)

// TemplateType represents the type of notification template
// @Enum transactional,marketing,system,custom
type TemplateType string

const (
	TemplateTypeTransactional TemplateType = "transactional"
	TemplateTypeMarketing     TemplateType = "marketing"
	TemplateTypeSystem        TemplateType = "system"
	TemplateTypeCustom        TemplateType = "custom"
)

type NotificationTemplate struct {
	ID              uint                `gorm:"primaryKey" json:"id"`
	TenantID        uint                `gorm:"not null;index:idx_notification_templates_tenant_id" json:"tenant_id"`
	Code            string              `gorm:"size:100;not null;index:idx_notification_templates_code" json:"code"`
	Name            string              `gorm:"size:255;not null" json:"name"`
	Type            TemplateType        `gorm:"type:enum('transactional','marketing','system','custom');not null;default:'transactional';index:idx_notification_templates_type" json:"type"`
	Channel         NotificationChannel `gorm:"type:enum('email','socket','push','sms');not null;index:idx_notification_templates_channel" json:"channel"`
	Description     *string             `gorm:"type:text" json:"description,omitempty"`
	Variables       json.RawMessage     `gorm:"type:json;default:'[]'" json:"variables"`
	IsActive        bool                `gorm:"not null;default:false;index:idx_notification_templates_is_active" json:"is_active"`
	VersionCount    uint                `gorm:"not null;default:0" json:"version_count"`
	ActiveVersionID *uint               `json:"active_version_id,omitempty"`
	CreatedBy       *uint               `gorm:"index:idx_notification_templates_created_by" json:"created_by,omitempty"`
	UpdatedBy       *uint               `json:"updated_by,omitempty"`
	CreatedAt       time.Time           `gorm:"index:idx_notification_templates_created_at" json:"created_at"`
	UpdatedAt       time.Time           `json:"updated_at"`

	// Relationships
	Versions      []NotificationTemplateVersion `gorm:"foreignKey:TemplateID" json:"versions,omitempty"`
	ActiveVersion *NotificationTemplateVersion  `gorm:"foreignKey:ActiveVersionID" json:"active_version,omitempty"`
	Notifications []Notification                `gorm:"foreignKey:TemplateID" json:"notifications,omitempty"`
}

func (NotificationTemplate) TableName() string {
	return "notification_templates"
}

// GetVariablesList returns variables as string slice
func (t *NotificationTemplate) GetVariablesList() ([]string, error) {
	var variables []string
	if err := json.Unmarshal(t.Variables, &variables); err != nil {
		return nil, err
	}
	return variables, nil
}

// SetVariables sets variables from string slice
func (t *NotificationTemplate) SetVariables(variables []string) error {
	jsonData, err := json.Marshal(variables)
	if err != nil {
		return err
	}
	t.Variables = json.RawMessage(jsonData)
	return nil
}

// GetTypeString returns type as string
func (t *NotificationTemplate) GetTypeString() string {
	return string(t.Type)
}

// GetChannelString returns channel as string
func (t *NotificationTemplate) GetChannelString() string {
	return string(t.Channel)
}

// Activate activates the template
func (t *NotificationTemplate) Activate() {
	t.IsActive = true
}

// Deactivate deactivates the template
func (t *NotificationTemplate) Deactivate() {
	t.IsActive = false
}

// IncrementVersionCount increments version count
func (t *NotificationTemplate) IncrementVersionCount() {
	t.VersionCount++
}

type NotificationTemplateVersion struct {
	ID            uint            `gorm:"primaryKey" json:"id"`
	TenantID      uint            `gorm:"not null;index:idx_notification_template_versions_tenant_id" json:"tenant_id"`
	TemplateID    uint            `gorm:"not null;index:idx_notification_template_versions_template_id" json:"template_id"`
	VersionNumber uint            `gorm:"not null;default:1" json:"version_number"`
	Language      string          `gorm:"size:10;not null;default:'en';index:idx_notification_template_versions_language" json:"language"`
	Subject       string          `gorm:"size:255;not null" json:"subject"`
	BodyHTML      string          `gorm:"type:longtext;not null" json:"body_html"`
	BodyText      *string         `gorm:"type:longtext" json:"body_text,omitempty"`
	Variables     json.RawMessage `gorm:"type:json;default:'[]'" json:"variables"`
	IsActive      bool            `gorm:"not null;default:false;index:idx_notification_template_versions_is_active" json:"is_active"`
	IsApproved    bool            `gorm:"not null;default:false;index:idx_notification_template_versions_is_approved" json:"is_approved"`
	ApprovedBy    *uint           `gorm:"index:idx_notification_template_versions_approved_by" json:"approved_by,omitempty"`
	ApprovedAt    *time.Time      `json:"approved_at,omitempty"`
	CreatedBy     *uint           `gorm:"index:idx_notification_template_versions_created_by" json:"created_by,omitempty"`
	CreatedAt     time.Time       `gorm:"index:idx_notification_template_versions_created_at" json:"created_at"`
	UpdatedAt     time.Time       `json:"updated_at"`

	// Relationships
	Template *NotificationTemplate `gorm:"foreignKey:TemplateID" json:"template,omitempty"`
}

func (NotificationTemplateVersion) TableName() string {
	return "notification_template_versions"
}

// GetVariablesList returns variables as string slice
func (v *NotificationTemplateVersion) GetVariablesList() ([]string, error) {
	var variables []string
	if err := json.Unmarshal(v.Variables, &variables); err != nil {
		return nil, err
	}
	return variables, nil
}

// SetVariables sets variables from string slice
func (v *NotificationTemplateVersion) SetVariables(variables []string) error {
	jsonData, err := json.Marshal(variables)
	if err != nil {
		return err
	}
	v.Variables = json.RawMessage(jsonData)
	return nil
}

// Activate activates the version
func (v *NotificationTemplateVersion) Activate() {
	v.IsActive = true
}

// Deactivate deactivates the version
func (v *NotificationTemplateVersion) Deactivate() {
	v.IsActive = false
}

// Approve approves the version
func (v *NotificationTemplateVersion) Approve(approvedBy uint) {
	now := time.Now()
	v.IsApproved = true
	v.ApprovedBy = &approvedBy
	v.ApprovedAt = &now
}

// CreateTemplateRequest represents request to create template
type CreateTemplateRequest struct {
	Code        string              `json:"code" binding:"required,max=100"`
	Name        string              `json:"name" binding:"required,max=255"`
	Type        TemplateType        `json:"type" binding:"required,oneof=transactional marketing system custom"`
	Channel     NotificationChannel `json:"channel" binding:"required,oneof=email socket push sms"`
	Description *string             `json:"description,omitempty"`
	Variables   []string            `json:"variables,omitempty"`
}

// UpdateTemplateRequest represents request to update template
type UpdateTemplateRequest struct {
	Name        *string       `json:"name,omitempty" binding:"omitempty,max=255"`
	Type        *TemplateType `json:"type,omitempty" binding:"omitempty,oneof=transactional marketing system custom"`
	Description *string       `json:"description,omitempty"`
	Variables   []string      `json:"variables,omitempty"`
	IsActive    *bool         `json:"is_active,omitempty"`
}

// CreateTemplateVersionRequest represents request to create template version
type CreateTemplateVersionRequest struct {
	Language  string   `json:"language" binding:"required,max=10"`
	Subject   string   `json:"subject" binding:"required,max=255"`
	BodyHTML  string   `json:"body_html" binding:"required"`
	BodyText  *string  `json:"body_text,omitempty"`
	Variables []string `json:"variables,omitempty"`
}

// UpdateTemplateVersionRequest represents request to update template version
type UpdateTemplateVersionRequest struct {
	Subject   *string  `json:"subject,omitempty" binding:"omitempty,max=255"`
	BodyHTML  *string  `json:"body_html,omitempty"`
	BodyText  *string  `json:"body_text,omitempty"`
	Variables []string `json:"variables,omitempty"`
	IsActive  *bool    `json:"is_active,omitempty"`
}

// NotificationTemplateResponse represents template response
type NotificationTemplateResponse struct {
	ID              uint                `json:"id"`
	TenantID        uint                `json:"tenant_id"`
	Code            string              `json:"code"`
	Name            string              `json:"name"`
	Type            TemplateType        `json:"type"`
	Channel         NotificationChannel `json:"channel"`
	Description     *string             `json:"description,omitempty"`
	Variables       json.RawMessage     `json:"variables,omitempty"`
	IsActive        bool                `json:"is_active"`
	VersionCount    uint                `json:"version_count"`
	ActiveVersionID *uint               `json:"active_version_id,omitempty"`
	CreatedBy       *uint               `json:"created_by,omitempty"`
	UpdatedBy       *uint               `json:"updated_by,omitempty"`
	CreatedAt       time.Time           `json:"created_at"`
	UpdatedAt       time.Time           `json:"updated_at"`

	// Optional relationships
	Versions      []NotificationTemplateVersionResponse `json:"versions,omitempty"`
	ActiveVersion *NotificationTemplateVersionResponse  `json:"active_version,omitempty"`
}

// NotificationTemplateVersionResponse represents template version response
type NotificationTemplateVersionResponse struct {
	ID            uint            `json:"id"`
	TenantID      uint            `json:"tenant_id"`
	TemplateID    uint            `json:"template_id"`
	VersionNumber uint            `json:"version_number"`
	Language      string          `json:"language"`
	Subject       string          `json:"subject"`
	BodyHTML      string          `json:"body_html"`
	BodyText      *string         `json:"body_text,omitempty"`
	Variables     json.RawMessage `json:"variables,omitempty"`
	IsActive      bool            `json:"is_active"`
	IsApproved    bool            `json:"is_approved"`
	ApprovedBy    *uint           `json:"approved_by,omitempty"`
	ApprovedAt    *time.Time      `json:"approved_at,omitempty"`
	CreatedBy     *uint           `json:"created_by,omitempty"`
	CreatedAt     time.Time       `json:"created_at"`
	UpdatedAt     time.Time       `json:"updated_at"`
}

// ToResponse converts template to response
func (t *NotificationTemplate) ToResponse() *NotificationTemplateResponse {
	response := &NotificationTemplateResponse{
		ID:              t.ID,
		TenantID:        t.TenantID,
		Code:            t.Code,
		Name:            t.Name,
		Type:            t.Type,
		Channel:         t.Channel,
		Description:     t.Description,
		Variables:       t.Variables,
		IsActive:        t.IsActive,
		VersionCount:    t.VersionCount,
		ActiveVersionID: t.ActiveVersionID,
		CreatedBy:       t.CreatedBy,
		UpdatedBy:       t.UpdatedBy,
		CreatedAt:       t.CreatedAt,
		UpdatedAt:       t.UpdatedAt,
	}

	if len(t.Versions) > 0 {
		response.Versions = make([]NotificationTemplateVersionResponse, len(t.Versions))
		for i, version := range t.Versions {
			response.Versions[i] = *version.ToResponse()
		}
	}

	if t.ActiveVersion != nil {
		response.ActiveVersion = t.ActiveVersion.ToResponse()
	}

	return response
}

// ToResponse converts template version to response
func (v *NotificationTemplateVersion) ToResponse() *NotificationTemplateVersionResponse {
	return &NotificationTemplateVersionResponse{
		ID:            v.ID,
		TenantID:      v.TenantID,
		TemplateID:    v.TemplateID,
		VersionNumber: v.VersionNumber,
		Language:      v.Language,
		Subject:       v.Subject,
		BodyHTML:      v.BodyHTML,
		BodyText:      v.BodyText,
		Variables:     v.Variables,
		IsActive:      v.IsActive,
		IsApproved:    v.IsApproved,
		ApprovedBy:    v.ApprovedBy,
		ApprovedAt:    v.ApprovedAt,
		CreatedBy:     v.CreatedBy,
		CreatedAt:     v.CreatedAt,
		UpdatedAt:     v.UpdatedAt,
	}
}

// TemplateFilters for querying templates
type TemplateFilters struct {
	Type      TemplateType        `form:"type" binding:"omitempty,oneof=transactional marketing system custom"`
	Channel   NotificationChannel `form:"channel" binding:"omitempty,oneof=email socket push sms"`
	IsActive  *bool               `form:"is_active"`
	Search    string              `form:"search"`
	
	// Cursor-based pagination (preferred)
	Cursor    string `form:"cursor"`
	Limit     int    `form:"limit,default=20" binding:"min=1,max=100"`
	
	// Offset-based pagination (deprecated, for backward compatibility)
	Page      int    `form:"page,default=1" binding:"min=1"`
	
	SortBy    string `form:"sort_by,default=created_at" binding:"oneof=id name code created_at updated_at"`
	SortOrder string `form:"sort_order,default=desc" binding:"oneof=asc desc"`
}
