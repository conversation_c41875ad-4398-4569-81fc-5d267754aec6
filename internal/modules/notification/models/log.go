package models

import (
	"encoding/json"
	"time"
)

type LogEventType string

const (
	EventTypeCreated      LogEventType = "created"
	EventTypeQueued       LogEventType = "queued"
	EventTypeSent         LogEventType = "sent"
	EventTypeDelivered    LogEventType = "delivered"
	EventTypeOpened       LogEventType = "opened"
	EventTypeClicked      LogEventType = "clicked"
	EventTypeFailed       LogEventType = "failed"
	EventTypeBounced      LogEventType = "bounced"
	EventTypeComplaint    LogEventType = "complaint"
	EventTypeUnsubscribed LogEventType = "unsubscribed"
)

type NotificationLog struct {
	ID               uint            `gorm:"primaryKey" json:"id"`
	TenantID         uint            `gorm:"not null;index:idx_notification_logs_tenant_id" json:"tenant_id"`
	NotificationID   uint            `gorm:"not null;index:idx_notification_logs_notification_id" json:"notification_id"`
	RecipientID      *uint           `gorm:"index:idx_notification_logs_recipient_id" json:"recipient_id,omitempty"`
	EventType        LogEventType    `gorm:"type:enum('created','queued','sent','delivered','opened','clicked','failed','bounced','complaint','unsubscribed');not null;index:idx_notification_logs_event_type" json:"event_type"`
	EventData        json.RawMessage `gorm:"type:json;default:'{}'" json:"event_data"`
	UserAgent        *string         `gorm:"type:text" json:"user_agent,omitempty"`
	IPAddress        *string         `gorm:"size:45;index:idx_notification_logs_ip_address" json:"ip_address,omitempty"`
	TrackingID       *string         `gorm:"size:255;index:idx_notification_logs_tracking_id" json:"tracking_id,omitempty"`
	ErrorCode        *string         `gorm:"size:50" json:"error_code,omitempty"`
	ErrorMessage     *string         `gorm:"type:text" json:"error_message,omitempty"`
	ExternalID       *string         `gorm:"size:255;index:idx_notification_logs_external_id" json:"external_id,omitempty"`
	ProviderResponse json.RawMessage `gorm:"type:json;default:'{}'" json:"provider_response"`
	OccurredAt       time.Time       `gorm:"not null;index:idx_notification_logs_occurred_at" json:"occurred_at"`
	CreatedAt        time.Time       `gorm:"index:idx_notification_logs_created_at" json:"created_at"`

	// Relationships
	Notification *Notification          `gorm:"foreignKey:NotificationID" json:"notification,omitempty"`
	Recipient    *NotificationRecipient `gorm:"foreignKey:RecipientID" json:"recipient,omitempty"`
}

func (NotificationLog) TableName() string {
	return "notification_logs"
}

// GetEventDataMap returns event data as map
func (l *NotificationLog) GetEventDataMap() (map[string]interface{}, error) {
	var data map[string]interface{}
	if err := json.Unmarshal(l.EventData, &data); err != nil {
		return nil, err
	}
	return data, nil
}

// SetEventData sets event data from map
func (l *NotificationLog) SetEventData(data map[string]interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	l.EventData = json.RawMessage(jsonData)
	return nil
}

// GetProviderResponseMap returns provider response as map
func (l *NotificationLog) GetProviderResponseMap() (map[string]interface{}, error) {
	var response map[string]interface{}
	if err := json.Unmarshal(l.ProviderResponse, &response); err != nil {
		return nil, err
	}
	return response, nil
}

// SetProviderResponse sets provider response from map
func (l *NotificationLog) SetProviderResponse(response map[string]interface{}) error {
	jsonData, err := json.Marshal(response)
	if err != nil {
		return err
	}
	l.ProviderResponse = json.RawMessage(jsonData)
	return nil
}

// GetEventTypeString returns event type as string
func (l *NotificationLog) GetEventTypeString() string {
	return string(l.EventType)
}

// CreateLogRequest represents request to create log entry
type CreateLogRequest struct {
	NotificationID   uint                   `json:"notification_id" binding:"required"`
	RecipientID      *uint                  `json:"recipient_id,omitempty"`
	EventType        LogEventType           `json:"event_type" binding:"required,oneof=created queued sent delivered opened clicked failed bounced complaint unsubscribed"`
	EventData        map[string]interface{} `json:"event_data,omitempty"`
	UserAgent        *string                `json:"user_agent,omitempty"`
	IPAddress        *string                `json:"ip_address,omitempty"`
	TrackingID       *string                `json:"tracking_id,omitempty"`
	ErrorCode        *string                `json:"error_code,omitempty"`
	ErrorMessage     *string                `json:"error_message,omitempty"`
	ExternalID       *string                `json:"external_id,omitempty"`
	ProviderResponse map[string]interface{} `json:"provider_response,omitempty"`
	OccurredAt       *time.Time             `json:"occurred_at,omitempty"`
}

// NotificationLogResponse represents log response
type NotificationLogResponse struct {
	ID               uint            `json:"id"`
	TenantID         uint            `json:"tenant_id"`
	NotificationID   uint            `json:"notification_id"`
	RecipientID      *uint           `json:"recipient_id,omitempty"`
	EventType        LogEventType    `json:"event_type"`
	EventData        json.RawMessage `json:"event_data,omitempty"`
	UserAgent        *string         `json:"user_agent,omitempty"`
	IPAddress        *string         `json:"ip_address,omitempty"`
	TrackingID       *string         `json:"tracking_id,omitempty"`
	ErrorCode        *string         `json:"error_code,omitempty"`
	ErrorMessage     *string         `json:"error_message,omitempty"`
	ExternalID       *string         `json:"external_id,omitempty"`
	ProviderResponse json.RawMessage `json:"provider_response,omitempty"`
	OccurredAt       time.Time       `json:"occurred_at"`
	CreatedAt        time.Time       `json:"created_at"`
}

// ToResponse converts log to response
func (l *NotificationLog) ToResponse() *NotificationLogResponse {
	return &NotificationLogResponse{
		ID:               l.ID,
		TenantID:         l.TenantID,
		NotificationID:   l.NotificationID,
		RecipientID:      l.RecipientID,
		EventType:        l.EventType,
		EventData:        l.EventData,
		UserAgent:        l.UserAgent,
		IPAddress:        l.IPAddress,
		TrackingID:       l.TrackingID,
		ErrorCode:        l.ErrorCode,
		ErrorMessage:     l.ErrorMessage,
		ExternalID:       l.ExternalID,
		ProviderResponse: l.ProviderResponse,
		OccurredAt:       l.OccurredAt,
		CreatedAt:        l.CreatedAt,
	}
}

// LogFilters for querying logs
type LogFilters struct {
	NotificationID uint         `form:"notification_id"`
	RecipientID    *uint        `form:"recipient_id"`
	EventType      LogEventType `form:"event_type" binding:"omitempty,oneof=created queued sent delivered opened clicked failed bounced complaint unsubscribed"`
	TrackingID     string       `form:"tracking_id"`
	ExternalID     string       `form:"external_id"`
	IPAddress      string       `form:"ip_address"`
	DateFrom       *time.Time   `form:"date_from" time_format:"2006-01-02"`
	DateTo         *time.Time   `form:"date_to" time_format:"2006-01-02"`
	Page           int          `form:"page,default=1" binding:"min=1"`
	Limit          int          `form:"limit,default=20" binding:"min=1,max=100"`
	SortBy         string       `form:"sort_by,default=occurred_at" binding:"oneof=id occurred_at created_at event_type"`
	SortOrder      string       `form:"sort_order,default=desc" binding:"oneof=asc desc"`
}

// Helper functions for creating logs
func CreateCreatedLog(tenantID, notificationID uint) *NotificationLog {
	return &NotificationLog{
		TenantID:       tenantID,
		NotificationID: notificationID,
		EventType:      EventTypeCreated,
		EventData:      json.RawMessage("{}"),
		OccurredAt:     time.Now(),
	}
}

func CreateQueuedLog(tenantID, notificationID uint, queueName string) *NotificationLog {
	eventData := map[string]interface{}{
		"queue_name": queueName,
		"queued_at":  time.Now(),
	}
	jsonData, _ := json.Marshal(eventData)

	return &NotificationLog{
		TenantID:       tenantID,
		NotificationID: notificationID,
		EventType:      EventTypeQueued,
		EventData:      json.RawMessage(jsonData),
		OccurredAt:     time.Now(),
	}
}

func CreateSentLog(tenantID, notificationID uint, recipientID *uint, providerResponse map[string]interface{}) *NotificationLog {
	jsonData, _ := json.Marshal(providerResponse)

	return &NotificationLog{
		TenantID:         tenantID,
		NotificationID:   notificationID,
		RecipientID:      recipientID,
		EventType:        EventTypeSent,
		EventData:        json.RawMessage("{}"),
		ProviderResponse: json.RawMessage(jsonData),
		OccurredAt:       time.Now(),
	}
}

func CreateDeliveredLog(tenantID, notificationID uint, recipientID *uint, deliveryInfo map[string]interface{}) *NotificationLog {
	jsonData, _ := json.Marshal(deliveryInfo)

	return &NotificationLog{
		TenantID:       tenantID,
		NotificationID: notificationID,
		RecipientID:    recipientID,
		EventType:      EventTypeDelivered,
		EventData:      json.RawMessage(jsonData),
		OccurredAt:     time.Now(),
	}
}

func CreateOpenedLog(tenantID, notificationID uint, recipientID *uint, userAgent, ipAddress *string) *NotificationLog {
	eventData := map[string]interface{}{
		"opened_at": time.Now(),
	}
	jsonData, _ := json.Marshal(eventData)

	return &NotificationLog{
		TenantID:       tenantID,
		NotificationID: notificationID,
		RecipientID:    recipientID,
		EventType:      EventTypeOpened,
		EventData:      json.RawMessage(jsonData),
		UserAgent:      userAgent,
		IPAddress:      ipAddress,
		OccurredAt:     time.Now(),
	}
}

func CreateClickedLog(tenantID, notificationID uint, recipientID *uint, clickedURL, userAgent, ipAddress *string) *NotificationLog {
	eventData := map[string]interface{}{
		"clicked_at":  time.Now(),
		"clicked_url": clickedURL,
	}
	jsonData, _ := json.Marshal(eventData)

	return &NotificationLog{
		TenantID:       tenantID,
		NotificationID: notificationID,
		RecipientID:    recipientID,
		EventType:      EventTypeClicked,
		EventData:      json.RawMessage(jsonData),
		UserAgent:      userAgent,
		IPAddress:      ipAddress,
		OccurredAt:     time.Now(),
	}
}

func CreateFailedLog(tenantID, notificationID uint, recipientID *uint, errorCode, errorMessage string) *NotificationLog {
	eventData := map[string]interface{}{
		"failed_at": time.Now(),
		"reason":    errorMessage,
	}
	jsonData, _ := json.Marshal(eventData)

	return &NotificationLog{
		TenantID:       tenantID,
		NotificationID: notificationID,
		RecipientID:    recipientID,
		EventType:      EventTypeFailed,
		EventData:      json.RawMessage(jsonData),
		ErrorCode:      &errorCode,
		ErrorMessage:   &errorMessage,
		OccurredAt:     time.Now(),
	}
}
