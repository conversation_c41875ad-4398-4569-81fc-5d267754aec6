package models

import (
	"encoding/json"
	"time"
)

type RecipientStatus string
type RecipientType string

const (
	RecipientStatusPending   RecipientStatus = "pending"
	RecipientStatusSent      RecipientStatus = "sent"
	RecipientStatusDelivered RecipientStatus = "delivered"
	RecipientStatusRead      RecipientStatus = "read"
	RecipientStatusFailed    RecipientStatus = "failed"
	RecipientStatusBounced   RecipientStatus = "bounced"
	RecipientStatusBlocked   RecipientStatus = "blocked"
)

const (
	RecipientTypeUser   RecipientType = "user"
	RecipientTypeEmail  RecipientType = "email"
	RecipientTypePhone  RecipientType = "phone"
	RecipientTypeDevice RecipientType = "device"
)

type NotificationRecipient struct {
	ID               uint            `gorm:"primaryKey" json:"id"`
	TenantID         uint            `gorm:"not null;index:idx_notification_recipients_tenant_id" json:"tenant_id"`
	NotificationID   uint            `gorm:"not null;index:idx_notification_recipients_notification_id" json:"notification_id"`
	UserID           *uint           `gorm:"index:idx_notification_recipients_user_id" json:"user_id,omitempty"`
	RecipientType    RecipientType   `gorm:"type:enum('user','email','phone','device');not null;default:'user'" json:"recipient_type"`
	RecipientAddress string          `gorm:"size:255;not null;index:idx_notification_recipients_recipient_address" json:"recipient_address"`
	DeviceToken      *string         `gorm:"size:512" json:"device_token,omitempty"`
	Status           RecipientStatus `gorm:"type:enum('pending','sent','delivered','read','failed','bounced','blocked');not null;default:'pending';index:idx_notification_recipients_status" json:"status"`
	DeliveredAt      *time.Time      `gorm:"index:idx_notification_recipients_delivered_at" json:"delivered_at,omitempty"`
	ReadAt           *time.Time      `gorm:"index:idx_notification_recipients_read_at" json:"read_at,omitempty"`
	FailedAt         *time.Time      `json:"failed_at,omitempty"`
	BouncedAt        *time.Time      `json:"bounced_at,omitempty"`
	ErrorMessage     *string         `gorm:"type:text" json:"error_message,omitempty"`
	DeliveryInfo     json.RawMessage `gorm:"type:json;default:'{}'" json:"delivery_info"`
	EngagementData   json.RawMessage `gorm:"type:json;default:'{}'" json:"engagement_data"`
	CreatedAt        time.Time       `json:"created_at"`
	UpdatedAt        time.Time       `json:"updated_at"`

	// Relationships
	Notification *Notification     `gorm:"foreignKey:NotificationID" json:"notification,omitempty"`
	Logs         []NotificationLog `gorm:"foreignKey:RecipientID" json:"logs,omitempty"`
}

func (NotificationRecipient) TableName() string {
	return "notification_recipients"
}

// MarkAsSent marks recipient as sent
func (r *NotificationRecipient) MarkAsSent() {
	r.Status = RecipientStatusSent
}

// MarkAsDelivered marks recipient as delivered
func (r *NotificationRecipient) MarkAsDelivered() {
	now := time.Now()
	r.Status = RecipientStatusDelivered
	r.DeliveredAt = &now
}

// MarkAsRead marks recipient as read
func (r *NotificationRecipient) MarkAsRead() {
	now := time.Now()
	r.Status = RecipientStatusRead
	r.ReadAt = &now
}

// MarkAsFailed marks recipient as failed
func (r *NotificationRecipient) MarkAsFailed(errorMsg string) {
	now := time.Now()
	r.Status = RecipientStatusFailed
	r.FailedAt = &now
	r.ErrorMessage = &errorMsg
}

// MarkAsBounced marks recipient as bounced
func (r *NotificationRecipient) MarkAsBounced(errorMsg string) {
	now := time.Now()
	r.Status = RecipientStatusBounced
	r.BouncedAt = &now
	r.ErrorMessage = &errorMsg
}

// GetDeliveryInfoMap returns delivery info as map
func (r *NotificationRecipient) GetDeliveryInfoMap() (map[string]interface{}, error) {
	var info map[string]interface{}
	if err := json.Unmarshal(r.DeliveryInfo, &info); err != nil {
		return nil, err
	}
	return info, nil
}

// SetDeliveryInfo sets delivery info from map
func (r *NotificationRecipient) SetDeliveryInfo(info map[string]interface{}) error {
	jsonData, err := json.Marshal(info)
	if err != nil {
		return err
	}
	r.DeliveryInfo = json.RawMessage(jsonData)
	return nil
}

// GetEngagementDataMap returns engagement data as map
func (r *NotificationRecipient) GetEngagementDataMap() (map[string]interface{}, error) {
	var data map[string]interface{}
	if err := json.Unmarshal(r.EngagementData, &data); err != nil {
		return nil, err
	}
	return data, nil
}

// SetEngagementData sets engagement data from map
func (r *NotificationRecipient) SetEngagementData(data map[string]interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	r.EngagementData = json.RawMessage(jsonData)
	return nil
}

// TrackOpen records email/notification open event
func (r *NotificationRecipient) TrackOpen() error {
	engagement, err := r.GetEngagementDataMap()
	if err != nil {
		engagement = make(map[string]interface{})
	}

	opens, _ := engagement["opens"].(float64)
	opens++
	engagement["opens"] = opens
	engagement["last_opened_at"] = time.Now()

	if opens == 1 {
		engagement["first_opened_at"] = time.Now()
		r.MarkAsRead()
	}

	return r.SetEngagementData(engagement)
}

// TrackClick records click event
func (r *NotificationRecipient) TrackClick(clickedURL string) error {
	engagement, err := r.GetEngagementDataMap()
	if err != nil {
		engagement = make(map[string]interface{})
	}

	clicks, _ := engagement["clicks"].(float64)
	clicks++
	engagement["clicks"] = clicks
	engagement["last_clicked_at"] = time.Now()

	if clicks == 1 {
		engagement["first_clicked_at"] = time.Now()
	}

	// Store clicked URLs
	if clickedUrls, ok := engagement["clicked_urls"].([]interface{}); ok {
		engagement["clicked_urls"] = append(clickedUrls, clickedURL)
	} else {
		engagement["clicked_urls"] = []string{clickedURL}
	}

	return r.SetEngagementData(engagement)
}

// GetStatusString returns status as string
func (r *NotificationRecipient) GetStatusString() string {
	return string(r.Status)
}

// GetTypeString returns type as string
func (r *NotificationRecipient) GetTypeString() string {
	return string(r.RecipientType)
}

// CreateRecipientRequest represents request to create recipient
type CreateRecipientRequest struct {
	UserID           *uint                  `json:"user_id,omitempty"`
	RecipientType    RecipientType          `json:"recipient_type" binding:"required,oneof=user email phone device"`
	RecipientAddress string                 `json:"recipient_address" binding:"required"`
	DeviceToken      *string                `json:"device_token,omitempty"`
	DeliveryInfo     map[string]interface{} `json:"delivery_info,omitempty"`
}

// UpdateRecipientRequest represents request to update recipient
type UpdateRecipientRequest struct {
	Status         *RecipientStatus       `json:"status,omitempty" binding:"omitempty,oneof=pending sent delivered read failed bounced blocked"`
	DeliveryInfo   map[string]interface{} `json:"delivery_info,omitempty"`
	EngagementData map[string]interface{} `json:"engagement_data,omitempty"`
}

// NotificationRecipientResponse represents recipient response
type NotificationRecipientResponse struct {
	ID               uint            `json:"id"`
	TenantID         uint            `json:"tenant_id"`
	NotificationID   uint            `json:"notification_id"`
	UserID           *uint           `json:"user_id,omitempty"`
	RecipientType    RecipientType   `json:"recipient_type"`
	RecipientAddress string          `json:"recipient_address"`
	DeviceToken      *string         `json:"device_token,omitempty"`
	Status           RecipientStatus `json:"status"`
	DeliveredAt      *time.Time      `json:"delivered_at,omitempty"`
	ReadAt           *time.Time      `json:"read_at,omitempty"`
	FailedAt         *time.Time      `json:"failed_at,omitempty"`
	BouncedAt        *time.Time      `json:"bounced_at,omitempty"`
	ErrorMessage     *string         `json:"error_message,omitempty"`
	DeliveryInfo     json.RawMessage `json:"delivery_info,omitempty"`
	EngagementData   json.RawMessage `json:"engagement_data,omitempty"`
	CreatedAt        time.Time       `json:"created_at"`
	UpdatedAt        time.Time       `json:"updated_at"`
}

// ToResponse converts recipient to response
func (r *NotificationRecipient) ToResponse() *NotificationRecipientResponse {
	return &NotificationRecipientResponse{
		ID:               r.ID,
		TenantID:         r.TenantID,
		NotificationID:   r.NotificationID,
		UserID:           r.UserID,
		RecipientType:    r.RecipientType,
		RecipientAddress: r.RecipientAddress,
		DeviceToken:      r.DeviceToken,
		Status:           r.Status,
		DeliveredAt:      r.DeliveredAt,
		ReadAt:           r.ReadAt,
		FailedAt:         r.FailedAt,
		BouncedAt:        r.BouncedAt,
		ErrorMessage:     r.ErrorMessage,
		DeliveryInfo:     r.DeliveryInfo,
		EngagementData:   r.EngagementData,
		CreatedAt:        r.CreatedAt,
		UpdatedAt:        r.UpdatedAt,
	}
}

// RecipientFilters for querying recipients
type RecipientFilters struct {
	NotificationID uint            `form:"notification_id"`
	UserID         *uint           `form:"user_id"`
	Type           RecipientType   `form:"type" binding:"omitempty,oneof=user email phone device"`
	Status         RecipientStatus `form:"status" binding:"omitempty,oneof=pending sent delivered read failed bounced blocked"`
	Address        string          `form:"address"`
	DateFrom       *time.Time      `form:"date_from" time_format:"2006-01-02"`
	DateTo         *time.Time      `form:"date_to" time_format:"2006-01-02"`
	Page           int             `form:"page,default=1" binding:"min=1"`
	Limit          int             `form:"limit,default=20" binding:"min=1,max=100"`
	SortBy         string          `form:"sort_by,default=created_at" binding:"oneof=id created_at updated_at delivered_at status"`
	SortOrder      string          `form:"sort_order,default=desc" binding:"oneof=asc desc"`
}
