package models

import (
	"encoding/json"
	"time"
)

// NotificationStatus represents the status of a notification
// @Enum pending,queued,sent,delivered,failed,cancelled
type NotificationStatus string

// NotificationChannel represents the delivery channel for notifications
// @Enum email,socket,push,sms
type NotificationChannel string

// NotificationPriority represents the priority level of notifications
// @Enum low,normal,high,urgent
type NotificationPriority string

const (
	StatusPending   NotificationStatus = "pending"
	StatusQueued    NotificationStatus = "queued"
	StatusSent      NotificationStatus = "sent"
	StatusDelivered NotificationStatus = "delivered"
	StatusFailed    NotificationStatus = "failed"
	StatusCancelled NotificationStatus = "cancelled"
)

const (
	ChannelEmail  NotificationChannel = "email"
	ChannelSocket NotificationChannel = "socket"
	ChannelPush   NotificationChannel = "push"
	ChannelSMS    NotificationChannel = "sms"
)

const (
	PriorityLow    NotificationPriority = "low"
	PriorityNormal NotificationPriority = "normal"
	PriorityHigh   NotificationPriority = "high"
	PriorityUrgent NotificationPriority = "urgent"
)

type Notification struct {
	ID           uint                 `gorm:"primaryKey" json:"id"`
	TenantID     uint                 `gorm:"not null;index:idx_notifications_tenant_id" json:"tenant_id"`
	Type         string               `gorm:"size:50;not null;index:idx_notifications_tenant_type" json:"type"`
	Channel      NotificationChannel  `gorm:"type:enum('email','socket','push','sms');not null;index:idx_notifications_tenant_channel" json:"channel"`
	Priority     NotificationPriority `gorm:"type:enum('low','normal','high','urgent');not null;default:'normal';index:idx_notifications_tenant_priority" json:"priority"`
	Subject      string               `gorm:"size:255;not null" json:"subject"`
	TemplateID   *uint                `gorm:"index" json:"template_id,omitempty"`
	TemplateData json.RawMessage      `gorm:"type:json;default:'{}'" json:"template_data"`
	Status       NotificationStatus   `gorm:"type:enum('pending','queued','sent','delivered','failed','cancelled');not null;default:'pending';index:idx_notifications_status" json:"status"`
	ScheduledAt  *time.Time           `gorm:"index:idx_notifications_scheduled_at" json:"scheduled_at,omitempty"`
	SentAt       *time.Time           `gorm:"index:idx_notifications_sent_at" json:"sent_at,omitempty"`
	DeliveredAt  *time.Time           `json:"delivered_at,omitempty"`
	FailedAt     *time.Time           `json:"failed_at,omitempty"`
	ErrorMessage *string              `gorm:"type:text" json:"error_message,omitempty"`
	RetryCount   uint                 `gorm:"not null;default:0" json:"retry_count"`
	MaxRetries   uint                 `gorm:"not null;default:3" json:"max_retries"`
	Metadata     json.RawMessage      `gorm:"type:json;default:'{}'" json:"metadata"`
	CreatedAt    time.Time            `gorm:"index:idx_notifications_created_at" json:"created_at"`
	UpdatedAt    time.Time            `json:"updated_at"`

	// Relationships
	Template   *NotificationTemplate   `gorm:"foreignKey:TemplateID" json:"template,omitempty"`
	Recipients []NotificationRecipient `gorm:"foreignKey:NotificationID" json:"recipients,omitempty"`
	Logs       []NotificationLog       `gorm:"foreignKey:NotificationID" json:"logs,omitempty"`
}

func (Notification) TableName() string {
	return "notifications"
}

// IsScheduled checks if notification is scheduled for future delivery
func (n *Notification) IsScheduled() bool {
	return n.ScheduledAt != nil && n.ScheduledAt.After(time.Now())
}

// CanRetry checks if notification can be retried
func (n *Notification) CanRetry() bool {
	return n.Status == StatusFailed && n.RetryCount < n.MaxRetries
}

// GetTemplateDataMap returns template data as map
func (n *Notification) GetTemplateDataMap() (map[string]interface{}, error) {
	var data map[string]interface{}
	if err := json.Unmarshal(n.TemplateData, &data); err != nil {
		return nil, err
	}
	return data, nil
}

// SetTemplateData sets template data from map
func (n *Notification) SetTemplateData(data map[string]interface{}) error {
	jsonData, err := json.Marshal(data)
	if err != nil {
		return err
	}
	n.TemplateData = json.RawMessage(jsonData)
	return nil
}

// GetMetadataMap returns metadata as map
func (n *Notification) GetMetadataMap() (map[string]interface{}, error) {
	var metadata map[string]interface{}
	if err := json.Unmarshal(n.Metadata, &metadata); err != nil {
		return nil, err
	}
	return metadata, nil
}

// SetMetadata sets metadata from map
func (n *Notification) SetMetadata(metadata map[string]interface{}) error {
	jsonData, err := json.Marshal(metadata)
	if err != nil {
		return err
	}
	n.Metadata = json.RawMessage(jsonData)
	return nil
}

// MarkAsSent marks notification as sent
func (n *Notification) MarkAsSent() {
	now := time.Now()
	n.Status = StatusSent
	n.SentAt = &now
}

// MarkAsDelivered marks notification as delivered
func (n *Notification) MarkAsDelivered() {
	now := time.Now()
	n.Status = StatusDelivered
	n.DeliveredAt = &now
}

// MarkAsFailed marks notification as failed
func (n *Notification) MarkAsFailed(errorMsg string) {
	now := time.Now()
	n.Status = StatusFailed
	n.FailedAt = &now
	n.ErrorMessage = &errorMsg
	n.RetryCount++
}

// GetChannelString returns channel as string
func (n *Notification) GetChannelString() string {
	return string(n.Channel)
}

// GetStatusString returns status as string
func (n *Notification) GetStatusString() string {
	return string(n.Status)
}

// GetPriorityString returns priority as string
func (n *Notification) GetPriorityString() string {
	return string(n.Priority)
}

// CreateNotificationRequest represents request to create notification
type CreateNotificationRequest struct {
	Type         string                   `json:"type" binding:"required"`
	Channel      NotificationChannel      `json:"channel" binding:"required,oneof=email socket push sms"`
	Priority     NotificationPriority     `json:"priority,omitempty" binding:"omitempty,oneof=low normal high urgent"`
	Subject      string                   `json:"subject" binding:"required,max=255"`
	TemplateID   *uint                    `json:"template_id,omitempty"`
	TemplateData map[string]interface{}   `json:"template_data,omitempty"`
	Recipients   []CreateRecipientRequest `json:"recipients" binding:"required,min=1"`
	ScheduledAt  *time.Time               `json:"scheduled_at,omitempty"`
	Metadata     map[string]interface{}   `json:"metadata,omitempty"`
}

// UpdateNotificationRequest represents request to update notification
type UpdateNotificationRequest struct {
	Status      *NotificationStatus    `json:"status,omitempty" binding:"omitempty,oneof=pending queued sent delivered failed cancelled"`
	ScheduledAt *time.Time             `json:"scheduled_at,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// NotificationResponse represents notification response
type NotificationResponse struct {
	ID           uint                 `json:"id"`
	TenantID     uint                 `json:"tenant_id"`
	Type         string               `json:"type"`
	Channel      NotificationChannel  `json:"channel"`
	Priority     NotificationPriority `json:"priority"`
	Subject      string               `json:"subject"`
	TemplateID   *uint                `json:"template_id,omitempty"`
	TemplateData json.RawMessage      `json:"template_data,omitempty"`
	Status       NotificationStatus   `json:"status"`
	ScheduledAt  *time.Time           `json:"scheduled_at,omitempty"`
	SentAt       *time.Time           `json:"sent_at,omitempty"`
	DeliveredAt  *time.Time           `json:"delivered_at,omitempty"`
	FailedAt     *time.Time           `json:"failed_at,omitempty"`
	ErrorMessage *string              `json:"error_message,omitempty"`
	RetryCount   uint                 `json:"retry_count"`
	MaxRetries   uint                 `json:"max_retries"`
	Metadata     json.RawMessage      `json:"metadata,omitempty"`
	CreatedAt    time.Time            `json:"created_at"`
	UpdatedAt    time.Time            `json:"updated_at"`

	// Optional relationships
	Template   *NotificationTemplateResponse   `json:"template,omitempty"`
	Recipients []NotificationRecipientResponse `json:"recipients,omitempty"`
}

// ToResponse converts notification to response
func (n *Notification) ToResponse() *NotificationResponse {
	response := &NotificationResponse{
		ID:           n.ID,
		TenantID:     n.TenantID,
		Type:         n.Type,
		Channel:      n.Channel,
		Priority:     n.Priority,
		Subject:      n.Subject,
		TemplateID:   n.TemplateID,
		TemplateData: n.TemplateData,
		Status:       n.Status,
		ScheduledAt:  n.ScheduledAt,
		SentAt:       n.SentAt,
		DeliveredAt:  n.DeliveredAt,
		FailedAt:     n.FailedAt,
		ErrorMessage: n.ErrorMessage,
		RetryCount:   n.RetryCount,
		MaxRetries:   n.MaxRetries,
		Metadata:     n.Metadata,
		CreatedAt:    n.CreatedAt,
		UpdatedAt:    n.UpdatedAt,
	}

	if n.Template != nil {
		response.Template = n.Template.ToResponse()
	}

	if len(n.Recipients) > 0 {
		response.Recipients = make([]NotificationRecipientResponse, len(n.Recipients))
		for i, recipient := range n.Recipients {
			response.Recipients[i] = *recipient.ToResponse()
		}
	}

	return response
}

// NotificationFilters for querying notifications
type NotificationFilters struct {
	Type      string               `form:"type"`
	Channel   NotificationChannel  `form:"channel" binding:"omitempty,oneof=email socket push sms"`
	Status    NotificationStatus   `form:"status" binding:"omitempty,oneof=pending queued sent delivered failed cancelled"`
	Priority  NotificationPriority `form:"priority" binding:"omitempty,oneof=low normal high urgent"`
	DateFrom  *time.Time           `form:"date_from" time_format:"2006-01-02"`
	DateTo    *time.Time           `form:"date_to" time_format:"2006-01-02"`
	Page      int                  `form:"page,default=1" binding:"min=1"`
	Limit     int                  `form:"limit,default=20" binding:"min=1,max=100"`
	SortBy    string               `form:"sort_by,default=created_at" binding:"oneof=id created_at updated_at sent_at priority"`
	SortOrder string               `form:"sort_order,default=desc" binding:"oneof=asc desc"`
}
