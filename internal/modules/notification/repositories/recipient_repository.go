package repositories

import (
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"gorm.io/gorm"
)

type RecipientRepository interface {
	Create(recipient *models.NotificationRecipient) error
	CreateBatch(recipients []models.NotificationRecipient) error
	GetByID(tenantID, id uint) (*models.NotificationRecipient, error)
	GetByNotificationID(tenantID, notificationID uint) ([]models.NotificationRecipient, error)
	List(tenantID uint, filters models.RecipientFilters) ([]models.NotificationRecipient, int64, error)
	Update(recipient *models.NotificationRecipient) error
	UpdateStatus(tenantID, id uint, status models.RecipientStatus) error
	BulkUpdateStatus(tenantID uint, ids []uint, status models.RecipientStatus) error
	Delete(tenantID, id uint) error
	GetPendingByNotification(tenantID, notificationID uint) ([]models.NotificationRecipient, error)
	GetFailedRecipients(tenantID uint, limit int) ([]models.NotificationRecipient, error)
	TrackOpen(tenantID, id uint, userAgent, ipAddress *string) error
	TrackClick(tenantID, id uint, clickedURL, userAgent, ipAddress *string) error
}

type recipientRepository struct {
	db *gorm.DB
}

func NewRecipientRepository(db *gorm.DB) RecipientRepository {
	return &recipientRepository{db: db}
}

func (r *recipientRepository) Create(recipient *models.NotificationRecipient) error {
	return r.db.Create(recipient).Error
}

func (r *recipientRepository) CreateBatch(recipients []models.NotificationRecipient) error {
	if len(recipients) == 0 {
		return nil
	}
	return r.db.CreateInBatches(recipients, 100).Error
}

func (r *recipientRepository) GetByID(tenantID, id uint) (*models.NotificationRecipient, error) {
	var recipient models.NotificationRecipient
	err := r.db.Where("tenant_id = ? AND id = ?", tenantID, id).First(&recipient).Error
	if err != nil {
		return nil, err
	}
	return &recipient, nil
}

func (r *recipientRepository) GetByNotificationID(tenantID, notificationID uint) ([]models.NotificationRecipient, error) {
	var recipients []models.NotificationRecipient
	err := r.db.
		Where("tenant_id = ? AND notification_id = ?", tenantID, notificationID).
		Order("created_at ASC").
		Find(&recipients).Error
	return recipients, err
}

func (r *recipientRepository) List(tenantID uint, filters models.RecipientFilters) ([]models.NotificationRecipient, int64, error) {
	query := r.db.Where("tenant_id = ?", tenantID)

	// Apply filters
	if filters.NotificationID != 0 {
		query = query.Where("notification_id = ?", filters.NotificationID)
	}
	if filters.UserID != nil {
		query = query.Where("user_id = ?", *filters.UserID)
	}
	if filters.Type != "" {
		query = query.Where("recipient_type = ?", filters.Type)
	}
	if filters.Status != "" {
		query = query.Where("status = ?", filters.Status)
	}
	if filters.Address != "" {
		query = query.Where("recipient_address LIKE ?", "%"+filters.Address+"%")
	}
	if filters.DateFrom != nil {
		query = query.Where("created_at >= ?", filters.DateFrom)
	}
	if filters.DateTo != nil {
		query = query.Where("created_at <= ?", filters.DateTo)
	}

	// Count total
	var total int64
	if err := query.Model(&models.NotificationRecipient{}).Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination and sorting
	offset := (filters.Page - 1) * filters.Limit
	orderBy := fmt.Sprintf("%s %s", filters.SortBy, filters.SortOrder)

	var recipients []models.NotificationRecipient
	err := query.
		Preload("Notification").
		Offset(offset).
		Limit(filters.Limit).
		Order(orderBy).
		Find(&recipients).Error

	return recipients, total, err
}

func (r *recipientRepository) Update(recipient *models.NotificationRecipient) error {
	return r.db.Save(recipient).Error
}

func (r *recipientRepository) UpdateStatus(tenantID, id uint, status models.RecipientStatus) error {
	updates := map[string]interface{}{
		"status": status,
	}

	// Add timestamp based on status
	switch status {
	case models.RecipientStatusDelivered:
		updates["delivered_at"] = gorm.Expr("NOW()")
	case models.RecipientStatusRead:
		updates["read_at"] = gorm.Expr("NOW()")
	case models.RecipientStatusFailed:
		updates["failed_at"] = gorm.Expr("NOW()")
	case models.RecipientStatusBounced:
		updates["bounced_at"] = gorm.Expr("NOW()")
	}

	return r.db.Model(&models.NotificationRecipient{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Updates(updates).Error
}

func (r *recipientRepository) BulkUpdateStatus(tenantID uint, ids []uint, status models.RecipientStatus) error {
	if len(ids) == 0 {
		return nil
	}

	updates := map[string]interface{}{
		"status": status,
	}

	// Add timestamp based on status
	switch status {
	case models.RecipientStatusDelivered:
		updates["delivered_at"] = gorm.Expr("NOW()")
	case models.RecipientStatusRead:
		updates["read_at"] = gorm.Expr("NOW()")
	case models.RecipientStatusFailed:
		updates["failed_at"] = gorm.Expr("NOW()")
	case models.RecipientStatusBounced:
		updates["bounced_at"] = gorm.Expr("NOW()")
	}

	return r.db.Model(&models.NotificationRecipient{}).
		Where("tenant_id = ? AND id IN ?", tenantID, ids).
		Updates(updates).Error
}

func (r *recipientRepository) Delete(tenantID, id uint) error {
	return r.db.Where("tenant_id = ? AND id = ?", tenantID, id).
		Delete(&models.NotificationRecipient{}).Error
}

func (r *recipientRepository) GetPendingByNotification(tenantID, notificationID uint) ([]models.NotificationRecipient, error) {
	var recipients []models.NotificationRecipient
	err := r.db.
		Where("tenant_id = ? AND notification_id = ? AND status = ?",
			tenantID, notificationID, models.RecipientStatusPending).
		Order("created_at ASC").
		Find(&recipients).Error
	return recipients, err
}

func (r *recipientRepository) GetFailedRecipients(tenantID uint, limit int) ([]models.NotificationRecipient, error) {
	var recipients []models.NotificationRecipient
	err := r.db.
		Where("tenant_id = ? AND status = ?", tenantID, models.RecipientStatusFailed).
		Preload("Notification").
		Order("failed_at ASC").
		Limit(limit).
		Find(&recipients).Error
	return recipients, err
}

func (r *recipientRepository) TrackOpen(tenantID, id uint, userAgent, ipAddress *string) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// Get the recipient
		var recipient models.NotificationRecipient
		if err := tx.Where("tenant_id = ? AND id = ?", tenantID, id).First(&recipient).Error; err != nil {
			return err
		}

		// Track the open
		if err := recipient.TrackOpen(); err != nil {
			return err
		}

		// Update the recipient
		if err := tx.Save(&recipient).Error; err != nil {
			return err
		}

		// Create log entry
		log := models.CreateOpenedLog(tenantID, recipient.NotificationID, &id, userAgent, ipAddress)
		return tx.Create(log).Error
	})
}

func (r *recipientRepository) TrackClick(tenantID, id uint, clickedURL, userAgent, ipAddress *string) error {
	return r.db.Transaction(func(tx *gorm.DB) error {
		// Get the recipient
		var recipient models.NotificationRecipient
		if err := tx.Where("tenant_id = ? AND id = ?", tenantID, id).First(&recipient).Error; err != nil {
			return err
		}

		// Track the click
		if err := recipient.TrackClick(*clickedURL); err != nil {
			return err
		}

		// Update the recipient
		if err := tx.Save(&recipient).Error; err != nil {
			return err
		}

		// Create log entry
		log := models.CreateClickedLog(tenantID, recipient.NotificationID, &id, clickedURL, userAgent, ipAddress)
		return tx.Create(log).Error
	})
}
