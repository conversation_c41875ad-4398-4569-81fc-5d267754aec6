package handlers

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/dto"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

type NotificationHandler struct {
	notificationService services.NotificationService
}

func NewNotificationHandler(notificationService services.NotificationService) *NotificationHandler {
	return &NotificationHandler{
		notificationService: notificationService,
	}
}

// CreateNotification creates a new notification
// @Summary Create notification
// @Description Create a new notification with recipients
// @Tags notifications
// @Accept json
// @Produce json
// @Param notification body dto.NotificationCreateRequest true "Notification data"
// @Success 201 {object} map[string]interface{}// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /notifications [post]
func (h *NotificationHandler) CreateNotification(c *gin.Context) {
	var req dto.NotificationCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request data")
		return
	}

	// Get tenant ID from context (assume middleware sets this)
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant ID not found")
		return
	}

	notification, err := h.notificationService.CreateNotification(tenantID.(uint), req.ToServiceModel())
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to create notification")
		return
	}

	response.Created(c.Writer, notification.ToResponse())
}

// GetNotification gets a notification by ID
// @Summary Get notification
// @Description Get notification details by ID
// @Tags notifications
// @Produce json
// @Param id path int true "Notification ID"
// @Success 200 {object} map[string]interface{}// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /notifications/{id} [get]
func (h *NotificationHandler) GetNotification(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid notification ID")
		return
	}

	tenantID, _ := c.Get("tenant_id")

	notification, err := h.notificationService.GetNotificationWithRelations(tenantID.(uint), uint(id))
	if err != nil {
		response.NotFound(c.Writer, "Notification not found")
		return
	}

	response.Success(c.Writer, notification.ToResponse())
}

// ListNotifications lists notifications with cursor-based pagination
// @Summary List notifications
// @Description Get paginated list of notifications with filters using cursor pagination
// @Tags notifications
// @Produce json
// @Param type query string false "Notification type"
// @Param channel query string false "Channel" Enums(email,socket,push,sms)
// @Param status query string false "Status" Enums(pending,queued,sent,delivered,failed,cancelled)
// @Param priority query string false "Priority" Enums(low,normal,high,urgent)
// @Param date_from query string false "Date from (YYYY-MM-DD)"
// @Param date_to query string false "Date to (YYYY-MM-DD)"
// @Param cursor query string false "Pagination cursor"
// @Param limit query int false "Items per page" default(20)
// @Param sort_by query string false "Sort by field" default(created_at)
// @Param sort_order query string false "Sort order" Enums(asc,desc) default(desc)
// @Success 200 {object} dto.NotificationListResponse
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /notifications [get]
func (h *NotificationHandler) ListNotifications(c *gin.Context) {
	var filter dto.NotificationFilter
	if err := c.ShouldBindQuery(&filter); err != nil {
		response.BadRequest(c.Writer, "Invalid filter parameters")
		return
	}

	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.BadRequest(c.Writer, "Missing tenant ID")
		return
	}

	// Create cursor request
	cursorReq := &pagination.CursorRequest{
		Cursor: filter.Cursor,
		Limit:  pagination.ValidateLimit(filter.Limit),
	}

	// Build filters map
	filters := make(map[string]interface{})
	if filter.Type != "" {
		filters["type"] = filter.Type
	}
	if filter.Channel != "" {
		filters["channel"] = filter.Channel
	}
	if filter.Status != "" {
		filters["status"] = filter.Status
	}
	if filter.Priority != "" {
		filters["priority"] = filter.Priority
	}
	if filter.DateFrom != nil {
		filters["date_from"] = filter.DateFrom
	}
	if filter.DateTo != nil {
		filters["date_to"] = filter.DateTo
	}
	if filter.SortBy != "" {
		filters["sort_by"] = filter.SortBy
	}
	if filter.SortOrder != "" {
		filters["sort_order"] = filter.SortOrder
	}

	result, err := h.notificationService.ListNotificationsWithCursor(c.Request.Context(), tenantID.(uint), cursorReq, filters)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to retrieve notifications")
		return
	}

	response.OK(c.Writer, result)
}

// UpdateNotification updates a notification
// @Summary Update notification
// @Description Update notification details
// @Tags notifications
// @Accept json
// @Produce json
// @Param id path int true "Notification ID"
// @Param notification body models.UpdateNotificationRequest true "Update data"
// @Success 200 {object} map[string]interface{}// @Failure 400 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /notifications/{id} [put]
func (h *NotificationHandler) UpdateNotification(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid notification ID")
		return
	}

	var req models.UpdateNotificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request data")
		return
	}

	tenantID, _ := c.Get("tenant_id")

	notification, err := h.notificationService.UpdateNotification(tenantID.(uint), uint(id), req)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to update notification")
		return
	}

	response.Success(c.Writer, notification.ToResponse())
}

// DeleteNotification deletes a notification
// @Summary Delete notification
// @Description Delete a notification by ID
// @Tags notifications
// @Produce json
// @Param id path int true "Notification ID"
// @Success 200 {object} map[string]interface{}
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /notifications/{id} [delete]
func (h *NotificationHandler) DeleteNotification(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid notification ID")
		return
	}

	tenantID, _ := c.Get("tenant_id")

	err = h.notificationService.DeleteNotification(tenantID.(uint), uint(id))
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to delete notification")
		return
	}

	response.Success(c.Writer, nil)
}

// SendNotification manually sends a notification
// @Summary Send notification
// @Description Manually trigger sending of a pending notification
// @Tags notifications
// @Produce json
// @Param id path int true "Notification ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /notifications/{id}/send [post]
func (h *NotificationHandler) SendNotification(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid notification ID")
		return
	}

	tenantID, _ := c.Get("tenant_id")

	err = h.notificationService.SendNotification(tenantID.(uint), uint(id))
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to send notification")
		return
	}

	response.Success(c.Writer, nil)
}

// CancelNotification cancels a notification
// @Summary Cancel notification
// @Description Cancel a pending or queued notification
// @Tags notifications
// @Produce json
// @Param id path int true "Notification ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /notifications/{id}/cancel [post]
func (h *NotificationHandler) CancelNotification(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid notification ID")
		return
	}

	tenantID, _ := c.Get("tenant_id")

	err = h.notificationService.CancelNotification(tenantID.(uint), uint(id))
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to cancel notification")
		return
	}

	response.Success(c.Writer, nil)
}

// RetryNotification retries a failed notification
// @Summary Retry notification
// @Description Retry a failed notification
// @Tags notifications
// @Produce json
// @Param id path int true "Notification ID"
// @Success 200 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /notifications/{id}/retry [post]
func (h *NotificationHandler) RetryNotification(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid notification ID")
		return
	}

	tenantID, _ := c.Get("tenant_id")

	err = h.notificationService.RetryNotification(tenantID.(uint), uint(id))
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to retry notification")
		return
	}

	response.Success(c.Writer, nil)
}

// GetStatistics gets notification statistics
// @Summary Get notification statistics
// @Description Get notification statistics for analytics
// @Tags notifications
// @Produce json
// @Param date_from query string false "Date from (YYYY-MM-DD)"
// @Param date_to query string false "Date to (YYYY-MM-DD)"
// @Success 200 {object} map[string]interface{}// @Failure 500 {object} map[string]interface{}
// @Router /notifications/statistics [get]
func (h *NotificationHandler) GetStatistics(c *gin.Context) {
	tenantID, _ := c.Get("tenant_id")

	var dateFrom, dateTo *time.Time
	if dateFromStr := c.Query("date_from"); dateFromStr != "" {
		if parsed, err := time.Parse("2006-01-02", dateFromStr); err == nil {
			dateFrom = &parsed
		}
	}
	if dateToStr := c.Query("date_to"); dateToStr != "" {
		if parsed, err := time.Parse("2006-01-02", dateToStr); err == nil {
			dateTo = &parsed
		}
	}

	stats, err := h.notificationService.GetStatistics(tenantID.(uint), dateFrom, dateTo)
	if err != nil {
		response.InternalServerError(c.Writer, "Failed to get statistics")
		return
	}

	response.Success(c.Writer, stats)
}
