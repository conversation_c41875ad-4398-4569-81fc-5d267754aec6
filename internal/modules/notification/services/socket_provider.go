package services

import (
	"encoding/json"
	"fmt"
	"net/url"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"github.com/tranthanhloi/wn-api-v3/internal/config"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	socketio "github.com/zhouhui8915/go-socket.io-client"
)

// SocketProvider interface for different Socket.IO delivery providers
type SocketProvider interface {
	SendNotification(recipient *models.NotificationRecipient, notification *models.Notification, content string, metadata map[string]interface{}) error
	GetProviderName() string
	Close() error
}

// SocketIOProvider implements SocketProvider for Socket.IO real-time notifications
type SocketIOProvider struct {
	ServerURL string
	Namespace string
	logger    utils.Logger
	client    *socketio.Client
	mu        sync.RWMutex
	connected bool
}

// NewSocketIOProvider creates a new Socket.IO notification provider
func NewSocketIOProvider(serverURL, namespace string, logger utils.Logger) SocketProvider {
	provider := &SocketIOProvider{
		ServerURL: serverURL,
		Namespace: namespace,
		logger:    logger,
		connected: false,
	}

	// Initialize connection in background
	go func() {
		if err := provider.connect(); err != nil {
			logger.WithFields(map[string]interface{}{
				"error": err.Error(),
			}).Error("Failed to connect Socket.IO client")
		}
	}()

	return provider
}

// connect establishes Socket.IO connection
func (p *SocketIOProvider) connect() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	// If already connected, don't reconnect
	if p.connected && p.client != nil {
		return nil
	}

	// Socket.IO client options
	opts := &socketio.Options{
		Transport: "websocket",
		Query:     make(map[string]string),
	}

	// Add namespace to URL if provided
	url := p.ServerURL
	if p.Namespace != "" && p.Namespace != "/" {
		url = fmt.Sprintf("%s%s", p.ServerURL, p.Namespace)
	}

	p.logger.WithFields(map[string]interface{}{
		"server_url": p.ServerURL,
		"namespace":  p.Namespace,
		"final_url":  url,
	}).Info("Connecting to Socket.IO server")

	// Create Socket.IO client - this will automatically attempt to connect
	client, err := socketio.NewClient(url, opts)
	if err != nil {
		p.logger.WithFields(map[string]interface{}{
			"error": err.Error(),
			"url":   url,
		}).Error("Failed to create Socket.IO client")
		return err
	}

	// Set up event handlers
	if err := client.On("error", func(err interface{}) {
		p.logger.WithFields(map[string]interface{}{
			"error": fmt.Sprintf("%v", err),
		}).Error("Socket.IO connection error")
		p.mu.Lock()
		p.connected = false
		p.mu.Unlock()
	}); err != nil {
		p.logger.WithFields(map[string]interface{}{
			"error": err.Error(),
		}).Error("Failed to register error handler")
	}

	if err := client.On("connect", func() {
		p.logger.WithFields(map[string]interface{}{
			"server_url": p.ServerURL,
			"namespace":  p.Namespace,
		}).Info("Socket.IO connected successfully")
		p.mu.Lock()
		p.connected = true
		p.mu.Unlock()
	}); err != nil {
		p.logger.WithFields(map[string]interface{}{
			"error": err.Error(),
		}).Error("Failed to register connect handler")
	}

	if err := client.On("disconnect", func() {
		p.logger.Info("Socket.IO disconnected")
		p.mu.Lock()
		p.connected = false
		p.mu.Unlock()

		// Attempt to reconnect after 5 seconds
		go func() {
			time.Sleep(5 * time.Second)
			if err := p.connect(); err != nil {
				p.logger.WithFields(map[string]interface{}{
					"error": err.Error(),
				}).Error("Failed to reconnect Socket.IO client")
			}
		}()
	}); err != nil {
		p.logger.WithFields(map[string]interface{}{
			"error": err.Error(),
		}).Error("Failed to register disconnect handler")
	}

	// Store client reference
	p.client = client

	// Give it a moment to establish connection
	time.Sleep(1 * time.Second)

	p.logger.WithFields(map[string]interface{}{
		"server_url": p.ServerURL,
		"namespace":  p.Namespace,
		"url":        url,
	}).Info("Socket.IO client initialized")

	return nil
}

// isConnected checks if the Socket.IO client is connected
func (p *SocketIOProvider) isConnected() bool {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.connected && p.client != nil
}

func (p *SocketIOProvider) SendNotification(recipient *models.NotificationRecipient, notification *models.Notification, content string, metadata map[string]interface{}) error {
	// Create notification payload for Socket.IO
	payload := map[string]interface{}{
		"id":        notification.ID,
		"type":      notification.Type,
		"title":     notification.Subject,
		"content":   content,
		"priority":  notification.Priority,
		"timestamp": time.Now().Unix(),
		"metadata":  metadata,
		"read":      false,
		"tenant_id": notification.TenantID,
	}

	// Check if connected
	if !p.isConnected() {
		p.logger.WithFields(map[string]interface{}{
			"provider":        "socket.io",
			"server_url":      p.ServerURL,
			"namespace":       p.Namespace,
			"notification_id": notification.ID,
		}).Warn("Socket.IO not connected, attempting to reconnect")

		// Try to reconnect
		go func() {
			if err := p.connect(); err != nil {
				p.logger.WithFields(map[string]interface{}{
					"error": err.Error(),
				}).Error("Failed to reconnect Socket.IO client")
			}
		}()

		// For now, log the notification that would be sent
		payloadJSON, _ := json.MarshalIndent(payload, "", "  ")
		p.logger.WithFields(map[string]interface{}{
			"payload": string(payloadJSON),
		}).Info("Notification queued for delivery when connection is restored")

		return fmt.Errorf("socket.io not connected")
	}

	// Determine the event name and room based on recipient type
	var eventName, room string
	if recipient.UserID != nil {
		// User-specific notification
		eventName = "user_notification"
		room = fmt.Sprintf("user_%d", *recipient.UserID)
	} else {
		// Tenant-wide notification
		eventName = "tenant_notification"  
		room = fmt.Sprintf("tenant_%d", notification.TenantID)
	}

	// Add room information to payload
	payload["room"] = room
	payload["event"] = eventName

	// Join the appropriate room first
	if err := p.client.Emit("join_room", room); err != nil {
		p.logger.WithFields(map[string]interface{}{
			"error": err.Error(),
			"room":  room,
		}).Warn("Failed to join room, attempting direct emit")
	}

	// Emit the notification to the room
	err := p.client.Emit(eventName, payload)
	if err != nil {
		p.logger.WithFields(map[string]interface{}{
			"error":           err.Error(),
			"event":           eventName,
			"room":            room,
			"notification_id": notification.ID,
		}).Error("Failed to emit Socket.IO notification")
		return err
	}

	// Log successful delivery
	logFields := map[string]interface{}{
		"provider":        "socket.io",
		"server_url":      p.ServerURL,
		"namespace":       p.Namespace,
		"event":           eventName,
		"room":            room,
		"recipient_type":  recipient.RecipientType,
		"notification_id": notification.ID,
		"subject":         notification.Subject,
		"type":            notification.Type,
		"priority":        notification.Priority,
	}
	
	if recipient.UserID != nil {
		logFields["recipient_id"] = *recipient.UserID
	}
	
	p.logger.WithFields(logFields).Info("Socket.IO notification sent successfully")

	return nil
}

func (p *SocketIOProvider) GetProviderName() string {
	return "socket.io"
}

// Close disconnects the Socket.IO client
func (p *SocketIOProvider) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.client != nil {
		// The go-socket.io-client library manages its own connection lifecycle
		// Setting to nil will let GC clean up the connection
		p.client = nil
		p.connected = false
		p.logger.Info("Socket.IO client connection released")
	}

	return nil
}

// HealthCheck performs a health check on the Socket.IO connection
func (p *SocketIOProvider) HealthCheck() error {
	if !p.isConnected() {
		return fmt.Errorf("socket.io client not connected")
	}

	// Try to emit a ping event to test the connection
	err := p.client.Emit("ping", map[string]interface{}{
		"timestamp": time.Now().Unix(),
		"health_check": true,
	})
	
	if err != nil {
		p.logger.WithFields(map[string]interface{}{
			"error": err.Error(),
		}).Error("Socket.IO health check failed")
		return err
	}

	return nil
}

// Reconnect attempts to reconnect to the Socket.IO server
func (p *SocketIOProvider) Reconnect() error {
	p.logger.Info("Attempting to reconnect Socket.IO client")
	
	// Close existing connection if any
	if p.client != nil {
		p.Close()
	}
	
	// Attempt to reconnect
	return p.connect()
}

// WebSocketProvider implements SocketProvider for native WebSocket connections
type WebSocketProvider struct {
	ServerURL string
	logger    utils.Logger
	conn      *websocket.Conn
	mu        sync.RWMutex
	connected bool
	dialer    *websocket.Dialer
}

// NewWebSocketProvider creates a new WebSocket notification provider
func NewWebSocketProvider(serverURL string, logger utils.Logger) SocketProvider {
	provider := &WebSocketProvider{
		ServerURL: serverURL,
		logger:    logger,
		connected: false,
		dialer: &websocket.Dialer{
			Proxy:            nil,
			HandshakeTimeout: 45 * time.Second,
		},
	}

	// Initialize connection in background
	go func() {
		if err := provider.connect(); err != nil {
			logger.WithFields(map[string]interface{}{
				"error": err.Error(),
				"url":   serverURL,
			}).Error("Failed to connect WebSocket client")
		}
	}()

	return provider
}

// connect establishes WebSocket connection
func (p *WebSocketProvider) connect() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	// Parse and validate URL
	u, err := url.Parse(p.ServerURL)
	if err != nil {
		p.logger.WithFields(map[string]interface{}{
			"error": err.Error(),
			"url":   p.ServerURL,
		}).Error("Failed to parse WebSocket URL")
		return err
	}

	// Convert HTTP(S) to WS(S)
	if u.Scheme == "http" {
		u.Scheme = "ws"
	} else if u.Scheme == "https" {
		u.Scheme = "wss"
	}

	// Connect to WebSocket server
	conn, _, err := p.dialer.Dial(u.String(), nil)
	if err != nil {
		p.logger.WithFields(map[string]interface{}{
			"error": err.Error(),
			"url":   u.String(),
		}).Error("Failed to connect to WebSocket server")
		return err
	}

	// Store connection
	p.conn = conn
	p.connected = true

	p.logger.WithFields(map[string]interface{}{
		"url": u.String(),
	}).Info("WebSocket connected successfully")

	// Start ping/pong heartbeat
	go p.heartbeat()

	return nil
}

// heartbeat sends periodic ping messages to keep connection alive
func (p *WebSocketProvider) heartbeat() {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			p.mu.RLock()
			if !p.connected || p.conn == nil {
				p.mu.RUnlock()
				return
			}
			conn := p.conn
			p.mu.RUnlock()

			// Send ping
			if err := conn.WriteMessage(websocket.PingMessage, nil); err != nil {
				p.logger.WithFields(map[string]interface{}{
					"error": err.Error(),
				}).Error("Failed to send WebSocket ping")
				p.handleDisconnection()
				return
			}
		}
	}
}

// handleDisconnection handles WebSocket disconnection and attempts reconnection
func (p *WebSocketProvider) handleDisconnection() {
	p.mu.Lock()
	p.connected = false
	if p.conn != nil {
		p.conn.Close()
		p.conn = nil
	}
	p.mu.Unlock()

	p.logger.Info("WebSocket disconnected, attempting to reconnect in 5 seconds")

	// Attempt to reconnect after 5 seconds
	time.Sleep(5 * time.Second)
	go func() {
		if err := p.connect(); err != nil {
			p.logger.WithFields(map[string]interface{}{
				"error": err.Error(),
			}).Error("Failed to reconnect WebSocket client")
		}
	}()
}

// isConnected checks if the WebSocket client is connected
func (p *WebSocketProvider) isConnected() bool {
	p.mu.RLock()
	defer p.mu.RUnlock()
	return p.connected && p.conn != nil
}

func (p *WebSocketProvider) SendNotification(recipient *models.NotificationRecipient, notification *models.Notification, content string, metadata map[string]interface{}) error {
	// Create notification payload for WebSocket
	payload := map[string]interface{}{
		"event": "notification",
		"data": map[string]interface{}{
			"id":           notification.ID,
			"type":         notification.Type,
			"title":        notification.Subject,
			"content":      content,
			"priority":     notification.Priority,
			"timestamp":    time.Now().Unix(),
			"metadata":     metadata,
			"read":         false,
			"tenant_id":    notification.TenantID,
			"recipient_id": recipient.UserID,
		},
	}

	// Check if connected
	if !p.isConnected() {
		p.logger.WithFields(map[string]interface{}{
			"provider":        "websocket",
			"server_url":      p.ServerURL,
			"notification_id": notification.ID,
		}).Warn("WebSocket not connected, attempting to reconnect")

		// Try to reconnect
		go func() {
			if err := p.connect(); err != nil {
				p.logger.WithFields(map[string]interface{}{
					"error": err.Error(),
				}).Error("Failed to reconnect WebSocket client")
			}
		}()

		return fmt.Errorf("websocket not connected")
	}

	// Convert payload to JSON
	payloadJSON, err := json.Marshal(payload)
	if err != nil {
		p.logger.WithFields(map[string]interface{}{
			"error":           err.Error(),
			"notification_id": notification.ID,
		}).Error("Failed to marshal WebSocket payload")
		return err
	}

	// Send the notification via WebSocket
	p.mu.RLock()
	conn := p.conn
	p.mu.RUnlock()

	if conn == nil {
		return fmt.Errorf("websocket connection is nil")
	}

	// Send the message
	if err := conn.WriteMessage(websocket.TextMessage, payloadJSON); err != nil {
		p.logger.WithFields(map[string]interface{}{
			"error":           err.Error(),
			"notification_id": notification.ID,
		}).Error("Failed to send WebSocket notification")

		// Handle disconnection
		p.handleDisconnection()
		return err
	}

	// Log successful delivery
	p.logger.WithFields(map[string]interface{}{
		"provider":        "websocket",
		"server_url":      p.ServerURL,
		"recipient_type":  recipient.RecipientType,
		"recipient_id":    recipient.UserID,
		"notification_id": notification.ID,
		"subject":         notification.Subject,
		"type":            notification.Type,
		"priority":        notification.Priority,
	}).Info("WebSocket notification sent successfully")

	return nil
}

func (p *WebSocketProvider) GetProviderName() string {
	return "websocket"
}

// Close disconnects the WebSocket connection
func (p *WebSocketProvider) Close() error {
	p.mu.Lock()
	defer p.mu.Unlock()

	if p.conn != nil {
		// Send close message to server
		err := p.conn.WriteMessage(websocket.CloseMessage, 
			websocket.FormatCloseMessage(websocket.CloseNormalClosure, ""))
		if err != nil {
			p.logger.WithFields(map[string]interface{}{
				"error": err.Error(),
			}).Warn("Failed to send close message")
		}

		// Close the connection
		if closeErr := p.conn.Close(); closeErr != nil {
			p.logger.WithFields(map[string]interface{}{
				"error": closeErr.Error(),
			}).Error("Failed to close WebSocket connection")
			return closeErr
		}

		p.conn = nil
		p.connected = false
		p.logger.Info("WebSocket connection closed successfully")
	}

	return nil
}

// MockSocketProvider implements SocketProvider for testing
type MockSocketProvider struct {
	logger utils.Logger
}

// NewMockSocketProvider creates a new mock socket provider for testing
func NewMockSocketProvider(logger utils.Logger) SocketProvider {
	return &MockSocketProvider{
		logger: logger,
	}
}

func (p *MockSocketProvider) SendNotification(recipient *models.NotificationRecipient, notification *models.Notification, content string, metadata map[string]interface{}) error {
	// Log notification details for debugging
	p.logger.WithFields(map[string]interface{}{
		"provider":        "mock_socket",
		"recipient_type":  recipient.RecipientType,
		"recipient_id":    recipient.UserID,
		"notification_id": notification.ID,
		"subject":         notification.Subject,
		"type":            notification.Type,
		"priority":        notification.Priority,
	}).Info("Socket notification sent successfully via Mock provider")

	// Simulate real-time delivery
	time.Sleep(1 * time.Millisecond)

	return nil
}

func (p *MockSocketProvider) GetProviderName() string {
	return "mock_socket"
}

// Close closes the mock socket provider
func (p *MockSocketProvider) Close() error {
	return nil
}

// CreateSocketProvider creates a socket provider based on configuration
func CreateSocketProvider(cfg *config.NotificationConfig, logger utils.Logger) SocketProvider {
	logger.WithFields(map[string]interface{}{
		"requested_provider": cfg.SocketProvider,
		"socketio_url":       cfg.SocketIOConfig.ServerURL,
		"socketio_namespace": cfg.SocketIOConfig.Namespace,
	}).Info("Creating socket provider")

	switch cfg.SocketProvider {
	case "socketio":
		logger.Info("Creating Socket.IO provider")
		return NewSocketIOProvider(
			cfg.SocketIOConfig.ServerURL,
			cfg.SocketIOConfig.Namespace,
			logger,
		)
	case "websocket":
		logger.Info("Creating WebSocket provider")
		return NewWebSocketProvider(
			cfg.SocketIOConfig.ServerURL,
			logger,
		)
	case "mock":
		logger.Info("Creating mock socket provider for development/testing")
		return NewMockSocketProvider(logger)
	default:
		logger.WithFields(map[string]interface{}{
			"provider": cfg.SocketProvider,
		}).Warn("Unknown socket provider, falling back to mock")
		return NewMockSocketProvider(logger)
	}
}
