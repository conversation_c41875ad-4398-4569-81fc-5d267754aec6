package services

import (
	"bytes"
	"crypto/tls"
	"fmt"
	"net/smtp"
	"strings"
	"text/template"
	"time"

	"github.com/sendgrid/sendgrid-go"
	"github.com/sendgrid/sendgrid-go/helpers/mail"
	"github.com/tranthanhloi/wn-api-v3/internal/config"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// EmailProvider interface for different email delivery providers
type EmailProvider interface {
	SendEmail(recipient *models.NotificationRecipient, subject, content string, metadata map[string]interface{}) error
	GetProviderName() string
}

// SMTPProvider implements EmailProvider for SMTP delivery
type SMTPProvider struct {
	Host      string
	Port      int
	Username  string
	Password  string
	FromName  string
	FromEmail string
	logger    utils.Logger
}

// NewSMTPProvider creates a new SMTP email provider
func NewSMTPProvider(host string, port int, username, password, fromName, fromEmail string, logger utils.Logger) EmailProvider {
	return &SMTPProvider{
		Host:      host,
		Port:      port,
		Username:  username,
		Password:  password,
		FromName:  fromName,
		FromEmail: fromEmail,
		logger:    logger,
	}
}

func (p *SMTPProvider) SendEmail(recipient *models.NotificationRecipient, subject, content string, metadata map[string]interface{}) error {
	// Create message
	message, err := p.createEmailMessage(recipient.RecipientAddress, subject, content)
	if err != nil {
		return fmt.Errorf("failed to create email message: %w", err)
	}

	// For MailCatcher, use simple SMTP without TLS
	if p.Port == 1025 {
		// Use simple SMTP connection for MailCatcher
		addr := fmt.Sprintf("%s:%d", p.Host, p.Port)
		return smtp.SendMail(addr, nil, p.FromEmail, []string{recipient.RecipientAddress}, message)
	}

	// Setup SMTP authentication for real SMTP servers
	var auth smtp.Auth
	if p.Username != "" && p.Password != "" {
		auth = smtp.PlainAuth("", p.Username, p.Password, p.Host)
	}

	// Setup TLS config
	tlsConfig := &tls.Config{
		InsecureSkipVerify: false,
		ServerName:         p.Host,
	}

	// Create connection
	conn, err := tls.Dial("tcp", fmt.Sprintf("%s:%d", p.Host, p.Port), tlsConfig)
	if err != nil {
		return fmt.Errorf("failed to connect to SMTP server: %w", err)
	}
	defer conn.Close()

	// Create SMTP client
	client, err := smtp.NewClient(conn, p.Host)
	if err != nil {
		return fmt.Errorf("failed to create SMTP client: %w", err)
	}
	defer client.Quit()

	// Authenticate if credentials provided
	if auth != nil {
		if err = client.Auth(auth); err != nil {
			return fmt.Errorf("SMTP authentication failed: %w", err)
		}
	}

	// Set sender
	if err = client.Mail(p.FromEmail); err != nil {
		return fmt.Errorf("failed to set sender: %w", err)
	}

	// Set recipient
	if err = client.Rcpt(recipient.RecipientAddress); err != nil {
		return fmt.Errorf("failed to set recipient: %w", err)
	}

	// Send email body
	writer, err := client.Data()
	if err != nil {
		return fmt.Errorf("failed to create data writer: %w", err)
	}

	_, err = writer.Write(message)
	if err != nil {
		return fmt.Errorf("failed to write email data: %w", err)
	}

	err = writer.Close()
	if err != nil {
		return fmt.Errorf("failed to close email data writer: %w", err)
	}

	p.logger.WithFields(map[string]interface{}{
		"provider":  "smtp",
		"recipient": recipient.RecipientAddress,
		"subject":   subject,
	}).Info("Email sent successfully via SMTP")

	return nil
}

func (p *SMTPProvider) GetProviderName() string {
	return "smtp"
}

func (p *SMTPProvider) createEmailMessage(to, subject, content string) ([]byte, error) {
	// Create email headers and body
	emailTemplate := `From: {{.FromName}} <{{.FromEmail}}>
To: {{.To}}
Subject: {{.Subject}}
MIME-Version: 1.0
Content-Type: text/html; charset=UTF-8

{{.Content}}`

	tmpl, err := template.New("email").Parse(emailTemplate)
	if err != nil {
		return nil, err
	}

	data := struct {
		FromName  string
		FromEmail string
		To        string
		Subject   string
		Content   string
	}{
		FromName:  p.FromName,
		FromEmail: p.FromEmail,
		To:        to,
		Subject:   subject,
		Content:   content,
	}

	var buffer bytes.Buffer
	if err := tmpl.Execute(&buffer, data); err != nil {
		return nil, err
	}

	return buffer.Bytes(), nil
}

// SendGridProvider implements EmailProvider for SendGrid API
type SendGridProvider struct {
	APIKey    string
	FromName  string
	FromEmail string
	logger    utils.Logger
}

// NewSendGridProvider creates a new SendGrid email provider
func NewSendGridProvider(apiKey, fromName, fromEmail string, logger utils.Logger) EmailProvider {
	provider := &SendGridProvider{
		APIKey:    apiKey,
		FromName:  fromName,
		FromEmail: fromEmail,
		logger:    logger,
	}
	
	// Log provider initialization
	logger.WithFields(map[string]interface{}{
		"provider":       "sendgrid",
		"from_email":     fromEmail,
		"from_name":      fromName,
		"api_key_set":    apiKey != "",
		"api_key_length": len(apiKey),
	}).Info("Initialized SendGrid email provider")
	
	// Validate configuration
	if apiKey == "" {
		logger.Warn("SendGrid API key is empty - emails will fail to send")
	}
	if fromEmail == "" {
		logger.Warn("SendGrid from email is empty - emails may fail to send")
	}
	
	return provider
}

func (p *SendGridProvider) SendEmail(recipient *models.NotificationRecipient, subject, content string, metadata map[string]interface{}) error {
	// Input validation with logging
	if recipient == nil {
		p.logger.Error("SendGrid: recipient is nil")
		return fmt.Errorf("recipient cannot be nil")
	}
	
	if recipient.RecipientAddress == "" {
		p.logger.WithField("recipient_id", recipient.ID).Error("SendGrid: recipient address is empty")
		return fmt.Errorf("recipient address cannot be empty")
	}
	
	if subject == "" {
		p.logger.WithField("recipient", recipient.RecipientAddress).Warn("SendGrid: email subject is empty")
	}
	
	if content == "" {
		p.logger.WithField("recipient", recipient.RecipientAddress).Warn("SendGrid: email content is empty")
	}
	
	if p.APIKey == "" {
		p.logger.Error("SendGrid: API key is not configured")
		return fmt.Errorf("SendGrid API key is not configured")
	}
	
	// Log email sending attempt
	p.logger.WithFields(map[string]interface{}{
		"provider":      "sendgrid",
		"recipient":     recipient.RecipientAddress,
		"subject":       subject,
		"content_length": len(content),
		"tenant_id":     recipient.TenantID,
		"has_metadata":  metadata != nil,
	}).Info("Attempting to send email via SendGrid")
	// Create email components
	from := mail.NewEmail(p.FromName, p.FromEmail)
	to := mail.NewEmail("", recipient.RecipientAddress)
	
	// Create email message based on content type
	var message *mail.SGMailV3
	isHTML := isHTMLContent(content)
	if isHTML {
		// HTML content
		message = mail.NewSingleEmail(from, subject, to, "", content)
		p.logger.Debug("Detected HTML content type")
	} else {
		// Plain text content
		message = mail.NewSingleEmail(from, subject, to, content, "")
		p.logger.Debug("Detected plain text content type")
	}
	
	// Add custom tracking metadata if provided  
	if metadata != nil {
		// Custom arguments are added through personalizations
		if len(message.Personalizations) > 0 {
			customArgs := make(map[string]string)
			if trackingID, ok := metadata["tracking_id"].(string); ok && trackingID != "" {
				customArgs["tracking_id"] = trackingID
			}
			if recipientID, ok := metadata["recipient_id"]; ok {
				customArgs["recipient_id"] = fmt.Sprintf("%v", recipientID)
			}
			if notificationID, ok := metadata["notification_id"]; ok {
				customArgs["notification_id"] = fmt.Sprintf("%v", notificationID)
			}
			if tenantID, ok := metadata["tenant_id"]; ok {
				customArgs["tenant_id"] = fmt.Sprintf("%v", tenantID)
			}
			
			// Add custom arguments to the first personalization
			if len(customArgs) > 0 {
				message.Personalizations[0].CustomArgs = customArgs
				p.logger.WithFields(map[string]interface{}{
					"custom_args": customArgs,
				}).Debug("Added custom tracking arguments to email")
			}
		}
	}
	
	// Create SendGrid client and send
	client := sendgrid.NewSendClient(p.APIKey)
	
	p.logger.WithFields(map[string]interface{}{
		"api_key_prefix": p.APIKey[:min(8, len(p.APIKey))] + "...",
		"from_email":     p.FromEmail,
		"from_name":      p.FromName,
	}).Debug("Initialized SendGrid client")
	
	response, err := client.Send(message)
	if err != nil {
		// Categorize error types for better debugging
		errorType := "unknown"
		if strings.Contains(err.Error(), "unauthorized") || strings.Contains(err.Error(), "401") {
			errorType = "authentication"
		} else if strings.Contains(err.Error(), "forbidden") || strings.Contains(err.Error(), "403") {
			errorType = "authorization"
		} else if strings.Contains(err.Error(), "rate limit") || strings.Contains(err.Error(), "429") {
			errorType = "rate_limit"
		} else if strings.Contains(err.Error(), "timeout") {
			errorType = "timeout"
		} else if strings.Contains(err.Error(), "network") || strings.Contains(err.Error(), "connection") {
			errorType = "network"
		}
		
		p.logger.WithFields(map[string]interface{}{
			"provider":    "sendgrid",
			"recipient":   recipient.RecipientAddress,
			"subject":     subject,
			"error":       err.Error(),
			"error_type":  errorType,
			"tenant_id":   recipient.TenantID,
		}).Error("Failed to send email via SendGrid")
		
		return fmt.Errorf("sendgrid send failed (%s): %w", errorType, err)
	}
	
	// Log successful delivery with detailed information
	messageID := ""
	if response.Headers != nil {
		if msgID, exists := response.Headers["X-Message-Id"]; exists && len(msgID) > 0 {
			messageID = msgID[0]
		}
	}
	
	p.logger.WithFields(map[string]interface{}{
		"provider":       "sendgrid",
		"recipient":      recipient.RecipientAddress,
		"subject":        subject,
		"status_code":    response.StatusCode,
		"message_id":     messageID,
		"response_body":  response.Body,
		"tenant_id":      recipient.TenantID,
		"content_type":   map[bool]string{true: "html", false: "text"}[isHTML],
		"has_tracking":   metadata != nil && len(metadata) > 0,
	}).Info("Email sent successfully via SendGrid")
	
	// Check for non-2xx status codes
	if response.StatusCode < 200 || response.StatusCode >= 300 {
		p.logger.WithFields(map[string]interface{}{
			"provider":      "sendgrid",
			"recipient":     recipient.RecipientAddress,
			"status_code":   response.StatusCode,
			"response_body": response.Body,
			"subject":       subject,
		}).Error("SendGrid API returned non-success status code")
		return fmt.Errorf("sendgrid API returned status %d: %s", response.StatusCode, response.Body)
	}
	
	// Final success log with delivery confirmation
	p.logger.WithFields(map[string]interface{}{
		"provider":       "sendgrid",
		"recipient":      recipient.RecipientAddress,
		"tenant_id":      recipient.TenantID,
		"message_id":     messageID,
		"delivery_time":  "immediate", // SendGrid delivers immediately to their queue
		"final_status":   "delivered_to_sendgrid",
	}).Info("Email successfully delivered to SendGrid for processing")
	
	return nil
}

func (p *SendGridProvider) GetProviderName() string {
	return "sendgrid"
}

// isHTMLContent detects if content contains HTML tags
func isHTMLContent(content string) bool {
	// Simple check for common HTML tags
	htmlTags := []string{"<html>", "<body>", "<div>", "<p>", "<br>", "<strong>", "<em>", "<a href", "<img", "<table>", "<tr>", "<td>", "<th>", "<ul>", "<ol>", "<li>", "<h1>", "<h2>", "<h3>", "<h4>", "<h5>", "<h6>", "<span>"}
	contentLower := strings.ToLower(content)
	
	for _, tag := range htmlTags {
		if strings.Contains(contentLower, tag) {
			return true
		}
	}
	return false
}

// CreateEmailProvider creates an email provider based on configuration
func CreateEmailProvider(cfg *config.NotificationConfig, logger utils.Logger) EmailProvider {
	logger.WithFields(map[string]interface{}{
		"requested_provider":   cfg.EmailProvider,
		"sendgrid_configured":  cfg.SendGridConfig.APIKey != "",
		"smtp_configured":      cfg.SMTPConfig.Host != "",
		"default_from_email":   cfg.DefaultFromEmail,
		"tracking_enabled":     cfg.EnableTracking,
	}).Info("Creating email provider")
	
	switch cfg.EmailProvider {
	case "sendgrid":
		logger.Info("Creating SendGrid email provider")
		return NewSendGridProvider(
			cfg.SendGridConfig.APIKey,
			cfg.SendGridConfig.FromName,
			cfg.SendGridConfig.FromEmail,
			logger,
		)
	case "smtp":
		logger.WithFields(map[string]interface{}{
			"smtp_host": cfg.SMTPConfig.Host,
			"smtp_port": cfg.SMTPConfig.Port,
			"use_tls":   cfg.SMTPConfig.UseTLS,
		}).Info("Creating SMTP email provider")
		return NewSMTPProvider(
			cfg.SMTPConfig.Host,
			cfg.SMTPConfig.Port,
			cfg.SMTPConfig.Username,
			cfg.SMTPConfig.Password,
			cfg.DefaultFromName,
			cfg.DefaultFromEmail,
			logger,
		)
	case "mock":
		logger.Info("Creating mock email provider for testing")
		return NewMockEmailProvider(logger)
	default:
		logger.WithField("provider", cfg.EmailProvider).Warn("Unknown email provider, falling back to mock")
		return NewMockEmailProvider(logger)
	}
}

// MockEmailProvider implements EmailProvider for testing
type MockEmailProvider struct {
	logger utils.Logger
}

// NewMockEmailProvider creates a new mock email provider for testing
func NewMockEmailProvider(logger utils.Logger) EmailProvider {
	return &MockEmailProvider{
		logger: logger,
	}
}

func (p *MockEmailProvider) SendEmail(recipient *models.NotificationRecipient, subject, content string, metadata map[string]interface{}) error {
	// Log email details for debugging
	p.logger.WithFields(map[string]interface{}{
		"provider":  "mock",
		"recipient": recipient.RecipientAddress,
		"subject":   subject,
		"content":   content[:min(100, len(content))] + "...",
	}).Info("Email sent successfully via Mock provider")

	// Simulate delivery delay
	time.Sleep(50 * time.Millisecond)

	return nil
}

func (p *MockEmailProvider) GetProviderName() string {
	return "mock"
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
