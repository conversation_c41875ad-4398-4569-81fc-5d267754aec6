package services

import (
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/notification/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

type DeliveryService interface {
	ProcessNotification(notification *models.Notification) error
	DeliverEmail(notification *models.Notification, recipients []models.NotificationRecipient) error
	DeliverSocket(notification *models.Notification, recipients []models.NotificationRecipient) error
	DeliverPush(notification *models.Notification, recipients []models.NotificationRecipient) error
	DeliverSMS(notification *models.Notification, recipients []models.NotificationRecipient) error
}

type deliveryService struct {
	notificationRepo repositories.NotificationRepository
	recipientRepo    repositories.RecipientRepository
	templateRepo     repositories.TemplateRepository
	logRepo          repositories.LogRepository
	templateService  TemplateService
	emailProvider    EmailProvider
	socketProvider   SocketProvider
	smsProvider      SMSProvider
	logger           utils.Logger
}

func NewDeliveryService(
	notificationRepo repositories.NotificationRepository,
	recipientRepo repositories.RecipientRepository,
	templateRepo repositories.TemplateRepository,
	logRepo repositories.LogRepository,
	templateService TemplateService,
	emailProvider EmailProvider,
	socketProvider SocketProvider,
	smsProvider SMSProvider,
	logger utils.Logger,
) DeliveryService {
	return &deliveryService{
		notificationRepo: notificationRepo,
		recipientRepo:    recipientRepo,
		templateRepo:     templateRepo,
		logRepo:          logRepo,
		templateService:  templateService,
		emailProvider:    emailProvider,
		socketProvider:   socketProvider,
		smsProvider:      smsProvider,
		logger:           logger,
	}
}

func (s *deliveryService) ProcessNotification(notification *models.Notification) error {
	// Get recipients
	recipients, err := s.recipientRepo.GetPendingByNotification(notification.TenantID, notification.ID)
	if err != nil {
		return err
	}

	if len(recipients) == 0 {
		// No recipients, mark as sent
		notification.MarkAsSent()
		return s.notificationRepo.Update(notification)
	}

	// Deliver based on channel
	switch notification.Channel {
	case models.ChannelEmail:
		return s.DeliverEmail(notification, recipients)
	case models.ChannelSocket:
		return s.DeliverSocket(notification, recipients)
	case models.ChannelPush:
		return s.DeliverPush(notification, recipients)
	case models.ChannelSMS:
		return s.DeliverSMS(notification, recipients)
	default:
		return fmt.Errorf("unsupported channel: %s", notification.Channel)
	}
}

func (s *deliveryService) DeliverEmail(notification *models.Notification, recipients []models.NotificationRecipient) error {
	// Render content if using template
	var content string

	if notification.TemplateID != nil {
		templateData, err := notification.GetTemplateDataMap()
		if err != nil {
			return err
		}

		// Get active template version
		version, err := s.templateRepo.GetActiveVersion(notification.TenantID, *notification.TemplateID)
		if err != nil {
			return err
		}

		content, err = s.templateService.RenderTemplate(version, templateData)
		if err != nil {
			return err
		}
	} else {
		// Use subject as basic content
		content = notification.Subject
	}

	// Process each recipient
	successCount := 0
	for _, recipient := range recipients {
		if err := s.deliverEmailToRecipient(notification, &recipient, content); err != nil {
			// Log failure
			log := models.CreateFailedLog(notification.TenantID, notification.ID, &recipient.ID, "DELIVERY_FAILED", err.Error())
			s.logRepo.Create(log)

			// Mark recipient as failed
			recipient.MarkAsFailed(err.Error())
			s.recipientRepo.Update(&recipient)
			continue
		}

		successCount++
	}

	// Update notification status based on results
	if successCount == len(recipients) {
		notification.MarkAsSent()
	} else if successCount > 0 {
		// Partial success - keep as sent but log the issues
		notification.MarkAsSent()
	} else {
		// Complete failure
		notification.MarkAsFailed("All recipients failed")
	}

	return s.notificationRepo.Update(notification)
}

func (s *deliveryService) deliverEmailToRecipient(notification *models.Notification, recipient *models.NotificationRecipient, content string) error {
	// Prepare metadata for the email provider
	metadata := map[string]interface{}{
		"notification_id": notification.ID,
		"recipient_id":    recipient.ID,
		"tenant_id":       notification.TenantID,
		"type":            notification.Type,
	}

	// Send email through the configured provider
	if err := s.emailProvider.SendEmail(recipient, notification.Subject, content, metadata); err != nil {
		return fmt.Errorf("email delivery failed: %w", err)
	}

	// Mark recipient as sent
	recipient.MarkAsSent()
	if err := s.recipientRepo.Update(recipient); err != nil {
		return err
	}

	// Create sent log
	providerResponse := map[string]interface{}{
		"message_id": fmt.Sprintf("email_%d_%d", notification.ID, recipient.ID),
		"provider":   s.emailProvider.GetProviderName(),
		"status":     "sent",
		"sent_at":    time.Now(),
		"recipient":  recipient.RecipientAddress,
		"subject":    notification.Subject,
	}

	log := models.CreateSentLog(notification.TenantID, notification.ID, &recipient.ID, providerResponse)
	return s.logRepo.Create(log)
}

func (s *deliveryService) DeliverSocket(notification *models.Notification, recipients []models.NotificationRecipient) error {
	// Render content if using template
	var content string

	if notification.TemplateID != nil {
		templateData, err := notification.GetTemplateDataMap()
		if err != nil {
			return err
		}

		// Get active template version
		version, err := s.templateRepo.GetActiveVersion(notification.TenantID, *notification.TemplateID)
		if err != nil {
			return err
		}

		content, err = s.templateService.RenderTemplate(version, templateData)
		if err != nil {
			return err
		}
	} else {
		// Use subject as basic content
		content = notification.Subject
	}

	// Process each recipient
	successCount := 0
	for _, recipient := range recipients {
		if err := s.deliverSocketToRecipient(notification, &recipient, content); err != nil {
			// Log failure
			log := models.CreateFailedLog(notification.TenantID, notification.ID, &recipient.ID, "SOCKET_FAILED", err.Error())
			s.logRepo.Create(log)

			recipient.MarkAsFailed(err.Error())
			s.recipientRepo.Update(&recipient)
			continue
		}

		successCount++
	}

	// Update notification status
	if successCount == len(recipients) {
		notification.MarkAsSent()
	} else if successCount > 0 {
		notification.MarkAsSent()
	} else {
		notification.MarkAsFailed("All socket deliveries failed")
	}

	return s.notificationRepo.Update(notification)
}

func (s *deliveryService) deliverSocketToRecipient(notification *models.Notification, recipient *models.NotificationRecipient, content string) error {
	// Prepare metadata for the socket provider
	metadata := map[string]interface{}{
		"notification_id": notification.ID,
		"recipient_id":    recipient.ID,
		"tenant_id":       notification.TenantID,
		"type":            notification.Type,
		"room":            fmt.Sprintf("tenant_%d", notification.TenantID),
	}

	// Send socket notification through the configured provider
	if err := s.socketProvider.SendNotification(recipient, notification, content, metadata); err != nil {
		return fmt.Errorf("socket delivery failed: %w", err)
	}

	// Mark as delivered immediately for socket (real-time)
	recipient.MarkAsDelivered()
	if err := s.recipientRepo.Update(recipient); err != nil {
		return err
	}

	// Create delivered log
	deliveryInfo := map[string]interface{}{
		"socket_id":    fmt.Sprintf("socket_%d_%d", notification.ID, recipient.ID),
		"provider":     s.socketProvider.GetProviderName(),
		"room":         fmt.Sprintf("tenant_%d", notification.TenantID),
		"delivered_at": time.Now(),
		"recipient":    recipient.RecipientAddress,
	}

	log := models.CreateDeliveredLog(notification.TenantID, notification.ID, &recipient.ID, deliveryInfo)
	return s.logRepo.Create(log)
}

func (s *deliveryService) DeliverPush(notification *models.Notification, recipients []models.NotificationRecipient) error {
	// TODO: Integrate with FCM/APNS
	// For now, simulate push delivery

	successCount := 0
	for _, recipient := range recipients {
		if err := s.deliverPushToRecipient(notification, &recipient); err != nil {
			log := models.CreateFailedLog(notification.TenantID, notification.ID, &recipient.ID, "PUSH_FAILED", err.Error())
			s.logRepo.Create(log)

			recipient.MarkAsFailed(err.Error())
			s.recipientRepo.Update(&recipient)
			continue
		}

		successCount++
	}

	// Update notification status
	if successCount == len(recipients) {
		notification.MarkAsSent()
	} else if successCount > 0 {
		notification.MarkAsSent()
	} else {
		notification.MarkAsFailed("All push deliveries failed")
	}

	return s.notificationRepo.Update(notification)
}

func (s *deliveryService) deliverPushToRecipient(notification *models.Notification, recipient *models.NotificationRecipient) error {
	// Simulate push delivery
	time.Sleep(200 * time.Millisecond)

	recipient.MarkAsSent()
	if err := s.recipientRepo.Update(recipient); err != nil {
		return err
	}

	// Create sent log
	providerResponse := map[string]interface{}{
		"message_id":   fmt.Sprintf("push_%d_%d", notification.ID, recipient.ID),
		"provider":     "mock_fcm",
		"status":       "sent",
		"sent_at":      time.Now(),
		"device_token": recipient.DeviceToken,
		"title":        notification.Subject,
	}

	log := models.CreateSentLog(notification.TenantID, notification.ID, &recipient.ID, providerResponse)
	return s.logRepo.Create(log)
}

func (s *deliveryService) DeliverSMS(notification *models.Notification, recipients []models.NotificationRecipient) error {
	successCount := 0
	for _, recipient := range recipients {
		if err := s.deliverSMSToRecipient(notification, &recipient); err != nil {
			log := models.CreateFailedLog(notification.TenantID, notification.ID, &recipient.ID, "SMS_FAILED", err.Error())
			s.logRepo.Create(log)

			recipient.MarkAsFailed(err.Error())
			s.recipientRepo.Update(&recipient)
			continue
		}

		successCount++
	}

	// Update notification status
	if successCount == len(recipients) {
		notification.MarkAsSent()
	} else if successCount > 0 {
		notification.MarkAsSent()
	} else {
		notification.MarkAsFailed("All SMS deliveries failed")
	}

	return s.notificationRepo.Update(notification)
}

func (s *deliveryService) deliverSMSToRecipient(notification *models.Notification, recipient *models.NotificationRecipient) error {
	// Render content if using template
	var smsMessage string
	var metadata map[string]interface{}

	if notification.TemplateID != nil {
		templateData, err := notification.GetTemplateDataMap()
		if err != nil {
			return fmt.Errorf("failed to get template data: %w", err)
		}

		// Get active template version
		version, err := s.templateRepo.GetActiveVersion(notification.TenantID, *notification.TemplateID)
		if err != nil {
			return fmt.Errorf("failed to get active template version: %w", err)
		}

		content, err := s.templateService.RenderTemplate(version, templateData)
		if err != nil {
			return fmt.Errorf("failed to render template: %w", err)
		}

		smsMessage = content
		metadata = templateData
	} else {
		// Use subject as SMS content
		smsMessage = notification.Subject
		metadata = make(map[string]interface{})
	}

	// Ensure SMS message is within character limit
	if len(smsMessage) > 160 {
		// Truncate to 157 chars and add "..."
		smsMessage = smsMessage[:157] + "..."
	}

	// Send SMS through the configured provider
	if err := s.smsProvider.SendSMS(recipient, notification, smsMessage); err != nil {
		return fmt.Errorf("SMS delivery failed: %w", err)
	}

	// Update recipient status
	recipient.MarkAsSent()
	if err := s.recipientRepo.Update(recipient); err != nil {
		s.logger.WithError(err).Error("Failed to update recipient status after SMS delivery")
		return err
	}

	// Create delivery log
	deliveryInfo := map[string]interface{}{
		"message_id":    fmt.Sprintf("sms_%d_%d", notification.ID, recipient.ID),
		"provider":      s.smsProvider.GetProviderName(),
		"status":        "sent",
		"sent_at":       time.Now(),
		"phone":         recipient.RecipientAddress,
		"message":       smsMessage,
		"message_chars": len(smsMessage),
		"metadata":      metadata,
	}

	log := models.CreateSentLog(notification.TenantID, notification.ID, &recipient.ID, deliveryInfo)
	if err := s.logRepo.Create(log); err != nil {
		s.logger.WithError(err).Error("Failed to create SMS delivery log")
	}

	return nil
}
