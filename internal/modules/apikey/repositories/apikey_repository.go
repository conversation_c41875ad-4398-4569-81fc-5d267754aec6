package repositories

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"gorm.io/gorm"
)

// APIKeyRepository defines the interface for API key data operations
type APIKeyRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, apiKey *models.APIKey) error
	GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.APIKey, error)
	GetByKeyHash(ctx context.Context, keyHash string) (*models.APIKey, error)
	Update(ctx context.Context, apiKey *models.APIKey) error
	Delete(ctx context.Context, tenantID, websiteID, id uint) error

	// List and filter operations
	List(ctx context.Context, tenantID, websiteID uint, filter *models.APIKeyFilter) ([]models.APIKey, int64, error)
	ListWithCursor(ctx context.Context, tenantID, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.APIKey, *pagination.CursorResponse, error)
	ListByStatus(ctx context.Context, tenantID, websiteID uint, status models.APIKeyStatus) ([]models.APIKey, error)
	ListExpired(ctx context.Context, tenantID, websiteID uint) ([]models.APIKey, error)

	// Permission operations
	CreatePermission(ctx context.Context, permission *models.APIKeyPermission) error
	GetPermissions(ctx context.Context, tenantID, apiKeyID uint) ([]models.APIKeyPermission, error)
	DeletePermission(ctx context.Context, tenantID, id uint) error
	DeletePermissionsByAPIKey(ctx context.Context, tenantID, apiKeyID uint) error

	// Usage tracking
	CreateUsage(ctx context.Context, usage *models.APIKeyUsage) error
	GetUsage(ctx context.Context, tenantID, apiKeyID uint, filter *models.APIKeyUsageRequest) ([]models.APIKeyUsage, error)
	GetUsageStats(ctx context.Context, tenantID, apiKeyID uint, period string) (*models.APIKeyUsageStats, error)

	// Rotation operations
	CreateRotation(ctx context.Context, rotation *models.APIKeyRotation) error
	GetRotations(ctx context.Context, tenantID, apiKeyID uint) ([]models.APIKeyRotation, error)
	UpdateRotation(ctx context.Context, rotation *models.APIKeyRotation) error

	// Scope operations
	CreateScope(ctx context.Context, scope *models.APIKeyScope) error
	GetScopes(ctx context.Context, tenantID, apiKeyID uint) ([]models.APIKeyScope, error)
	UpdateScope(ctx context.Context, scope *models.APIKeyScope) error
	DeleteScope(ctx context.Context, tenantID, id uint) error

	// Utility operations
	UpdateLastUsed(ctx context.Context, tenantID, apiKeyID uint) error
	CountByWebsite(ctx context.Context, tenantID, websiteID uint) (int64, error)
	CleanupExpired(ctx context.Context, tenantID, websiteID uint) error
}

// apiKeyRepository implements the APIKeyRepository interface
type apiKeyRepository struct {
	db *gorm.DB
}

// NewAPIKeyRepository creates a new API key repository
func NewAPIKeyRepository(db *gorm.DB) APIKeyRepository {
	return &apiKeyRepository{db: db}
}

// Create creates a new API key
func (r *apiKeyRepository) Create(ctx context.Context, apiKey *models.APIKey) error {
	return r.db.WithContext(ctx).Create(apiKey).Error
}

// GetByID retrieves an API key by ID
func (r *apiKeyRepository) GetByID(ctx context.Context, tenantID, websiteID, id uint) (*models.APIKey, error) {
	var apiKey models.APIKey
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND id = ? AND status != ?",
			tenantID, websiteID, id, models.APIKeyStatusDeleted).
		First(&apiKey).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("api key not found")
		}
		return nil, err
	}

	return &apiKey, nil
}

// GetByKeyHash retrieves an API key by its hash
func (r *apiKeyRepository) GetByKeyHash(ctx context.Context, keyHash string) (*models.APIKey, error) {
	var apiKey models.APIKey
	err := r.db.WithContext(ctx).
		Where("key_hash = ? AND status = ?", keyHash, models.APIKeyStatusActive).
		First(&apiKey).Error

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("api key not found")
		}
		return nil, err
	}

	return &apiKey, nil
}

// Update updates an existing API key
func (r *apiKeyRepository) Update(ctx context.Context, apiKey *models.APIKey) error {
	return r.db.WithContext(ctx).Save(apiKey).Error
}

// Delete soft deletes an API key
func (r *apiKeyRepository) Delete(ctx context.Context, tenantID, websiteID, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.APIKey{}).
		Where("tenant_id = ? AND website_id = ? AND id = ?", tenantID, websiteID, id).
		Update("status", models.APIKeyStatusDeleted).Error
}

// List retrieves API keys with filtering and pagination
func (r *apiKeyRepository) List(ctx context.Context, tenantID, websiteID uint, filter *models.APIKeyFilter) ([]models.APIKey, int64, error) {
	var apiKeys []models.APIKey
	var total int64

	query := r.db.WithContext(ctx).Model(&models.APIKey{}).
		Where("tenant_id = ? AND website_id = ? AND status != ?",
			tenantID, websiteID, models.APIKeyStatusDeleted)

	// Apply filters
	if filter.Status != nil {
		query = query.Where("status = ?", *filter.Status)
	}

	if filter.Search != "" {
		query = query.Where("name LIKE ? OR description LIKE ?",
			"%"+filter.Search+"%", "%"+filter.Search+"%")
	}

	if filter.CreatedBy != nil {
		query = query.Where("created_by = ?", *filter.CreatedBy)
	}

	if filter.ExpiresIn != nil {
		expiresAt := time.Now().Add(time.Duration(*filter.ExpiresIn) * time.Second)
		query = query.Where("expires_at IS NOT NULL AND expires_at <= ?", expiresAt)
	}

	// Count total records
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply sorting
	sortBy := "created_at"
	if filter.SortBy != "" {
		sortBy = filter.SortBy
	}

	sortOrder := "desc"
	if filter.SortOrder != "" {
		sortOrder = filter.SortOrder
	}

	query = query.Order(fmt.Sprintf("%s %s", sortBy, sortOrder))

	// Apply pagination
	if filter.Page > 0 && filter.Limit > 0 {
		offset := (filter.Page - 1) * filter.Limit
		query = query.Offset(offset).Limit(filter.Limit)
	}

	err := query.Find(&apiKeys).Error
	return apiKeys, total, err
}

// ListWithCursor retrieves API keys using cursor-based pagination
func (r *apiKeyRepository) ListWithCursor(ctx context.Context, tenantID, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]models.APIKey, *pagination.CursorResponse, error) {
	var apiKeys []models.APIKey

	// Build base query
	query := r.db.WithContext(ctx).Model(&models.APIKey{}).
		Where("tenant_id = ? AND website_id = ? AND status != ?",
			tenantID, websiteID, models.APIKeyStatusDeleted)

	// Apply filters
	if status, ok := filters["status"]; ok {
		query = query.Where("status = ?", status)
	}

	if search, ok := filters["search"]; ok && search != "" {
		query = query.Where("name LIKE ? OR description LIKE ?",
			"%"+search.(string)+"%", "%"+search.(string)+"%")
	}

	if createdBy, ok := filters["created_by"]; ok {
		query = query.Where("created_by = ?", createdBy)
	}

	if expiresIn, ok := filters["expires_in"]; ok {
		expiresAt := time.Now().Add(time.Duration(expiresIn.(int)) * time.Second)
		query = query.Where("expires_at IS NOT NULL AND expires_at <= ?", expiresAt)
	}

	// Apply cursor
	if req.Cursor != "" {
		cursor, err := pagination.ParseCursor(req.Cursor)
		if err == nil {
			query = query.Where("(created_at < ? OR (created_at = ? AND id < ?))",
				cursor.CreatedAt, cursor.CreatedAt, cursor.ID)
		}
	}

	// Apply sorting and limit
	sortBy := "created_at"
	sortOrder := "DESC"
	if sortByFilter, ok := filters["sort_by"]; ok && sortByFilter != "" {
		sortBy = sortByFilter.(string)
	}
	if sortOrderFilter, ok := filters["sort_order"]; ok && sortOrderFilter != "" {
		sortOrder = sortOrderFilter.(string)
	}

	query = query.Order(fmt.Sprintf("%s %s, id %s", sortBy, sortOrder, sortOrder))

	// Get one extra record to check if there are more
	limit := pagination.ValidateLimit(req.Limit)
	query = query.Limit(limit + 1)

	err := query.Find(&apiKeys).Error
	if err != nil {
		return nil, nil, err
	}

	// Build cursor response
	hasNext := len(apiKeys) > limit
	if hasNext {
		apiKeys = apiKeys[:limit]
	}

	response := &pagination.CursorResponse{
		HasNext: hasNext,
		Count:   len(apiKeys),
		Limit:   limit,
	}

	// Generate next cursor if there are more records
	if hasNext && len(apiKeys) > 0 {
		last := apiKeys[len(apiKeys)-1]
		response.NextCursor, _ = pagination.EncodeCursor(last.ID, last.CreatedAt)
	}

	return apiKeys, response, nil
}

// ListByStatus retrieves API keys by status
func (r *apiKeyRepository) ListByStatus(ctx context.Context, tenantID, websiteID uint, status models.APIKeyStatus) ([]models.APIKey, error) {
	var apiKeys []models.APIKey
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND status = ?", tenantID, websiteID, status).
		Find(&apiKeys).Error
	return apiKeys, err
}

// ListExpired retrieves expired API keys
func (r *apiKeyRepository) ListExpired(ctx context.Context, tenantID, websiteID uint) ([]models.APIKey, error) {
	var apiKeys []models.APIKey
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND website_id = ? AND expires_at IS NOT NULL AND expires_at < ? AND status = ?",
			tenantID, websiteID, time.Now(), models.APIKeyStatusActive).
		Find(&apiKeys).Error
	return apiKeys, err
}

// CreatePermission creates a new API key permission
func (r *apiKeyRepository) CreatePermission(ctx context.Context, permission *models.APIKeyPermission) error {
	return r.db.WithContext(ctx).Create(permission).Error
}

// GetPermissions retrieves permissions for an API key
func (r *apiKeyRepository) GetPermissions(ctx context.Context, tenantID, apiKeyID uint) ([]models.APIKeyPermission, error) {
	var permissions []models.APIKeyPermission
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND api_key_id = ?", tenantID, apiKeyID).
		Find(&permissions).Error
	return permissions, err
}

// DeletePermission deletes a specific permission
func (r *apiKeyRepository) DeletePermission(ctx context.Context, tenantID, id uint) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Delete(&models.APIKeyPermission{}).Error
}

// DeletePermissionsByAPIKey deletes all permissions for an API key
func (r *apiKeyRepository) DeletePermissionsByAPIKey(ctx context.Context, tenantID, apiKeyID uint) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND api_key_id = ?", tenantID, apiKeyID).
		Delete(&models.APIKeyPermission{}).Error
}

// CreateUsage creates a new usage record
func (r *apiKeyRepository) CreateUsage(ctx context.Context, usage *models.APIKeyUsage) error {
	return r.db.WithContext(ctx).Create(usage).Error
}

// GetUsage retrieves usage records for an API key
func (r *apiKeyRepository) GetUsage(ctx context.Context, tenantID, apiKeyID uint, filter *models.APIKeyUsageRequest) ([]models.APIKeyUsage, error) {
	var usage []models.APIKeyUsage

	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND api_key_id = ?", tenantID, apiKeyID).
		Order("created_at DESC")

	// Apply time filters
	if filter.StartDate != "" {
		startDate, err := time.Parse("2006-01-02", filter.StartDate)
		if err == nil {
			query = query.Where("created_at >= ?", startDate)
		}
	}

	if filter.EndDate != "" {
		endDate, err := time.Parse("2006-01-02", filter.EndDate)
		if err == nil {
			query = query.Where("created_at <= ?", endDate.Add(24*time.Hour))
		}
	}

	// Apply period filter
	if filter.Period != "" {
		var since time.Time
		switch filter.Period {
		case "1h":
			since = time.Now().Add(-time.Hour)
		case "24h":
			since = time.Now().Add(-24 * time.Hour)
		case "7d":
			since = time.Now().Add(-7 * 24 * time.Hour)
		case "30d":
			since = time.Now().Add(-30 * 24 * time.Hour)
		case "90d":
			since = time.Now().Add(-90 * 24 * time.Hour)
		}
		if !since.IsZero() {
			query = query.Where("created_at >= ?", since)
		}
	}

	err := query.Limit(1000).Find(&usage).Error
	return usage, err
}

// GetUsageStats retrieves usage statistics for an API key
func (r *apiKeyRepository) GetUsageStats(ctx context.Context, tenantID, apiKeyID uint, period string) (*models.APIKeyUsageStats, error) {
	var stats models.APIKeyUsageStats

	// Calculate time range
	var since time.Time
	switch period {
	case "1h":
		since = time.Now().Add(-time.Hour)
	case "24h":
		since = time.Now().Add(-24 * time.Hour)
	case "7d":
		since = time.Now().Add(-7 * 24 * time.Hour)
	case "30d":
		since = time.Now().Add(-30 * 24 * time.Hour)
	case "90d":
		since = time.Now().Add(-90 * 24 * time.Hour)
	default:
		since = time.Now().Add(-24 * time.Hour)
	}

	// Get basic stats
	var result struct {
		TotalRequests       int64   `gorm:"column:total_requests"`
		SuccessfulRequests  int64   `gorm:"column:successful_requests"`
		FailedRequests      int64   `gorm:"column:failed_requests"`
		AverageResponseTime float64 `gorm:"column:avg_response_time"`
	}

	err := r.db.WithContext(ctx).
		Model(&models.APIKeyUsage{}).
		Select(`
			COUNT(*) as total_requests,
			COUNT(CASE WHEN response_code >= 200 AND response_code < 300 THEN 1 END) as successful_requests,
			COUNT(CASE WHEN response_code >= 400 THEN 1 END) as failed_requests,
			AVG(response_time) as avg_response_time
		`).
		Where("tenant_id = ? AND api_key_id = ? AND created_at >= ?", tenantID, apiKeyID, since).
		Scan(&result).Error

	if err != nil {
		return nil, err
	}

	stats.TotalRequests = result.TotalRequests
	stats.SuccessfulRequests = result.SuccessfulRequests
	stats.FailedRequests = result.FailedRequests
	stats.AverageResponseTime = int64(result.AverageResponseTime)

	if stats.TotalRequests > 0 {
		stats.ErrorRate = float64(stats.FailedRequests) / float64(stats.TotalRequests) * 100
	}

	// Get last used time
	var lastUsed time.Time
	err = r.db.WithContext(ctx).
		Model(&models.APIKeyUsage{}).
		Select("MAX(created_at)").
		Where("tenant_id = ? AND api_key_id = ?", tenantID, apiKeyID).
		Scan(&lastUsed).Error

	if err == nil && !lastUsed.IsZero() {
		stats.LastUsedAt = &lastUsed
	}

	// Get most used endpoint
	var mostUsedEndpoint string
	err = r.db.WithContext(ctx).
		Model(&models.APIKeyUsage{}).
		Select("endpoint").
		Where("tenant_id = ? AND api_key_id = ? AND created_at >= ?", tenantID, apiKeyID, since).
		Group("endpoint").
		Order("COUNT(*) DESC").
		Limit(1).
		Scan(&mostUsedEndpoint).Error

	if err == nil {
		stats.MostUsedEndpoint = mostUsedEndpoint
	}

	// Get requests for different periods
	now := time.Now()
	todayStart := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location())
	weekStart := todayStart.AddDate(0, 0, -int(todayStart.Weekday()))
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	// Requests today
	r.db.WithContext(ctx).
		Model(&models.APIKeyUsage{}).
		Where("tenant_id = ? AND api_key_id = ? AND created_at >= ?", tenantID, apiKeyID, todayStart).
		Count(&stats.RequestsToday)

	// Requests this week
	r.db.WithContext(ctx).
		Model(&models.APIKeyUsage{}).
		Where("tenant_id = ? AND api_key_id = ? AND created_at >= ?", tenantID, apiKeyID, weekStart).
		Count(&stats.RequestsThisWeek)

	// Requests this month
	r.db.WithContext(ctx).
		Model(&models.APIKeyUsage{}).
		Where("tenant_id = ? AND api_key_id = ? AND created_at >= ?", tenantID, apiKeyID, monthStart).
		Count(&stats.RequestsThisMonth)

	return &stats, nil
}

// CreateRotation creates a new rotation record
func (r *apiKeyRepository) CreateRotation(ctx context.Context, rotation *models.APIKeyRotation) error {
	return r.db.WithContext(ctx).Create(rotation).Error
}

// GetRotations retrieves rotation history for an API key
func (r *apiKeyRepository) GetRotations(ctx context.Context, tenantID, apiKeyID uint) ([]models.APIKeyRotation, error) {
	var rotations []models.APIKeyRotation
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND api_key_id = ?", tenantID, apiKeyID).
		Order("rotated_at DESC").
		Find(&rotations).Error
	return rotations, err
}

// UpdateRotation updates a rotation record
func (r *apiKeyRepository) UpdateRotation(ctx context.Context, rotation *models.APIKeyRotation) error {
	return r.db.WithContext(ctx).Save(rotation).Error
}

// CreateScope creates a new scope
func (r *apiKeyRepository) CreateScope(ctx context.Context, scope *models.APIKeyScope) error {
	return r.db.WithContext(ctx).Create(scope).Error
}

// GetScopes retrieves scopes for an API key
func (r *apiKeyRepository) GetScopes(ctx context.Context, tenantID, apiKeyID uint) ([]models.APIKeyScope, error) {
	var scopes []models.APIKeyScope
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND api_key_id = ? AND is_active = ?", tenantID, apiKeyID, true).
		Find(&scopes).Error
	return scopes, err
}

// UpdateScope updates a scope
func (r *apiKeyRepository) UpdateScope(ctx context.Context, scope *models.APIKeyScope) error {
	return r.db.WithContext(ctx).Save(scope).Error
}

// DeleteScope deletes a scope
func (r *apiKeyRepository) DeleteScope(ctx context.Context, tenantID, id uint) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Delete(&models.APIKeyScope{}).Error
}

// UpdateLastUsed updates the last used timestamp for an API key
func (r *apiKeyRepository) UpdateLastUsed(ctx context.Context, tenantID, apiKeyID uint) error {
	return r.db.WithContext(ctx).
		Model(&models.APIKey{}).
		Where("tenant_id = ? AND id = ?", tenantID, apiKeyID).
		Update("last_used_at", time.Now()).Error
}

// CountByWebsite counts API keys for a website
func (r *apiKeyRepository) CountByWebsite(ctx context.Context, tenantID, websiteID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.APIKey{}).
		Where("tenant_id = ? AND website_id = ? AND status != ?",
			tenantID, websiteID, models.APIKeyStatusDeleted).
		Count(&count).Error
	return count, err
}

// CleanupExpired marks expired API keys as expired
func (r *apiKeyRepository) CleanupExpired(ctx context.Context, tenantID, websiteID uint) error {
	return r.db.WithContext(ctx).
		Model(&models.APIKey{}).
		Where("tenant_id = ? AND website_id = ? AND expires_at IS NOT NULL AND expires_at < ? AND status = ?",
			tenantID, websiteID, time.Now(), models.APIKeyStatusActive).
		Update("status", models.APIKeyStatusExpired).Error
}
