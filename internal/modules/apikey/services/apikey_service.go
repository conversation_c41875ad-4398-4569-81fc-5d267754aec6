package services

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"strconv"
	"strings"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// APIKeyService defines the interface for API key business logic
type APIKeyService interface {
	// Core API key operations
	CreateAPIKey(ctx context.Context, tenantID, websiteID, createdBy uint, req *models.CreateAPIKeyRequest) (*models.APIKeyResponse, error)
	GetAPIKey(ctx context.Context, tenantID, websiteID, id uint) (*models.APIKeyDetailResponse, error)
	UpdateAPIKey(ctx context.Context, tenantID, websiteID, id uint, req *models.UpdateAPIKeyRequest) (*models.APIKeyResponse, error)
	DeleteAPIKey(ctx context.Context, tenantID, websiteID, id uint) error
	ListAPIKeys(ctx context.Context, tenantID, websiteID uint, filter *models.APIKeyFilter) (*models.APIKeyListResponse, error)
	ListAPIKeysWithCursor(ctx context.Context, tenantID, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) (*models.APIKeyListResponse, error)

	// Key validation and authentication
	ValidateAPIKey(ctx context.Context, rawKey string) (*models.APIKeyValidationResponse, error)
	CheckPermission(ctx context.Context, apiKey *models.APIKey, resource, action string) bool
	CheckRateLimit(ctx context.Context, apiKey *models.APIKey) (*models.RateLimitInfo, error)

	// Key rotation operations
	RotateAPIKey(ctx context.Context, tenantID, websiteID, id uint, req *models.RotateAPIKeyRequest) (*models.RotateAPIKeyResponse, error)
	RevokeAPIKey(ctx context.Context, tenantID, websiteID, id uint, req *models.RevokeAPIKeyRequest) error

	// Usage tracking
	TrackUsage(ctx context.Context, apiKey *models.APIKey, endpoint, method string, responseCode, responseTime int, metadata map[string]interface{}) error
	GetUsage(ctx context.Context, tenantID, apiKeyID uint, req *models.APIKeyUsageRequest) (*models.APIKeyUsageResponse, error)

	// Permission management
	CreatePermission(ctx context.Context, tenantID, apiKeyID uint, req *models.CreateAPIKeyPermissionRequest) (*models.APIKeyPermissionResponse, error)
	GetPermissions(ctx context.Context, tenantID, apiKeyID uint) ([]models.APIKeyPermissionResponse, error)
	DeletePermission(ctx context.Context, tenantID, permissionID uint) error

	// Scope management
	CreateScope(ctx context.Context, tenantID, apiKeyID uint, req *models.CreateAPIKeyScopeRequest) (*models.APIKeyScopeResponse, error)
	GetScopes(ctx context.Context, tenantID, apiKeyID uint) ([]models.APIKeyScopeResponse, error)
	UpdateScope(ctx context.Context, tenantID, scopeID uint, req *models.CreateAPIKeyScopeRequest) (*models.APIKeyScopeResponse, error)
	DeleteScope(ctx context.Context, tenantID, scopeID uint) error

	// Analytics
	GetAnalytics(ctx context.Context, tenantID uint, req *models.APIKeyAnalyticsRequest) (*models.APIKeyAnalyticsResponse, error)

	// Bulk operations
	BulkAction(ctx context.Context, tenantID, websiteID uint, req *models.APIKeyBulkActionRequest) (*models.APIKeyBulkActionResponse, error)

	// Utility operations
	CleanupExpired(ctx context.Context, tenantID, websiteID uint) error
}

// apiKeyService implements the APIKeyService interface
type apiKeyService struct {
	repo   repositories.APIKeyRepository
	logger utils.Logger
}

// NewAPIKeyService creates a new API key service
func NewAPIKeyService(repo repositories.APIKeyRepository, logger utils.Logger) APIKeyService {
	return &apiKeyService{
		repo:   repo,
		logger: logger,
	}
}

// CreateAPIKey creates a new API key
func (s *apiKeyService) CreateAPIKey(ctx context.Context, tenantID, websiteID, createdBy uint, req *models.CreateAPIKeyRequest) (*models.APIKeyResponse, error) {
	// Check if website has reached API key limit
	count, err := s.repo.CountByWebsite(ctx, tenantID, websiteID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to count API keys for website")
		return nil, errors.New("failed to create API key")
	}

	maxKeys := utils.GetEnvAsInt("MAX_API_KEYS_PER_WEBSITE", 50)
	if count >= int64(maxKeys) {
		return nil, errors.New("maximum number of API keys reached for this website")
	}

	// Generate secure API key
	rawKey, keyPrefix, keyHash, err := s.generateAPIKey()
	if err != nil {
		s.logger.WithError(err).Error("Failed to generate API key")
		return nil, errors.New("failed to generate API key")
	}

	// Create API key model
	apiKey := &models.APIKey{
		TenantID:    tenantID,
		WebsiteID:   websiteID,
		KeyHash:     keyHash,
		KeyPrefix:   keyPrefix,
		Name:        req.Name,
		Description: req.Description,
		Status:      models.APIKeyStatusActive,
		RateLimit:   req.RateLimit,
		RateWindow:  req.RateWindow,
		CreatedBy:   createdBy,
	}

	// Set default rate limits if not provided
	if apiKey.RateLimit == 0 {
		apiKey.RateLimit = utils.GetEnvAsInt("DEFAULT_API_KEY_RATE_LIMIT", 1000)
	}
	if apiKey.RateWindow == 0 {
		apiKey.RateWindow = utils.GetEnvAsInt("DEFAULT_API_KEY_RATE_WINDOW", 3600)
	}

	// Set permissions
	if req.Permissions != nil {
		if err := apiKey.SetPermissions(req.Permissions); err != nil {
			s.logger.WithError(err).Error("Failed to set API key permissions")
			return nil, errors.New("invalid permissions format")
		}
	}

	// Set scopes
	if req.Scopes != nil {
		if err := apiKey.SetScopes(req.Scopes); err != nil {
			s.logger.WithError(err).Error("Failed to set API key scopes")
			return nil, errors.New("invalid scopes format")
		}
	}

	// Set IP whitelist
	if req.IPWhitelist != nil {
		if err := apiKey.SetIPWhitelist(req.IPWhitelist); err != nil {
			s.logger.WithError(err).Error("Failed to set API key IP whitelist")
			return nil, errors.New("invalid IP whitelist format")
		}
	}

	// Set expiration
	if req.ExpiresIn > 0 {
		expiresAt := time.Now().Add(time.Duration(req.ExpiresIn) * time.Second)
		apiKey.ExpiresAt = &expiresAt
	}

	// Create API key in database
	if err := s.repo.Create(ctx, apiKey); err != nil {
		s.logger.WithError(err).Error("Failed to create API key in database")
		return nil, errors.New("failed to create API key")
	}

	s.logger.WithFields(map[string]interface{}{
		"tenant_id":  tenantID,
		"website_id": websiteID,
		"api_key_id": apiKey.ID,
		"key_prefix": keyPrefix,
		"created_by": createdBy,
	}).Info("API key created successfully")

	// Return response with raw key (only shown once)
	return &models.APIKeyResponse{
		ID:          apiKey.ID,
		Key:         rawKey,
		KeyPrefix:   keyPrefix,
		Name:        apiKey.Name,
		Description: apiKey.Description,
		Status:      apiKey.Status,
		RateLimit:   apiKey.RateLimit,
		RateWindow:  apiKey.RateWindow,
		ExpiresAt:   apiKey.ExpiresAt,
		LastUsedAt:  apiKey.LastUsedAt,
		CreatedAt:   apiKey.CreatedAt,
		UpdatedAt:   apiKey.UpdatedAt,
	}, nil
}

// GetAPIKey retrieves a specific API key with detailed information
func (s *apiKeyService) GetAPIKey(ctx context.Context, tenantID, websiteID, id uint) (*models.APIKeyDetailResponse, error) {
	apiKey, err := s.repo.GetByID(ctx, tenantID, websiteID, id)
	if err != nil {
		return nil, err
	}

	// Get permissions
	permissions, _ := apiKey.GetPermissionsList()

	// Get scopes
	scopes, _ := apiKey.GetScopesList()

	// Get IP whitelist
	ipWhitelist, _ := apiKey.GetIPWhitelistList()

	// Get usage stats
	usageStats, _ := s.repo.GetUsageStats(ctx, tenantID, id, "30d")

	// Get permission details
	permissionList, _ := s.repo.GetPermissions(ctx, tenantID, id)

	// Get scope details
	scopeList, _ := s.repo.GetScopes(ctx, tenantID, id)

	// Get recent usage
	recentUsage, _ := s.repo.GetUsage(ctx, tenantID, id, &models.APIKeyUsageRequest{Period: "7d"})

	// Get rotation history
	rotationHistory, _ := s.repo.GetRotations(ctx, tenantID, id)

	return &models.APIKeyDetailResponse{
		APIKeyResponse: models.APIKeyResponse{
			ID:          apiKey.ID,
			KeyPrefix:   apiKey.KeyPrefix,
			Name:        apiKey.Name,
			Description: apiKey.Description,
			Status:      apiKey.Status,
			RateLimit:   apiKey.RateLimit,
			RateWindow:  apiKey.RateWindow,
			ExpiresAt:   apiKey.ExpiresAt,
			LastUsedAt:  apiKey.LastUsedAt,
			CreatedAt:   apiKey.CreatedAt,
			UpdatedAt:   apiKey.UpdatedAt,
		},
		Permissions:     permissions,
		Scopes:          scopes,
		IPWhitelist:     ipWhitelist,
		UsageStats:      usageStats,
		PermissionList:  permissionList,
		ScopeList:       scopeList,
		RecentUsage:     recentUsage,
		RotationHistory: rotationHistory,
	}, nil
}

// UpdateAPIKey updates an existing API key
func (s *apiKeyService) UpdateAPIKey(ctx context.Context, tenantID, websiteID, id uint, req *models.UpdateAPIKeyRequest) (*models.APIKeyResponse, error) {
	apiKey, err := s.repo.GetByID(ctx, tenantID, websiteID, id)
	if err != nil {
		return nil, err
	}

	// Update fields
	if req.Name != nil {
		apiKey.Name = *req.Name
	}
	if req.Description != nil {
		apiKey.Description = *req.Description
	}
	if req.Status != nil {
		apiKey.Status = *req.Status
	}
	if req.RateLimit != nil {
		apiKey.RateLimit = *req.RateLimit
	}
	if req.RateWindow != nil {
		apiKey.RateWindow = *req.RateWindow
	}

	// Update permissions
	if req.Permissions != nil {
		if err := apiKey.SetPermissions(*req.Permissions); err != nil {
			return nil, errors.New("invalid permissions format")
		}
	}

	// Update scopes
	if req.Scopes != nil {
		if err := apiKey.SetScopes(*req.Scopes); err != nil {
			return nil, errors.New("invalid scopes format")
		}
	}

	// Update IP whitelist
	if req.IPWhitelist != nil {
		if err := apiKey.SetIPWhitelist(*req.IPWhitelist); err != nil {
			return nil, errors.New("invalid IP whitelist format")
		}
	}

	// Save changes
	if err := s.repo.Update(ctx, apiKey); err != nil {
		s.logger.WithError(err).Error("Failed to update API key")
		return nil, errors.New("failed to update API key")
	}

	s.logger.WithFields(map[string]interface{}{
		"tenant_id":  tenantID,
		"website_id": websiteID,
		"api_key_id": id,
	}).Info("API key updated successfully")

	return &models.APIKeyResponse{
		ID:          apiKey.ID,
		KeyPrefix:   apiKey.KeyPrefix,
		Name:        apiKey.Name,
		Description: apiKey.Description,
		Status:      apiKey.Status,
		RateLimit:   apiKey.RateLimit,
		RateWindow:  apiKey.RateWindow,
		ExpiresAt:   apiKey.ExpiresAt,
		LastUsedAt:  apiKey.LastUsedAt,
		CreatedAt:   apiKey.CreatedAt,
		UpdatedAt:   apiKey.UpdatedAt,
	}, nil
}

// DeleteAPIKey soft deletes an API key
func (s *apiKeyService) DeleteAPIKey(ctx context.Context, tenantID, websiteID, id uint) error {
	// Check if API key exists
	_, err := s.repo.GetByID(ctx, tenantID, websiteID, id)
	if err != nil {
		return err
	}

	// Soft delete the API key
	if err := s.repo.Delete(ctx, tenantID, websiteID, id); err != nil {
		s.logger.WithError(err).Error("Failed to delete API key")
		return errors.New("failed to delete API key")
	}

	s.logger.WithFields(map[string]interface{}{
		"tenant_id":  tenantID,
		"website_id": websiteID,
		"api_key_id": id,
	}).Info("API key deleted successfully")

	return nil
}

// ListAPIKeys retrieves a list of API keys with pagination
func (s *apiKeyService) ListAPIKeys(ctx context.Context, tenantID, websiteID uint, filter *models.APIKeyFilter) (*models.APIKeyListResponse, error) {
	// Set default pagination
	if filter.Page == 0 {
		filter.Page = 1
	}
	if filter.Limit == 0 {
		filter.Limit = 20
	}

	// Get API keys from repository
	apiKeys, total, err := s.repo.List(ctx, tenantID, websiteID, filter)
	if err != nil {
		s.logger.WithError(err).Error("Failed to list API keys")
		return nil, errors.New("failed to list API keys")
	}

	// Convert to response format
	responses := make([]models.APIKeyResponse, len(apiKeys))
	for i, apiKey := range apiKeys {
		responses[i] = models.APIKeyResponse{
			ID:          apiKey.ID,
			KeyPrefix:   apiKey.KeyPrefix,
			Name:        apiKey.Name,
			Description: apiKey.Description,
			Status:      apiKey.Status,
			RateLimit:   apiKey.RateLimit,
			RateWindow:  apiKey.RateWindow,
			ExpiresAt:   apiKey.ExpiresAt,
			LastUsedAt:  apiKey.LastUsedAt,
			CreatedAt:   apiKey.CreatedAt,
			UpdatedAt:   apiKey.UpdatedAt,
		}
	}

	// Calculate pagination info
	totalPages := int((total + int64(filter.Limit) - 1) / int64(filter.Limit))
	hasNext := filter.Page < totalPages
	hasPrev := filter.Page > 1

	return &models.APIKeyListResponse{
		APIKeys:    responses,
		Total:      total,
		Page:       filter.Page,
		Limit:      filter.Limit,
		TotalPages: totalPages,
		HasNext:    hasNext,
		HasPrev:    hasPrev,
	}, nil
}

// ListAPIKeysWithCursor lists API keys using cursor-based pagination
func (s *apiKeyService) ListAPIKeysWithCursor(ctx context.Context, tenantID, websiteID uint, req *pagination.CursorRequest, filters map[string]interface{}) (*models.APIKeyListResponse, error) {
	// Get API keys from repository using cursor pagination
	apiKeys, pagResp, err := s.repo.ListWithCursor(ctx, tenantID, websiteID, req, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to list API keys with cursor")
		return nil, errors.New("failed to list API keys")
	}

	// Convert to response format
	responses := make([]models.APIKeyResponse, len(apiKeys))
	for i, apiKey := range apiKeys {
		responses[i] = models.APIKeyResponse{
			ID:          apiKey.ID,
			TenantID:    apiKey.TenantID,
			WebsiteID:   apiKey.WebsiteID,
			KeyPrefix:   apiKey.KeyPrefix,
			Name:        apiKey.Name,
			Description: apiKey.Description,
			Status:      apiKey.Status,
			RateLimit:   apiKey.RateLimit,
			RateWindow:  apiKey.RateWindow,
			ExpiresAt:   apiKey.ExpiresAt,
			LastUsedAt:  apiKey.LastUsedAt,
			CreatedAt:   apiKey.CreatedAt,
			UpdatedAt:   apiKey.UpdatedAt,
		}
	}

	return &models.APIKeyListResponse{
		APIKeys:    responses,
		Pagination: pagResp,
	}, nil
}

// ValidateAPIKey validates an API key and returns its details
func (s *apiKeyService) ValidateAPIKey(ctx context.Context, rawKey string) (*models.APIKeyValidationResponse, error) {
	// Hash the key
	keyHash := s.hashKey(rawKey)

	// Get API key from database
	apiKey, err := s.repo.GetByKeyHash(ctx, keyHash)
	if err != nil {
		return &models.APIKeyValidationResponse{
			Valid: false,
			Error: "invalid API key",
		}, nil
	}

	// Check if key is expired
	if apiKey.IsExpired() {
		return &models.APIKeyValidationResponse{
			Valid: false,
			Error: "API key has expired",
		}, nil
	}

	// Check rate limit
	rateLimitInfo, err := s.CheckRateLimit(ctx, apiKey)
	if err != nil {
		s.logger.WithError(err).Error("Failed to check rate limit")
		return &models.APIKeyValidationResponse{
			Valid: false,
			Error: "rate limit check failed",
		}, nil
	}

	// Update last used timestamp
	go func() {
		if err := s.repo.UpdateLastUsed(context.Background(), apiKey.TenantID, apiKey.ID); err != nil {
			s.logger.WithError(err).Error("Failed to update last used timestamp")
		}
	}()

	// Get permissions and scopes
	permissions, _ := apiKey.GetPermissionsList()
	scopes, _ := apiKey.GetScopesList()

	return &models.APIKeyValidationResponse{
		Valid: true,
		APIKey: &models.APIKeyResponse{
			ID:          apiKey.ID,
			KeyPrefix:   apiKey.KeyPrefix,
			Name:        apiKey.Name,
			Description: apiKey.Description,
			Status:      apiKey.Status,
			RateLimit:   apiKey.RateLimit,
			RateWindow:  apiKey.RateWindow,
			ExpiresAt:   apiKey.ExpiresAt,
			LastUsedAt:  apiKey.LastUsedAt,
			CreatedAt:   apiKey.CreatedAt,
			UpdatedAt:   apiKey.UpdatedAt,
		},
		Permissions: permissions,
		Scopes:      scopes,
		RateLimit:   rateLimitInfo,
	}, nil
}

// CheckPermission checks if an API key has permission for a specific resource and action
func (s *apiKeyService) CheckPermission(ctx context.Context, apiKey *models.APIKey, resource, action string) bool {
	// Get permissions from the API key
	permissions, err := apiKey.GetPermissionsList()
	if err != nil {
		s.logger.WithError(err).Error("Failed to get permissions from API key")
		return false
	}

	// Check for wildcard permissions
	if resourcePerms, ok := permissions["*"]; ok {
		if actions, ok := resourcePerms.([]interface{}); ok {
			for _, a := range actions {
				if a == "*" || a == action {
					return true
				}
			}
		}
	}

	// Check for specific resource permissions
	if resourcePerms, ok := permissions[resource]; ok {
		if actions, ok := resourcePerms.([]interface{}); ok {
			for _, a := range actions {
				if a == "*" || a == action {
					return true
				}
			}
		}
	}

	return false
}

// CheckRateLimit checks if the API key has exceeded its rate limit
func (s *apiKeyService) CheckRateLimit(ctx context.Context, apiKey *models.APIKey) (*models.RateLimitInfo, error) {
	// This is a simplified implementation
	// In a real implementation, you would use Redis or similar for distributed rate limiting

	// For now, return a successful rate limit check
	return &models.RateLimitInfo{
		Limit:     apiKey.RateLimit,
		Remaining: apiKey.RateLimit - 1, // Simplified calculation
		Reset:     time.Now().Add(time.Duration(apiKey.RateWindow) * time.Second).Unix(),
	}, nil
}

// RotateAPIKey rotates an API key
func (s *apiKeyService) RotateAPIKey(ctx context.Context, tenantID, websiteID, id uint, req *models.RotateAPIKeyRequest) (*models.RotateAPIKeyResponse, error) {
	apiKey, err := s.repo.GetByID(ctx, tenantID, websiteID, id)
	if err != nil {
		return nil, err
	}

	// Generate new key
	newRawKey, newKeyPrefix, newKeyHash, err := s.generateAPIKey()
	if err != nil {
		s.logger.WithError(err).Error("Failed to generate new API key")
		return nil, errors.New("failed to generate new API key")
	}

	// Create rotation record
	gracePeriod := 24 // Default 24 hours
	if req.GracePeriodHours > 0 {
		gracePeriod = req.GracePeriodHours
	}

	graceExpiresAt := time.Now().Add(time.Duration(gracePeriod) * time.Hour)

	rotation := &models.APIKeyRotation{
		TenantID:             tenantID,
		WebsiteID:            websiteID,
		APIKeyID:             id,
		OldKeyHash:           apiKey.KeyHash,
		NewKeyHash:           newKeyHash,
		RotationType:         models.RotationTypeManual,
		Reason:               req.Reason,
		GracePeriodHours:     gracePeriod,
		GracePeriodExpiresAt: &graceExpiresAt,
		Status:               models.RotationStatusCompleted,
		RotatedAt:            time.Now(),
	}

	now := time.Now()
	rotation.CompletedAt = &now

	// Save rotation record
	if err := s.repo.CreateRotation(ctx, rotation); err != nil {
		s.logger.WithError(err).Error("Failed to create rotation record")
		return nil, errors.New("failed to create rotation record")
	}

	// Update API key with new hash
	apiKey.KeyHash = newKeyHash
	apiKey.KeyPrefix = newKeyPrefix
	if err := s.repo.Update(ctx, apiKey); err != nil {
		s.logger.WithError(err).Error("Failed to update API key with new hash")
		return nil, errors.New("failed to update API key")
	}

	s.logger.WithFields(map[string]interface{}{
		"tenant_id":   tenantID,
		"website_id":  websiteID,
		"api_key_id":  id,
		"rotation_id": rotation.ID,
	}).Info("API key rotated successfully")

	return &models.RotateAPIKeyResponse{
		ID:             rotation.ID,
		NewKey:         newRawKey,
		NewKeyPrefix:   newKeyPrefix,
		GracePeriod:    gracePeriod,
		GraceExpiresAt: &graceExpiresAt,
		RotationType:   rotation.RotationType,
		RotationStatus: rotation.Status,
		RotatedAt:      rotation.RotatedAt,
	}, nil
}

// RevokeAPIKey revokes an API key
func (s *apiKeyService) RevokeAPIKey(ctx context.Context, tenantID, websiteID, id uint, req *models.RevokeAPIKeyRequest) error {
	apiKey, err := s.repo.GetByID(ctx, tenantID, websiteID, id)
	if err != nil {
		return err
	}

	// Update status to revoked
	apiKey.Status = models.APIKeyStatusRevoked
	if err := s.repo.Update(ctx, apiKey); err != nil {
		s.logger.WithError(err).Error("Failed to revoke API key")
		return errors.New("failed to revoke API key")
	}

	s.logger.WithFields(map[string]interface{}{
		"tenant_id":  tenantID,
		"website_id": websiteID,
		"api_key_id": id,
		"reason":     req.Reason,
	}).Info("API key revoked successfully")

	return nil
}

// TrackUsage tracks API key usage
func (s *apiKeyService) TrackUsage(ctx context.Context, apiKey *models.APIKey, endpoint, method string, responseCode, responseTime int, metadata map[string]interface{}) error {
	usage := &models.APIKeyUsage{
		TenantID:     apiKey.TenantID,
		WebsiteID:    apiKey.WebsiteID,
		APIKeyID:     apiKey.ID,
		Endpoint:     endpoint,
		Method:       models.HTTPMethod(method),
		ResponseCode: responseCode,
		ResponseTime: responseTime,
		IPAddress:    "127.0.0.1", // Will be set by middleware
		UserAgent:    "",          // Will be set by middleware
		RequestID:    "",          // Will be set by middleware
	}

	// Set metadata if provided
	if metadata != nil {
		metadataBytes, _ := json.Marshal(metadata)
		usage.Metadata = metadataBytes
	}

	// Save usage record asynchronously
	go func() {
		if err := s.repo.CreateUsage(context.Background(), usage); err != nil {
			s.logger.WithError(err).Error("Failed to track API key usage")
		}
	}()

	return nil
}

// GetUsage retrieves usage analytics for an API key
func (s *apiKeyService) GetUsage(ctx context.Context, tenantID, apiKeyID uint, req *models.APIKeyUsageRequest) (*models.APIKeyUsageResponse, error) {
	// Get usage statistics
	stats, err := s.repo.GetUsageStats(ctx, tenantID, apiKeyID, req.Period)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get usage statistics")
		return nil, errors.New("failed to get usage statistics")
	}

	// Get detailed usage records
	usage, err := s.repo.GetUsage(ctx, tenantID, apiKeyID, req)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get usage records")
		return nil, errors.New("failed to get usage records")
	}

	// Process usage data for response
	timeline := make([]models.UsageDataPoint, 0)
	byEndpoint := make([]models.EndpointUsage, 0)
	byStatus := make(map[string]int64)
	byMethod := make(map[string]int64)

	// Aggregate data
	endpointMap := make(map[string]*models.EndpointUsage)

	for _, u := range usage {
		// Count by status
		statusKey := strconv.Itoa(u.ResponseCode)
		byStatus[statusKey]++

		// Count by method
		byMethod[string(u.Method)]++

		// Aggregate by endpoint
		endpointKey := fmt.Sprintf("%s:%s", u.Method, u.Endpoint)
		if endpointUsage, exists := endpointMap[endpointKey]; exists {
			endpointUsage.Count++
			endpointUsage.AvgResponseTime = (endpointUsage.AvgResponseTime + int64(u.ResponseTime)) / 2
			if u.CreatedAt.After(endpointUsage.LastUsed) {
				endpointUsage.LastUsed = u.CreatedAt
			}
			if u.ResponseCode >= 400 {
				endpointUsage.ErrorRate = (endpointUsage.ErrorRate + 1) / 2
			}
		} else {
			errorRate := 0.0
			if u.ResponseCode >= 400 {
				errorRate = 1.0
			}
			endpointMap[endpointKey] = &models.EndpointUsage{
				Endpoint:        u.Endpoint,
				Method:          string(u.Method),
				Count:           1,
				AvgResponseTime: int64(u.ResponseTime),
				ErrorRate:       errorRate,
				LastUsed:        u.CreatedAt,
			}
		}
	}

	// Convert map to slice
	for _, endpointUsage := range endpointMap {
		byEndpoint = append(byEndpoint, *endpointUsage)
	}

	return &models.APIKeyUsageResponse{
		Summary:    *stats,
		Timeline:   timeline,
		ByEndpoint: byEndpoint,
		ByStatus:   byStatus,
		ByMethod:   byMethod,
		TopIPs:     []models.IPUsage{}, // Will be implemented later
	}, nil
}

// CreatePermission creates a new permission for an API key
func (s *apiKeyService) CreatePermission(ctx context.Context, tenantID, apiKeyID uint, req *models.CreateAPIKeyPermissionRequest) (*models.APIKeyPermissionResponse, error) {
	permission := &models.APIKeyPermission{
		TenantID: tenantID,
		APIKeyID: apiKeyID,
		Resource: req.Resource,
		Action:   req.Action,
	}

	// Set conditions if provided
	if req.Conditions != nil {
		conditionsBytes, _ := json.Marshal(req.Conditions)
		permission.Conditions = conditionsBytes
	}

	if err := s.repo.CreatePermission(ctx, permission); err != nil {
		s.logger.WithError(err).Error("Failed to create API key permission")
		return nil, errors.New("failed to create permission")
	}

	conditions, _ := permission.GetConditions()

	return &models.APIKeyPermissionResponse{
		ID:         permission.ID,
		Resource:   permission.Resource,
		Action:     permission.Action,
		Conditions: conditions,
		CreatedAt:  permission.CreatedAt,
	}, nil
}

// GetPermissions retrieves permissions for an API key
func (s *apiKeyService) GetPermissions(ctx context.Context, tenantID, apiKeyID uint) ([]models.APIKeyPermissionResponse, error) {
	permissions, err := s.repo.GetPermissions(ctx, tenantID, apiKeyID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get API key permissions")
		return nil, errors.New("failed to get permissions")
	}

	responses := make([]models.APIKeyPermissionResponse, len(permissions))
	for i, perm := range permissions {
		conditions, _ := perm.GetConditions()
		responses[i] = models.APIKeyPermissionResponse{
			ID:         perm.ID,
			Resource:   perm.Resource,
			Action:     perm.Action,
			Conditions: conditions,
			CreatedAt:  perm.CreatedAt,
		}
	}

	return responses, nil
}

// DeletePermission deletes a permission
func (s *apiKeyService) DeletePermission(ctx context.Context, tenantID, permissionID uint) error {
	if err := s.repo.DeletePermission(ctx, tenantID, permissionID); err != nil {
		s.logger.WithError(err).Error("Failed to delete API key permission")
		return errors.New("failed to delete permission")
	}

	return nil
}

// CreateScope creates a new scope for an API key
func (s *apiKeyService) CreateScope(ctx context.Context, tenantID, apiKeyID uint, req *models.CreateAPIKeyScopeRequest) (*models.APIKeyScopeResponse, error) {
	scope := &models.APIKeyScope{
		TenantID: tenantID,
		APIKeyID: apiKeyID,
		Scope:    req.Scope,
		Resource: req.Resource,
		IsActive: true,
	}

	// Set actions
	actionsBytes, err := json.Marshal(req.Actions)
	if err != nil {
		return nil, errors.New("invalid actions format")
	}
	scope.Actions = actionsBytes

	// Set conditions
	if req.Conditions != nil {
		conditionsBytes, _ := json.Marshal(req.Conditions)
		scope.Conditions = conditionsBytes
	}

	// Set expiration
	if req.ExpiresIn > 0 {
		expiresAt := time.Now().Add(time.Duration(req.ExpiresIn) * time.Second)
		scope.ExpiresAt = &expiresAt
	}

	if err := s.repo.CreateScope(ctx, scope); err != nil {
		s.logger.WithError(err).Error("Failed to create API key scope")
		return nil, errors.New("failed to create scope")
	}

	actions, _ := scope.GetActions()
	conditions, _ := scope.GetConditions()

	return &models.APIKeyScopeResponse{
		ID:         scope.ID,
		Scope:      scope.Scope,
		Resource:   scope.Resource,
		Actions:    actions,
		Conditions: conditions,
		IsActive:   scope.IsActive,
		ExpiresAt:  scope.ExpiresAt,
		CreatedAt:  scope.CreatedAt,
		UpdatedAt:  scope.UpdatedAt,
	}, nil
}

// GetScopes retrieves scopes for an API key
func (s *apiKeyService) GetScopes(ctx context.Context, tenantID, apiKeyID uint) ([]models.APIKeyScopeResponse, error) {
	scopes, err := s.repo.GetScopes(ctx, tenantID, apiKeyID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get API key scopes")
		return nil, errors.New("failed to get scopes")
	}

	responses := make([]models.APIKeyScopeResponse, len(scopes))
	for i, scope := range scopes {
		actions, _ := scope.GetActions()
		conditions, _ := scope.GetConditions()
		responses[i] = models.APIKeyScopeResponse{
			ID:         scope.ID,
			Scope:      scope.Scope,
			Resource:   scope.Resource,
			Actions:    actions,
			Conditions: conditions,
			IsActive:   scope.IsActive,
			ExpiresAt:  scope.ExpiresAt,
			CreatedAt:  scope.CreatedAt,
			UpdatedAt:  scope.UpdatedAt,
		}
	}

	return responses, nil
}

// UpdateScope updates a scope
func (s *apiKeyService) UpdateScope(ctx context.Context, tenantID, scopeID uint, req *models.CreateAPIKeyScopeRequest) (*models.APIKeyScopeResponse, error) {
	// This would get the scope first, then update it
	// For brevity, returning a simplified response
	return nil, errors.New("not implemented")
}

// DeleteScope deletes a scope
func (s *apiKeyService) DeleteScope(ctx context.Context, tenantID, scopeID uint) error {
	if err := s.repo.DeleteScope(ctx, tenantID, scopeID); err != nil {
		s.logger.WithError(err).Error("Failed to delete API key scope")
		return errors.New("failed to delete scope")
	}

	return nil
}

// GetAnalytics retrieves analytics for API keys
func (s *apiKeyService) GetAnalytics(ctx context.Context, tenantID uint, req *models.APIKeyAnalyticsRequest) (*models.APIKeyAnalyticsResponse, error) {
	// This would implement comprehensive analytics
	// For brevity, returning a simplified response
	return &models.APIKeyAnalyticsResponse{
		Summary: map[string]interface{}{
			"total_keys":     0,
			"active_keys":    0,
			"total_requests": 0,
		},
		KeyMetrics:  []models.KeyMetric{},
		Timeline:    []models.AnalyticsDataPoint{},
		Comparisons: []models.KeyComparison{},
	}, nil
}

// BulkAction performs bulk actions on API keys
func (s *apiKeyService) BulkAction(ctx context.Context, tenantID, websiteID uint, req *models.APIKeyBulkActionRequest) (*models.APIKeyBulkActionResponse, error) {
	response := &models.APIKeyBulkActionResponse{
		Success: []uint{},
		Failed:  []models.BulkActionFailure{},
		Total:   len(req.APIKeyIDs),
	}

	for _, apiKeyID := range req.APIKeyIDs {
		switch req.Action {
		case "activate":
			if err := s.updateAPIKeyStatus(ctx, tenantID, websiteID, apiKeyID, models.APIKeyStatusActive); err != nil {
				response.Failed = append(response.Failed, models.BulkActionFailure{
					APIKeyID: apiKeyID,
					Error:    err.Error(),
				})
			} else {
				response.Success = append(response.Success, apiKeyID)
			}
		case "deactivate":
			if err := s.updateAPIKeyStatus(ctx, tenantID, websiteID, apiKeyID, models.APIKeyStatusInactive); err != nil {
				response.Failed = append(response.Failed, models.BulkActionFailure{
					APIKeyID: apiKeyID,
					Error:    err.Error(),
				})
			} else {
				response.Success = append(response.Success, apiKeyID)
			}
		case "revoke":
			if err := s.updateAPIKeyStatus(ctx, tenantID, websiteID, apiKeyID, models.APIKeyStatusRevoked); err != nil {
				response.Failed = append(response.Failed, models.BulkActionFailure{
					APIKeyID: apiKeyID,
					Error:    err.Error(),
				})
			} else {
				response.Success = append(response.Success, apiKeyID)
			}
		case "delete":
			if err := s.DeleteAPIKey(ctx, tenantID, websiteID, apiKeyID); err != nil {
				response.Failed = append(response.Failed, models.BulkActionFailure{
					APIKeyID: apiKeyID,
					Error:    err.Error(),
				})
			} else {
				response.Success = append(response.Success, apiKeyID)
			}
		default:
			response.Failed = append(response.Failed, models.BulkActionFailure{
				APIKeyID: apiKeyID,
				Error:    "unsupported action",
			})
		}
	}

	response.Successful = len(response.Success)
	response.FailedCount = len(response.Failed)

	return response, nil
}

// CleanupExpired cleans up expired API keys
func (s *apiKeyService) CleanupExpired(ctx context.Context, tenantID, websiteID uint) error {
	return s.repo.CleanupExpired(ctx, tenantID, websiteID)
}

// Helper methods

// generateAPIKey generates a new API key
func (s *apiKeyService) generateAPIKey() (string, string, string, error) {
	// Generate 32 bytes of random data
	b := make([]byte, 32)
	if _, err := rand.Read(b); err != nil {
		return "", "", "", err
	}

	// Encode to base64 URL-safe
	encoded := base64.URLEncoding.EncodeToString(b)

	// Add prefix based on environment
	prefix := "sk_test_"
	if utils.GetEnv("ENVIRONMENT", "development") == "production" {
		prefix = "sk_live_"
	}

	rawKey := prefix + encoded
	keyPrefix := rawKey[:10]
	keyHash := s.hashKey(rawKey)

	return rawKey, keyPrefix, keyHash, nil
}

// hashKey hashes an API key using SHA-256
func (s *apiKeyService) hashKey(key string) string {
	hash := sha256.Sum256([]byte(key))
	return hex.EncodeToString(hash[:])
}

// updateAPIKeyStatus updates the status of an API key
func (s *apiKeyService) updateAPIKeyStatus(ctx context.Context, tenantID, websiteID, apiKeyID uint, status models.APIKeyStatus) error {
	apiKey, err := s.repo.GetByID(ctx, tenantID, websiteID, apiKeyID)
	if err != nil {
		return err
	}

	apiKey.Status = status
	return s.repo.Update(ctx, apiKey)
}

// checkIPWhitelist checks if an IP is in the whitelist
func (s *apiKeyService) checkIPWhitelist(clientIP string, whitelist []string) bool {
	if len(whitelist) == 0 {
		return true // No whitelist means allow all
	}

	ip := net.ParseIP(clientIP)
	if ip == nil {
		return false
	}

	for _, allowed := range whitelist {
		if strings.Contains(allowed, "/") {
			// CIDR notation
			_, ipnet, err := net.ParseCIDR(allowed)
			if err == nil && ipnet.Contains(ip) {
				return true
			}
		} else if allowed == clientIP {
			// Exact match
			return true
		}
	}

	return false
}
