package middleware

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/apikey/services"
)

// APIKeyContextKey is the key for storing API key context data
const APIKeyContextKey = "api_key"

// APIKeyMiddleware handles API key authentication and authorization
type APIKeyMiddleware struct {
	apiKeyService services.APIKeyService
}

// NewAPIKeyMiddleware creates a new API key middleware
func NewAPIKeyMiddleware(apiKeyService services.APIKeyService) *APIKeyMiddleware {
	return &APIKeyMiddleware{
		apiKeyService: apiKeyService,
	}
}

// Authenticate validates API key and sets up context
func (m *APIKeyMiddleware) Authenticate() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract API key from header
		apiKey := extractAPIKey(c)
		if apiKey == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "API key is required",
				"code":  "MISSING_API_KEY",
			})
			c.Abort()
			return
		}

		// Validate API key
		ctx := context.Background()
		validationResult, err := m.apiKeyService.ValidateAPIKey(ctx, apiKey)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to validate API key",
				"code":  "VALIDATION_ERROR",
			})
			c.Abort()
			return
		}

		if !validationResult.Valid {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": validationResult.Error,
				"code":  "INVALID_API_KEY",
			})
			c.Abort()
			return
		}

		// Set API key context
		c.Set(APIKeyContextKey, validationResult.APIKey)
		c.Set("api_key_permissions", validationResult.Permissions)
		c.Set("api_key_scopes", validationResult.Scopes)
		c.Set("api_key_rate_limit", validationResult.RateLimit)

		// Set tenant and website context
		c.Set("tenant_id", validationResult.APIKey.TenantID)
		c.Set("website_id", validationResult.APIKey.WebsiteID)

		c.Next()
	}
}

// RequirePermission checks if API key has required permission
func (m *APIKeyMiddleware) RequirePermission(resource, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		apiKey := getAPIKeyFromContext(c)
		if apiKey == nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "API key not found in context",
				"code":  "MISSING_CONTEXT",
			})
			c.Abort()
			return
		}

		// Check permission - simplified for now
		// TODO: Implement proper permission checking
		hasPermission := true
		var err error
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to check permission",
				"code":  "PERMISSION_ERROR",
			})
			c.Abort()
			return
		}

		if !hasPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Insufficient permissions",
				"code":  "PERMISSION_DENIED",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireScope checks if API key has required scope
func (m *APIKeyMiddleware) RequireScope(scope string) gin.HandlerFunc {
	return func(c *gin.Context) {
		apiKey := getAPIKeyFromContext(c)
		if apiKey == nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "API key not found in context",
				"code":  "MISSING_CONTEXT",
			})
			c.Abort()
			return
		}

		// Check scope
		scopes, exists := c.Get("api_key_scopes")
		if !exists {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Scopes not found in context",
				"code":  "MISSING_SCOPES",
			})
			c.Abort()
			return
		}

		scopeList, ok := scopes.([]string)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Invalid scope format",
				"code":  "INVALID_SCOPES",
			})
			c.Abort()
			return
		}

		// Check if required scope exists
		hasScope := false
		for _, s := range scopeList {
			if s == scope {
				hasScope = true
				break
			}
		}

		if !hasScope {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Required scope not found",
				"code":  "SCOPE_DENIED",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RateLimit applies rate limiting based on API key configuration
func (m *APIKeyMiddleware) RateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		apiKey := getAPIKeyFromContext(c)
		if apiKey == nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "API key not found in context",
				"code":  "MISSING_CONTEXT",
			})
			c.Abort()
			return
		}

		// Check rate limit - simplified for now
		// TODO: Implement proper rate limit checking
		rateLimitResult := &models.RateLimitInfo{
			Limit:     1000,
			Remaining: 999,
			Reset:     time.Now().Add(time.Hour).Unix(),
		}
		var err error
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to check rate limit",
				"code":  "RATE_LIMIT_ERROR",
			})
			c.Abort()
			return
		}

		// For now, always allow requests
		if false {
			c.Header("X-RateLimit-Limit", fmt.Sprintf("%d", rateLimitResult.Limit))
			c.Header("X-RateLimit-Remaining", "0")
			c.Header("X-RateLimit-Reset", fmt.Sprintf("%d", rateLimitResult.Reset))
			c.Header("Retry-After", fmt.Sprintf("%d", rateLimitResult.RetryAfter))

			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":       "Rate limit exceeded",
				"code":        "RATE_LIMIT_EXCEEDED",
				"retry_after": rateLimitResult.RetryAfter,
			})
			c.Abort()
			return
		}

		// Set rate limit headers
		c.Header("X-RateLimit-Limit", fmt.Sprintf("%d", rateLimitResult.Limit))
		c.Header("X-RateLimit-Remaining", fmt.Sprintf("%d", rateLimitResult.Remaining))
		c.Header("X-RateLimit-Reset", fmt.Sprintf("%d", rateLimitResult.Reset))

		c.Next()
	}
}

// IPWhitelist checks if request IP is in the API key's whitelist
func (m *APIKeyMiddleware) IPWhitelist() gin.HandlerFunc {
	return func(c *gin.Context) {
		apiKey := getAPIKeyFromContext(c)
		if apiKey == nil {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "API key not found in context",
				"code":  "MISSING_CONTEXT",
			})
			c.Abort()
			return
		}

		// Get client IP
		_ = c.ClientIP()

		// Check IP whitelist - simplified for now
		// TODO: Implement proper IP whitelist checking
		isAllowed := true
		var err error
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to check IP whitelist",
				"code":  "IP_WHITELIST_ERROR",
			})
			c.Abort()
			return
		}

		if !isAllowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "IP address not allowed",
				"code":  "IP_BLOCKED",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// TrackUsage tracks API key usage
func (m *APIKeyMiddleware) TrackUsage() gin.HandlerFunc {
	return func(c *gin.Context) {
		apiKey := getAPIKeyFromContext(c)
		if apiKey == nil {
			c.Next()
			return
		}

		// Record start time
		startTime := time.Now()

		// Process request
		c.Next()

		// Calculate response time
		responseTime := time.Since(startTime)

		// Create usage record
		usage := &models.APIKeyUsage{
			TenantID:     apiKey.TenantID,
			WebsiteID:    apiKey.WebsiteID,
			APIKeyID:     apiKey.ID,
			Endpoint:     c.Request.URL.Path,
			Method:       models.HTTPMethod(c.Request.Method),
			ResponseCode: c.Writer.Status(),
			ResponseTime: int(responseTime.Milliseconds()),
			RequestSize:  int(c.Request.ContentLength),
			ResponseSize: c.Writer.Size(),
			IPAddress:    c.ClientIP(),
			UserAgent:    c.GetHeader("User-Agent"),
			Referer:      c.GetHeader("Referer"),
			RequestID:    c.GetHeader("X-Request-ID"),
		}

		// Add error message if response is an error
		if c.Writer.Status() >= 400 {
			if errors, exists := c.Get("errors"); exists {
				if errorList, ok := errors.([]string); ok && len(errorList) > 0 {
					usage.ErrorMessage = errorList[0]
				}
			}
		}

		// Track usage asynchronously - simplified for now
		// TODO: Implement proper usage tracking
		_ = usage
	}
}

// extractAPIKey extracts API key from request headers
func extractAPIKey(c *gin.Context) string {
	// Try Authorization header first (Bearer token)
	authHeader := c.GetHeader("Authorization")
	if authHeader != "" {
		parts := strings.Split(authHeader, " ")
		if len(parts) == 2 && parts[0] == "Bearer" {
			return parts[1]
		}
	}

	// Try X-API-Key header
	apiKey := c.GetHeader("X-API-Key")
	if apiKey != "" {
		return apiKey
	}

	// Try query parameter
	return c.Query("api_key")
}

// getAPIKeyFromContext retrieves API key from Gin context
func getAPIKeyFromContext(c *gin.Context) *models.APIKeyResponse {
	if apiKey, exists := c.Get(APIKeyContextKey); exists {
		if key, ok := apiKey.(*models.APIKeyResponse); ok {
			return key
		}
	}
	return nil
}

// GetAPIKeyFromContext is a helper function to get API key from context
func GetAPIKeyFromContext(c *gin.Context) *models.APIKeyResponse {
	return getAPIKeyFromContext(c)
}

// GetTenantIDFromContext gets tenant ID from context
func GetTenantIDFromContext(c *gin.Context) uint {
	if tenantID, exists := c.Get("tenant_id"); exists {
		if id, ok := tenantID.(uint); ok {
			return id
		}
	}
	return 0
}

// GetWebsiteIDFromContext gets website ID from context
func GetWebsiteIDFromContext(c *gin.Context) uint {
	if websiteID, exists := c.Get("website_id"); exists {
		if id, ok := websiteID.(uint); ok {
			return id
		}
	}
	return 0
}
