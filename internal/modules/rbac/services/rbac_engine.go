package services

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories"
)

// PermissionCheck represents a permission check request
type PermissionCheck struct {
	UserID         uint   `json:"user_id"`
	TenantID       *uint  `json:"tenant_id,omitempty"`
	PermissionName string `json:"permission_name"`
	ContextID      *uint  `json:"context_id,omitempty"`
	ResourceID     *uint  `json:"resource_id,omitempty"`
}

// PermissionResult represents the result of a permission check
type PermissionResult struct {
	Allowed    bool               `json:"allowed"`
	Reason     string             `json:"reason,omitempty"`
	Roles      []string           `json:"roles,omitempty"`
	Permission *models.Permission `json:"permission,omitempty"`
}

// UserPermissionCache represents cached user permissions
type UserPermissionCache struct {
	UserID      uint            `json:"user_id"`
	TenantID    *uint           `json:"tenant_id,omitempty"`
	Permissions map[string]bool `json:"permissions"`
	Roles       []string        `json:"roles"`
	ExpiresAt   time.Time       `json:"expires_at"`
	LastUpdated time.Time       `json:"last_updated"`
}

// RBACEngine provides fast permission checking with caching
type RBACEngine interface {
	// Permission checking
	CheckPermission(ctx context.Context, req *PermissionCheck) (*PermissionResult, error)
	CheckPermissions(ctx context.Context, reqs []*PermissionCheck) ([]*PermissionResult, error)

	// User permissions
	GetUserPermissions(ctx context.Context, userID uint, tenantID *uint) (map[string]bool, error)
	GetUserRoles(ctx context.Context, userID uint, tenantID *uint) ([]string, error)
	RefreshUserPermissions(ctx context.Context, userID uint, tenantID *uint) error

	// Role hierarchy
	GetRoleHierarchy(ctx context.Context, roleID uint) ([]*models.Role, error)
	GetInheritedPermissions(ctx context.Context, roleID uint) ([]*models.Permission, error)

	// Permission patterns
	MatchPermissionPattern(ctx context.Context, userID uint, tenantID *uint, pattern string) ([]string, error)

	// Cache management
	ClearUserCache(ctx context.Context, userID uint, tenantID *uint) error
	ClearAllCache(ctx context.Context) error
	GetCacheStats(ctx context.Context) (map[string]interface{}, error)

	// Bulk operations
	PreloadUserPermissions(ctx context.Context, userIDs []uint, tenantID *uint) error
	WarmupCache(ctx context.Context, tenantID *uint) error
}

type rbacEngine struct {
	roleService        RoleService
	permissionService  PermissionService
	userRoleRepo       repositories.UserRoleRepository
	rolePermissionRepo repositories.RolePermissionRepository

	// Cache
	cache       map[string]*UserPermissionCache
	cacheMutex  sync.RWMutex
	cacheExpiry time.Duration

	// Configuration
	enableHierarchy bool
	enableCaching   bool
	maxCacheSize    int
}

// NewRBACEngine creates a new RBAC engine
func NewRBACEngine(
	roleService RoleService,
	permissionService PermissionService,
	userRoleRepo repositories.UserRoleRepository,
	rolePermissionRepo repositories.RolePermissionRepository,
) RBACEngine {
	return &rbacEngine{
		roleService:        roleService,
		permissionService:  permissionService,
		userRoleRepo:       userRoleRepo,
		rolePermissionRepo: rolePermissionRepo,
		cache:              make(map[string]*UserPermissionCache),
		cacheExpiry:        time.Minute * 15, // 15 minutes
		enableHierarchy:    true,
		enableCaching:      true,
		maxCacheSize:       10000,
	}
}

// CheckPermission checks if a user has a specific permission
func (e *rbacEngine) CheckPermission(ctx context.Context, req *PermissionCheck) (*PermissionResult, error) {
	result := &PermissionResult{
		Allowed: false,
	}

	// Get permission details
	permission, err := e.permissionService.GetPermissionByName(ctx, req.PermissionName)
	if err != nil {
		return result, fmt.Errorf("failed to get permission: %w", err)
	}

	if permission == nil {
		result.Reason = "Permission not found"
		return result, nil
	}

	result.Permission = permission

	// Check if permission is active
	if !permission.IsActive() {
		result.Reason = "Permission is not active"
		return result, nil
	}

	// Get user permissions from cache or database
	userPermissions, err := e.GetUserPermissions(ctx, req.UserID, req.TenantID)
	if err != nil {
		return result, fmt.Errorf("failed to get user permissions: %w", err)
	}

	// Check if user has the permission
	hasPermission, exists := userPermissions[req.PermissionName]
	if !exists || !hasPermission {
		result.Reason = "Permission not granted"
		return result, nil
	}

	// Additional validation for ownership-required permissions
	if permission.RequiresOwnership && req.ResourceID != nil {
		// For now, assume ownership validation is handled elsewhere
		// In a real implementation, you would check if the user owns the resource
	}

	// Additional validation for context-specific permissions
	if permission.Scope == models.PermissionScopeWebsite && req.ContextID != nil {
		// For now, assume context validation is handled elsewhere
		// In a real implementation, you would check if the user has permission in the specific context
	}

	// Get user roles for additional information
	userRoles, err := e.GetUserRoles(ctx, req.UserID, req.TenantID)
	if err != nil {
		return result, fmt.Errorf("failed to get user roles: %w", err)
	}

	result.Allowed = true
	result.Roles = userRoles
	result.Reason = "Permission granted"

	return result, nil
}

// CheckPermissions checks multiple permissions at once
func (e *rbacEngine) CheckPermissions(ctx context.Context, reqs []*PermissionCheck) ([]*PermissionResult, error) {
	results := make([]*PermissionResult, len(reqs))

	// Process each permission check
	for i, req := range reqs {
		result, err := e.CheckPermission(ctx, req)
		if err != nil {
			results[i] = &PermissionResult{
				Allowed: false,
				Reason:  fmt.Sprintf("Error checking permission: %v", err),
			}
		} else {
			results[i] = result
		}
	}

	return results, nil
}

// GetUserPermissions retrieves user permissions with caching
func (e *rbacEngine) GetUserPermissions(ctx context.Context, userID uint, tenantID *uint) (map[string]bool, error) {
	if !e.enableCaching {
		return e.loadUserPermissions(ctx, userID, tenantID)
	}

	cacheKey := e.getCacheKey(userID, tenantID)

	e.cacheMutex.RLock()
	cached, exists := e.cache[cacheKey]
	e.cacheMutex.RUnlock()

	// Return cached data if valid
	if exists && cached.ExpiresAt.After(time.Now()) {
		return cached.Permissions, nil
	}

	// Load from database
	permissions, err := e.loadUserPermissions(ctx, userID, tenantID)
	if err != nil {
		return nil, err
	}

	// Cache the result
	e.cacheUserPermissions(userID, tenantID, permissions, nil)

	return permissions, nil
}

// GetUserRoles retrieves user roles with caching
func (e *rbacEngine) GetUserRoles(ctx context.Context, userID uint, tenantID *uint) ([]string, error) {
	if !e.enableCaching {
		return e.loadUserRoles(ctx, userID, tenantID)
	}

	cacheKey := e.getCacheKey(userID, tenantID)

	e.cacheMutex.RLock()
	cached, exists := e.cache[cacheKey]
	e.cacheMutex.RUnlock()

	// Return cached data if valid
	if exists && cached.ExpiresAt.After(time.Now()) {
		return cached.Roles, nil
	}

	// Load from database
	roles, err := e.loadUserRoles(ctx, userID, tenantID)
	if err != nil {
		return nil, err
	}

	// Cache the result
	e.cacheUserPermissions(userID, tenantID, nil, roles)

	return roles, nil
}

// RefreshUserPermissions refreshes cached permissions for a user
func (e *rbacEngine) RefreshUserPermissions(ctx context.Context, userID uint, tenantID *uint) error {
	// Clear existing cache
	if err := e.ClearUserCache(ctx, userID, tenantID); err != nil {
		return err
	}

	// Reload permissions
	_, err := e.GetUserPermissions(ctx, userID, tenantID)
	return err
}

// GetRoleHierarchy returns the hierarchy of roles (parent roles)
func (e *rbacEngine) GetRoleHierarchy(ctx context.Context, roleID uint) ([]*models.Role, error) {
	if !e.enableHierarchy {
		// Return just the role itself
		role, err := e.roleService.GetRole(ctx, roleID)
		if err != nil {
			return nil, err
		}
		return []*models.Role{role}, nil
	}

	// Get role hierarchy from database
	// This would need to be implemented in the role service
	return e.roleService.GetRoleHierarchy(ctx, 0) // Placeholder
}

// GetInheritedPermissions returns permissions inherited from parent roles
func (e *rbacEngine) GetInheritedPermissions(ctx context.Context, roleID uint) ([]*models.Permission, error) {
	if !e.enableHierarchy {
		// Return direct permissions only
		return e.roleService.GetRolePermissions(ctx, roleID)
	}

	// Get role hierarchy
	hierarchy, err := e.GetRoleHierarchy(ctx, roleID)
	if err != nil {
		return nil, err
	}

	// Collect permissions from all roles in hierarchy
	permissionMap := make(map[uint]*models.Permission)

	for _, role := range hierarchy {
		rolePermissions, err := e.roleService.GetRolePermissions(ctx, role.ID)
		if err != nil {
			return nil, err
		}

		for _, perm := range rolePermissions {
			permissionMap[perm.ID] = perm
		}
	}

	// Convert map to slice
	permissions := make([]*models.Permission, 0, len(permissionMap))
	for _, perm := range permissionMap {
		permissions = append(permissions, perm)
	}

	return permissions, nil
}

// MatchPermissionPattern matches permissions against a pattern
func (e *rbacEngine) MatchPermissionPattern(ctx context.Context, userID uint, tenantID *uint, pattern string) ([]string, error) {
	userPermissions, err := e.GetUserPermissions(ctx, userID, tenantID)
	if err != nil {
		return nil, err
	}

	var matchedPermissions []string

	for permName, allowed := range userPermissions {
		if allowed && e.matchPattern(permName, pattern) {
			matchedPermissions = append(matchedPermissions, permName)
		}
	}

	return matchedPermissions, nil
}

// ClearUserCache clears cache for a specific user
func (e *rbacEngine) ClearUserCache(ctx context.Context, userID uint, tenantID *uint) error {
	if !e.enableCaching {
		return nil
	}

	cacheKey := e.getCacheKey(userID, tenantID)

	e.cacheMutex.Lock()
	defer e.cacheMutex.Unlock()

	delete(e.cache, cacheKey)
	return nil
}

// ClearAllCache clears all cached data
func (e *rbacEngine) ClearAllCache(ctx context.Context) error {
	if !e.enableCaching {
		return nil
	}

	e.cacheMutex.Lock()
	defer e.cacheMutex.Unlock()

	e.cache = make(map[string]*UserPermissionCache)
	return nil
}

// GetCacheStats returns cache statistics
func (e *rbacEngine) GetCacheStats(ctx context.Context) (map[string]interface{}, error) {
	e.cacheMutex.RLock()
	defer e.cacheMutex.RUnlock()

	stats := map[string]interface{}{
		"enabled":   e.enableCaching,
		"size":      len(e.cache),
		"max_size":  e.maxCacheSize,
		"expiry":    e.cacheExpiry.String(),
		"expired":   0,
		"hit_ratio": 0.0, // Would need tracking to calculate
	}

	// Count expired entries
	now := time.Now()
	expired := 0
	for _, entry := range e.cache {
		if entry.ExpiresAt.Before(now) {
			expired++
		}
	}
	stats["expired"] = expired

	return stats, nil
}

// PreloadUserPermissions preloads permissions for multiple users
func (e *rbacEngine) PreloadUserPermissions(ctx context.Context, userIDs []uint, tenantID *uint) error {
	if !e.enableCaching {
		return nil
	}

	// Load permissions for each user
	for _, userID := range userIDs {
		_, err := e.GetUserPermissions(ctx, userID, tenantID)
		if err != nil {
			return fmt.Errorf("failed to preload permissions for user %d: %w", userID, err)
		}
	}

	return nil
}

// WarmupCache warms up the cache for a tenant
func (e *rbacEngine) WarmupCache(ctx context.Context, tenantID *uint) error {
	if !e.enableCaching {
		return nil
	}

	// This would typically load common permissions and roles
	// For now, just return success
	return nil
}

// loadUserPermissions loads user permissions from database
func (e *rbacEngine) loadUserPermissions(ctx context.Context, userID uint, tenantID *uint) (map[string]bool, error) {
	permissions := make(map[string]bool)

	// Get user roles
	userRoles, err := e.userRoleRepo.GetRolesByUser(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user roles: %w", err)
	}

	// Get permissions for each role
	for _, userRole := range userRoles {
		// Skip inactive roles
		if userRole.Status != models.UserRoleStatusActive {
			continue
		}

		// Get role permissions
		rolePermissions, err := e.roleService.GetRolePermissions(ctx, userRole.RoleID)
		if err != nil {
			return nil, fmt.Errorf("failed to get role permissions: %w", err)
		}

		// Add permissions to map
		for _, perm := range rolePermissions {
			if perm.IsActive() {
				permissions[perm.Name] = true
			}
		}
	}

	return permissions, nil
}

// loadUserRoles loads user roles from database
func (e *rbacEngine) loadUserRoles(ctx context.Context, userID uint, tenantID *uint) ([]string, error) {
	userRoles, err := e.userRoleRepo.GetRolesByUser(ctx, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to get user roles: %w", err)
	}

	var roleNames []string
	for _, userRole := range userRoles {
		if userRole.Status == models.UserRoleStatusActive {
			// Get role details
			role, err := e.roleService.GetRole(ctx, userRole.RoleID)
			if err != nil {
				return nil, fmt.Errorf("failed to get role: %w", err)
			}

			if role != nil && role.Status == models.RoleStatusActive {
				roleNames = append(roleNames, role.Name)
			}
		}
	}

	return roleNames, nil
}

// cacheUserPermissions caches user permissions
func (e *rbacEngine) cacheUserPermissions(userID uint, tenantID *uint, permissions map[string]bool, roles []string) {
	if !e.enableCaching {
		return
	}

	cacheKey := e.getCacheKey(userID, tenantID)

	e.cacheMutex.Lock()
	defer e.cacheMutex.Unlock()

	// Check cache size limit
	if len(e.cache) >= e.maxCacheSize {
		// Remove oldest entries (simple LRU)
		e.evictOldEntries()
	}

	// Get existing cache or create new
	cached, exists := e.cache[cacheKey]
	if !exists {
		cached = &UserPermissionCache{
			UserID:   userID,
			TenantID: tenantID,
		}
		e.cache[cacheKey] = cached
	}

	// Update cache
	if permissions != nil {
		cached.Permissions = permissions
	}
	if roles != nil {
		cached.Roles = roles
	}

	cached.ExpiresAt = time.Now().Add(e.cacheExpiry)
	cached.LastUpdated = time.Now()
}

// getCacheKey generates a cache key for user permissions
func (e *rbacEngine) getCacheKey(userID uint, tenantID *uint) string {
	if tenantID != nil {
		return fmt.Sprintf("user:%d:tenant:%d", userID, *tenantID)
	}
	return fmt.Sprintf("user:%d", userID)
}

// evictOldEntries removes old cache entries
func (e *rbacEngine) evictOldEntries() {
	now := time.Now()

	// Remove expired entries first
	for key, entry := range e.cache {
		if entry.ExpiresAt.Before(now) {
			delete(e.cache, key)
		}
	}

	// If still over limit, remove oldest entries
	if len(e.cache) >= e.maxCacheSize {
		// Find oldest entries
		var oldestKeys []string
		oldestTime := time.Now()

		for key, entry := range e.cache {
			if entry.LastUpdated.Before(oldestTime) {
				oldestKeys = []string{key}
				oldestTime = entry.LastUpdated
			} else if entry.LastUpdated.Equal(oldestTime) {
				oldestKeys = append(oldestKeys, key)
			}
		}

		// Remove oldest entries
		for _, key := range oldestKeys {
			delete(e.cache, key)
			if len(e.cache) < e.maxCacheSize {
				break
			}
		}
	}
}

// matchPattern checks if a permission name matches a pattern
func (e *rbacEngine) matchPattern(permName, pattern string) bool {
	// Simple wildcard matching
	if pattern == "*" {
		return true
	}

	// Split both into parts
	permParts := strings.Split(permName, ".")
	patternParts := strings.Split(pattern, ".")

	if len(permParts) != len(patternParts) {
		return false
	}

	// Check each part
	for i, patternPart := range patternParts {
		if patternPart == "*" {
			continue
		}
		if patternPart != permParts[i] {
			return false
		}
	}

	return true
}
