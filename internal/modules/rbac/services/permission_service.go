package services

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories"
)

var (
	ErrPermissionNotFound           = errors.New("permission not found")
	ErrPermissionAlreadyExists      = errors.New("permission already exists")
	ErrCannotDeleteSystemPermission = errors.New("cannot delete system permission")
	ErrInvalidPermissionPattern     = errors.New("invalid permission pattern")
)

// PermissionService handles permission business logic
type PermissionService interface {
	// Basic CRUD operations
	CreatePermission(ctx context.Context, req *models.PermissionCreateRequest) (*models.Permission, error)
	GetPermission(ctx context.Context, id uint) (*models.Permission, error)
	GetPermissionByName(ctx context.Context, name string) (*models.Permission, error)
	UpdatePermission(ctx context.Context, id uint, req *models.PermissionUpdateRequest) (*models.Permission, error)
	DeletePermission(ctx context.Context, id uint) error

	// Permission discovery
	GetPermissionsByModule(ctx context.Context, module string, page, pageSize int) ([]*models.Permission, int64, error)
	GetPermissionsByResource(ctx context.Context, resource string, page, pageSize int) ([]*models.Permission, int64, error)
	GetPermissionsByScope(ctx context.Context, scope models.PermissionScope) ([]*models.Permission, error)
	GetPermissionsByRiskLevel(ctx context.Context, riskLevel models.PermissionRiskLevel) ([]*models.Permission, error)

	// System and active permissions
	GetSystemPermissions(ctx context.Context) ([]*models.Permission, error)
	GetActivePermissions(ctx context.Context) ([]*models.Permission, error)

	// Permission checking and validation
	ValidatePermission(ctx context.Context, userID uint, tenantID *uint, permissionName string, contextID *uint, ownedResourceID *uint) (bool, error)
	CheckPermission(ctx context.Context, userID uint, tenantID *uint, permissionName string) (bool, error)
	CheckOwnershipPermission(ctx context.Context, userID uint, tenantID *uint, permissionName string, resourceID uint) (bool, error)
	CheckPermissionWithContext(ctx context.Context, userID uint, tenantID *uint, permissionName string, contextID uint) (bool, error)

	// Permission pattern matching
	MatchPermissionPattern(ctx context.Context, userID uint, tenantID *uint, pattern string) ([]*models.Permission, error)
	ValidatePermissionPattern(pattern string) error

	// Bulk operations
	CreatePermissionsBulk(ctx context.Context, req *models.PermissionBulkCreateRequest) ([]*models.Permission, error)
	UpdatePermissionsBulk(ctx context.Context, req *models.PermissionBulkUpdateRequest) error
	GetPermissionsByIDs(ctx context.Context, ids []uint) ([]*models.Permission, error)

	// Permission analysis
	GetPermissionRoles(ctx context.Context, permissionID uint) ([]*models.Role, error)
	GetUnusedPermissions(ctx context.Context) ([]*models.Permission, error)
	GetPermissionUsageCount(ctx context.Context, permissionID uint) (int64, error)

	// Status management
	ActivatePermission(ctx context.Context, permissionID uint) error
	DeactivatePermission(ctx context.Context, permissionID uint) error
	DeprecatePermission(ctx context.Context, permissionID uint) error

	// Search and filtering
	SearchPermissions(ctx context.Context, query string, filters *repositories.PermissionFilters) ([]*models.Permission, error)

	// Module discovery
	GetModules(ctx context.Context) ([]string, error)
	GetResourcesByModule(ctx context.Context, module string) ([]string, error)
	GetActionsByResource(ctx context.Context, resource string) ([]string, error)

	// Validation
	ValidatePermissionModel(ctx context.Context, permission *models.Permission) error
	CanDeletePermission(ctx context.Context, permissionID uint) error
}

type permissionService struct {
	permissionRepo     repositories.PermissionRepository
	rolePermissionRepo repositories.RolePermissionRepository
	userRoleRepo       repositories.UserRoleRepository
}

// NewPermissionService creates a new permission service
func NewPermissionService(
	permissionRepo repositories.PermissionRepository,
	rolePermissionRepo repositories.RolePermissionRepository,
	userRoleRepo repositories.UserRoleRepository,
) PermissionService {
	return &permissionService{
		permissionRepo:     permissionRepo,
		rolePermissionRepo: rolePermissionRepo,
		userRoleRepo:       userRoleRepo,
	}
}

// CreatePermission creates a new permission
func (s *permissionService) CreatePermission(ctx context.Context, req *models.PermissionCreateRequest) (*models.Permission, error) {
	// Check if permission already exists
	exists, err := s.permissionRepo.ExistsByModuleResourceAction(ctx, req.Module, req.Resource, req.Action)
	if err != nil {
		return nil, fmt.Errorf("failed to check permission existence: %w", err)
	}
	if exists {
		return nil, ErrPermissionAlreadyExists
	}

	// Create permission
	permission := &models.Permission{
		Name:               req.Name,
		DisplayName:        req.DisplayName,
		Description:        req.Description,
		Module:             req.Module,
		Resource:           req.Resource,
		Action:             req.Action,
		Scope:              req.Scope,
		RequiresOwnership:  req.RequiresOwnership,
		IsSystemPermission: req.IsSystemPermission,
		RiskLevel:          req.RiskLevel,
		Conditions:         req.Conditions,
		Limitations:        req.Limitations,
		Status:             models.PermissionStatusActive,
	}

	// Set defaults
	if permission.Scope == "" {
		permission.Scope = models.PermissionScopeTenant
	}
	if permission.RiskLevel == "" {
		permission.RiskLevel = models.PermissionRiskLevelLow
	}

	// Validate permission
	if err := s.ValidatePermissionModel(ctx, permission); err != nil {
		return nil, err
	}

	// Create permission in database
	if err := s.permissionRepo.Create(ctx, permission); err != nil {
		return nil, fmt.Errorf("failed to create permission: %w", err)
	}

	return permission, nil
}

// GetPermission retrieves a permission by ID
func (s *permissionService) GetPermission(ctx context.Context, id uint) (*models.Permission, error) {
	permission, err := s.permissionRepo.GetByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get permission: %w", err)
	}
	if permission == nil {
		return nil, ErrPermissionNotFound
	}

	return permission, nil
}

// GetPermissionByName retrieves a permission by name
func (s *permissionService) GetPermissionByName(ctx context.Context, name string) (*models.Permission, error) {
	permission, err := s.permissionRepo.GetByName(ctx, name)
	if err != nil {
		return nil, fmt.Errorf("failed to get permission by name: %w", err)
	}
	if permission == nil {
		return nil, ErrPermissionNotFound
	}

	return permission, nil
}

// UpdatePermission updates a permission
func (s *permissionService) UpdatePermission(ctx context.Context, id uint, req *models.PermissionUpdateRequest) (*models.Permission, error) {
	// Get existing permission
	permission, err := s.GetPermission(ctx, id)
	if err != nil {
		return nil, err
	}

	// Update fields
	if req.Name != nil {
		permission.Name = *req.Name
	}
	if req.DisplayName != nil {
		permission.DisplayName = *req.DisplayName
	}
	if req.Description != nil {
		permission.Description = req.Description
	}
	if req.Module != nil {
		permission.Module = *req.Module
	}
	if req.Resource != nil {
		permission.Resource = *req.Resource
	}
	if req.Action != nil {
		permission.Action = *req.Action
	}
	if req.Scope != nil {
		permission.Scope = *req.Scope
	}
	if req.RequiresOwnership != nil {
		permission.RequiresOwnership = *req.RequiresOwnership
	}
	if req.IsSystemPermission != nil {
		permission.IsSystemPermission = *req.IsSystemPermission
	}
	if req.RiskLevel != nil {
		permission.RiskLevel = *req.RiskLevel
	}
	if req.Status != nil {
		permission.Status = *req.Status
	}
	if req.Conditions != nil {
		permission.Conditions = *req.Conditions
	}
	if req.Limitations != nil {
		permission.Limitations = *req.Limitations
	}

	// Validate permission
	if err := s.ValidatePermissionModel(ctx, permission); err != nil {
		return nil, err
	}

	// Update permission in database
	if err := s.permissionRepo.Update(ctx, permission); err != nil {
		return nil, fmt.Errorf("failed to update permission: %w", err)
	}

	return permission, nil
}

// DeletePermission deletes a permission
func (s *permissionService) DeletePermission(ctx context.Context, id uint) error {
	// Check if permission can be deleted
	if err := s.CanDeletePermission(ctx, id); err != nil {
		return err
	}

	// Delete permission
	if err := s.permissionRepo.Delete(ctx, id); err != nil {
		return fmt.Errorf("failed to delete permission: %w", err)
	}

	return nil
}

// GetPermissionsByModule retrieves permissions by module with pagination
func (s *permissionService) GetPermissionsByModule(ctx context.Context, module string, page, pageSize int) ([]*models.Permission, int64, error) {
	offset := (page - 1) * pageSize

	permissions, err := s.permissionRepo.GetByModule(ctx, module, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get permissions by module: %w", err)
	}

	count, err := s.permissionRepo.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count permissions: %w", err)
	}

	return permissions, count, nil
}

// GetPermissionsByResource retrieves permissions by resource with pagination
func (s *permissionService) GetPermissionsByResource(ctx context.Context, resource string, page, pageSize int) ([]*models.Permission, int64, error) {
	offset := (page - 1) * pageSize

	permissions, err := s.permissionRepo.GetByResource(ctx, resource, pageSize, offset)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to get permissions by resource: %w", err)
	}

	count, err := s.permissionRepo.Count(ctx)
	if err != nil {
		return nil, 0, fmt.Errorf("failed to count permissions: %w", err)
	}

	return permissions, count, nil
}

// GetPermissionsByScope retrieves permissions by scope
func (s *permissionService) GetPermissionsByScope(ctx context.Context, scope models.PermissionScope) ([]*models.Permission, error) {
	permissions, err := s.permissionRepo.GetByScope(ctx, scope)
	if err != nil {
		return nil, fmt.Errorf("failed to get permissions by scope: %w", err)
	}

	return permissions, nil
}

// GetPermissionsByRiskLevel retrieves permissions by risk level
func (s *permissionService) GetPermissionsByRiskLevel(ctx context.Context, riskLevel models.PermissionRiskLevel) ([]*models.Permission, error) {
	permissions, err := s.permissionRepo.GetByRiskLevel(ctx, riskLevel)
	if err != nil {
		return nil, fmt.Errorf("failed to get permissions by risk level: %w", err)
	}

	return permissions, nil
}

// GetSystemPermissions retrieves system permissions
func (s *permissionService) GetSystemPermissions(ctx context.Context) ([]*models.Permission, error) {
	permissions, err := s.permissionRepo.GetSystemPermissions(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get system permissions: %w", err)
	}

	return permissions, nil
}

// GetActivePermissions retrieves active permissions
func (s *permissionService) GetActivePermissions(ctx context.Context) ([]*models.Permission, error) {
	permissions, err := s.permissionRepo.GetActivePermissions(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get active permissions: %w", err)
	}

	return permissions, nil
}

// ValidatePermission validates a user's permission with full context
func (s *permissionService) ValidatePermission(ctx context.Context, userID uint, tenantID *uint, permissionName string, contextID *uint, ownedResourceID *uint) (bool, error) {
	// Get user roles
	userRoles, err := s.userRoleRepo.GetRolesByUser(ctx, userID)
	if err != nil {
		return false, fmt.Errorf("failed to get user roles: %w", err)
	}

	if len(userRoles) == 0 {
		return false, nil
	}

	// Get permission by name
	permission, err := s.GetPermissionByName(ctx, permissionName)
	if err != nil {
		return false, err
	}

	// Check each role for the permission
	for _, userRole := range userRoles {
		// Skip inactive roles
		if userRole.Status != models.UserRoleStatusActive {
			continue
		}

		// Check if role has the permission
		hasPermission, err := s.rolePermissionRepo.HasPermission(ctx, userRole.RoleID, permission.ID)
		if err != nil {
			return false, fmt.Errorf("failed to check role permission: %w", err)
		}

		if hasPermission {
			// Check ownership requirement
			if permission.RequiresOwnership && ownedResourceID != nil {
				// Additional ownership validation logic would go here
				// For now, assume ownership is validated elsewhere
			}

			// Check scope and context
			if permission.Scope == models.PermissionScopeWebsite && contextID != nil {
				// Additional context validation logic would go here
			}

			return true, nil
		}
	}

	return false, nil
}

// CheckPermission checks if user has a basic permission
func (s *permissionService) CheckPermission(ctx context.Context, userID uint, tenantID *uint, permissionName string) (bool, error) {
	return s.ValidatePermission(ctx, userID, tenantID, permissionName, nil, nil)
}

// CheckOwnershipPermission checks if user has permission with ownership requirement
func (s *permissionService) CheckOwnershipPermission(ctx context.Context, userID uint, tenantID *uint, permissionName string, resourceID uint) (bool, error) {
	return s.ValidatePermission(ctx, userID, tenantID, permissionName, nil, &resourceID)
}

// CheckPermissionWithContext checks if user has permission in a specific context
func (s *permissionService) CheckPermissionWithContext(ctx context.Context, userID uint, tenantID *uint, permissionName string, contextID uint) (bool, error) {
	return s.ValidatePermission(ctx, userID, tenantID, permissionName, &contextID, nil)
}

// MatchPermissionPattern matches permissions against a pattern
func (s *permissionService) MatchPermissionPattern(ctx context.Context, userID uint, tenantID *uint, pattern string) ([]*models.Permission, error) {
	// Validate pattern
	if err := s.ValidatePermissionPattern(pattern); err != nil {
		return nil, err
	}

	// Get permissions by pattern
	permissions, err := s.permissionRepo.GetPermissionsByPattern(ctx, pattern)
	if err != nil {
		return nil, fmt.Errorf("failed to get permissions by pattern: %w", err)
	}

	// Filter permissions based on user's roles
	var allowedPermissions []*models.Permission

	for _, permission := range permissions {
		hasPermission, err := s.CheckPermission(ctx, userID, tenantID, permission.Name)
		if err != nil {
			return nil, err
		}

		if hasPermission {
			allowedPermissions = append(allowedPermissions, permission)
		}
	}

	return allowedPermissions, nil
}

// ValidatePermissionPattern validates a permission pattern
func (s *permissionService) ValidatePermissionPattern(pattern string) error {
	// Pattern should be in format: module.resource.action
	// Support wildcards: * for any, ** for any including dots
	parts := strings.Split(pattern, ".")
	if len(parts) != 3 {
		return ErrInvalidPermissionPattern
	}

	for _, part := range parts {
		if part == "" {
			return ErrInvalidPermissionPattern
		}
		// Allow wildcards and alphanumeric characters with underscores and hyphens
		if !isValidPatternPart(part) {
			return ErrInvalidPermissionPattern
		}
	}

	return nil
}

// isValidPatternPart validates a single part of a permission pattern
func isValidPatternPart(part string) bool {
	if part == "*" || part == "**" {
		return true
	}

	for _, char := range part {
		if !((char >= 'a' && char <= 'z') ||
			(char >= '0' && char <= '9') ||
			char == '_' || char == '-') {
			return false
		}
	}

	return true
}

// CreatePermissionsBulk creates permissions in bulk
func (s *permissionService) CreatePermissionsBulk(ctx context.Context, req *models.PermissionBulkCreateRequest) ([]*models.Permission, error) {
	var permissions []*models.Permission

	for _, permReq := range req.Permissions {
		// Check if permission already exists
		exists, err := s.permissionRepo.ExistsByModuleResourceAction(ctx, permReq.Module, permReq.Resource, permReq.Action)
		if err != nil {
			return nil, fmt.Errorf("failed to check permission existence: %w", err)
		}
		if exists {
			return nil, fmt.Errorf("permission %s.%s.%s already exists", permReq.Module, permReq.Resource, permReq.Action)
		}

		// Create permission
		permission := &models.Permission{
			Name:               permReq.Name,
			DisplayName:        permReq.DisplayName,
			Description:        permReq.Description,
			Module:             permReq.Module,
			Resource:           permReq.Resource,
			Action:             permReq.Action,
			Scope:              permReq.Scope,
			RequiresOwnership:  permReq.RequiresOwnership,
			IsSystemPermission: permReq.IsSystemPermission,
			RiskLevel:          permReq.RiskLevel,
			Conditions:         permReq.Conditions,
			Limitations:        permReq.Limitations,
			Status:             models.PermissionStatusActive,
		}

		// Set defaults
		if permission.Scope == "" {
			permission.Scope = models.PermissionScopeTenant
		}
		if permission.RiskLevel == "" {
			permission.RiskLevel = models.PermissionRiskLevelLow
		}

		// Validate permission
		if err := s.ValidatePermissionModel(ctx, permission); err != nil {
			return nil, err
		}

		permissions = append(permissions, permission)
	}

	// Create permissions in bulk
	if err := s.permissionRepo.CreateBulk(ctx, permissions); err != nil {
		return nil, fmt.Errorf("failed to create permissions in bulk: %w", err)
	}

	return permissions, nil
}

// UpdatePermissionsBulk updates permissions in bulk
func (s *permissionService) UpdatePermissionsBulk(ctx context.Context, req *models.PermissionBulkUpdateRequest) error {
	// Get existing permissions
	permissions, err := s.GetPermissionsByIDs(ctx, req.PermissionIDs)
	if err != nil {
		return err
	}

	if len(permissions) != len(req.PermissionIDs) {
		return fmt.Errorf("some permissions not found")
	}

	// Update each permission
	for _, permission := range permissions {
		_, err := s.UpdatePermission(ctx, permission.ID, &req.Updates)
		if err != nil {
			return fmt.Errorf("failed to update permission %d: %w", permission.ID, err)
		}
	}

	return nil
}

// GetPermissionsByIDs retrieves permissions by IDs
func (s *permissionService) GetPermissionsByIDs(ctx context.Context, ids []uint) ([]*models.Permission, error) {
	permissions, err := s.permissionRepo.GetByIDs(ctx, ids)
	if err != nil {
		return nil, fmt.Errorf("failed to get permissions by IDs: %w", err)
	}

	return permissions, nil
}

// GetPermissionRoles retrieves roles that have a specific permission
func (s *permissionService) GetPermissionRoles(ctx context.Context, permissionID uint) ([]*models.Role, error) {
	roles, err := s.permissionRepo.GetPermissionRoles(ctx, permissionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get permission roles: %w", err)
	}

	return roles, nil
}

// GetUnusedPermissions retrieves permissions that are not assigned to any role
func (s *permissionService) GetUnusedPermissions(ctx context.Context) ([]*models.Permission, error) {
	// Get all permissions
	allPermissions, err := s.GetActivePermissions(ctx)
	if err != nil {
		return nil, err
	}

	var unusedPermissions []*models.Permission

	// Check each permission
	for _, permission := range allPermissions {
		roles, err := s.GetPermissionRoles(ctx, permission.ID)
		if err != nil {
			return nil, err
		}

		if len(roles) == 0 {
			unusedPermissions = append(unusedPermissions, permission)
		}
	}

	return unusedPermissions, nil
}

// GetPermissionUsageCount gets the usage count of a permission (number of roles using it)
func (s *permissionService) GetPermissionUsageCount(ctx context.Context, permissionID uint) (int64, error) {
	roles, err := s.GetPermissionRoles(ctx, permissionID)
	if err != nil {
		return 0, err
	}

	return int64(len(roles)), nil
}

// ActivatePermission activates a permission
func (s *permissionService) ActivatePermission(ctx context.Context, permissionID uint) error {
	if err := s.permissionRepo.UpdateStatus(ctx, permissionID, models.PermissionStatusActive); err != nil {
		return fmt.Errorf("failed to activate permission: %w", err)
	}

	return nil
}

// DeactivatePermission deactivates a permission
func (s *permissionService) DeactivatePermission(ctx context.Context, permissionID uint) error {
	if err := s.permissionRepo.UpdateStatus(ctx, permissionID, models.PermissionStatusInactive); err != nil {
		return fmt.Errorf("failed to deactivate permission: %w", err)
	}

	return nil
}

// DeprecatePermission marks a permission as deprecated
func (s *permissionService) DeprecatePermission(ctx context.Context, permissionID uint) error {
	if err := s.permissionRepo.UpdateStatus(ctx, permissionID, models.PermissionStatusDeprecated); err != nil {
		return fmt.Errorf("failed to deprecate permission: %w", err)
	}

	return nil
}

// SearchPermissions searches permissions
func (s *permissionService) SearchPermissions(ctx context.Context, query string, filters *repositories.PermissionFilters) ([]*models.Permission, error) {
	permissions, err := s.permissionRepo.Search(ctx, query, filters)
	if err != nil {
		return nil, fmt.Errorf("failed to search permissions: %w", err)
	}

	return permissions, nil
}

// GetModules retrieves all available modules
func (s *permissionService) GetModules(ctx context.Context) ([]string, error) {
	modules, err := s.permissionRepo.GetModules(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get modules: %w", err)
	}

	return modules, nil
}

// GetResourcesByModule retrieves resources by module
func (s *permissionService) GetResourcesByModule(ctx context.Context, module string) ([]string, error) {
	resources, err := s.permissionRepo.GetResourcesByModule(ctx, module)
	if err != nil {
		return nil, fmt.Errorf("failed to get resources by module: %w", err)
	}

	return resources, nil
}

// GetActionsByResource retrieves actions by resource
func (s *permissionService) GetActionsByResource(ctx context.Context, resource string) ([]string, error) {
	actions, err := s.permissionRepo.GetActionsByResource(ctx, resource)
	if err != nil {
		return nil, fmt.Errorf("failed to get actions by resource: %w", err)
	}

	return actions, nil
}

// ValidatePermissionModel validates a permission model
func (s *permissionService) ValidatePermissionModel(ctx context.Context, permission *models.Permission) error {
	// Basic validation
	if permission.Name == "" {
		return fmt.Errorf("permission name is required")
	}
	if permission.DisplayName == "" {
		return fmt.Errorf("permission display name is required")
	}
	if permission.Module == "" {
		return fmt.Errorf("permission module is required")
	}
	if permission.Resource == "" {
		return fmt.Errorf("permission resource is required")
	}
	if permission.Action == "" {
		return fmt.Errorf("permission action is required")
	}

	// Scope validation
	if permission.Scope != models.PermissionScopeTenant &&
		permission.Scope != models.PermissionScopeWebsite &&
		permission.Scope != models.PermissionScopeGlobal {
		return fmt.Errorf("invalid permission scope")
	}

	// Risk level validation
	if permission.RiskLevel != models.PermissionRiskLevelLow &&
		permission.RiskLevel != models.PermissionRiskLevelMedium &&
		permission.RiskLevel != models.PermissionRiskLevelHigh &&
		permission.RiskLevel != models.PermissionRiskLevelCritical {
		return fmt.Errorf("invalid permission risk level")
	}

	// Status validation
	if permission.Status != models.PermissionStatusActive &&
		permission.Status != models.PermissionStatusInactive &&
		permission.Status != models.PermissionStatusDeprecated {
		return fmt.Errorf("invalid permission status")
	}

	return nil
}

// CanDeletePermission checks if a permission can be deleted
func (s *permissionService) CanDeletePermission(ctx context.Context, permissionID uint) error {
	// Get permission
	permission, err := s.GetPermission(ctx, permissionID)
	if err != nil {
		return err
	}

	// Check if system permission
	if permission.IsSystemPermission {
		return ErrCannotDeleteSystemPermission
	}

	// Check if permission is in use
	usageCount, err := s.GetPermissionUsageCount(ctx, permissionID)
	if err != nil {
		return err
	}
	if usageCount > 0 {
		return fmt.Errorf("permission is in use by %d roles", usageCount)
	}

	return nil
}
