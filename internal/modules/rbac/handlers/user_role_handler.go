package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/services"
)

// UserRoleHandler handles user role assignment HTTP requests
type UserRoleHandler struct {
	userRoleService services.UserRoleService
	rbacEngine      services.RBACEngine
}

// NewUserRoleHandler creates a new user role handler
func NewUserRoleHandler(userRoleService services.UserRoleService, rbacEngine services.RBACEngine) *UserRoleHandler {
	return &UserRoleHandler{
		userRoleService: userRoleService,
		rbacEngine:      rbacEngine,
	}
}

// AssignRole assigns a role to a user
func (h *UserRoleHandler) AssignRole(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.J<PERSON>N(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID",
		})
		return
	}

	var req models.UserRoleCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Set user ID from URL parameter
	req.UserID = uint(userID)

	// Validation will be handled by the service layer

	// Assign role
	userRole, err := h.userRoleService.AssignRole(c.Request.Context(), &req)
	if err != nil {
		if err == services.ErrUserRoleAlreadyExists {
			c.JSON(http.StatusConflict, gin.H{
				"error": "User already has this role",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to assign role",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Role assigned successfully",
		"data":    userRole,
	})
}

// AssignRoles assigns multiple roles to a user
func (h *UserRoleHandler) AssignRoles(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID",
		})
		return
	}

	var req struct {
		RoleIDs []uint `json:"role_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Get current user ID for audit (authentication is handled by middleware)
	currentUserID, exists := c.Get("user_id")
	if !exists {
		// This should not happen if middleware is properly configured
		currentUserID = uint(0) // Default value for audit log
	}

	// Assign roles
	userRoles, err := h.userRoleService.AssignRoles(c.Request.Context(), uint(userID), req.RoleIDs, currentUserID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to assign roles",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Roles assigned successfully",
		"data":    userRoles,
	})
}

// AssignTemporaryRole assigns a temporary role to a user
func (h *UserRoleHandler) AssignTemporaryRole(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID",
		})
		return
	}

	var req struct {
		RoleID     uint      `json:"role_id" binding:"required"`
		ValidUntil time.Time `json:"valid_until" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Create temporary role request
	createReq := &models.UserRoleCreateRequest{
		UserID:      uint(userID),
		RoleID:      req.RoleID,
		IsTemporary: true,
		ValidUntil:  &req.ValidUntil,
	}

	// Assign temporary role
	userRole, err := h.userRoleService.AssignTemporaryRole(c.Request.Context(), createReq, req.ValidUntil)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to assign temporary role",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Temporary role assigned successfully",
		"data":    userRole,
	})
}

// RevokeRole revokes a role from a user
func (h *UserRoleHandler) RevokeRole(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID",
		})
		return
	}

	roleID, err := strconv.ParseUint(c.Param("roleId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid role ID",
		})
		return
	}

	// Get current user ID for audit (authentication is handled by middleware)
	currentUserID, exists := c.Get("user_id")
	if !exists {
		// This should not happen if middleware is properly configured
		currentUserID = uint(0) // Default value for audit log
	}

	// Revoke role
	err = h.userRoleService.RevokeRoleByUserAndRole(c.Request.Context(), uint(userID), uint(roleID), currentUserID.(uint))
	if err != nil {
		if err == services.ErrUserRoleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "User role not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to revoke role",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Role revoked successfully",
	})
}

// RevokeUserRole revokes a user role by ID
func (h *UserRoleHandler) RevokeUserRole(c *gin.Context) {
	userRoleID, err := strconv.ParseUint(c.Param("userRoleId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user role ID",
		})
		return
	}

	// Get current user ID for audit (authentication is handled by middleware)
	currentUserID, exists := c.Get("user_id")
	if !exists {
		// This should not happen if middleware is properly configured
		currentUserID = uint(0) // Default value for audit log
	}

	// Revoke user role
	err = h.userRoleService.RevokeRole(c.Request.Context(), uint(userRoleID), currentUserID.(uint))
	if err != nil {
		if err == services.ErrUserRoleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "User role not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to revoke user role",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "User role revoked successfully",
	})
}

// GetUserRoles retrieves roles for a user
func (h *UserRoleHandler) GetUserRoles(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID",
		})
		return
	}

	// Check if only active roles are requested
	activeOnly := c.Query("active") == "true"

	var userRoles []*models.UserRole
	if activeOnly {
		userRoles, err = h.userRoleService.GetActiveUserRoles(c.Request.Context(), uint(userID))
	} else {
		userRoles, err = h.userRoleService.GetUserRoles(c.Request.Context(), uint(userID))
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get user roles",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": userRoles,
	})
}

// GetUserPermissions retrieves permissions for a user
func (h *UserRoleHandler) GetUserPermissions(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID",
		})
		return
	}

	// Get tenant ID from context
	var tenantID *uint
	if tid, exists := c.Get("tenant_id"); exists {
		if id, ok := tid.(uint); ok {
			tenantID = &id
		}
	}

	// Get user permissions
	permissions, err := h.rbacEngine.GetUserPermissions(c.Request.Context(), uint(userID), tenantID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get user permissions",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": permissions,
	})
}

// GetUserEffectivePermissions retrieves effective permissions for a user (including inherited)
func (h *UserRoleHandler) GetUserEffectivePermissions(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID",
		})
		return
	}

	// Get effective roles (including inherited)
	effectiveRoles, err := h.userRoleService.GetEffectiveRoles(c.Request.Context(), uint(userID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get effective roles",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": effectiveRoles,
	})
}

// GetUserRolesByContext retrieves user roles by context
func (h *UserRoleHandler) GetUserRolesByContext(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID",
		})
		return
	}

	contextType := c.Query("context_type")
	if contextType == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Context type is required",
		})
		return
	}

	contextID, err := strconv.ParseUint(c.Query("context_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid context ID",
		})
		return
	}

	// Validate context type
	var userRoleContextType models.UserRoleContextType
	switch contextType {
	case "tenant":
		userRoleContextType = models.UserRoleContextTypeTenant
	case "website":
		userRoleContextType = models.UserRoleContextTypeWebsite
	case "global":
		userRoleContextType = models.UserRoleContextTypeGlobal
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid context type",
		})
		return
	}

	// Get user roles by context
	userRoles, err := h.userRoleService.GetUserRolesByContext(c.Request.Context(), uint(userID), userRoleContextType, uint(contextID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get user roles by context",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": userRoles,
	})
}

// GetPrimaryRole retrieves the primary role for a user in a context
func (h *UserRoleHandler) GetPrimaryRole(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID",
		})
		return
	}

	contextType := c.Query("context_type")
	if contextType == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Context type is required",
		})
		return
	}

	contextID, err := strconv.ParseUint(c.Query("context_id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid context ID",
		})
		return
	}

	// Validate context type
	var userRoleContextType models.UserRoleContextType
	switch contextType {
	case "tenant":
		userRoleContextType = models.UserRoleContextTypeTenant
	case "website":
		userRoleContextType = models.UserRoleContextTypeWebsite
	case "global":
		userRoleContextType = models.UserRoleContextTypeGlobal
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid context type",
		})
		return
	}

	// Get primary role
	primaryRole, err := h.userRoleService.GetPrimaryRole(c.Request.Context(), uint(userID), userRoleContextType, uint(contextID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get primary role",
			"details": err.Error(),
		})
		return
	}

	if primaryRole == nil {
		c.JSON(http.StatusNotFound, gin.H{
			"error": "Primary role not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": primaryRole,
	})
}

// SetPrimaryRole sets the primary role for a user in a context
func (h *UserRoleHandler) SetPrimaryRole(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID",
		})
		return
	}

	var req struct {
		RoleID      uint   `json:"role_id" binding:"required"`
		ContextType string `json:"context_type" binding:"required"`
		ContextID   uint   `json:"context_id" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Validate context type
	var userRoleContextType models.UserRoleContextType
	switch req.ContextType {
	case "tenant":
		userRoleContextType = models.UserRoleContextTypeTenant
	case "website":
		userRoleContextType = models.UserRoleContextTypeWebsite
	case "global":
		userRoleContextType = models.UserRoleContextTypeGlobal
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid context type",
		})
		return
	}

	// Set primary role
	err = h.userRoleService.SetPrimaryRole(c.Request.Context(), uint(userID), req.RoleID, userRoleContextType, req.ContextID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to set primary role",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Primary role set successfully",
	})
}

// GetTemporaryRoles retrieves temporary roles for a user
func (h *UserRoleHandler) GetTemporaryRoles(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID",
		})
		return
	}

	temporaryRoles, err := h.userRoleService.GetTemporaryRoles(c.Request.Context(), uint(userID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get temporary roles",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": temporaryRoles,
	})
}

// ExtendRoleValidity extends the validity of a user role
func (h *UserRoleHandler) ExtendRoleValidity(c *gin.Context) {
	userRoleID, err := strconv.ParseUint(c.Param("userRoleId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user role ID",
		})
		return
	}

	var req struct {
		ValidUntil time.Time `json:"valid_until" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Extend role validity
	err = h.userRoleService.ExtendRoleValidity(c.Request.Context(), uint(userRoleID), req.ValidUntil)
	if err != nil {
		if err == services.ErrInvalidTimeRange {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid time range",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to extend role validity",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Role validity extended successfully",
	})
}

// SuspendUserRole suspends a user role
func (h *UserRoleHandler) SuspendUserRole(c *gin.Context) {
	userRoleID, err := strconv.ParseUint(c.Param("userRoleId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user role ID",
		})
		return
	}

	// Suspend user role
	err = h.userRoleService.SuspendUserRole(c.Request.Context(), uint(userRoleID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to suspend user role",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "User role suspended successfully",
	})
}

// ReactivateUserRole reactivates a user role
func (h *UserRoleHandler) ReactivateUserRole(c *gin.Context) {
	userRoleID, err := strconv.ParseUint(c.Param("userRoleId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user role ID",
		})
		return
	}

	// Reactivate user role
	err = h.userRoleService.ReactivateUserRole(c.Request.Context(), uint(userRoleID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to reactivate user role",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "User role reactivated successfully",
	})
}

// SearchUserRoles searches user roles
func (h *UserRoleHandler) SearchUserRoles(c *gin.Context) {
	// Build filters from query parameters
	filters := &repositories.UserRoleFilters{}

	// User ID filter
	if userIDStr := c.Query("user_id"); userIDStr != "" {
		userID, err := strconv.ParseUint(userIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid user ID",
			})
			return
		}
		userIDVal := uint(userID)
		filters.UserID = &userIDVal
	}

	// Role ID filter
	if roleIDStr := c.Query("role_id"); roleIDStr != "" {
		roleID, err := strconv.ParseUint(roleIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid role ID",
			})
			return
		}
		roleIDVal := uint(roleID)
		filters.RoleID = &roleIDVal
	}

	// Status filter
	if status := c.Query("status"); status != "" {
		var userRoleStatus models.UserRoleStatus
		switch status {
		case "active":
			userRoleStatus = models.UserRoleStatusActive
		case "inactive":
			userRoleStatus = models.UserRoleStatusInactive
		case "suspended":
			userRoleStatus = models.UserRoleStatusSuspended
		case "revoked":
			userRoleStatus = models.UserRoleStatusRevoked
		default:
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid status",
			})
			return
		}
		filters.Status = &userRoleStatus
	}

	// Context type filter
	if contextType := c.Query("context_type"); contextType != "" {
		var userRoleContextType models.UserRoleContextType
		switch contextType {
		case "tenant":
			userRoleContextType = models.UserRoleContextTypeTenant
		case "website":
			userRoleContextType = models.UserRoleContextTypeWebsite
		case "global":
			userRoleContextType = models.UserRoleContextTypeGlobal
		default:
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid context type",
			})
			return
		}
		filters.ContextType = &userRoleContextType
	}

	// Context ID filter
	if contextIDStr := c.Query("context_id"); contextIDStr != "" {
		contextID, err := strconv.ParseUint(contextIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid context ID",
			})
			return
		}
		contextIDVal := uint(contextID)
		filters.ContextID = &contextIDVal
	}

	// Primary role filter
	if isPrimaryStr := c.Query("is_primary"); isPrimaryStr != "" {
		isPrimary := isPrimaryStr == "true"
		filters.IsPrimary = &isPrimary
	}

	// Temporary role filter
	if isTemporaryStr := c.Query("is_temporary"); isTemporaryStr != "" {
		isTemporary := isTemporaryStr == "true"
		filters.IsTemporary = &isTemporary
	}

	// Search user roles
	userRoles, err := h.userRoleService.SearchUserRoles(c.Request.Context(), filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to search user roles",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": userRoles,
	})
}

// GetUserRoleHistory retrieves role history for a user
func (h *UserRoleHandler) GetUserRoleHistory(c *gin.Context) {
	userID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID",
		})
		return
	}

	history, err := h.userRoleService.GetUserRoleHistory(c.Request.Context(), uint(userID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get user role history",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": history,
	})
}

// GetUsersByRole retrieves users by role
func (h *UserRoleHandler) GetUsersByRole(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("roleId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid role ID",
		})
		return
	}

	userRoles, err := h.userRoleService.GetUsersByRole(c.Request.Context(), uint(roleID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get users by role",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": userRoles,
	})
}

// GetRoleUsageStats retrieves role usage statistics
func (h *UserRoleHandler) GetRoleUsageStats(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("roleId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid role ID",
		})
		return
	}

	stats, err := h.userRoleService.GetRoleUsageStats(c.Request.Context(), uint(roleID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get role usage stats",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": stats,
	})
}
