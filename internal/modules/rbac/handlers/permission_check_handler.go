package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/services"
)

// PermissionCheckHandler handles permission checking HTTP requests
type PermissionCheckHandler struct {
	rbacEngine services.RBACEngine
}

// NewPermissionCheckHandler creates a new permission check handler
func NewPermissionCheckHandler(rbacEngine services.RBACEngine) *PermissionCheckHandler {
	return &PermissionCheckHandler{
		rbacEngine: rbacEngine,
	}
}

// CheckPermission checks if the current user has a specific permission
func (h *PermissionCheckHandler) CheckPermission(c *gin.Context) {
	// Get user from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// Get permission name from URL parameter
	permissionName := c.Param("permission")
	if permissionName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Permission name is required",
		})
		return
	}

	// Get tenant ID from context
	var tenantID *uint
	if tid, exists := c.Get("tenant_id"); exists {
		if id, ok := tid.(uint); ok {
			tenantID = &id
		}
	}

	// Get context ID from query parameter
	var contextID *uint
	if contextIDStr := c.Query("context_id"); contextIDStr != "" {
		contextIDUint, err := strconv.ParseUint(contextIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid context ID",
			})
			return
		}
		contextIDVal := uint(contextIDUint)
		contextID = &contextIDVal
	}

	// Get resource ID from query parameter
	var resourceID *uint
	if resourceIDStr := c.Query("resource_id"); resourceIDStr != "" {
		resourceIDUint, err := strconv.ParseUint(resourceIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid resource ID",
			})
			return
		}
		resourceIDVal := uint(resourceIDUint)
		resourceID = &resourceIDVal
	}

	// Create permission check request
	req := &services.PermissionCheck{
		UserID:         userID.(uint),
		TenantID:       tenantID,
		PermissionName: permissionName,
		ContextID:      contextID,
		ResourceID:     resourceID,
	}

	// Check permission
	result, err := h.rbacEngine.CheckPermission(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Permission check failed",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"allowed":    result.Allowed,
		"reason":     result.Reason,
		"roles":      result.Roles,
		"permission": result.Permission,
	})
}

// CheckPermissions checks multiple permissions at once
func (h *PermissionCheckHandler) CheckPermissions(c *gin.Context) {
	// Get user from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// Get tenant ID from context
	var tenantID *uint
	if tid, exists := c.Get("tenant_id"); exists {
		if id, ok := tid.(uint); ok {
			tenantID = &id
		}
	}

	// Parse request body
	var req struct {
		Permissions []string `json:"permissions" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Create permission check requests
	var checkReqs []*services.PermissionCheck
	for _, permissionName := range req.Permissions {
		checkReqs = append(checkReqs, &services.PermissionCheck{
			UserID:         userID.(uint),
			TenantID:       tenantID,
			PermissionName: permissionName,
		})
	}

	// Check permissions
	results, err := h.rbacEngine.CheckPermissions(c.Request.Context(), checkReqs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Permission check failed",
			"details": err.Error(),
		})
		return
	}

	// Format results
	response := make(map[string]interface{})
	for i, result := range results {
		response[req.Permissions[i]] = gin.H{
			"allowed": result.Allowed,
			"reason":  result.Reason,
			"roles":   result.Roles,
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"data": response,
	})
}

// CheckPermissionPattern checks permissions matching a pattern
func (h *PermissionCheckHandler) CheckPermissionPattern(c *gin.Context) {
	// Get user from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// Get pattern from URL parameter
	pattern := c.Param("pattern")
	if pattern == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Pattern is required",
		})
		return
	}

	// Get tenant ID from context
	var tenantID *uint
	if tid, exists := c.Get("tenant_id"); exists {
		if id, ok := tid.(uint); ok {
			tenantID = &id
		}
	}

	// Match permissions against pattern
	matchedPermissions, err := h.rbacEngine.MatchPermissionPattern(c.Request.Context(), userID.(uint), tenantID, pattern)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Permission pattern matching failed",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"pattern":             pattern,
		"matched_permissions": matchedPermissions,
		"count":               len(matchedPermissions),
	})
}

// GetCurrentUserPermissions returns the current user's permissions
func (h *PermissionCheckHandler) GetCurrentUserPermissions(c *gin.Context) {
	// Get user from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// Get tenant ID from context
	var tenantID *uint
	if tid, exists := c.Get("tenant_id"); exists {
		if id, ok := tid.(uint); ok {
			tenantID = &id
		}
	}

	// Get user permissions
	permissions, err := h.rbacEngine.GetUserPermissions(c.Request.Context(), userID.(uint), tenantID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get user permissions",
			"details": err.Error(),
		})
		return
	}

	// Get user roles
	roles, err := h.rbacEngine.GetUserRoles(c.Request.Context(), userID.(uint), tenantID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get user roles",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user_id":     userID,
		"tenant_id":   tenantID,
		"permissions": permissions,
		"roles":       roles,
	})
}

// GetCurrentUserRoles returns the current user's roles
func (h *PermissionCheckHandler) GetCurrentUserRoles(c *gin.Context) {
	// Get user from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// Get tenant ID from context
	var tenantID *uint
	if tid, exists := c.Get("tenant_id"); exists {
		if id, ok := tid.(uint); ok {
			tenantID = &id
		}
	}

	// Get user roles
	roles, err := h.rbacEngine.GetUserRoles(c.Request.Context(), userID.(uint), tenantID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get user roles",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user_id":   userID,
		"tenant_id": tenantID,
		"roles":     roles,
	})
}

// RefreshUserPermissions refreshes cached permissions for the current user
func (h *PermissionCheckHandler) RefreshUserPermissions(c *gin.Context) {
	// Get user from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// Get tenant ID from context
	var tenantID *uint
	if tid, exists := c.Get("tenant_id"); exists {
		if id, ok := tid.(uint); ok {
			tenantID = &id
		}
	}

	// Refresh user permissions
	err := h.rbacEngine.RefreshUserPermissions(c.Request.Context(), userID.(uint), tenantID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to refresh user permissions",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "User permissions refreshed successfully",
	})
}

// CheckUserPermission checks if a specific user has a permission (admin only)
func (h *PermissionCheckHandler) CheckUserPermission(c *gin.Context) {
	// Get target user ID from URL parameter
	targetUserID, err := strconv.ParseUint(c.Param("userId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid user ID",
		})
		return
	}

	// Get permission name from URL parameter
	permissionName := c.Param("permission")
	if permissionName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Permission name is required",
		})
		return
	}

	// Get tenant ID from context
	var tenantID *uint
	if tid, exists := c.Get("tenant_id"); exists {
		if id, ok := tid.(uint); ok {
			tenantID = &id
		}
	}

	// Get context ID from query parameter
	var contextID *uint
	if contextIDStr := c.Query("context_id"); contextIDStr != "" {
		contextIDUint, err := strconv.ParseUint(contextIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid context ID",
			})
			return
		}
		contextIDVal := uint(contextIDUint)
		contextID = &contextIDVal
	}

	// Get resource ID from query parameter
	var resourceID *uint
	if resourceIDStr := c.Query("resource_id"); resourceIDStr != "" {
		resourceIDUint, err := strconv.ParseUint(resourceIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid resource ID",
			})
			return
		}
		resourceIDVal := uint(resourceIDUint)
		resourceID = &resourceIDVal
	}

	// Create permission check request
	req := &services.PermissionCheck{
		UserID:         uint(targetUserID),
		TenantID:       tenantID,
		PermissionName: permissionName,
		ContextID:      contextID,
		ResourceID:     resourceID,
	}

	// Check permission
	result, err := h.rbacEngine.CheckPermission(c.Request.Context(), req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Permission check failed",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user_id":    targetUserID,
		"permission": permissionName,
		"allowed":    result.Allowed,
		"reason":     result.Reason,
		"roles":      result.Roles,
	})
}

// CheckUsersPermissions checks permissions for multiple users (admin only)
func (h *PermissionCheckHandler) CheckUsersPermissions(c *gin.Context) {
	// Parse request body
	var req struct {
		UserIDs     []uint   `json:"user_ids" binding:"required"`
		Permissions []string `json:"permissions" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Get tenant ID from context
	var tenantID *uint
	if tid, exists := c.Get("tenant_id"); exists {
		if id, ok := tid.(uint); ok {
			tenantID = &id
		}
	}

	// Create permission check requests
	var checkReqs []*services.PermissionCheck
	for _, userID := range req.UserIDs {
		for _, permissionName := range req.Permissions {
			checkReqs = append(checkReqs, &services.PermissionCheck{
				UserID:         userID,
				TenantID:       tenantID,
				PermissionName: permissionName,
			})
		}
	}

	// Check permissions
	results, err := h.rbacEngine.CheckPermissions(c.Request.Context(), checkReqs)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Permission check failed",
			"details": err.Error(),
		})
		return
	}

	// Format results
	response := make(map[uint]map[string]interface{})
	resultIndex := 0
	for _, userID := range req.UserIDs {
		if response[userID] == nil {
			response[userID] = make(map[string]interface{})
		}
		for _, permissionName := range req.Permissions {
			result := results[resultIndex]
			response[userID][permissionName] = gin.H{
				"allowed": result.Allowed,
				"reason":  result.Reason,
				"roles":   result.Roles,
			}
			resultIndex++
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"data": response,
	})
}

// GetCacheStats returns cache statistics
func (h *PermissionCheckHandler) GetCacheStats(c *gin.Context) {
	stats, err := h.rbacEngine.GetCacheStats(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get cache stats",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"cache_stats": stats,
	})
}

// ClearCache clears the permission cache
func (h *PermissionCheckHandler) ClearCache(c *gin.Context) {
	// Check if clearing cache for specific user
	if userIDStr := c.Query("user_id"); userIDStr != "" {
		userID, err := strconv.ParseUint(userIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid user ID",
			})
			return
		}

		// Get tenant ID from context
		var tenantID *uint
		if tid, exists := c.Get("tenant_id"); exists {
			if id, ok := tid.(uint); ok {
				tenantID = &id
			}
		}

		// Clear cache for specific user
		err = h.rbacEngine.ClearUserCache(c.Request.Context(), uint(userID), tenantID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error":   "Failed to clear user cache",
				"details": err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"message": "User cache cleared successfully",
		})
		return
	}

	// Clear all cache
	err := h.rbacEngine.ClearAllCache(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to clear cache",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Cache cleared successfully",
	})
}

// WarmupCache warms up the permission cache
func (h *PermissionCheckHandler) WarmupCache(c *gin.Context) {
	// Get tenant ID from context
	var tenantID *uint
	if tid, exists := c.Get("tenant_id"); exists {
		if id, ok := tid.(uint); ok {
			tenantID = &id
		}
	}

	// Warmup cache
	err := h.rbacEngine.WarmupCache(c.Request.Context(), tenantID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to warmup cache",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Cache warmed up successfully",
	})
}

// PreloadUserPermissions preloads permissions for multiple users
func (h *PermissionCheckHandler) PreloadUserPermissions(c *gin.Context) {
	// Parse request body
	var req struct {
		UserIDs []uint `json:"user_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Get tenant ID from context
	var tenantID *uint
	if tid, exists := c.Get("tenant_id"); exists {
		if id, ok := tid.(uint); ok {
			tenantID = &id
		}
	}

	// Preload user permissions
	err := h.rbacEngine.PreloadUserPermissions(c.Request.Context(), req.UserIDs, tenantID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to preload user permissions",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message":    "User permissions preloaded successfully",
		"user_count": len(req.UserIDs),
	})
}
