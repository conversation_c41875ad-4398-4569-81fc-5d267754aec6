package handlers

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/services"
)

// PermissionMiddleware handles permission-based route protection
type PermissionMiddleware struct {
	rbacEngine services.RBACEngine
}

// NewPermissionMiddleware creates a new permission middleware
func NewPermissionMiddleware(rbacEngine services.RBACEngine) *PermissionMiddleware {
	return &PermissionMiddleware{
		rbacEngine: rbacEngine,
	}
}

// RequirePermission middleware that requires a specific permission
func (m *PermissionMiddleware) RequirePermission(permissionName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user from context (set by auth middleware)
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User not authenticated",
			})
			c.Abort()
			return
		}

		// Get tenant ID if available
		var tenantID *uint
		if tid, exists := c.Get("tenant_id"); exists {
			if id, ok := tid.(uint); ok {
				tenantID = &id
			}
		}

		// Create permission check request
		req := &services.PermissionCheck{
			UserID:         userID.(uint),
			TenantID:       tenantID,
			PermissionName: permissionName,
		}

		// Check permission
		result, err := m.rbacEngine.CheckPermission(c.Request.Context(), req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": fmt.Sprintf("Permission check failed: %v", err),
			})
			c.Abort()
			return
		}

		if !result.Allowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":               "Permission denied",
				"reason":              result.Reason,
				"required_permission": permissionName,
			})
			c.Abort()
			return
		}

		// Store permission result in context for later use
		c.Set("permission_result", result)
		c.Set("user_roles", result.Roles)

		c.Next()
	}
}

// RequireAnyPermission middleware that requires any of the specified permissions
func (m *PermissionMiddleware) RequireAnyPermission(permissionNames ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user from context
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User not authenticated",
			})
			c.Abort()
			return
		}

		// Get tenant ID if available
		var tenantID *uint
		if tid, exists := c.Get("tenant_id"); exists {
			if id, ok := tid.(uint); ok {
				tenantID = &id
			}
		}

		// Check each permission
		var lastResult *services.PermissionResult
		for _, permissionName := range permissionNames {
			req := &services.PermissionCheck{
				UserID:         userID.(uint),
				TenantID:       tenantID,
				PermissionName: permissionName,
			}

			result, err := m.rbacEngine.CheckPermission(c.Request.Context(), req)
			if err != nil {
				c.JSON(http.StatusInternalServerError, gin.H{
					"error": fmt.Sprintf("Permission check failed: %v", err),
				})
				c.Abort()
				return
			}

			lastResult = result

			// If permission is allowed, continue
			if result.Allowed {
				c.Set("permission_result", result)
				c.Set("user_roles", result.Roles)
				c.Set("granted_permission", permissionName)
				c.Next()
				return
			}
		}

		// None of the permissions were granted
		c.JSON(http.StatusForbidden, gin.H{
			"error":                "Permission denied",
			"reason":               lastResult.Reason,
			"required_permissions": permissionNames,
		})
		c.Abort()
	}
}

// RequireAllPermissions middleware that requires all specified permissions
func (m *PermissionMiddleware) RequireAllPermissions(permissionNames ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user from context
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User not authenticated",
			})
			c.Abort()
			return
		}

		// Get tenant ID if available
		var tenantID *uint
		if tid, exists := c.Get("tenant_id"); exists {
			if id, ok := tid.(uint); ok {
				tenantID = &id
			}
		}

		// Create permission check requests
		var reqs []*services.PermissionCheck
		for _, permissionName := range permissionNames {
			reqs = append(reqs, &services.PermissionCheck{
				UserID:         userID.(uint),
				TenantID:       tenantID,
				PermissionName: permissionName,
			})
		}

		// Check all permissions
		results, err := m.rbacEngine.CheckPermissions(c.Request.Context(), reqs)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": fmt.Sprintf("Permission check failed: %v", err),
			})
			c.Abort()
			return
		}

		// Check if all permissions are allowed
		var deniedPermissions []string
		var lastResult *services.PermissionResult

		for i, result := range results {
			lastResult = result
			if !result.Allowed {
				deniedPermissions = append(deniedPermissions, permissionNames[i])
			}
		}

		if len(deniedPermissions) > 0 {
			c.JSON(http.StatusForbidden, gin.H{
				"error":                "Permission denied",
				"reason":               lastResult.Reason,
				"denied_permissions":   deniedPermissions,
				"required_permissions": permissionNames,
			})
			c.Abort()
			return
		}

		// All permissions granted
		c.Set("permission_results", results)
		c.Set("user_roles", lastResult.Roles)

		c.Next()
	}
}

// RequireRole middleware that requires a specific role
func (m *PermissionMiddleware) RequireRole(roleName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user from context
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User not authenticated",
			})
			c.Abort()
			return
		}

		// Get tenant ID if available
		var tenantID *uint
		if tid, exists := c.Get("tenant_id"); exists {
			if id, ok := tid.(uint); ok {
				tenantID = &id
			}
		}

		// Get user roles
		userRoles, err := m.rbacEngine.GetUserRoles(c.Request.Context(), userID.(uint), tenantID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": fmt.Sprintf("Failed to get user roles: %v", err),
			})
			c.Abort()
			return
		}

		// Check if user has the required role
		hasRole := false
		for _, role := range userRoles {
			if role == roleName {
				hasRole = true
				break
			}
		}

		if !hasRole {
			c.JSON(http.StatusForbidden, gin.H{
				"error":         "Role required",
				"required_role": roleName,
				"user_roles":    userRoles,
			})
			c.Abort()
			return
		}

		c.Set("user_roles", userRoles)
		c.Next()
	}
}

// RequireOwnership middleware that requires ownership of a resource
func (m *PermissionMiddleware) RequireOwnership(permissionName string, resourceIDParam string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user from context
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User not authenticated",
			})
			c.Abort()
			return
		}

		// Get resource ID from URL parameter
		resourceIDStr := c.Param(resourceIDParam)
		if resourceIDStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": fmt.Sprintf("Missing resource ID parameter: %s", resourceIDParam),
			})
			c.Abort()
			return
		}

		resourceID, err := strconv.ParseUint(resourceIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": fmt.Sprintf("Invalid resource ID: %s", resourceIDStr),
			})
			c.Abort()
			return
		}

		// Get tenant ID if available
		var tenantID *uint
		if tid, exists := c.Get("tenant_id"); exists {
			if id, ok := tid.(uint); ok {
				tenantID = &id
			}
		}

		// Create permission check request with resource ID
		req := &services.PermissionCheck{
			UserID:         userID.(uint),
			TenantID:       tenantID,
			PermissionName: permissionName,
			ResourceID:     func() *uint { id := uint(resourceID); return &id }(),
		}

		// Check permission
		result, err := m.rbacEngine.CheckPermission(c.Request.Context(), req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": fmt.Sprintf("Permission check failed: %v", err),
			})
			c.Abort()
			return
		}

		if !result.Allowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":               "Permission denied",
				"reason":              result.Reason,
				"required_permission": permissionName,
				"resource_id":         resourceID,
			})
			c.Abort()
			return
		}

		// Store results in context
		c.Set("permission_result", result)
		c.Set("user_roles", result.Roles)
		c.Set("resource_id", uint(resourceID))

		c.Next()
	}
}

// RequireContext middleware that requires permission in a specific context
func (m *PermissionMiddleware) RequireContext(permissionName string, contextIDParam string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user from context
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User not authenticated",
			})
			c.Abort()
			return
		}

		// Get context ID from URL parameter
		contextIDStr := c.Param(contextIDParam)
		if contextIDStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": fmt.Sprintf("Missing context ID parameter: %s", contextIDParam),
			})
			c.Abort()
			return
		}

		contextID, err := strconv.ParseUint(contextIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": fmt.Sprintf("Invalid context ID: %s", contextIDStr),
			})
			c.Abort()
			return
		}

		// Get tenant ID if available
		var tenantID *uint
		if tid, exists := c.Get("tenant_id"); exists {
			if id, ok := tid.(uint); ok {
				tenantID = &id
			}
		}

		// Create permission check request with context ID
		req := &services.PermissionCheck{
			UserID:         userID.(uint),
			TenantID:       tenantID,
			PermissionName: permissionName,
			ContextID:      func() *uint { id := uint(contextID); return &id }(),
		}

		// Check permission
		result, err := m.rbacEngine.CheckPermission(c.Request.Context(), req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": fmt.Sprintf("Permission check failed: %v", err),
			})
			c.Abort()
			return
		}

		if !result.Allowed {
			c.JSON(http.StatusForbidden, gin.H{
				"error":               "Permission denied",
				"reason":              result.Reason,
				"required_permission": permissionName,
				"context_id":          contextID,
			})
			c.Abort()
			return
		}

		// Store results in context
		c.Set("permission_result", result)
		c.Set("user_roles", result.Roles)
		c.Set("context_id", uint(contextID))

		c.Next()
	}
}

// RequirePermissionPattern middleware that requires permissions matching a pattern
func (m *PermissionMiddleware) RequirePermissionPattern(pattern string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user from context
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User not authenticated",
			})
			c.Abort()
			return
		}

		// Get tenant ID if available
		var tenantID *uint
		if tid, exists := c.Get("tenant_id"); exists {
			if id, ok := tid.(uint); ok {
				tenantID = &id
			}
		}

		// Match permissions against pattern
		matchedPermissions, err := m.rbacEngine.MatchPermissionPattern(c.Request.Context(), userID.(uint), tenantID, pattern)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": fmt.Sprintf("Permission pattern matching failed: %v", err),
			})
			c.Abort()
			return
		}

		if len(matchedPermissions) == 0 {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "No permissions match the required pattern",
				"pattern": pattern,
			})
			c.Abort()
			return
		}

		// Get user roles
		userRoles, err := m.rbacEngine.GetUserRoles(c.Request.Context(), userID.(uint), tenantID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": fmt.Sprintf("Failed to get user roles: %v", err),
			})
			c.Abort()
			return
		}

		// Store results in context
		c.Set("matched_permissions", matchedPermissions)
		c.Set("user_roles", userRoles)
		c.Set("permission_pattern", pattern)

		c.Next()
	}
}

// PermissionCheck is a helper middleware that checks permissions but doesn't abort on failure
func (m *PermissionMiddleware) PermissionCheck(permissionName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user from context
		userID, exists := c.Get("user_id")
		if !exists {
			c.Set("has_permission", false)
			c.Set("permission_error", "User not authenticated")
			c.Next()
			return
		}

		// Get tenant ID if available
		var tenantID *uint
		if tid, exists := c.Get("tenant_id"); exists {
			if id, ok := tid.(uint); ok {
				tenantID = &id
			}
		}

		// Create permission check request
		req := &services.PermissionCheck{
			UserID:         userID.(uint),
			TenantID:       tenantID,
			PermissionName: permissionName,
		}

		// Check permission
		result, err := m.rbacEngine.CheckPermission(c.Request.Context(), req)
		if err != nil {
			c.Set("has_permission", false)
			c.Set("permission_error", fmt.Sprintf("Permission check failed: %v", err))
		} else {
			c.Set("has_permission", result.Allowed)
			c.Set("permission_result", result)
			if result.Allowed {
				c.Set("user_roles", result.Roles)
			} else {
				c.Set("permission_error", result.Reason)
			}
		}

		c.Next()
	}
}

// EnforceRateLimit middleware that enforces rate limits based on user roles
func (m *PermissionMiddleware) EnforceRateLimit() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user roles
		userRoles, exists := c.Get("user_roles")
		if !exists {
			// No roles available, apply default rate limit
			c.Header("X-RateLimit-Role", "default")
			c.Next()
			return
		}

		roles, ok := userRoles.([]string)
		if !ok {
			c.Next()
			return
		}

		// Determine rate limit based on highest priority role
		rateLimit := m.getRateLimitForRoles(roles)
		c.Header("X-RateLimit-Role", strings.Join(roles, ","))
		c.Header("X-RateLimit-Limit", fmt.Sprintf("%d", rateLimit))

		c.Next()
	}
}

// getRateLimitForRoles determines the appropriate rate limit for user roles
func (m *PermissionMiddleware) getRateLimitForRoles(roles []string) int {
	// Default rate limit
	rateLimit := 100

	// Check for admin roles (higher limits)
	for _, role := range roles {
		switch role {
		case "super_admin":
			return 10000
		case "admin":
			return 5000
		case "moderator":
			return 1000
		case "premium_user":
			return 500
		}
	}

	return rateLimit
}

// UserPermissionsHandler returns the current user's permissions
func (m *PermissionMiddleware) UserPermissionsHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get user from context
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User not authenticated",
			})
			return
		}

		// Get tenant ID if available
		var tenantID *uint
		if tid, exists := c.Get("tenant_id"); exists {
			if id, ok := tid.(uint); ok {
				tenantID = &id
			}
		}

		// Get user permissions
		permissions, err := m.rbacEngine.GetUserPermissions(c.Request.Context(), userID.(uint), tenantID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": fmt.Sprintf("Failed to get user permissions: %v", err),
			})
			return
		}

		// Get user roles
		roles, err := m.rbacEngine.GetUserRoles(c.Request.Context(), userID.(uint), tenantID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": fmt.Sprintf("Failed to get user roles: %v", err),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"user_id":     userID,
			"tenant_id":   tenantID,
			"permissions": permissions,
			"roles":       roles,
		})
	}
}

// CacheStatsHandler returns cache statistics for RBAC engine
func (m *PermissionMiddleware) CacheStatsHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		stats, err := m.rbacEngine.GetCacheStats(c.Request.Context())
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": fmt.Sprintf("Failed to get cache stats: %v", err),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"cache_stats": stats,
		})
	}
}
