package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/services"
)

// RoleHandler handles role-related HTTP requests
type RoleHandler struct {
	roleService services.RoleService
	rbacEngine  services.RBACEngine
}

// NewRoleHandler creates a new role handler
func NewRoleHandler(roleService services.RoleService, rbacEngine services.RBACEngine) *RoleHandler {
	return &RoleHandler{
		roleService: roleService,
		rbacEngine:  rbacEngine,
	}
}

// CreateRole creates a new role
func (h *RoleHandler) CreateRole(c *gin.Context) {
	var req models.RoleCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Tenant ID is required",
		})
		return
	}
	req.TenantID = tenantID.(uint)

	// Basic validation
	if req.Name == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Role name is required",
		})
		return
	}

	// Create role
	role, err := h.roleService.CreateRole(c.Request.Context(), &req)
	if err != nil {
		if err == services.ErrRoleAlreadyExists {
			c.JSON(http.StatusConflict, gin.H{
				"error": "Role already exists",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create role",
			"details": err.Error(),
		})
		return
	}

	// Clear cache for affected users
	h.rbacEngine.ClearAllCache(c.Request.Context())

	c.JSON(http.StatusCreated, gin.H{
		"message": "Role created successfully",
		"data":    role,
	})
}

// GetRole retrieves a role by ID
func (h *RoleHandler) GetRole(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid role ID",
		})
		return
	}

	role, err := h.roleService.GetRole(c.Request.Context(), uint(roleID))
	if err != nil {
		if err == services.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Role not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get role",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": role,
	})
}

// UpdateRole updates a role
func (h *RoleHandler) UpdateRole(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid role ID",
		})
		return
	}

	var req models.RoleUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Basic validation
	if req.Name != nil && *req.Name == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Role name cannot be empty",
		})
		return
	}

	// Update role
	role, err := h.roleService.UpdateRole(c.Request.Context(), uint(roleID), &req)
	if err != nil {
		if err == services.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Role not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to update role",
			"details": err.Error(),
		})
		return
	}

	// Clear cache for affected users
	h.rbacEngine.ClearAllCache(c.Request.Context())

	c.JSON(http.StatusOK, gin.H{
		"message": "Role updated successfully",
		"data":    role,
	})
}

// DeleteRole deletes a role
func (h *RoleHandler) DeleteRole(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid role ID",
		})
		return
	}

	err = h.roleService.DeleteRole(c.Request.Context(), uint(roleID))
	if err != nil {
		if err == services.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Role not found",
			})
			return
		}
		if err == services.ErrCannotDeleteSystemRole {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Cannot delete system role",
			})
			return
		}
		if err == services.ErrRoleInUse {
			c.JSON(http.StatusConflict, gin.H{
				"error": "Role is in use and cannot be deleted",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to delete role",
			"details": err.Error(),
		})
		return
	}

	// Clear cache for affected users
	h.rbacEngine.ClearAllCache(c.Request.Context())

	c.JSON(http.StatusOK, gin.H{
		"message": "Role deleted successfully",
	})
}

// ListRoles lists roles with pagination
func (h *RoleHandler) ListRoles(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Tenant ID is required",
		})
		return
	}

	// Parse pagination parameters
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 20
	}

	// Get roles by tenant
	roles, total, err := h.roleService.GetRolesByTenant(c.Request.Context(), tenantID.(uint), page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get roles",
			"details": err.Error(),
		})
		return
	}

	// Calculate pagination info
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	c.JSON(http.StatusOK, gin.H{
		"data": roles,
		"pagination": gin.H{
			"page":        page,
			"page_size":   pageSize,
			"total":       total,
			"total_pages": totalPages,
		},
	})
}

// GetRolesByScope retrieves roles by scope
func (h *RoleHandler) GetRolesByScope(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Tenant ID is required",
		})
		return
	}

	scope := c.Param("scope")
	if scope == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Scope is required",
		})
		return
	}

	// Validate scope
	var roleScope models.RoleScope
	switch scope {
	case "tenant":
		roleScope = models.RoleScopeTenant
	case "website":
		roleScope = models.RoleScopeWebsite
	case "global":
		roleScope = models.RoleScopeGlobal
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid scope",
		})
		return
	}

	// Get context ID if provided
	var contextID *uint
	if contextIDStr := c.Query("context_id"); contextIDStr != "" {
		contextIDUint, err := strconv.ParseUint(contextIDStr, 10, 32)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid context ID",
			})
			return
		}
		contextIDVal := uint(contextIDUint)
		contextID = &contextIDVal
	}

	// Get roles by scope
	roles, err := h.roleService.GetRolesByScope(c.Request.Context(), tenantID.(uint), roleScope, contextID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get roles by scope",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": roles,
	})
}

// GetSystemRoles retrieves system roles
func (h *RoleHandler) GetSystemRoles(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Tenant ID is required",
		})
		return
	}

	roles, err := h.roleService.GetSystemRoles(c.Request.Context(), tenantID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get system roles",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": roles,
	})
}

// GetRoleHierarchy retrieves role hierarchy
func (h *RoleHandler) GetRoleHierarchy(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Tenant ID is required",
		})
		return
	}

	roles, err := h.roleService.GetRoleHierarchy(c.Request.Context(), tenantID.(uint))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get role hierarchy",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": roles,
	})
}

// GetRolePermissions retrieves permissions for a role
func (h *RoleHandler) GetRolePermissions(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid role ID",
		})
		return
	}

	permissions, err := h.roleService.GetRolePermissions(c.Request.Context(), uint(roleID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get role permissions",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": permissions,
	})
}

// AssignPermissionsToRole assigns permissions to a role
func (h *RoleHandler) AssignPermissionsToRole(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid role ID",
		})
		return
	}

	var req struct {
		PermissionIDs []uint `json:"permission_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Get current user ID for audit
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// Assign permissions
	grantedBy := userID.(uint)
	err = h.roleService.AssignPermissionsToRole(c.Request.Context(), uint(roleID), req.PermissionIDs, &grantedBy)
	if err != nil {
		if err == services.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Role not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to assign permissions",
			"details": err.Error(),
		})
		return
	}

	// Clear cache for affected users
	h.rbacEngine.ClearAllCache(c.Request.Context())

	c.JSON(http.StatusOK, gin.H{
		"message": "Permissions assigned successfully",
	})
}

// RevokePermissionsFromRole revokes permissions from a role
func (h *RoleHandler) RevokePermissionsFromRole(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid role ID",
		})
		return
	}

	var req struct {
		PermissionIDs []uint `json:"permission_ids" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Get current user ID for audit
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	// Revoke permissions
	revokedBy := userID.(uint)
	err = h.roleService.RevokePermissionsFromRole(c.Request.Context(), uint(roleID), req.PermissionIDs, &revokedBy)
	if err != nil {
		if err == services.ErrRoleNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Role not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to revoke permissions",
			"details": err.Error(),
		})
		return
	}

	// Clear cache for affected users
	h.rbacEngine.ClearAllCache(c.Request.Context())

	c.JSON(http.StatusOK, gin.H{
		"message": "Permissions revoked successfully",
	})
}

// GetRoleUsers retrieves users for a role
func (h *RoleHandler) GetRoleUsers(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid role ID",
		})
		return
	}

	userRoles, err := h.roleService.GetRoleUsers(c.Request.Context(), uint(roleID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get role users",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": userRoles,
	})
}

// ActivateRole activates a role
func (h *RoleHandler) ActivateRole(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid role ID",
		})
		return
	}

	err = h.roleService.ActivateRole(c.Request.Context(), uint(roleID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to activate role",
			"details": err.Error(),
		})
		return
	}

	// Clear cache for affected users
	h.rbacEngine.ClearAllCache(c.Request.Context())

	c.JSON(http.StatusOK, gin.H{
		"message": "Role activated successfully",
	})
}

// DeactivateRole deactivates a role
func (h *RoleHandler) DeactivateRole(c *gin.Context) {
	roleID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid role ID",
		})
		return
	}

	err = h.roleService.DeactivateRole(c.Request.Context(), uint(roleID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to deactivate role",
			"details": err.Error(),
		})
		return
	}

	// Clear cache for affected users
	h.rbacEngine.ClearAllCache(c.Request.Context())

	c.JSON(http.StatusOK, gin.H{
		"message": "Role deactivated successfully",
	})
}

// SearchRoles searches roles
func (h *RoleHandler) SearchRoles(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Tenant ID is required",
		})
		return
	}

	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Search query is required",
		})
		return
	}

	// Build filters (simplified for now)
	filters := &repositories.RoleFilters{
		// Add filter implementation based on query parameters
	}

	roles, err := h.roleService.SearchRoles(c.Request.Context(), tenantID.(uint), query, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to search roles",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": roles,
	})
}
