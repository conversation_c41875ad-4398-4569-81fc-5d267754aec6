package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/services"
)

// PermissionHandler handles permission-related HTTP requests
type PermissionHandler struct {
	permissionService services.PermissionService
	rbacEngine        services.RBACEngine
}

// NewPermissionHandler creates a new permission handler
func NewPermissionHandler(permissionService services.PermissionService, rbacEngine services.RBACEngine) *PermissionHandler {
	return &PermissionHandler{
		permissionService: permissionService,
		rbacEngine:        rbacEngine,
	}
}

// CreatePermission creates a new permission
func (h *PermissionHandler) CreatePermission(c *gin.Context) {
	var req models.PermissionCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.J<PERSON>N(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Validation will be handled by the service layer

	// Create permission
	permission, err := h.permissionService.CreatePermission(c.Request.Context(), &req)
	if err != nil {
		if err == services.ErrPermissionAlreadyExists {
			c.JSON(http.StatusConflict, gin.H{
				"error": "Permission already exists",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create permission",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Permission created successfully",
		"data":    permission,
	})
}

// GetPermission retrieves a permission by ID
func (h *PermissionHandler) GetPermission(c *gin.Context) {
	permissionID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid permission ID",
		})
		return
	}

	permission, err := h.permissionService.GetPermission(c.Request.Context(), uint(permissionID))
	if err != nil {
		if err == services.ErrPermissionNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Permission not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get permission",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": permission,
	})
}

// GetPermissionByName retrieves a permission by name
func (h *PermissionHandler) GetPermissionByName(c *gin.Context) {
	name := c.Param("name")
	if name == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Permission name is required",
		})
		return
	}

	permission, err := h.permissionService.GetPermissionByName(c.Request.Context(), name)
	if err != nil {
		if err == services.ErrPermissionNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Permission not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get permission",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": permission,
	})
}

// UpdatePermission updates a permission
func (h *PermissionHandler) UpdatePermission(c *gin.Context) {
	permissionID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid permission ID",
		})
		return
	}

	var req models.PermissionUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Validation will be handled by the service layer

	// Update permission
	permission, err := h.permissionService.UpdatePermission(c.Request.Context(), uint(permissionID), &req)
	if err != nil {
		if err == services.ErrPermissionNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Permission not found",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to update permission",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Permission updated successfully",
		"data":    permission,
	})
}

// DeletePermission deletes a permission
func (h *PermissionHandler) DeletePermission(c *gin.Context) {
	permissionID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid permission ID",
		})
		return
	}

	err = h.permissionService.DeletePermission(c.Request.Context(), uint(permissionID))
	if err != nil {
		if err == services.ErrPermissionNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "Permission not found",
			})
			return
		}
		if err == services.ErrCannotDeleteSystemPermission {
			c.JSON(http.StatusForbidden, gin.H{
				"error": "Cannot delete system permission",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to delete permission",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Permission deleted successfully",
	})
}

// ListPermissions lists all permissions
func (h *PermissionHandler) ListPermissions(c *gin.Context) {
	// Parse pagination parameters
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(c.DefaultQuery("page_size", "50"))
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 50
	}

	// Get permissions
	permissions, err := h.permissionService.GetActivePermissions(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get permissions",
			"details": err.Error(),
		})
		return
	}

	// Simple pagination (in production, implement at repository level)
	total := len(permissions)
	start := (page - 1) * pageSize
	end := start + pageSize

	if start >= total {
		permissions = []*models.Permission{}
	} else {
		if end > total {
			end = total
		}
		permissions = permissions[start:end]
	}

	totalPages := (total + pageSize - 1) / pageSize

	c.JSON(http.StatusOK, gin.H{
		"data": permissions,
		"pagination": gin.H{
			"page":        page,
			"page_size":   pageSize,
			"total":       total,
			"total_pages": totalPages,
		},
	})
}

// GetPermissionsByModule retrieves permissions by module
func (h *PermissionHandler) GetPermissionsByModule(c *gin.Context) {
	module := c.Param("module")
	if module == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Module is required",
		})
		return
	}

	// Parse pagination parameters
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	pageSize, err := strconv.Atoi(c.DefaultQuery("page_size", "50"))
	if err != nil || pageSize < 1 || pageSize > 100 {
		pageSize = 50
	}

	permissions, total, err := h.permissionService.GetPermissionsByModule(c.Request.Context(), module, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get permissions by module",
			"details": err.Error(),
		})
		return
	}

	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	c.JSON(http.StatusOK, gin.H{
		"data": permissions,
		"pagination": gin.H{
			"page":        page,
			"page_size":   pageSize,
			"total":       total,
			"total_pages": totalPages,
		},
	})
}

// GetPermissionsByScope retrieves permissions by scope
func (h *PermissionHandler) GetPermissionsByScope(c *gin.Context) {
	scope := c.Param("scope")
	if scope == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Scope is required",
		})
		return
	}

	// Validate scope
	var permissionScope models.PermissionScope
	switch scope {
	case "tenant":
		permissionScope = models.PermissionScopeTenant
	case "website":
		permissionScope = models.PermissionScopeWebsite
	case "global":
		permissionScope = models.PermissionScopeGlobal
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid scope",
		})
		return
	}

	permissions, err := h.permissionService.GetPermissionsByScope(c.Request.Context(), permissionScope)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get permissions by scope",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": permissions,
	})
}

// GetPermissionsByRiskLevel retrieves permissions by risk level
func (h *PermissionHandler) GetPermissionsByRiskLevel(c *gin.Context) {
	riskLevel := c.Param("risk_level")
	if riskLevel == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Risk level is required",
		})
		return
	}

	// Validate risk level
	var permissionRiskLevel models.PermissionRiskLevel
	switch riskLevel {
	case "low":
		permissionRiskLevel = models.PermissionRiskLevelLow
	case "medium":
		permissionRiskLevel = models.PermissionRiskLevelMedium
	case "high":
		permissionRiskLevel = models.PermissionRiskLevelHigh
	case "critical":
		permissionRiskLevel = models.PermissionRiskLevelCritical
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid risk level",
		})
		return
	}

	permissions, err := h.permissionService.GetPermissionsByRiskLevel(c.Request.Context(), permissionRiskLevel)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get permissions by risk level",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": permissions,
	})
}

// GetSystemPermissions retrieves system permissions
func (h *PermissionHandler) GetSystemPermissions(c *gin.Context) {
	permissions, err := h.permissionService.GetSystemPermissions(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get system permissions",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": permissions,
	})
}

// GetPermissionRoles retrieves roles that have a specific permission
func (h *PermissionHandler) GetPermissionRoles(c *gin.Context) {
	permissionID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid permission ID",
		})
		return
	}

	roles, err := h.permissionService.GetPermissionRoles(c.Request.Context(), uint(permissionID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get permission roles",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": roles,
	})
}

// GetUnusedPermissions retrieves permissions that are not assigned to any role
func (h *PermissionHandler) GetUnusedPermissions(c *gin.Context) {
	permissions, err := h.permissionService.GetUnusedPermissions(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get unused permissions",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": permissions,
	})
}

// GetPermissionUsageStats retrieves usage statistics for a permission
func (h *PermissionHandler) GetPermissionUsageStats(c *gin.Context) {
	permissionID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid permission ID",
		})
		return
	}

	usageCount, err := h.permissionService.GetPermissionUsageCount(c.Request.Context(), uint(permissionID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get permission usage stats",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": gin.H{
			"permission_id": permissionID,
			"usage_count":   usageCount,
		},
	})
}

// ActivatePermission activates a permission
func (h *PermissionHandler) ActivatePermission(c *gin.Context) {
	permissionID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid permission ID",
		})
		return
	}

	err = h.permissionService.ActivatePermission(c.Request.Context(), uint(permissionID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to activate permission",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Permission activated successfully",
	})
}

// DeactivatePermission deactivates a permission
func (h *PermissionHandler) DeactivatePermission(c *gin.Context) {
	permissionID, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid permission ID",
		})
		return
	}

	err = h.permissionService.DeactivatePermission(c.Request.Context(), uint(permissionID))
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to deactivate permission",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Permission deactivated successfully",
	})
}

// SearchPermissions searches permissions
func (h *PermissionHandler) SearchPermissions(c *gin.Context) {
	query := c.Query("q")
	if query == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Search query is required",
		})
		return
	}

	// Build filters from query parameters
	filters := &repositories.PermissionFilters{}

	// Add filter for module
	if module := c.Query("module"); module != "" {
		filters.Module = &module
	}

	// Add filter for resource
	if resource := c.Query("resource"); resource != "" {
		filters.Resource = &resource
	}

	// Add filter for scope
	if scope := c.Query("scope"); scope != "" {
		var permissionScope models.PermissionScope
		switch scope {
		case "tenant":
			permissionScope = models.PermissionScopeTenant
		case "website":
			permissionScope = models.PermissionScopeWebsite
		case "global":
			permissionScope = models.PermissionScopeGlobal
		default:
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid scope",
			})
			return
		}
		filters.Scope = &permissionScope
	}

	// Add filter for risk level
	if riskLevel := c.Query("risk_level"); riskLevel != "" {
		var permissionRiskLevel models.PermissionRiskLevel
		switch riskLevel {
		case "low":
			permissionRiskLevel = models.PermissionRiskLevelLow
		case "medium":
			permissionRiskLevel = models.PermissionRiskLevelMedium
		case "high":
			permissionRiskLevel = models.PermissionRiskLevelHigh
		case "critical":
			permissionRiskLevel = models.PermissionRiskLevelCritical
		default:
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Invalid risk level",
			})
			return
		}
		filters.RiskLevel = &permissionRiskLevel
	}

	permissions, err := h.permissionService.SearchPermissions(c.Request.Context(), query, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to search permissions",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": permissions,
	})
}

// GetModules retrieves all available modules
func (h *PermissionHandler) GetModules(c *gin.Context) {
	modules, err := h.permissionService.GetModules(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get modules",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": modules,
	})
}

// GetResourcesByModule retrieves resources by module
func (h *PermissionHandler) GetResourcesByModule(c *gin.Context) {
	module := c.Param("module")
	if module == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Module is required",
		})
		return
	}

	resources, err := h.permissionService.GetResourcesByModule(c.Request.Context(), module)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get resources by module",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": resources,
	})
}

// GetActionsByResource retrieves actions by resource
func (h *PermissionHandler) GetActionsByResource(c *gin.Context) {
	resource := c.Param("resource")
	if resource == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Resource is required",
		})
		return
	}

	actions, err := h.permissionService.GetActionsByResource(c.Request.Context(), resource)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get actions by resource",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"data": actions,
	})
}

// CreatePermissionsBulk creates permissions in bulk
func (h *PermissionHandler) CreatePermissionsBulk(c *gin.Context) {
	var req models.PermissionBulkCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Validation will be handled by the service layer

	// Create permissions in bulk
	permissions, err := h.permissionService.CreatePermissionsBulk(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to create permissions in bulk",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusCreated, gin.H{
		"message": "Permissions created successfully",
		"data":    permissions,
	})
}

// UpdatePermissionsBulk updates permissions in bulk
func (h *PermissionHandler) UpdatePermissionsBulk(c *gin.Context) {
	var req models.PermissionBulkUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request body",
			"details": err.Error(),
		})
		return
	}

	// Validation will be handled by the service layer

	// Update permissions in bulk
	err := h.permissionService.UpdatePermissionsBulk(c.Request.Context(), &req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to update permissions in bulk",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Permissions updated successfully",
	})
}
