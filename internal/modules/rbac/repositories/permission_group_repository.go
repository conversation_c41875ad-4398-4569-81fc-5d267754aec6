package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
)

// PermissionGroupRepository defines the interface for permission group operations
type PermissionGroupRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, group *models.PermissionGroup) error
	GetByID(ctx context.Context, id uint) (*models.PermissionGroup, error)
	GetByName(ctx context.Context, module, name string) (*models.PermissionGroup, error)
	Update(ctx context.Context, group *models.PermissionGroup) error
	Delete(ctx context.Context, id uint) error

	// Module-specific operations
	GetByModule(ctx context.Context, module string, limit, offset int) ([]*models.PermissionGroup, error)
	GetByCategory(ctx context.Context, category models.PermissionGroupCategory, limit, offset int) ([]*models.PermissionGroup, error)
	GetActiveGroups(ctx context.Context) ([]*models.PermissionGroup, error)
	GetSystemGroups(ctx context.Context) ([]*models.PermissionGroup, error)
	GetRequiredGroups(ctx context.Context) ([]*models.PermissionGroup, error)

	// Hierarchy operations
	GetRootGroups(ctx context.Context, module string) ([]*models.PermissionGroup, error)
	GetChildGroups(ctx context.Context, parentGroupID uint) ([]*models.PermissionGroup, error)
	GetGroupHierarchy(ctx context.Context, module string) ([]*models.PermissionGroup, error)
	GetGroupTree(ctx context.Context, rootGroupID uint) ([]*models.PermissionGroup, error)
	GetGroupPath(ctx context.Context, groupID uint) ([]*models.PermissionGroup, error)

	// Level and ordering
	GetByLevel(ctx context.Context, level uint) ([]*models.PermissionGroup, error)
	GetOrderedGroups(ctx context.Context, module string, level uint) ([]*models.PermissionGroup, error)
	UpdateSortOrder(ctx context.Context, groupID uint, sortOrder uint) error
	ReorderGroups(ctx context.Context, groupIDs []uint) error

	// Status management
	UpdateStatus(ctx context.Context, groupID uint, status models.PermissionGroupStatus) error

	// Bulk operations
	GetByIDs(ctx context.Context, ids []uint) ([]*models.PermissionGroup, error)
	CreateBulk(ctx context.Context, groups []*models.PermissionGroup) error
	Count(ctx context.Context) (int64, error)
	CountByModule(ctx context.Context, module string) (int64, error)

	// Search and filtering
	Search(ctx context.Context, query string, filters *PermissionGroupFilters) ([]*models.PermissionGroup, error)
	Exists(ctx context.Context, module, name string) (bool, error)

	// Module and category discovery
	GetModules(ctx context.Context) ([]string, error)
	GetCategoriesByModule(ctx context.Context, module string) ([]models.PermissionGroupCategory, error)

	// Permission relationships
	GetGroupPermissions(ctx context.Context, groupID uint) ([]*models.Permission, error)
	GetPermissionsByGroup(ctx context.Context, groupID uint) ([]*models.Permission, error)
	GetGroupsWithPermission(ctx context.Context, permissionID uint) ([]*models.PermissionGroup, error)

	// Default permissions management
	AddDefaultPermission(ctx context.Context, groupID uint, permissionName string) error
	RemoveDefaultPermission(ctx context.Context, groupID uint, permissionName string) error
	GetDefaultPermissions(ctx context.Context, groupID uint) ([]string, error)

	// Metadata operations
	UpdateMetadata(ctx context.Context, groupID uint, metadata models.PermissionGroupMetadata) error
	GetMetadata(ctx context.Context, groupID uint) (models.PermissionGroupMetadata, error)

	// Validation and integrity
	ValidateHierarchy(ctx context.Context, groupID uint, parentGroupID uint) error
	DetectCircularReferences(ctx context.Context, groupID uint, parentGroupID uint) (bool, error)
	GetMaxLevel(ctx context.Context, module string) (uint, error)
}

// PermissionGroupFilters represents filters for querying permission groups
type PermissionGroupFilters struct {
	Status        *models.PermissionGroupStatus   `json:"status,omitempty"`
	Module        *string                         `json:"module,omitempty"`
	Category      *models.PermissionGroupCategory `json:"category,omitempty"`
	ParentGroupID *uint                           `json:"parent_group_id,omitempty"`
	Level         *uint                           `json:"level,omitempty"`
	IsSystemGroup *bool                           `json:"is_system_group,omitempty"`
	IsRequired    *bool                           `json:"is_required,omitempty"`
	CreatedAfter  *time.Time                      `json:"created_after,omitempty"`
	CreatedBefore *time.Time                      `json:"created_before,omitempty"`
	SortBy        string                          `json:"sort_by,omitempty"`
	SortOrder     string                          `json:"sort_order,omitempty"`
	Limit         int                             `json:"limit,omitempty"`
	Offset        int                             `json:"offset,omitempty"`
}
