package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
)

// RoleRepository defines the interface for role-related database operations
type RoleRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, role *models.Role) error
	GetByID(ctx context.Context, id uint) (*models.Role, error)
	GetByName(ctx context.Context, tenantID uint, name string) (*models.Role, error)
	Update(ctx context.Context, role *models.Role) error
	Delete(ctx context.Context, id uint) error

	// Role-specific operations
	GetByTenant(ctx context.Context, tenantID uint, limit, offset int) ([]*models.Role, error)
	GetByScope(ctx context.Context, tenantID uint, scope models.RoleScope, contextID *uint) ([]*models.Role, error)
	GetSystemRoles(ctx context.Context, tenantID uint) ([]*models.Role, error)
	GetDefaultRoles(ctx context.Context, tenantID uint) ([]*models.Role, error)

	// Status management
	UpdateStatus(ctx context.Context, roleID uint, status models.RoleStatus) error
	GetActiveRolesByTenant(ctx context.Context, tenantID uint) ([]*models.Role, error)

	// Bulk operations
	GetByIDs(ctx context.Context, ids []uint) ([]*models.Role, error)
	CountByTenant(ctx context.Context, tenantID uint) (int64, error)

	// Search and filtering
	Search(ctx context.Context, tenantID uint, query string, filters *RoleFilters) ([]*models.Role, error)
	Exists(ctx context.Context, tenantID uint, name string) (bool, error)

	// Hierarchy and relationships
	GetRoleHierarchy(ctx context.Context, tenantID uint) ([]*models.Role, error)
	GetRolesByLevel(ctx context.Context, tenantID uint, level uint) ([]*models.Role, error)
	GetRolePermissions(ctx context.Context, roleID uint) ([]*models.Permission, error)
	GetRoleUsers(ctx context.Context, roleID uint) ([]*models.UserRole, error)

	// Permission assignments
	AssignPermission(ctx context.Context, roleID uint, permissionID uint, grantedBy *uint) error
	RevokePermission(ctx context.Context, roleID uint, permissionID uint, revokedBy *uint) error
	HasPermission(ctx context.Context, roleID uint, permissionID uint) (bool, error)
}

// RoleFilters represents filters for querying roles
type RoleFilters struct {
	TenantID      *uint              `json:"tenant_id,omitempty"`
	Status        *models.RoleStatus `json:"status,omitempty"`
	Scope         *models.RoleScope  `json:"scope,omitempty"`
	ContextID     *uint              `json:"context_id,omitempty"`
	IsSystemRole  *bool              `json:"is_system_role,omitempty"`
	IsDefaultRole *bool              `json:"is_default_role,omitempty"`
	Level         *uint              `json:"level,omitempty"`
	CreatedAfter  *time.Time         `json:"created_after,omitempty"`
	CreatedBefore *time.Time         `json:"created_before,omitempty"`
	SortBy        string             `json:"sort_by,omitempty"`
	SortOrder     string             `json:"sort_order,omitempty"`
	Limit         int                `json:"limit,omitempty"`
	Offset        int                `json:"offset,omitempty"`
}
