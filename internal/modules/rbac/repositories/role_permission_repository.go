package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
)

// RolePermissionRepository defines the interface for role-permission mapping operations
type RolePermissionRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, rolePermission *models.RolePermission) error
	GetByID(ctx context.Context, id uint) (*models.RolePermission, error)
	Update(ctx context.Context, rolePermission *models.RolePermission) error
	Delete(ctx context.Context, id uint) error

	// Role-permission specific operations
	GetByRole(ctx context.Context, roleID uint) ([]*models.RolePermission, error)
	GetByPermission(ctx context.Context, permissionID uint) ([]*models.RolePermission, error)
	GetByRoleAndPermission(ctx context.Context, roleID uint, permissionID uint) (*models.RolePermission, error)

	// Context-based operations
	GetByContext(ctx context.Context, contextType models.RolePermissionContextType, contextID uint) ([]*models.RolePermission, error)
	GetByRoleAndContext(ctx context.Context, roleID uint, contextType models.RolePermissionContextType, contextID uint) ([]*models.RolePermission, error)

	// Permission checking
	HasPermission(ctx context.Context, roleID uint, permissionID uint) (bool, error)
	HasPermissionInContext(ctx context.Context, roleID uint, permissionID uint, contextType models.RolePermissionContextType, contextID uint) (bool, error)
	GetActivePermissions(ctx context.Context, roleID uint) ([]*models.RolePermission, error)
	GetValidPermissions(ctx context.Context, roleID uint, timestamp time.Time) ([]*models.RolePermission, error)

	// Status management
	UpdateStatus(ctx context.Context, rolePermissionID uint, status models.RolePermissionStatus) error
	Revoke(ctx context.Context, rolePermissionID uint, revokedBy uint) error
	RevokeByRoleAndPermission(ctx context.Context, roleID uint, permissionID uint, revokedBy uint) error

	// Bulk operations
	GetByIDs(ctx context.Context, ids []uint) ([]*models.RolePermission, error)
	CreateBulk(ctx context.Context, rolePermissions []*models.RolePermission) error
	RevokeBulk(ctx context.Context, rolePermissionIDs []uint, revokedBy uint) error

	// Temporal operations
	GetExpiredPermissions(ctx context.Context, timestamp time.Time) ([]*models.RolePermission, error)
	GetTemporaryPermissions(ctx context.Context, roleID uint) ([]*models.RolePermission, error)
	GetInheritedPermissions(ctx context.Context, roleID uint) ([]*models.RolePermission, error)

	// Search and filtering
	Search(ctx context.Context, filters *RolePermissionFilters) ([]*models.RolePermission, error)
	Count(ctx context.Context, filters *RolePermissionFilters) (int64, error)

	// Analytics and reporting
	GetPermissionUsageByRole(ctx context.Context, roleID uint) (map[uint]int64, error)
	GetRoleUsageByPermission(ctx context.Context, permissionID uint) (map[uint]int64, error)
	GetMostUsedPermissions(ctx context.Context, limit int) ([]*models.Permission, error)

	// Cleanup operations
	CleanupExpiredPermissions(ctx context.Context) (int64, error)
	CleanupRevokedPermissions(ctx context.Context, olderThan time.Time) (int64, error)
}

// RolePermissionFilters represents filters for querying role permissions
type RolePermissionFilters struct {
	RoleID        *uint                             `json:"role_id,omitempty"`
	PermissionID  *uint                             `json:"permission_id,omitempty"`
	Status        *models.RolePermissionStatus      `json:"status,omitempty"`
	ContextType   *models.RolePermissionContextType `json:"context_type,omitempty"`
	ContextID     *uint                             `json:"context_id,omitempty"`
	IsInherited   *bool                             `json:"is_inherited,omitempty"`
	IsTemporary   *bool                             `json:"is_temporary,omitempty"`
	GrantedBy     *uint                             `json:"granted_by,omitempty"`
	ValidFrom     *time.Time                        `json:"valid_from,omitempty"`
	ValidUntil    *time.Time                        `json:"valid_until,omitempty"`
	GrantedAfter  *time.Time                        `json:"granted_after,omitempty"`
	GrantedBefore *time.Time                        `json:"granted_before,omitempty"`
	SortBy        string                            `json:"sort_by,omitempty"`
	SortOrder     string                            `json:"sort_order,omitempty"`
	Limit         int                               `json:"limit,omitempty"`
	Offset        int                               `json:"offset,omitempty"`
}
