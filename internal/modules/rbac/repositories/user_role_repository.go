package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
)

// UserRoleRepository defines the interface for user-role mapping operations
type UserRoleRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, userRole *models.UserRole) error
	GetByID(ctx context.Context, id uint) (*models.UserRole, error)
	Update(ctx context.Context, userRole *models.UserRole) error
	Delete(ctx context.Context, id uint) error

	// User-role specific operations
	GetByUser(ctx context.Context, userID uint) ([]*models.UserRole, error)
	GetByRole(ctx context.Context, roleID uint) ([]*models.UserRole, error)
	GetByUserAndRole(ctx context.Context, userID uint, roleID uint) (*models.UserRole, error)

	// Context-based operations
	GetByContext(ctx context.Context, contextType models.UserRoleContextType, contextID uint) ([]*models.UserRole, error)
	GetByUserAndContext(ctx context.Context, userID uint, contextType models.UserRoleContextType, contextID uint) ([]*models.UserRole, error)
	GetByUserRoleAndContext(ctx context.Context, userID uint, roleID uint, contextType models.UserRoleContextType, contextID uint) (*models.UserRole, error)

	// Role checking
	HasRole(ctx context.Context, userID uint, roleID uint) (bool, error)
	HasRoleInContext(ctx context.Context, userID uint, roleID uint, contextType models.UserRoleContextType, contextID uint) (bool, error)
	GetActiveRoles(ctx context.Context, userID uint) ([]*models.UserRole, error)
	GetValidRoles(ctx context.Context, userID uint, timestamp time.Time) ([]*models.UserRole, error)

	// Primary role management
	GetPrimaryRole(ctx context.Context, userID uint, contextType models.UserRoleContextType, contextID uint) (*models.UserRole, error)
	SetPrimaryRole(ctx context.Context, userID uint, roleID uint, contextType models.UserRoleContextType, contextID uint) error
	ClearPrimaryRole(ctx context.Context, userID uint, contextType models.UserRoleContextType, contextID uint) error

	// Status management
	UpdateStatus(ctx context.Context, userRoleID uint, status models.UserRoleStatus) error
	Revoke(ctx context.Context, userRoleID uint, revokedBy uint) error
	RevokeByUserAndRole(ctx context.Context, userID uint, roleID uint, revokedBy uint) error
	Suspend(ctx context.Context, userRoleID uint) error
	Reactivate(ctx context.Context, userRoleID uint) error

	// Bulk operations
	GetByIDs(ctx context.Context, ids []uint) ([]*models.UserRole, error)
	CreateBulk(ctx context.Context, userRoles []*models.UserRole) error
	RevokeBulk(ctx context.Context, userRoleIDs []uint, revokedBy uint) error

	// Temporal operations
	GetExpiredRoles(ctx context.Context, timestamp time.Time) ([]*models.UserRole, error)
	GetTemporaryRoles(ctx context.Context, userID uint) ([]*models.UserRole, error)
	GetInheritedRoles(ctx context.Context, userID uint) ([]*models.UserRole, error)

	// Search and filtering
	Search(ctx context.Context, filters *UserRoleFilters) ([]*models.UserRole, error)
	Count(ctx context.Context, filters *UserRoleFilters) (int64, error)

	// User management
	GetUsersByRole(ctx context.Context, roleID uint) ([]*models.UserRole, error)
	GetRolesByUser(ctx context.Context, userID uint) ([]*models.UserRole, error)
	GetUsersInContext(ctx context.Context, contextType models.UserRoleContextType, contextID uint) ([]*models.UserRole, error)

	// Analytics and reporting
	GetRoleUsageByUser(ctx context.Context, userID uint) (map[uint]int64, error)
	GetUserUsageByRole(ctx context.Context, roleID uint) (map[uint]int64, error)
	GetMostAssignedRoles(ctx context.Context, limit int) ([]*models.Role, error)

	// Cleanup operations
	CleanupExpiredRoles(ctx context.Context) (int64, error)
	CleanupRevokedRoles(ctx context.Context, olderThan time.Time) (int64, error)

	// Permission resolution
	GetUserPermissions(ctx context.Context, userID uint) ([]*models.Permission, error)
	GetUserPermissionsInContext(ctx context.Context, userID uint, contextType models.UserRoleContextType, contextID uint) ([]*models.Permission, error)
	HasPermission(ctx context.Context, userID uint, permissionName string) (bool, error)
	HasPermissionInContext(ctx context.Context, userID uint, permissionName string, contextType models.UserRoleContextType, contextID uint) (bool, error)
}

// UserRoleFilters represents filters for querying user roles
type UserRoleFilters struct {
	UserID         *uint                       `json:"user_id,omitempty"`
	RoleID         *uint                       `json:"role_id,omitempty"`
	Status         *models.UserRoleStatus      `json:"status,omitempty"`
	ContextType    *models.UserRoleContextType `json:"context_type,omitempty"`
	ContextID      *uint                       `json:"context_id,omitempty"`
	IsPrimary      *bool                       `json:"is_primary,omitempty"`
	IsInherited    *bool                       `json:"is_inherited,omitempty"`
	IsTemporary    *bool                       `json:"is_temporary,omitempty"`
	AssignedBy     *uint                       `json:"assigned_by,omitempty"`
	ValidFrom      *time.Time                  `json:"valid_from,omitempty"`
	ValidUntil     *time.Time                  `json:"valid_until,omitempty"`
	AssignedAfter  *time.Time                  `json:"assigned_after,omitempty"`
	AssignedBefore *time.Time                  `json:"assigned_before,omitempty"`
	SortBy         string                      `json:"sort_by,omitempty"`
	SortOrder      string                      `json:"sort_order,omitempty"`
	Limit          int                         `json:"limit,omitempty"`
	Offset         int                         `json:"offset,omitempty"`
}
