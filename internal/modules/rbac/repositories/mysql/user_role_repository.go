package mysql

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories"
	"gorm.io/gorm"
)

type userRoleRepository struct {
	db *gorm.DB
}

// NewUserRoleRepository creates a new MySQL user role repository
func NewUserRoleRepository(db *gorm.DB) repositories.UserRoleRepository {
	return &userRoleRepository{db: db}
}

// Create creates a new user role
func (r *userRoleRepository) Create(ctx context.Context, userRole *models.UserRole) error {
	if err := r.db.WithContext(ctx).Create(userRole).Error; err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") {
			return fmt.Errorf("user role already exists")
		}
		return fmt.Errorf("failed to create user role: %w", err)
	}
	return nil
}

// GetByID retrieves a user role by ID
func (r *userRoleRepository) GetByID(ctx context.Context, id uint) (*models.UserRole, error) {
	var userRole models.UserRole
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&userRole).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get user role by id: %w", err)
	}
	return &userRole, nil
}

// Update updates a user role
func (r *userRoleRepository) Update(ctx context.Context, userRole *models.UserRole) error {
	if err := r.db.WithContext(ctx).Save(userRole).Error; err != nil {
		return fmt.Errorf("failed to update user role: %w", err)
	}
	return nil
}

// Delete deletes a user role
func (r *userRoleRepository) Delete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Delete(&models.UserRole{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete user role: %w", err)
	}
	return nil
}

// GetByUser retrieves user roles by user ID
func (r *userRoleRepository) GetByUser(ctx context.Context, userID uint) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole
	if err := r.db.WithContext(ctx).Where("user_id = ?", userID).Find(&userRoles).Error; err != nil {
		return nil, fmt.Errorf("failed to get user roles by user: %w", err)
	}
	return userRoles, nil
}

// GetByRole retrieves user roles by role ID
func (r *userRoleRepository) GetByRole(ctx context.Context, roleID uint) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole
	if err := r.db.WithContext(ctx).Where("role_id = ?", roleID).Find(&userRoles).Error; err != nil {
		return nil, fmt.Errorf("failed to get user roles by role: %w", err)
	}
	return userRoles, nil
}

// GetByUserAndRole retrieves a user role by user and role
func (r *userRoleRepository) GetByUserAndRole(ctx context.Context, userID uint, roleID uint) (*models.UserRole, error) {
	var userRole models.UserRole
	if err := r.db.WithContext(ctx).Where("user_id = ? AND role_id = ?", userID, roleID).First(&userRole).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get user role by user and role: %w", err)
	}
	return &userRole, nil
}

// GetByContext retrieves user roles by context
func (r *userRoleRepository) GetByContext(ctx context.Context, contextType models.UserRoleContextType, contextID uint) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole
	if err := r.db.WithContext(ctx).Where("context_type = ? AND context_id = ?", contextType, contextID).Find(&userRoles).Error; err != nil {
		return nil, fmt.Errorf("failed to get user roles by context: %w", err)
	}
	return userRoles, nil
}

// GetByUserAndContext retrieves user roles by user and context
func (r *userRoleRepository) GetByUserAndContext(ctx context.Context, userID uint, contextType models.UserRoleContextType, contextID uint) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole
	query := r.db.WithContext(ctx).Where("user_id = ? AND context_type = ?", userID, contextType)
	if contextID > 0 {
		query = query.Where("context_id = ?", contextID)
	}
	if err := query.Find(&userRoles).Error; err != nil {
		return nil, fmt.Errorf("failed to get user roles by user and context: %w", err)
	}
	return userRoles, nil
}

// GetByUserRoleAndContext retrieves a user role by user, role and context
func (r *userRoleRepository) GetByUserRoleAndContext(ctx context.Context, userID uint, roleID uint, contextType models.UserRoleContextType, contextID uint) (*models.UserRole, error) {
	var userRole models.UserRole
	query := r.db.WithContext(ctx).Where("user_id = ? AND role_id = ? AND context_type = ?", userID, roleID, contextType)
	if contextID > 0 {
		query = query.Where("context_id = ?", contextID)
	}
	if err := query.First(&userRole).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get user role by user, role and context: %w", err)
	}
	return &userRole, nil
}

// HasRole checks if a user has a specific role
func (r *userRoleRepository) HasRole(ctx context.Context, userID uint, roleID uint) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.UserRole{}).
		Where("user_id = ? AND role_id = ? AND status = ?", userID, roleID, models.UserRoleStatusActive).
		Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check user role: %w", err)
	}
	return count > 0, nil
}

// HasRoleInContext checks if a user has a specific role in context
func (r *userRoleRepository) HasRoleInContext(ctx context.Context, userID uint, roleID uint, contextType models.UserRoleContextType, contextID uint) (bool, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.UserRole{}).
		Where("user_id = ? AND role_id = ? AND context_type = ? AND status = ?", userID, roleID, contextType, models.UserRoleStatusActive)
	if contextID > 0 {
		query = query.Where("context_id = ?", contextID)
	}
	if err := query.Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check user role in context: %w", err)
	}
	return count > 0, nil
}

// GetActiveRoles retrieves active roles for a user
func (r *userRoleRepository) GetActiveRoles(ctx context.Context, userID uint) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole
	if err := r.db.WithContext(ctx).Where("user_id = ? AND status = ?", userID, models.UserRoleStatusActive).Find(&userRoles).Error; err != nil {
		return nil, fmt.Errorf("failed to get active roles: %w", err)
	}
	return userRoles, nil
}

// GetValidRoles retrieves valid roles for a user at a specific time
func (r *userRoleRepository) GetValidRoles(ctx context.Context, userID uint, timestamp time.Time) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole
	if err := r.db.WithContext(ctx).Where("user_id = ? AND status = ? AND (valid_from IS NULL OR valid_from <= ?) AND (valid_until IS NULL OR valid_until >= ?)",
		userID, models.UserRoleStatusActive, timestamp, timestamp).Find(&userRoles).Error; err != nil {
		return nil, fmt.Errorf("failed to get valid roles: %w", err)
	}
	return userRoles, nil
}

// GetPrimaryRole retrieves the primary role for a user in a context
func (r *userRoleRepository) GetPrimaryRole(ctx context.Context, userID uint, contextType models.UserRoleContextType, contextID uint) (*models.UserRole, error) {
	var userRole models.UserRole
	query := r.db.WithContext(ctx).Where("user_id = ? AND context_type = ? AND is_primary = ? AND status = ?",
		userID, contextType, true, models.UserRoleStatusActive)
	if contextID > 0 {
		query = query.Where("context_id = ?", contextID)
	}
	if err := query.First(&userRole).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get primary role: %w", err)
	}
	return &userRole, nil
}

// SetPrimaryRole sets a role as primary for a user in a context
func (r *userRoleRepository) SetPrimaryRole(ctx context.Context, userID uint, roleID uint, contextType models.UserRoleContextType, contextID uint) error {
	// First, clear any existing primary role
	if err := r.ClearPrimaryRole(ctx, userID, contextType, contextID); err != nil {
		return err
	}

	// Set the new primary role
	query := r.db.WithContext(ctx).Model(&models.UserRole{}).
		Where("user_id = ? AND role_id = ? AND context_type = ? AND status = ?",
			userID, roleID, contextType, models.UserRoleStatusActive)
	if contextID > 0 {
		query = query.Where("context_id = ?", contextID)
	}

	result := query.Update("is_primary", true)
	if result.Error != nil {
		return fmt.Errorf("failed to set primary role: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("user role not found")
	}

	return nil
}

// ClearPrimaryRole clears the primary role for a user in a context
func (r *userRoleRepository) ClearPrimaryRole(ctx context.Context, userID uint, contextType models.UserRoleContextType, contextID uint) error {
	query := r.db.WithContext(ctx).Model(&models.UserRole{}).
		Where("user_id = ? AND context_type = ? AND is_primary = ?", userID, contextType, true)
	if contextID > 0 {
		query = query.Where("context_id = ?", contextID)
	}

	if err := query.Update("is_primary", false).Error; err != nil {
		return fmt.Errorf("failed to clear primary role: %w", err)
	}

	return nil
}

// UpdateStatus updates a user role's status
func (r *userRoleRepository) UpdateStatus(ctx context.Context, userRoleID uint, status models.UserRoleStatus) error {
	result := r.db.WithContext(ctx).Model(&models.UserRole{}).
		Where("id = ?", userRoleID).
		Update("status", status)

	if result.Error != nil {
		return fmt.Errorf("failed to update user role status: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("user role not found")
	}

	return nil
}

// Revoke revokes a user role
func (r *userRoleRepository) Revoke(ctx context.Context, userRoleID uint, revokedBy uint) error {
	now := time.Now()
	result := r.db.WithContext(ctx).Model(&models.UserRole{}).
		Where("id = ?", userRoleID).
		Updates(map[string]interface{}{
			"status":     models.UserRoleStatusRevoked,
			"revoked_at": &now,
			"revoked_by": revokedBy,
		})

	if result.Error != nil {
		return fmt.Errorf("failed to revoke user role: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("user role not found")
	}

	return nil
}

// RevokeByUserAndRole revokes a user role by user and role
func (r *userRoleRepository) RevokeByUserAndRole(ctx context.Context, userID uint, roleID uint, revokedBy uint) error {
	now := time.Now()
	result := r.db.WithContext(ctx).Model(&models.UserRole{}).
		Where("user_id = ? AND role_id = ? AND status = ?", userID, roleID, models.UserRoleStatusActive).
		Updates(map[string]interface{}{
			"status":     models.UserRoleStatusRevoked,
			"revoked_at": &now,
			"revoked_by": revokedBy,
		})

	if result.Error != nil {
		return fmt.Errorf("failed to revoke user role: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("user role not found or already revoked")
	}

	return nil
}

// Suspend suspends a user role
func (r *userRoleRepository) Suspend(ctx context.Context, userRoleID uint) error {
	return r.UpdateStatus(ctx, userRoleID, models.UserRoleStatusSuspended)
}

// Reactivate reactivates a user role
func (r *userRoleRepository) Reactivate(ctx context.Context, userRoleID uint) error {
	return r.UpdateStatus(ctx, userRoleID, models.UserRoleStatusActive)
}

// GetByIDs retrieves user roles by IDs
func (r *userRoleRepository) GetByIDs(ctx context.Context, ids []uint) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole
	if err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&userRoles).Error; err != nil {
		return nil, fmt.Errorf("failed to get user roles by ids: %w", err)
	}
	return userRoles, nil
}

// CreateBulk creates multiple user roles
func (r *userRoleRepository) CreateBulk(ctx context.Context, userRoles []*models.UserRole) error {
	if err := r.db.WithContext(ctx).Create(userRoles).Error; err != nil {
		return fmt.Errorf("failed to create user roles in bulk: %w", err)
	}
	return nil
}

// RevokeBulk revokes multiple user roles
func (r *userRoleRepository) RevokeBulk(ctx context.Context, userRoleIDs []uint, revokedBy uint) error {
	now := time.Now()
	result := r.db.WithContext(ctx).Model(&models.UserRole{}).
		Where("id IN ? AND status = ?", userRoleIDs, models.UserRoleStatusActive).
		Updates(map[string]interface{}{
			"status":     models.UserRoleStatusRevoked,
			"revoked_at": &now,
			"revoked_by": revokedBy,
		})

	if result.Error != nil {
		return fmt.Errorf("failed to revoke user roles in bulk: %w", result.Error)
	}

	return nil
}

// GetExpiredRoles retrieves expired roles
func (r *userRoleRepository) GetExpiredRoles(ctx context.Context, timestamp time.Time) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole
	if err := r.db.WithContext(ctx).Where("status = ? AND valid_until IS NOT NULL AND valid_until < ?",
		models.UserRoleStatusActive, timestamp).Find(&userRoles).Error; err != nil {
		return nil, fmt.Errorf("failed to get expired roles: %w", err)
	}
	return userRoles, nil
}

// GetTemporaryRoles retrieves temporary roles for a user
func (r *userRoleRepository) GetTemporaryRoles(ctx context.Context, userID uint) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole
	if err := r.db.WithContext(ctx).Where("user_id = ? AND is_temporary = ? AND status = ?",
		userID, true, models.UserRoleStatusActive).Find(&userRoles).Error; err != nil {
		return nil, fmt.Errorf("failed to get temporary roles: %w", err)
	}
	return userRoles, nil
}

// GetInheritedRoles retrieves inherited roles for a user
func (r *userRoleRepository) GetInheritedRoles(ctx context.Context, userID uint) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole
	if err := r.db.WithContext(ctx).Where("user_id = ? AND is_inherited = ? AND status = ?",
		userID, true, models.UserRoleStatusActive).Find(&userRoles).Error; err != nil {
		return nil, fmt.Errorf("failed to get inherited roles: %w", err)
	}
	return userRoles, nil
}

// Search searches user roles with filters
func (r *userRoleRepository) Search(ctx context.Context, filters *repositories.UserRoleFilters) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole

	query := r.db.WithContext(ctx)

	if filters != nil {
		if filters.UserID != nil {
			query = query.Where("user_id = ?", *filters.UserID)
		}
		if filters.RoleID != nil {
			query = query.Where("role_id = ?", *filters.RoleID)
		}
		if filters.Status != nil {
			query = query.Where("status = ?", *filters.Status)
		}
		if filters.ContextType != nil {
			query = query.Where("context_type = ?", *filters.ContextType)
		}
		if filters.ContextID != nil {
			query = query.Where("context_id = ?", *filters.ContextID)
		}
		if filters.IsPrimary != nil {
			query = query.Where("is_primary = ?", *filters.IsPrimary)
		}
		if filters.IsInherited != nil {
			query = query.Where("is_inherited = ?", *filters.IsInherited)
		}
		if filters.IsTemporary != nil {
			query = query.Where("is_temporary = ?", *filters.IsTemporary)
		}
		if filters.AssignedBy != nil {
			query = query.Where("assigned_by = ?", *filters.AssignedBy)
		}
		if filters.ValidFrom != nil {
			query = query.Where("valid_from >= ?", *filters.ValidFrom)
		}
		if filters.ValidUntil != nil {
			query = query.Where("valid_until <= ?", *filters.ValidUntil)
		}
		if filters.AssignedAfter != nil {
			query = query.Where("assigned_at >= ?", *filters.AssignedAfter)
		}
		if filters.AssignedBefore != nil {
			query = query.Where("assigned_at <= ?", *filters.AssignedBefore)
		}

		// Apply sorting
		if filters.SortBy != "" {
			order := "ASC"
			if filters.SortOrder == "desc" {
				order = "DESC"
			}
			query = query.Order(fmt.Sprintf("%s %s", filters.SortBy, order))
		} else {
			query = query.Order("assigned_at DESC")
		}

		// Apply pagination
		if filters.Limit > 0 {
			query = query.Limit(filters.Limit)
		}
		if filters.Offset > 0 {
			query = query.Offset(filters.Offset)
		}
	}

	if err := query.Find(&userRoles).Error; err != nil {
		return nil, fmt.Errorf("failed to search user roles: %w", err)
	}

	return userRoles, nil
}

// Count counts user roles with filters
func (r *userRoleRepository) Count(ctx context.Context, filters *repositories.UserRoleFilters) (int64, error) {
	var count int64

	query := r.db.WithContext(ctx).Model(&models.UserRole{})

	if filters != nil {
		if filters.UserID != nil {
			query = query.Where("user_id = ?", *filters.UserID)
		}
		if filters.RoleID != nil {
			query = query.Where("role_id = ?", *filters.RoleID)
		}
		if filters.Status != nil {
			query = query.Where("status = ?", *filters.Status)
		}
		// Add other filters as needed
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count user roles: %w", err)
	}

	return count, nil
}

// GetUsersByRole retrieves users by role
func (r *userRoleRepository) GetUsersByRole(ctx context.Context, roleID uint) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole
	if err := r.db.WithContext(ctx).Where("role_id = ? AND status = ?", roleID, models.UserRoleStatusActive).Find(&userRoles).Error; err != nil {
		return nil, fmt.Errorf("failed to get users by role: %w", err)
	}
	return userRoles, nil
}

// GetRolesByUser retrieves roles by user
func (r *userRoleRepository) GetRolesByUser(ctx context.Context, userID uint) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole
	if err := r.db.WithContext(ctx).Where("user_id = ? AND status = ?", userID, models.UserRoleStatusActive).Find(&userRoles).Error; err != nil {
		return nil, fmt.Errorf("failed to get roles by user: %w", err)
	}
	return userRoles, nil
}

// GetUsersInContext retrieves users in a specific context
func (r *userRoleRepository) GetUsersInContext(ctx context.Context, contextType models.UserRoleContextType, contextID uint) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole
	query := r.db.WithContext(ctx).Where("context_type = ? AND status = ?", contextType, models.UserRoleStatusActive)
	if contextID > 0 {
		query = query.Where("context_id = ?", contextID)
	}
	if err := query.Find(&userRoles).Error; err != nil {
		return nil, fmt.Errorf("failed to get users in context: %w", err)
	}
	return userRoles, nil
}

// GetRoleUsageByUser retrieves role usage statistics by user
func (r *userRoleRepository) GetRoleUsageByUser(ctx context.Context, userID uint) (map[uint]int64, error) {
	var results []struct {
		RoleID uint  `json:"role_id"`
		Count  int64 `json:"count"`
	}

	if err := r.db.WithContext(ctx).Model(&models.UserRole{}).
		Select("role_id, COUNT(*) as count").
		Where("user_id = ? AND status = ?", userID, models.UserRoleStatusActive).
		Group("role_id").
		Find(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get role usage by user: %w", err)
	}

	usage := make(map[uint]int64)
	for _, result := range results {
		usage[result.RoleID] = result.Count
	}

	return usage, nil
}

// GetUserUsageByRole retrieves user usage statistics by role
func (r *userRoleRepository) GetUserUsageByRole(ctx context.Context, roleID uint) (map[uint]int64, error) {
	var results []struct {
		UserID uint  `json:"user_id"`
		Count  int64 `json:"count"`
	}

	if err := r.db.WithContext(ctx).Model(&models.UserRole{}).
		Select("user_id, COUNT(*) as count").
		Where("role_id = ? AND status = ?", roleID, models.UserRoleStatusActive).
		Group("user_id").
		Find(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get user usage by role: %w", err)
	}

	usage := make(map[uint]int64)
	for _, result := range results {
		usage[result.UserID] = result.Count
	}

	return usage, nil
}

// GetMostAssignedRoles retrieves the most assigned roles
func (r *userRoleRepository) GetMostAssignedRoles(ctx context.Context, limit int) ([]*models.Role, error) {
	var roles []*models.Role

	if err := r.db.WithContext(ctx).
		Table("rbac_roles").
		Joins("JOIN rbac_user_roles ON rbac_roles.id = rbac_user_roles.role_id").
		Where("rbac_user_roles.status = ?", models.UserRoleStatusActive).
		Group("rbac_roles.id").
		Order("COUNT(*) DESC").
		Limit(limit).
		Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to get most assigned roles: %w", err)
	}

	return roles, nil
}

// CleanupExpiredRoles removes expired roles
func (r *userRoleRepository) CleanupExpiredRoles(ctx context.Context) (int64, error) {
	now := time.Now()
	result := r.db.WithContext(ctx).Model(&models.UserRole{}).
		Where("status = ? AND valid_until IS NOT NULL AND valid_until < ?",
			models.UserRoleStatusActive, now).
		Update("status", models.UserRoleStatusRevoked)

	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup expired roles: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// CleanupRevokedRoles removes old revoked roles
func (r *userRoleRepository) CleanupRevokedRoles(ctx context.Context, olderThan time.Time) (int64, error) {
	result := r.db.WithContext(ctx).Where("status = ? AND revoked_at < ?",
		models.UserRoleStatusRevoked, olderThan).Delete(&models.UserRole{})

	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup revoked roles: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// GetUserPermissions retrieves all permissions for a user through roles
func (r *userRoleRepository) GetUserPermissions(ctx context.Context, userID uint) ([]*models.Permission, error) {
	var permissions []*models.Permission

	if err := r.db.WithContext(ctx).
		Table("rbac_permissions").
		Joins("JOIN rbac_role_permissions ON rbac_permissions.id = rbac_role_permissions.permission_id").
		Joins("JOIN rbac_user_roles ON rbac_role_permissions.role_id = rbac_user_roles.role_id").
		Where("rbac_user_roles.user_id = ? AND rbac_user_roles.status = ? AND rbac_role_permissions.status = ? AND rbac_permissions.status = ?",
			userID, models.UserRoleStatusActive, models.RolePermissionStatusActive, models.PermissionStatusActive).
		Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get user permissions: %w", err)
	}

	return permissions, nil
}

// GetUserPermissionsInContext retrieves permissions for a user in a specific context
func (r *userRoleRepository) GetUserPermissionsInContext(ctx context.Context, userID uint, contextType models.UserRoleContextType, contextID uint) ([]*models.Permission, error) {
	var permissions []*models.Permission

	query := r.db.WithContext(ctx).
		Table("rbac_permissions").
		Joins("JOIN rbac_role_permissions ON rbac_permissions.id = rbac_role_permissions.permission_id").
		Joins("JOIN rbac_user_roles ON rbac_role_permissions.role_id = rbac_user_roles.role_id").
		Where("rbac_user_roles.user_id = ? AND rbac_user_roles.context_type = ? AND rbac_user_roles.status = ? AND rbac_role_permissions.status = ? AND rbac_permissions.status = ?",
			userID, contextType, models.UserRoleStatusActive, models.RolePermissionStatusActive, models.PermissionStatusActive)

	if contextID > 0 {
		query = query.Where("rbac_user_roles.context_id = ?", contextID)
	}

	if err := query.Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get user permissions in context: %w", err)
	}

	return permissions, nil
}

// HasPermission checks if a user has a specific permission
func (r *userRoleRepository) HasPermission(ctx context.Context, userID uint, permissionName string) (bool, error) {
	var count int64

	if err := r.db.WithContext(ctx).
		Table("rbac_permissions").
		Joins("JOIN rbac_role_permissions ON rbac_permissions.id = rbac_role_permissions.permission_id").
		Joins("JOIN rbac_user_roles ON rbac_role_permissions.role_id = rbac_user_roles.role_id").
		Where("rbac_user_roles.user_id = ? AND rbac_permissions.name = ? AND rbac_user_roles.status = ? AND rbac_role_permissions.status = ? AND rbac_permissions.status = ?",
			userID, permissionName, models.UserRoleStatusActive, models.RolePermissionStatusActive, models.PermissionStatusActive).
		Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check user permission: %w", err)
	}

	return count > 0, nil
}

// HasPermissionInContext checks if a user has a specific permission in a context
func (r *userRoleRepository) HasPermissionInContext(ctx context.Context, userID uint, permissionName string, contextType models.UserRoleContextType, contextID uint) (bool, error) {
	var count int64

	query := r.db.WithContext(ctx).
		Table("rbac_permissions").
		Joins("JOIN rbac_role_permissions ON rbac_permissions.id = rbac_role_permissions.permission_id").
		Joins("JOIN rbac_user_roles ON rbac_role_permissions.role_id = rbac_user_roles.role_id").
		Where("rbac_user_roles.user_id = ? AND rbac_permissions.name = ? AND rbac_user_roles.context_type = ? AND rbac_user_roles.status = ? AND rbac_role_permissions.status = ? AND rbac_permissions.status = ?",
			userID, permissionName, contextType, models.UserRoleStatusActive, models.RolePermissionStatusActive, models.PermissionStatusActive)

	if contextID > 0 {
		query = query.Where("rbac_user_roles.context_id = ?", contextID)
	}

	if err := query.Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check user permission in context: %w", err)
	}

	return count > 0, nil
}
