package mysql

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories"
	"gorm.io/gorm"
)

type permissionGroupRepository struct {
	db *gorm.DB
}

// NewPermissionGroupRepository creates a new MySQL permission group repository
func NewPermissionGroupRepository(db *gorm.DB) repositories.PermissionGroupRepository {
	return &permissionGroupRepository{db: db}
}

// Create creates a new permission group
func (r *permissionGroupRepository) Create(ctx context.Context, group *models.PermissionGroup) error {
	if err := r.db.WithContext(ctx).Create(group).Error; err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") {
			return fmt.Errorf("permission group name already exists in this module")
		}
		return fmt.Errorf("failed to create permission group: %w", err)
	}
	return nil
}

// GetByID retrieves a permission group by ID
func (r *permissionGroupRepository) GetByID(ctx context.Context, id uint) (*models.PermissionGroup, error) {
	var group models.PermissionGroup
	if err := r.db.WithContext(ctx).Where("id = ? AND status != ?", id, models.PermissionGroupStatusDeprecated).First(&group).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get permission group by id: %w", err)
	}
	return &group, nil
}

// GetByName retrieves a permission group by module and name
func (r *permissionGroupRepository) GetByName(ctx context.Context, module, name string) (*models.PermissionGroup, error) {
	var group models.PermissionGroup
	if err := r.db.WithContext(ctx).Where("module = ? AND name = ? AND status != ?", module, name, models.PermissionGroupStatusDeprecated).First(&group).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get permission group by name: %w", err)
	}
	return &group, nil
}

// Update updates a permission group
func (r *permissionGroupRepository) Update(ctx context.Context, group *models.PermissionGroup) error {
	if err := r.db.WithContext(ctx).Save(group).Error; err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") {
			return fmt.Errorf("permission group name already exists in this module")
		}
		return fmt.Errorf("failed to update permission group: %w", err)
	}
	return nil
}

// Delete soft deletes a permission group
func (r *permissionGroupRepository) Delete(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).Model(&models.PermissionGroup{}).
		Where("id = ?", id).
		Update("status", models.PermissionGroupStatusDeprecated)

	if result.Error != nil {
		return fmt.Errorf("failed to delete permission group: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("permission group not found")
	}

	return nil
}

// GetByModule retrieves permission groups by module
func (r *permissionGroupRepository) GetByModule(ctx context.Context, module string, limit, offset int) ([]*models.PermissionGroup, error) {
	var groups []*models.PermissionGroup
	query := r.db.WithContext(ctx).Where("module = ? AND status != ?", module, models.PermissionGroupStatusDeprecated).Order("level ASC, sort_order ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("failed to get permission groups by module: %w", err)
	}

	return groups, nil
}

// GetByCategory retrieves permission groups by category
func (r *permissionGroupRepository) GetByCategory(ctx context.Context, category models.PermissionGroupCategory, limit, offset int) ([]*models.PermissionGroup, error) {
	var groups []*models.PermissionGroup
	query := r.db.WithContext(ctx).Where("category = ? AND status != ?", category, models.PermissionGroupStatusDeprecated).Order("module ASC, level ASC, sort_order ASC")

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("failed to get permission groups by category: %w", err)
	}

	return groups, nil
}

// GetActiveGroups retrieves active permission groups
func (r *permissionGroupRepository) GetActiveGroups(ctx context.Context) ([]*models.PermissionGroup, error) {
	var groups []*models.PermissionGroup
	if err := r.db.WithContext(ctx).Where("status = ?", models.PermissionGroupStatusActive).Order("module ASC, level ASC, sort_order ASC").Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("failed to get active permission groups: %w", err)
	}
	return groups, nil
}

// GetSystemGroups retrieves system permission groups
func (r *permissionGroupRepository) GetSystemGroups(ctx context.Context) ([]*models.PermissionGroup, error) {
	var groups []*models.PermissionGroup
	if err := r.db.WithContext(ctx).Where("is_system_group = ? AND status != ?", true, models.PermissionGroupStatusDeprecated).Order("module ASC, level ASC, sort_order ASC").Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("failed to get system permission groups: %w", err)
	}
	return groups, nil
}

// GetRequiredGroups retrieves required permission groups
func (r *permissionGroupRepository) GetRequiredGroups(ctx context.Context) ([]*models.PermissionGroup, error) {
	var groups []*models.PermissionGroup
	if err := r.db.WithContext(ctx).Where("is_required = ? AND status != ?", true, models.PermissionGroupStatusDeprecated).Order("module ASC, level ASC, sort_order ASC").Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("failed to get required permission groups: %w", err)
	}
	return groups, nil
}

// GetRootGroups retrieves root permission groups for a module
func (r *permissionGroupRepository) GetRootGroups(ctx context.Context, module string) ([]*models.PermissionGroup, error) {
	var groups []*models.PermissionGroup
	if err := r.db.WithContext(ctx).Where("module = ? AND parent_group_id IS NULL AND status != ?", module, models.PermissionGroupStatusDeprecated).Order("sort_order ASC").Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("failed to get root permission groups: %w", err)
	}
	return groups, nil
}

// GetChildGroups retrieves child permission groups
func (r *permissionGroupRepository) GetChildGroups(ctx context.Context, parentGroupID uint) ([]*models.PermissionGroup, error) {
	var groups []*models.PermissionGroup
	if err := r.db.WithContext(ctx).Where("parent_group_id = ? AND status != ?", parentGroupID, models.PermissionGroupStatusDeprecated).Order("sort_order ASC").Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("failed to get child permission groups: %w", err)
	}
	return groups, nil
}

// GetGroupHierarchy retrieves permission group hierarchy for a module
func (r *permissionGroupRepository) GetGroupHierarchy(ctx context.Context, module string) ([]*models.PermissionGroup, error) {
	var groups []*models.PermissionGroup
	if err := r.db.WithContext(ctx).Where("module = ? AND status != ?", module, models.PermissionGroupStatusDeprecated).Order("level ASC, sort_order ASC").Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("failed to get permission group hierarchy: %w", err)
	}
	return groups, nil
}

// GetGroupTree retrieves permission group tree starting from root
func (r *permissionGroupRepository) GetGroupTree(ctx context.Context, rootGroupID uint) ([]*models.PermissionGroup, error) {
	var groups []*models.PermissionGroup

	// Using recursive CTE to get the tree structure
	// This is a simplified version - in production, you might want to use a more sophisticated approach
	if err := r.db.WithContext(ctx).Raw(`
		WITH RECURSIVE group_tree AS (
			SELECT id, parent_group_id, name, display_name, level, sort_order, module, status
			FROM rbac_permission_groups 
			WHERE id = ? AND status != ?
			UNION ALL
			SELECT g.id, g.parent_group_id, g.name, g.display_name, g.level, g.sort_order, g.module, g.status
			FROM rbac_permission_groups g
			INNER JOIN group_tree gt ON g.parent_group_id = gt.id
			WHERE g.status != ?
		)
		SELECT * FROM group_tree ORDER BY level ASC, sort_order ASC
	`, rootGroupID, models.PermissionGroupStatusDeprecated, models.PermissionGroupStatusDeprecated).Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("failed to get permission group tree: %w", err)
	}

	return groups, nil
}

// GetGroupPath retrieves the path to a permission group
func (r *permissionGroupRepository) GetGroupPath(ctx context.Context, groupID uint) ([]*models.PermissionGroup, error) {
	var groups []*models.PermissionGroup

	// Using recursive CTE to get the path
	if err := r.db.WithContext(ctx).Raw(`
		WITH RECURSIVE group_path AS (
			SELECT id, parent_group_id, name, display_name, level, sort_order, module, status
			FROM rbac_permission_groups 
			WHERE id = ? AND status != ?
			UNION ALL
			SELECT g.id, g.parent_group_id, g.name, g.display_name, g.level, g.sort_order, g.module, g.status
			FROM rbac_permission_groups g
			INNER JOIN group_path gp ON g.id = gp.parent_group_id
			WHERE g.status != ?
		)
		SELECT * FROM group_path ORDER BY level ASC
	`, groupID, models.PermissionGroupStatusDeprecated, models.PermissionGroupStatusDeprecated).Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("failed to get permission group path: %w", err)
	}

	return groups, nil
}

// GetByLevel retrieves permission groups by level
func (r *permissionGroupRepository) GetByLevel(ctx context.Context, level uint) ([]*models.PermissionGroup, error) {
	var groups []*models.PermissionGroup
	if err := r.db.WithContext(ctx).Where("level = ? AND status != ?", level, models.PermissionGroupStatusDeprecated).Order("module ASC, sort_order ASC").Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("failed to get permission groups by level: %w", err)
	}
	return groups, nil
}

// GetOrderedGroups retrieves ordered permission groups by module and level
func (r *permissionGroupRepository) GetOrderedGroups(ctx context.Context, module string, level uint) ([]*models.PermissionGroup, error) {
	var groups []*models.PermissionGroup
	if err := r.db.WithContext(ctx).Where("module = ? AND level = ? AND status != ?", module, level, models.PermissionGroupStatusDeprecated).Order("sort_order ASC").Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("failed to get ordered permission groups: %w", err)
	}
	return groups, nil
}

// UpdateSortOrder updates the sort order of a permission group
func (r *permissionGroupRepository) UpdateSortOrder(ctx context.Context, groupID uint, sortOrder uint) error {
	result := r.db.WithContext(ctx).Model(&models.PermissionGroup{}).
		Where("id = ?", groupID).
		Update("sort_order", sortOrder)

	if result.Error != nil {
		return fmt.Errorf("failed to update sort order: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("permission group not found")
	}

	return nil
}

// ReorderGroups reorders permission groups
func (r *permissionGroupRepository) ReorderGroups(ctx context.Context, groupIDs []uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for i, groupID := range groupIDs {
			if err := tx.Model(&models.PermissionGroup{}).
				Where("id = ?", groupID).
				Update("sort_order", i).Error; err != nil {
				return fmt.Errorf("failed to reorder group %d: %w", groupID, err)
			}
		}
		return nil
	})
}

// UpdateStatus updates a permission group's status
func (r *permissionGroupRepository) UpdateStatus(ctx context.Context, groupID uint, status models.PermissionGroupStatus) error {
	result := r.db.WithContext(ctx).Model(&models.PermissionGroup{}).
		Where("id = ?", groupID).
		Update("status", status)

	if result.Error != nil {
		return fmt.Errorf("failed to update permission group status: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("permission group not found")
	}

	return nil
}

// GetByIDs retrieves permission groups by IDs
func (r *permissionGroupRepository) GetByIDs(ctx context.Context, ids []uint) ([]*models.PermissionGroup, error) {
	var groups []*models.PermissionGroup
	if err := r.db.WithContext(ctx).Where("id IN ? AND status != ?", ids, models.PermissionGroupStatusDeprecated).Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("failed to get permission groups by ids: %w", err)
	}
	return groups, nil
}

// CreateBulk creates multiple permission groups
func (r *permissionGroupRepository) CreateBulk(ctx context.Context, groups []*models.PermissionGroup) error {
	if err := r.db.WithContext(ctx).Create(groups).Error; err != nil {
		return fmt.Errorf("failed to create permission groups in bulk: %w", err)
	}
	return nil
}

// Count counts all permission groups
func (r *permissionGroupRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.PermissionGroup{}).Where("status != ?", models.PermissionGroupStatusDeprecated).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count permission groups: %w", err)
	}
	return count, nil
}

// CountByModule counts permission groups by module
func (r *permissionGroupRepository) CountByModule(ctx context.Context, module string) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.PermissionGroup{}).Where("module = ? AND status != ?", module, models.PermissionGroupStatusDeprecated).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count permission groups by module: %w", err)
	}
	return count, nil
}

// Search searches permission groups with filters
func (r *permissionGroupRepository) Search(ctx context.Context, query string, filters *repositories.PermissionGroupFilters) ([]*models.PermissionGroup, error) {
	var groups []*models.PermissionGroup

	dbQuery := r.db.WithContext(ctx).Where("status != ?", models.PermissionGroupStatusDeprecated)

	// Apply search query
	if query != "" {
		dbQuery = dbQuery.Where("name LIKE ? OR display_name LIKE ? OR description LIKE ?",
			"%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	// Apply filters
	if filters != nil {
		if filters.Status != nil {
			dbQuery = dbQuery.Where("status = ?", *filters.Status)
		}
		if filters.Module != nil {
			dbQuery = dbQuery.Where("module = ?", *filters.Module)
		}
		if filters.Category != nil {
			dbQuery = dbQuery.Where("category = ?", *filters.Category)
		}
		if filters.ParentGroupID != nil {
			dbQuery = dbQuery.Where("parent_group_id = ?", *filters.ParentGroupID)
		}
		if filters.Level != nil {
			dbQuery = dbQuery.Where("level = ?", *filters.Level)
		}
		if filters.IsSystemGroup != nil {
			dbQuery = dbQuery.Where("is_system_group = ?", *filters.IsSystemGroup)
		}
		if filters.IsRequired != nil {
			dbQuery = dbQuery.Where("is_required = ?", *filters.IsRequired)
		}
		if filters.CreatedAfter != nil {
			dbQuery = dbQuery.Where("created_at >= ?", *filters.CreatedAfter)
		}
		if filters.CreatedBefore != nil {
			dbQuery = dbQuery.Where("created_at <= ?", *filters.CreatedBefore)
		}

		// Apply sorting
		if filters.SortBy != "" {
			order := "ASC"
			if filters.SortOrder == "desc" {
				order = "DESC"
			}
			dbQuery = dbQuery.Order(fmt.Sprintf("%s %s", filters.SortBy, order))
		} else {
			dbQuery = dbQuery.Order("module ASC, level ASC, sort_order ASC")
		}

		// Apply pagination
		if filters.Limit > 0 {
			dbQuery = dbQuery.Limit(filters.Limit)
		}
		if filters.Offset > 0 {
			dbQuery = dbQuery.Offset(filters.Offset)
		}
	}

	if err := dbQuery.Find(&groups).Error; err != nil {
		return nil, fmt.Errorf("failed to search permission groups: %w", err)
	}

	return groups, nil
}

// Exists checks if a permission group exists by module and name
func (r *permissionGroupRepository) Exists(ctx context.Context, module, name string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.PermissionGroup{}).Where("module = ? AND name = ? AND status != ?", module, name, models.PermissionGroupStatusDeprecated).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check permission group existence: %w", err)
	}
	return count > 0, nil
}

// GetModules retrieves all unique modules
func (r *permissionGroupRepository) GetModules(ctx context.Context) ([]string, error) {
	var modules []string
	if err := r.db.WithContext(ctx).Model(&models.PermissionGroup{}).
		Where("status != ?", models.PermissionGroupStatusDeprecated).
		Distinct("module").
		Pluck("module", &modules).Error; err != nil {
		return nil, fmt.Errorf("failed to get modules: %w", err)
	}
	return modules, nil
}

// GetCategoriesByModule retrieves categories by module
func (r *permissionGroupRepository) GetCategoriesByModule(ctx context.Context, module string) ([]models.PermissionGroupCategory, error) {
	var categories []models.PermissionGroupCategory
	if err := r.db.WithContext(ctx).Model(&models.PermissionGroup{}).
		Where("module = ? AND status != ?", module, models.PermissionGroupStatusDeprecated).
		Distinct("category").
		Pluck("category", &categories).Error; err != nil {
		return nil, fmt.Errorf("failed to get categories by module: %w", err)
	}
	return categories, nil
}

// GetGroupPermissions retrieves permissions for a group
func (r *permissionGroupRepository) GetGroupPermissions(ctx context.Context, groupID uint) ([]*models.Permission, error) {
	// This is a conceptual implementation - in practice, you'd need a join table or logic
	// to associate permissions with groups
	var permissions []*models.Permission
	// Implementation would depend on your specific association logic
	return permissions, nil
}

// GetPermissionsByGroup retrieves permissions by group
func (r *permissionGroupRepository) GetPermissionsByGroup(ctx context.Context, groupID uint) ([]*models.Permission, error) {
	return r.GetGroupPermissions(ctx, groupID)
}

// GetGroupsWithPermission retrieves groups that have a specific permission
func (r *permissionGroupRepository) GetGroupsWithPermission(ctx context.Context, permissionID uint) ([]*models.PermissionGroup, error) {
	var groups []*models.PermissionGroup
	// Implementation would depend on your specific association logic
	return groups, nil
}

// AddDefaultPermission adds a default permission to a group
func (r *permissionGroupRepository) AddDefaultPermission(ctx context.Context, groupID uint, permissionName string) error {
	// Get the group
	group, err := r.GetByID(ctx, groupID)
	if err != nil {
		return err
	}
	if group == nil {
		return fmt.Errorf("permission group not found")
	}

	// Check if permission already exists
	for _, perm := range group.DefaultPermissions {
		if perm == permissionName {
			return fmt.Errorf("permission already exists in default permissions")
		}
	}

	// Add the permission
	group.DefaultPermissions = append(group.DefaultPermissions, permissionName)

	// Update the group
	return r.Update(ctx, group)
}

// RemoveDefaultPermission removes a default permission from a group
func (r *permissionGroupRepository) RemoveDefaultPermission(ctx context.Context, groupID uint, permissionName string) error {
	// Get the group
	group, err := r.GetByID(ctx, groupID)
	if err != nil {
		return err
	}
	if group == nil {
		return fmt.Errorf("permission group not found")
	}

	// Find and remove the permission
	for i, perm := range group.DefaultPermissions {
		if perm == permissionName {
			group.DefaultPermissions = append(group.DefaultPermissions[:i], group.DefaultPermissions[i+1:]...)
			// Update the group
			return r.Update(ctx, group)
		}
	}

	return fmt.Errorf("permission not found in default permissions")
}

// GetDefaultPermissions retrieves default permissions for a group
func (r *permissionGroupRepository) GetDefaultPermissions(ctx context.Context, groupID uint) ([]string, error) {
	group, err := r.GetByID(ctx, groupID)
	if err != nil {
		return nil, err
	}
	if group == nil {
		return nil, fmt.Errorf("permission group not found")
	}

	return group.DefaultPermissions, nil
}

// UpdateMetadata updates metadata for a permission group
func (r *permissionGroupRepository) UpdateMetadata(ctx context.Context, groupID uint, metadata models.PermissionGroupMetadata) error {
	result := r.db.WithContext(ctx).Model(&models.PermissionGroup{}).
		Where("id = ?", groupID).
		Update("group_metadata", metadata)

	if result.Error != nil {
		return fmt.Errorf("failed to update permission group metadata: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("permission group not found")
	}

	return nil
}

// GetMetadata retrieves metadata for a permission group
func (r *permissionGroupRepository) GetMetadata(ctx context.Context, groupID uint) (models.PermissionGroupMetadata, error) {
	group, err := r.GetByID(ctx, groupID)
	if err != nil {
		return nil, err
	}
	if group == nil {
		return nil, fmt.Errorf("permission group not found")
	}

	return group.GroupMetadata, nil
}

// ValidateHierarchy validates if a hierarchy is valid
func (r *permissionGroupRepository) ValidateHierarchy(ctx context.Context, groupID uint, parentGroupID uint) error {
	// Check for circular reference
	circular, err := r.DetectCircularReferences(ctx, groupID, parentGroupID)
	if err != nil {
		return err
	}
	if circular {
		return fmt.Errorf("circular reference detected in hierarchy")
	}

	// Additional validation logic can be added here
	return nil
}

// DetectCircularReferences detects circular references in hierarchy
func (r *permissionGroupRepository) DetectCircularReferences(ctx context.Context, groupID uint, parentGroupID uint) (bool, error) {
	if groupID == parentGroupID {
		return true, nil
	}

	// Check if parentGroupID is a descendant of groupID
	var count int64
	if err := r.db.WithContext(ctx).Raw(`
		WITH RECURSIVE group_hierarchy AS (
			SELECT id, parent_group_id
			FROM rbac_permission_groups 
			WHERE id = ? AND status != ?
			UNION ALL
			SELECT g.id, g.parent_group_id
			FROM rbac_permission_groups g
			INNER JOIN group_hierarchy gh ON g.parent_group_id = gh.id
			WHERE g.status != ?
		)
		SELECT COUNT(*) FROM group_hierarchy WHERE id = ?
	`, groupID, models.PermissionGroupStatusDeprecated, models.PermissionGroupStatusDeprecated, parentGroupID).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to detect circular references: %w", err)
	}

	return count > 0, nil
}

// GetMaxLevel retrieves the maximum level in a module
func (r *permissionGroupRepository) GetMaxLevel(ctx context.Context, module string) (uint, error) {
	var maxLevel uint
	if err := r.db.WithContext(ctx).Model(&models.PermissionGroup{}).
		Where("module = ? AND status != ?", module, models.PermissionGroupStatusDeprecated).
		Select("COALESCE(MAX(level), 0)").
		Scan(&maxLevel).Error; err != nil {
		return 0, fmt.Errorf("failed to get max level: %w", err)
	}
	return maxLevel, nil
}
