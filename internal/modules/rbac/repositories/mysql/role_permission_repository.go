package mysql

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories"
	"gorm.io/gorm"
)

type rolePermissionRepository struct {
	db *gorm.DB
}

// NewRolePermissionRepository creates a new MySQL role permission repository
func NewRolePermissionRepository(db *gorm.DB) repositories.RolePermissionRepository {
	return &rolePermissionRepository{db: db}
}

// Create creates a new role permission
func (r *rolePermissionRepository) Create(ctx context.Context, rolePermission *models.RolePermission) error {
	if err := r.db.WithContext(ctx).Create(rolePermission).Error; err != nil {
		if strings.Contains(err.<PERSON>r(), "Duplicate entry") {
			return fmt.Errorf("role permission already exists")
		}
		return fmt.Errorf("failed to create role permission: %w", err)
	}
	return nil
}

// GetByID retrieves a role permission by ID
func (r *rolePermissionRepository) GetByID(ctx context.Context, id uint) (*models.RolePermission, error) {
	var rolePermission models.RolePermission
	if err := r.db.WithContext(ctx).Where("id = ?", id).First(&rolePermission).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get role permission by id: %w", err)
	}
	return &rolePermission, nil
}

// Update updates a role permission
func (r *rolePermissionRepository) Update(ctx context.Context, rolePermission *models.RolePermission) error {
	if err := r.db.WithContext(ctx).Save(rolePermission).Error; err != nil {
		return fmt.Errorf("failed to update role permission: %w", err)
	}
	return nil
}

// Delete deletes a role permission
func (r *rolePermissionRepository) Delete(ctx context.Context, id uint) error {
	if err := r.db.WithContext(ctx).Delete(&models.RolePermission{}, id).Error; err != nil {
		return fmt.Errorf("failed to delete role permission: %w", err)
	}
	return nil
}

// GetByRole retrieves role permissions by role ID
func (r *rolePermissionRepository) GetByRole(ctx context.Context, roleID uint) ([]*models.RolePermission, error) {
	var rolePermissions []*models.RolePermission
	if err := r.db.WithContext(ctx).Where("role_id = ?", roleID).Find(&rolePermissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get role permissions by role: %w", err)
	}
	return rolePermissions, nil
}

// GetByPermission retrieves role permissions by permission ID
func (r *rolePermissionRepository) GetByPermission(ctx context.Context, permissionID uint) ([]*models.RolePermission, error) {
	var rolePermissions []*models.RolePermission
	if err := r.db.WithContext(ctx).Where("permission_id = ?", permissionID).Find(&rolePermissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get role permissions by permission: %w", err)
	}
	return rolePermissions, nil
}

// GetByRoleAndPermission retrieves a role permission by role and permission
func (r *rolePermissionRepository) GetByRoleAndPermission(ctx context.Context, roleID uint, permissionID uint) (*models.RolePermission, error) {
	var rolePermission models.RolePermission
	if err := r.db.WithContext(ctx).Where("role_id = ? AND permission_id = ?", roleID, permissionID).First(&rolePermission).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get role permission by role and permission: %w", err)
	}
	return &rolePermission, nil
}

// GetByContext retrieves role permissions by context
func (r *rolePermissionRepository) GetByContext(ctx context.Context, contextType models.RolePermissionContextType, contextID uint) ([]*models.RolePermission, error) {
	var rolePermissions []*models.RolePermission
	if err := r.db.WithContext(ctx).Where("context_type = ? AND context_id = ?", contextType, contextID).Find(&rolePermissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get role permissions by context: %w", err)
	}
	return rolePermissions, nil
}

// GetByRoleAndContext retrieves role permissions by role and context
func (r *rolePermissionRepository) GetByRoleAndContext(ctx context.Context, roleID uint, contextType models.RolePermissionContextType, contextID uint) ([]*models.RolePermission, error) {
	var rolePermissions []*models.RolePermission
	if err := r.db.WithContext(ctx).Where("role_id = ? AND context_type = ? AND context_id = ?", roleID, contextType, contextID).Find(&rolePermissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get role permissions by role and context: %w", err)
	}
	return rolePermissions, nil
}

// HasPermission checks if a role has a specific permission
func (r *rolePermissionRepository) HasPermission(ctx context.Context, roleID uint, permissionID uint) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.RolePermission{}).
		Where("role_id = ? AND permission_id = ? AND status = ?", roleID, permissionID, models.RolePermissionStatusActive).
		Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check role permission: %w", err)
	}
	return count > 0, nil
}

// HasPermissionInContext checks if a role has a specific permission in context
func (r *rolePermissionRepository) HasPermissionInContext(ctx context.Context, roleID uint, permissionID uint, contextType models.RolePermissionContextType, contextID uint) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.RolePermission{}).
		Where("role_id = ? AND permission_id = ? AND context_type = ? AND context_id = ? AND status = ?",
			roleID, permissionID, contextType, contextID, models.RolePermissionStatusActive).
		Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check role permission in context: %w", err)
	}
	return count > 0, nil
}

// GetActivePermissions retrieves active permissions for a role
func (r *rolePermissionRepository) GetActivePermissions(ctx context.Context, roleID uint) ([]*models.RolePermission, error) {
	var rolePermissions []*models.RolePermission
	if err := r.db.WithContext(ctx).Where("role_id = ? AND status = ?", roleID, models.RolePermissionStatusActive).Find(&rolePermissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get active permissions: %w", err)
	}
	return rolePermissions, nil
}

// GetValidPermissions retrieves valid permissions for a role at a specific time
func (r *rolePermissionRepository) GetValidPermissions(ctx context.Context, roleID uint, timestamp time.Time) ([]*models.RolePermission, error) {
	var rolePermissions []*models.RolePermission
	if err := r.db.WithContext(ctx).Where("role_id = ? AND status = ? AND (valid_from IS NULL OR valid_from <= ?) AND (valid_until IS NULL OR valid_until >= ?)",
		roleID, models.RolePermissionStatusActive, timestamp, timestamp).Find(&rolePermissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get valid permissions: %w", err)
	}
	return rolePermissions, nil
}

// UpdateStatus updates a role permission's status
func (r *rolePermissionRepository) UpdateStatus(ctx context.Context, rolePermissionID uint, status models.RolePermissionStatus) error {
	result := r.db.WithContext(ctx).Model(&models.RolePermission{}).
		Where("id = ?", rolePermissionID).
		Update("status", status)

	if result.Error != nil {
		return fmt.Errorf("failed to update role permission status: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("role permission not found")
	}

	return nil
}

// Revoke revokes a role permission
func (r *rolePermissionRepository) Revoke(ctx context.Context, rolePermissionID uint, revokedBy uint) error {
	now := time.Now()
	result := r.db.WithContext(ctx).Model(&models.RolePermission{}).
		Where("id = ?", rolePermissionID).
		Updates(map[string]interface{}{
			"status":     models.RolePermissionStatusRevoked,
			"revoked_at": &now,
			"revoked_by": revokedBy,
		})

	if result.Error != nil {
		return fmt.Errorf("failed to revoke role permission: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("role permission not found")
	}

	return nil
}

// RevokeByRoleAndPermission revokes a role permission by role and permission
func (r *rolePermissionRepository) RevokeByRoleAndPermission(ctx context.Context, roleID uint, permissionID uint, revokedBy uint) error {
	now := time.Now()
	result := r.db.WithContext(ctx).Model(&models.RolePermission{}).
		Where("role_id = ? AND permission_id = ? AND status = ?", roleID, permissionID, models.RolePermissionStatusActive).
		Updates(map[string]interface{}{
			"status":     models.RolePermissionStatusRevoked,
			"revoked_at": &now,
			"revoked_by": revokedBy,
		})

	if result.Error != nil {
		return fmt.Errorf("failed to revoke role permission: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("role permission not found or already revoked")
	}

	return nil
}

// GetByIDs retrieves role permissions by IDs
func (r *rolePermissionRepository) GetByIDs(ctx context.Context, ids []uint) ([]*models.RolePermission, error) {
	var rolePermissions []*models.RolePermission
	if err := r.db.WithContext(ctx).Where("id IN ?", ids).Find(&rolePermissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get role permissions by ids: %w", err)
	}
	return rolePermissions, nil
}

// CreateBulk creates multiple role permissions
func (r *rolePermissionRepository) CreateBulk(ctx context.Context, rolePermissions []*models.RolePermission) error {
	if err := r.db.WithContext(ctx).Create(rolePermissions).Error; err != nil {
		return fmt.Errorf("failed to create role permissions in bulk: %w", err)
	}
	return nil
}

// RevokeBulk revokes multiple role permissions
func (r *rolePermissionRepository) RevokeBulk(ctx context.Context, rolePermissionIDs []uint, revokedBy uint) error {
	now := time.Now()
	result := r.db.WithContext(ctx).Model(&models.RolePermission{}).
		Where("id IN ? AND status = ?", rolePermissionIDs, models.RolePermissionStatusActive).
		Updates(map[string]interface{}{
			"status":     models.RolePermissionStatusRevoked,
			"revoked_at": &now,
			"revoked_by": revokedBy,
		})

	if result.Error != nil {
		return fmt.Errorf("failed to revoke role permissions in bulk: %w", result.Error)
	}

	return nil
}

// GetExpiredPermissions retrieves expired permissions
func (r *rolePermissionRepository) GetExpiredPermissions(ctx context.Context, timestamp time.Time) ([]*models.RolePermission, error) {
	var rolePermissions []*models.RolePermission
	if err := r.db.WithContext(ctx).Where("status = ? AND valid_until IS NOT NULL AND valid_until < ?",
		models.RolePermissionStatusActive, timestamp).Find(&rolePermissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get expired permissions: %w", err)
	}
	return rolePermissions, nil
}

// GetTemporaryPermissions retrieves temporary permissions for a role
func (r *rolePermissionRepository) GetTemporaryPermissions(ctx context.Context, roleID uint) ([]*models.RolePermission, error) {
	var rolePermissions []*models.RolePermission
	if err := r.db.WithContext(ctx).Where("role_id = ? AND is_temporary = ? AND status = ?",
		roleID, true, models.RolePermissionStatusActive).Find(&rolePermissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get temporary permissions: %w", err)
	}
	return rolePermissions, nil
}

// GetInheritedPermissions retrieves inherited permissions for a role
func (r *rolePermissionRepository) GetInheritedPermissions(ctx context.Context, roleID uint) ([]*models.RolePermission, error) {
	var rolePermissions []*models.RolePermission
	if err := r.db.WithContext(ctx).Where("role_id = ? AND is_inherited = ? AND status = ?",
		roleID, true, models.RolePermissionStatusActive).Find(&rolePermissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get inherited permissions: %w", err)
	}
	return rolePermissions, nil
}

// Search searches role permissions with filters
func (r *rolePermissionRepository) Search(ctx context.Context, filters *repositories.RolePermissionFilters) ([]*models.RolePermission, error) {
	var rolePermissions []*models.RolePermission

	query := r.db.WithContext(ctx)

	if filters != nil {
		if filters.RoleID != nil {
			query = query.Where("role_id = ?", *filters.RoleID)
		}
		if filters.PermissionID != nil {
			query = query.Where("permission_id = ?", *filters.PermissionID)
		}
		if filters.Status != nil {
			query = query.Where("status = ?", *filters.Status)
		}
		if filters.ContextType != nil {
			query = query.Where("context_type = ?", *filters.ContextType)
		}
		if filters.ContextID != nil {
			query = query.Where("context_id = ?", *filters.ContextID)
		}
		if filters.IsInherited != nil {
			query = query.Where("is_inherited = ?", *filters.IsInherited)
		}
		if filters.IsTemporary != nil {
			query = query.Where("is_temporary = ?", *filters.IsTemporary)
		}
		if filters.GrantedBy != nil {
			query = query.Where("granted_by = ?", *filters.GrantedBy)
		}
		if filters.ValidFrom != nil {
			query = query.Where("valid_from >= ?", *filters.ValidFrom)
		}
		if filters.ValidUntil != nil {
			query = query.Where("valid_until <= ?", *filters.ValidUntil)
		}
		if filters.GrantedAfter != nil {
			query = query.Where("granted_at >= ?", *filters.GrantedAfter)
		}
		if filters.GrantedBefore != nil {
			query = query.Where("granted_at <= ?", *filters.GrantedBefore)
		}

		// Apply sorting
		if filters.SortBy != "" {
			order := "ASC"
			if filters.SortOrder == "desc" {
				order = "DESC"
			}
			query = query.Order(fmt.Sprintf("%s %s", filters.SortBy, order))
		} else {
			query = query.Order("granted_at DESC")
		}

		// Apply pagination
		if filters.Limit > 0 {
			query = query.Limit(filters.Limit)
		}
		if filters.Offset > 0 {
			query = query.Offset(filters.Offset)
		}
	}

	if err := query.Find(&rolePermissions).Error; err != nil {
		return nil, fmt.Errorf("failed to search role permissions: %w", err)
	}

	return rolePermissions, nil
}

// Count counts role permissions with filters
func (r *rolePermissionRepository) Count(ctx context.Context, filters *repositories.RolePermissionFilters) (int64, error) {
	var count int64

	query := r.db.WithContext(ctx).Model(&models.RolePermission{})

	if filters != nil {
		if filters.RoleID != nil {
			query = query.Where("role_id = ?", *filters.RoleID)
		}
		if filters.PermissionID != nil {
			query = query.Where("permission_id = ?", *filters.PermissionID)
		}
		if filters.Status != nil {
			query = query.Where("status = ?", *filters.Status)
		}
		// Add other filters as needed
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count role permissions: %w", err)
	}

	return count, nil
}

// GetPermissionUsageByRole retrieves permission usage statistics by role
func (r *rolePermissionRepository) GetPermissionUsageByRole(ctx context.Context, roleID uint) (map[uint]int64, error) {
	var results []struct {
		PermissionID uint  `json:"permission_id"`
		Count        int64 `json:"count"`
	}

	if err := r.db.WithContext(ctx).Model(&models.RolePermission{}).
		Select("permission_id, COUNT(*) as count").
		Where("role_id = ? AND status = ?", roleID, models.RolePermissionStatusActive).
		Group("permission_id").
		Find(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get permission usage by role: %w", err)
	}

	usage := make(map[uint]int64)
	for _, result := range results {
		usage[result.PermissionID] = result.Count
	}

	return usage, nil
}

// GetRoleUsageByPermission retrieves role usage statistics by permission
func (r *rolePermissionRepository) GetRoleUsageByPermission(ctx context.Context, permissionID uint) (map[uint]int64, error) {
	var results []struct {
		RoleID uint  `json:"role_id"`
		Count  int64 `json:"count"`
	}

	if err := r.db.WithContext(ctx).Model(&models.RolePermission{}).
		Select("role_id, COUNT(*) as count").
		Where("permission_id = ? AND status = ?", permissionID, models.RolePermissionStatusActive).
		Group("role_id").
		Find(&results).Error; err != nil {
		return nil, fmt.Errorf("failed to get role usage by permission: %w", err)
	}

	usage := make(map[uint]int64)
	for _, result := range results {
		usage[result.RoleID] = result.Count
	}

	return usage, nil
}

// GetMostUsedPermissions retrieves the most used permissions
func (r *rolePermissionRepository) GetMostUsedPermissions(ctx context.Context, limit int) ([]*models.Permission, error) {
	var permissions []*models.Permission

	if err := r.db.WithContext(ctx).
		Table("rbac_permissions").
		Joins("JOIN rbac_role_permissions ON rbac_permissions.id = rbac_role_permissions.permission_id").
		Where("rbac_role_permissions.status = ?", models.RolePermissionStatusActive).
		Group("rbac_permissions.id").
		Order("COUNT(*) DESC").
		Limit(limit).
		Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get most used permissions: %w", err)
	}

	return permissions, nil
}

// CleanupExpiredPermissions removes expired permissions
func (r *rolePermissionRepository) CleanupExpiredPermissions(ctx context.Context) (int64, error) {
	now := time.Now()
	result := r.db.WithContext(ctx).Model(&models.RolePermission{}).
		Where("status = ? AND valid_until IS NOT NULL AND valid_until < ?",
			models.RolePermissionStatusActive, now).
		Update("status", models.RolePermissionStatusRevoked)

	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup expired permissions: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// CleanupRevokedPermissions removes old revoked permissions
func (r *rolePermissionRepository) CleanupRevokedPermissions(ctx context.Context, olderThan time.Time) (int64, error) {
	result := r.db.WithContext(ctx).Where("status = ? AND revoked_at < ?",
		models.RolePermissionStatusRevoked, olderThan).Delete(&models.RolePermission{})

	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup revoked permissions: %w", result.Error)
	}

	return result.RowsAffected, nil
}
