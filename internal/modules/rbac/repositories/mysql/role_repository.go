package mysql

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories"
	"gorm.io/gorm"
)

type roleRepository struct {
	db *gorm.DB
}

// NewRoleRepository creates a new MySQL role repository
func NewRoleRepository(db *gorm.DB) repositories.RoleRepository {
	return &roleRepository{db: db}
}

// Create creates a new role
func (r *roleRepository) Create(ctx context.Context, role *models.Role) error {
	if err := r.db.WithContext(ctx).Create(role).Error; err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") {
			return fmt.Errorf("role name already exists in this tenant and scope")
		}
		return fmt.Errorf("failed to create role: %w", err)
	}
	return nil
}

// GetByID retrieves a role by ID
func (r *roleRepository) GetByID(ctx context.Context, id uint) (*models.Role, error) {
	var role models.Role
	if err := r.db.WithContext(ctx).Where("id = ? AND status != ?", id, models.RoleStatusDeprecated).First(&role).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get role by id: %w", err)
	}
	return &role, nil
}

// GetByName retrieves a role by name within a tenant
func (r *roleRepository) GetByName(ctx context.Context, tenantID uint, name string) (*models.Role, error) {
	var role models.Role
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND name = ? AND status != ?", tenantID, name, models.RoleStatusDeprecated).First(&role).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get role by name: %w", err)
	}
	return &role, nil
}

// Update updates a role
func (r *roleRepository) Update(ctx context.Context, role *models.Role) error {
	if err := r.db.WithContext(ctx).Save(role).Error; err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") {
			return fmt.Errorf("role name already exists in this tenant and scope")
		}
		return fmt.Errorf("failed to update role: %w", err)
	}
	return nil
}

// Delete soft deletes a role
func (r *roleRepository) Delete(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).Model(&models.Role{}).
		Where("id = ?", id).
		Update("status", models.RoleStatusDeprecated)

	if result.Error != nil {
		return fmt.Errorf("failed to delete role: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("role not found")
	}

	return nil
}

// GetByTenant retrieves roles by tenant with pagination
func (r *roleRepository) GetByTenant(ctx context.Context, tenantID uint, limit, offset int) ([]*models.Role, error) {
	var roles []*models.Role
	query := r.db.WithContext(ctx).Where("tenant_id = ? AND status != ?", tenantID, models.RoleStatusDeprecated)

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to get roles by tenant: %w", err)
	}

	return roles, nil
}

// GetByScope retrieves roles by scope within a tenant
func (r *roleRepository) GetByScope(ctx context.Context, tenantID uint, scope models.RoleScope, contextID *uint) ([]*models.Role, error) {
	var roles []*models.Role
	query := r.db.WithContext(ctx).Where("tenant_id = ? AND scope = ? AND status != ?", tenantID, scope, models.RoleStatusDeprecated)

	if contextID != nil {
		query = query.Where("context_id = ?", *contextID)
	}

	if err := query.Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to get roles by scope: %w", err)
	}

	return roles, nil
}

// GetSystemRoles retrieves system roles for a tenant
func (r *roleRepository) GetSystemRoles(ctx context.Context, tenantID uint) ([]*models.Role, error) {
	var roles []*models.Role
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND is_system_role = ? AND status != ?", tenantID, true, models.RoleStatusDeprecated).Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to get system roles: %w", err)
	}
	return roles, nil
}

// GetDefaultRoles retrieves default roles for a tenant
func (r *roleRepository) GetDefaultRoles(ctx context.Context, tenantID uint) ([]*models.Role, error) {
	var roles []*models.Role
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND is_default_role = ? AND status != ?", tenantID, true, models.RoleStatusDeprecated).Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to get default roles: %w", err)
	}
	return roles, nil
}

// UpdateStatus updates a role's status
func (r *roleRepository) UpdateStatus(ctx context.Context, roleID uint, status models.RoleStatus) error {
	result := r.db.WithContext(ctx).Model(&models.Role{}).
		Where("id = ?", roleID).
		Update("status", status)

	if result.Error != nil {
		return fmt.Errorf("failed to update role status: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("role not found")
	}

	return nil
}

// GetActiveRolesByTenant retrieves active roles for a tenant
func (r *roleRepository) GetActiveRolesByTenant(ctx context.Context, tenantID uint) ([]*models.Role, error) {
	var roles []*models.Role
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND status = ?", tenantID, models.RoleStatusActive).Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to get active roles: %w", err)
	}
	return roles, nil
}

// GetByIDs retrieves roles by IDs
func (r *roleRepository) GetByIDs(ctx context.Context, ids []uint) ([]*models.Role, error) {
	var roles []*models.Role
	if err := r.db.WithContext(ctx).Where("id IN ? AND status != ?", ids, models.RoleStatusDeprecated).Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to get roles by ids: %w", err)
	}
	return roles, nil
}

// CountByTenant counts roles by tenant
func (r *roleRepository) CountByTenant(ctx context.Context, tenantID uint) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.Role{}).Where("tenant_id = ? AND status != ?", tenantID, models.RoleStatusDeprecated).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count roles by tenant: %w", err)
	}
	return count, nil
}

// Search searches roles with filters
func (r *roleRepository) Search(ctx context.Context, tenantID uint, query string, filters *repositories.RoleFilters) ([]*models.Role, error) {
	var roles []*models.Role

	dbQuery := r.db.WithContext(ctx).Where("tenant_id = ? AND status != ?", tenantID, models.RoleStatusDeprecated)

	// Apply search query
	if query != "" {
		dbQuery = dbQuery.Where("name LIKE ? OR display_name LIKE ? OR description LIKE ?",
			"%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	// Apply filters
	if filters != nil {
		if filters.Status != nil {
			dbQuery = dbQuery.Where("status = ?", *filters.Status)
		}
		if filters.Scope != nil {
			dbQuery = dbQuery.Where("scope = ?", *filters.Scope)
		}
		if filters.ContextID != nil {
			dbQuery = dbQuery.Where("context_id = ?", *filters.ContextID)
		}
		if filters.IsSystemRole != nil {
			dbQuery = dbQuery.Where("is_system_role = ?", *filters.IsSystemRole)
		}
		if filters.IsDefaultRole != nil {
			dbQuery = dbQuery.Where("is_default_role = ?", *filters.IsDefaultRole)
		}
		if filters.Level != nil {
			dbQuery = dbQuery.Where("level = ?", *filters.Level)
		}
		if filters.CreatedAfter != nil {
			dbQuery = dbQuery.Where("created_at >= ?", *filters.CreatedAfter)
		}
		if filters.CreatedBefore != nil {
			dbQuery = dbQuery.Where("created_at <= ?", *filters.CreatedBefore)
		}

		// Apply sorting
		if filters.SortBy != "" {
			order := "ASC"
			if filters.SortOrder == "desc" {
				order = "DESC"
			}
			dbQuery = dbQuery.Order(fmt.Sprintf("%s %s", filters.SortBy, order))
		} else {
			dbQuery = dbQuery.Order("level ASC, name ASC")
		}

		// Apply pagination
		if filters.Limit > 0 {
			dbQuery = dbQuery.Limit(filters.Limit)
		}
		if filters.Offset > 0 {
			dbQuery = dbQuery.Offset(filters.Offset)
		}
	}

	if err := dbQuery.Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to search roles: %w", err)
	}

	return roles, nil
}

// Exists checks if a role exists by name within a tenant
func (r *roleRepository) Exists(ctx context.Context, tenantID uint, name string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.Role{}).Where("tenant_id = ? AND name = ? AND status != ?", tenantID, name, models.RoleStatusDeprecated).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check role existence: %w", err)
	}
	return count > 0, nil
}

// GetRoleHierarchy retrieves roles ordered by level
func (r *roleRepository) GetRoleHierarchy(ctx context.Context, tenantID uint) ([]*models.Role, error) {
	var roles []*models.Role
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND status != ?", tenantID, models.RoleStatusDeprecated).Order("level ASC, name ASC").Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to get role hierarchy: %w", err)
	}
	return roles, nil
}

// GetRolesByLevel retrieves roles by level
func (r *roleRepository) GetRolesByLevel(ctx context.Context, tenantID uint, level uint) ([]*models.Role, error) {
	var roles []*models.Role
	if err := r.db.WithContext(ctx).Where("tenant_id = ? AND level = ? AND status != ?", tenantID, level, models.RoleStatusDeprecated).Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to get roles by level: %w", err)
	}
	return roles, nil
}

// GetRolePermissions retrieves permissions for a role
func (r *roleRepository) GetRolePermissions(ctx context.Context, roleID uint) ([]*models.Permission, error) {
	var permissions []*models.Permission
	if err := r.db.WithContext(ctx).
		Table("rbac_permissions").
		Joins("JOIN rbac_role_permissions ON rbac_permissions.id = rbac_role_permissions.permission_id").
		Where("rbac_role_permissions.role_id = ? AND rbac_role_permissions.status = ? AND rbac_permissions.status = ?",
			roleID, models.RolePermissionStatusActive, models.PermissionStatusActive).
		Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get role permissions: %w", err)
	}
	return permissions, nil
}

// GetRoleUsers retrieves user roles for a role
func (r *roleRepository) GetRoleUsers(ctx context.Context, roleID uint) ([]*models.UserRole, error) {
	var userRoles []*models.UserRole
	if err := r.db.WithContext(ctx).Where("role_id = ? AND status = ?", roleID, models.UserRoleStatusActive).Find(&userRoles).Error; err != nil {
		return nil, fmt.Errorf("failed to get role users: %w", err)
	}
	return userRoles, nil
}

// AssignPermission assigns a permission to a role
func (r *roleRepository) AssignPermission(ctx context.Context, roleID uint, permissionID uint, grantedBy *uint) error {
	rolePermission := &models.RolePermission{
		RoleID:       roleID,
		PermissionID: permissionID,
		GrantedBy:    grantedBy,
		Status:       models.RolePermissionStatusActive,
	}

	if err := r.db.WithContext(ctx).Create(rolePermission).Error; err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") {
			return fmt.Errorf("permission already assigned to role")
		}
		return fmt.Errorf("failed to assign permission to role: %w", err)
	}

	return nil
}

// RevokePermission revokes a permission from a role
func (r *roleRepository) RevokePermission(ctx context.Context, roleID uint, permissionID uint, revokedBy *uint) error {
	now := time.Now()
	result := r.db.WithContext(ctx).Model(&models.RolePermission{}).
		Where("role_id = ? AND permission_id = ? AND status = ?", roleID, permissionID, models.RolePermissionStatusActive).
		Updates(map[string]interface{}{
			"status":     models.RolePermissionStatusRevoked,
			"revoked_at": &now,
			"revoked_by": revokedBy,
		})

	if result.Error != nil {
		return fmt.Errorf("failed to revoke permission from role: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("permission not found or already revoked")
	}

	return nil
}

// HasPermission checks if a role has a specific permission
func (r *roleRepository) HasPermission(ctx context.Context, roleID uint, permissionID uint) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.RolePermission{}).
		Where("role_id = ? AND permission_id = ? AND status = ?", roleID, permissionID, models.RolePermissionStatusActive).
		Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check role permission: %w", err)
	}
	return count > 0, nil
}
