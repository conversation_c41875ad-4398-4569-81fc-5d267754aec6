package mysql

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/repositories"
	"gorm.io/gorm"
)

type permissionRepository struct {
	db *gorm.DB
}

// NewPermissionRepository creates a new MySQL permission repository
func NewPermissionRepository(db *gorm.DB) repositories.PermissionRepository {
	return &permissionRepository{db: db}
}

// Create creates a new permission
func (r *permissionRepository) Create(ctx context.Context, permission *models.Permission) error {
	if err := r.db.WithContext(ctx).Create(permission).Error; err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") {
			return fmt.Errorf("permission name already exists")
		}
		return fmt.Errorf("failed to create permission: %w", err)
	}
	return nil
}

// GetByID retrieves a permission by ID
func (r *permissionRepository) GetByID(ctx context.Context, id uint) (*models.Permission, error) {
	var permission models.Permission
	if err := r.db.WithContext(ctx).Where("id = ? AND status != ?", id, models.PermissionStatusDeprecated).First(&permission).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get permission by id: %w", err)
	}
	return &permission, nil
}

// GetByName retrieves a permission by name
func (r *permissionRepository) GetByName(ctx context.Context, name string) (*models.Permission, error) {
	var permission models.Permission
	if err := r.db.WithContext(ctx).Where("name = ? AND status != ?", name, models.PermissionStatusDeprecated).First(&permission).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get permission by name: %w", err)
	}
	return &permission, nil
}

// Update updates a permission
func (r *permissionRepository) Update(ctx context.Context, permission *models.Permission) error {
	if err := r.db.WithContext(ctx).Save(permission).Error; err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") {
			return fmt.Errorf("permission name already exists")
		}
		return fmt.Errorf("failed to update permission: %w", err)
	}
	return nil
}

// Delete soft deletes a permission
func (r *permissionRepository) Delete(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).Model(&models.Permission{}).
		Where("id = ?", id).
		Update("status", models.PermissionStatusDeprecated)

	if result.Error != nil {
		return fmt.Errorf("failed to delete permission: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("permission not found")
	}

	return nil
}

// GetByModule retrieves permissions by module
func (r *permissionRepository) GetByModule(ctx context.Context, module string, limit, offset int) ([]*models.Permission, error) {
	var permissions []*models.Permission
	query := r.db.WithContext(ctx).Where("module = ? AND status != ?", module, models.PermissionStatusDeprecated)

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get permissions by module: %w", err)
	}

	return permissions, nil
}

// GetByResource retrieves permissions by resource
func (r *permissionRepository) GetByResource(ctx context.Context, resource string, limit, offset int) ([]*models.Permission, error) {
	var permissions []*models.Permission
	query := r.db.WithContext(ctx).Where("resource = ? AND status != ?", resource, models.PermissionStatusDeprecated)

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get permissions by resource: %w", err)
	}

	return permissions, nil
}

// GetByAction retrieves permissions by action
func (r *permissionRepository) GetByAction(ctx context.Context, action string, limit, offset int) ([]*models.Permission, error) {
	var permissions []*models.Permission
	query := r.db.WithContext(ctx).Where("action = ? AND status != ?", action, models.PermissionStatusDeprecated)

	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get permissions by action: %w", err)
	}

	return permissions, nil
}

// GetByScope retrieves permissions by scope
func (r *permissionRepository) GetByScope(ctx context.Context, scope models.PermissionScope) ([]*models.Permission, error) {
	var permissions []*models.Permission
	if err := r.db.WithContext(ctx).Where("scope = ? AND status != ?", scope, models.PermissionStatusDeprecated).Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get permissions by scope: %w", err)
	}
	return permissions, nil
}

// GetByRiskLevel retrieves permissions by risk level
func (r *permissionRepository) GetByRiskLevel(ctx context.Context, riskLevel models.PermissionRiskLevel) ([]*models.Permission, error) {
	var permissions []*models.Permission
	if err := r.db.WithContext(ctx).Where("risk_level = ? AND status != ?", riskLevel, models.PermissionStatusDeprecated).Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get permissions by risk level: %w", err)
	}
	return permissions, nil
}

// GetSystemPermissions retrieves system permissions
func (r *permissionRepository) GetSystemPermissions(ctx context.Context) ([]*models.Permission, error) {
	var permissions []*models.Permission
	if err := r.db.WithContext(ctx).Where("is_system_permission = ? AND status != ?", true, models.PermissionStatusDeprecated).Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get system permissions: %w", err)
	}
	return permissions, nil
}

// GetActivePermissions retrieves active permissions
func (r *permissionRepository) GetActivePermissions(ctx context.Context) ([]*models.Permission, error) {
	var permissions []*models.Permission
	if err := r.db.WithContext(ctx).Where("status = ?", models.PermissionStatusActive).Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get active permissions: %w", err)
	}
	return permissions, nil
}

// UpdateStatus updates a permission's status
func (r *permissionRepository) UpdateStatus(ctx context.Context, permissionID uint, status models.PermissionStatus) error {
	result := r.db.WithContext(ctx).Model(&models.Permission{}).
		Where("id = ?", permissionID).
		Update("status", status)

	if result.Error != nil {
		return fmt.Errorf("failed to update permission status: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("permission not found")
	}

	return nil
}

// GetByIDs retrieves permissions by IDs
func (r *permissionRepository) GetByIDs(ctx context.Context, ids []uint) ([]*models.Permission, error) {
	var permissions []*models.Permission
	if err := r.db.WithContext(ctx).Where("id IN ? AND status != ?", ids, models.PermissionStatusDeprecated).Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get permissions by ids: %w", err)
	}
	return permissions, nil
}

// CreateBulk creates multiple permissions
func (r *permissionRepository) CreateBulk(ctx context.Context, permissions []*models.Permission) error {
	if err := r.db.WithContext(ctx).Create(permissions).Error; err != nil {
		return fmt.Errorf("failed to create permissions in bulk: %w", err)
	}
	return nil
}

// Count counts all permissions
func (r *permissionRepository) Count(ctx context.Context) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.Permission{}).Where("status != ?", models.PermissionStatusDeprecated).Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count permissions: %w", err)
	}
	return count, nil
}

// Search searches permissions with filters
func (r *permissionRepository) Search(ctx context.Context, query string, filters *repositories.PermissionFilters) ([]*models.Permission, error) {
	var permissions []*models.Permission

	dbQuery := r.db.WithContext(ctx).Where("status != ?", models.PermissionStatusDeprecated)

	// Apply search query
	if query != "" {
		dbQuery = dbQuery.Where("name LIKE ? OR display_name LIKE ? OR description LIKE ?",
			"%"+query+"%", "%"+query+"%", "%"+query+"%")
	}

	// Apply filters
	if filters != nil {
		if filters.Status != nil {
			dbQuery = dbQuery.Where("status = ?", *filters.Status)
		}
		if filters.Module != nil {
			dbQuery = dbQuery.Where("module = ?", *filters.Module)
		}
		if filters.Resource != nil {
			dbQuery = dbQuery.Where("resource = ?", *filters.Resource)
		}
		if filters.Action != nil {
			dbQuery = dbQuery.Where("action = ?", *filters.Action)
		}
		if filters.Scope != nil {
			dbQuery = dbQuery.Where("scope = ?", *filters.Scope)
		}
		if filters.RiskLevel != nil {
			dbQuery = dbQuery.Where("risk_level = ?", *filters.RiskLevel)
		}
		if filters.IsSystemPermission != nil {
			dbQuery = dbQuery.Where("is_system_permission = ?", *filters.IsSystemPermission)
		}
		if filters.RequiresOwnership != nil {
			dbQuery = dbQuery.Where("requires_ownership = ?", *filters.RequiresOwnership)
		}
		if filters.CreatedAfter != nil {
			dbQuery = dbQuery.Where("created_at >= ?", *filters.CreatedAfter)
		}
		if filters.CreatedBefore != nil {
			dbQuery = dbQuery.Where("created_at <= ?", *filters.CreatedBefore)
		}

		// Apply sorting
		if filters.SortBy != "" {
			order := "ASC"
			if filters.SortOrder == "desc" {
				order = "DESC"
			}
			dbQuery = dbQuery.Order(fmt.Sprintf("%s %s", filters.SortBy, order))
		} else {
			dbQuery = dbQuery.Order("module ASC, resource ASC, action ASC")
		}

		// Apply pagination
		if filters.Limit > 0 {
			dbQuery = dbQuery.Limit(filters.Limit)
		}
		if filters.Offset > 0 {
			dbQuery = dbQuery.Offset(filters.Offset)
		}
	}

	if err := dbQuery.Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to search permissions: %w", err)
	}

	return permissions, nil
}

// Exists checks if a permission exists by name
func (r *permissionRepository) Exists(ctx context.Context, name string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.Permission{}).Where("name = ? AND status != ?", name, models.PermissionStatusDeprecated).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check permission existence: %w", err)
	}
	return count > 0, nil
}

// ExistsByModuleResourceAction checks if a permission exists by module, resource, and action
func (r *permissionRepository) ExistsByModuleResourceAction(ctx context.Context, module, resource, action string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.Permission{}).
		Where("module = ? AND resource = ? AND action = ? AND status != ?", module, resource, action, models.PermissionStatusDeprecated).
		Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check permission existence: %w", err)
	}
	return count > 0, nil
}

// GetModules retrieves all unique modules
func (r *permissionRepository) GetModules(ctx context.Context) ([]string, error) {
	var modules []string
	if err := r.db.WithContext(ctx).Model(&models.Permission{}).
		Where("status != ?", models.PermissionStatusDeprecated).
		Distinct("module").
		Pluck("module", &modules).Error; err != nil {
		return nil, fmt.Errorf("failed to get modules: %w", err)
	}
	return modules, nil
}

// GetResourcesByModule retrieves resources by module
func (r *permissionRepository) GetResourcesByModule(ctx context.Context, module string) ([]string, error) {
	var resources []string
	if err := r.db.WithContext(ctx).Model(&models.Permission{}).
		Where("module = ? AND status != ?", module, models.PermissionStatusDeprecated).
		Distinct("resource").
		Pluck("resource", &resources).Error; err != nil {
		return nil, fmt.Errorf("failed to get resources by module: %w", err)
	}
	return resources, nil
}

// GetActionsByResource retrieves actions by resource
func (r *permissionRepository) GetActionsByResource(ctx context.Context, resource string) ([]string, error) {
	var actions []string
	if err := r.db.WithContext(ctx).Model(&models.Permission{}).
		Where("resource = ? AND status != ?", resource, models.PermissionStatusDeprecated).
		Distinct("action").
		Pluck("action", &actions).Error; err != nil {
		return nil, fmt.Errorf("failed to get actions by resource: %w", err)
	}
	return actions, nil
}

// GetPermissionRoles retrieves roles for a permission
func (r *permissionRepository) GetPermissionRoles(ctx context.Context, permissionID uint) ([]*models.Role, error) {
	var roles []*models.Role
	if err := r.db.WithContext(ctx).
		Table("rbac_roles").
		Joins("JOIN rbac_role_permissions ON rbac_roles.id = rbac_role_permissions.role_id").
		Where("rbac_role_permissions.permission_id = ? AND rbac_role_permissions.status = ? AND rbac_roles.status = ?",
			permissionID, models.RolePermissionStatusActive, models.RoleStatusActive).
		Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to get permission roles: %w", err)
	}
	return roles, nil
}

// GetPermissionsByPattern retrieves permissions by pattern
func (r *permissionRepository) GetPermissionsByPattern(ctx context.Context, pattern string) ([]*models.Permission, error) {
	var permissions []*models.Permission
	if err := r.db.WithContext(ctx).Where("name LIKE ? AND status != ?", pattern, models.PermissionStatusDeprecated).Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get permissions by pattern: %w", err)
	}
	return permissions, nil
}

// GetOwnershipRequiredPermissions retrieves permissions that require ownership
func (r *permissionRepository) GetOwnershipRequiredPermissions(ctx context.Context) ([]*models.Permission, error) {
	var permissions []*models.Permission
	if err := r.db.WithContext(ctx).Where("requires_ownership = ? AND status != ?", true, models.PermissionStatusDeprecated).Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get ownership required permissions: %w", err)
	}
	return permissions, nil
}

// GetPermissionsByContext retrieves permissions by context type
func (r *permissionRepository) GetPermissionsByContext(ctx context.Context, contextType string) ([]*models.Permission, error) {
	var permissions []*models.Permission
	if err := r.db.WithContext(ctx).Where("scope = ? AND status != ?", contextType, models.PermissionStatusDeprecated).Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get permissions by context: %w", err)
	}
	return permissions, nil
}
