package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/rbac/models"
)

// PermissionRepository defines the interface for permission-related database operations
type PermissionRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, permission *models.Permission) error
	GetByID(ctx context.Context, id uint) (*models.Permission, error)
	GetByName(ctx context.Context, name string) (*models.Permission, error)
	Update(ctx context.Context, permission *models.Permission) error
	Delete(ctx context.Context, id uint) error

	// Permission-specific operations
	GetByModule(ctx context.Context, module string, limit, offset int) ([]*models.Permission, error)
	GetByResource(ctx context.Context, resource string, limit, offset int) ([]*models.Permission, error)
	GetByAction(ctx context.Context, action string, limit, offset int) ([]*models.Permission, error)
	GetByScope(ctx context.Context, scope models.PermissionScope) ([]*models.Permission, error)
	GetByRiskLevel(ctx context.Context, riskLevel models.PermissionRiskLevel) ([]*models.Permission, error)

	// System permissions
	GetSystemPermissions(ctx context.Context) ([]*models.Permission, error)
	GetActivePermissions(ctx context.Context) ([]*models.Permission, error)

	// Status management
	UpdateStatus(ctx context.Context, permissionID uint, status models.PermissionStatus) error

	// Bulk operations
	GetByIDs(ctx context.Context, ids []uint) ([]*models.Permission, error)
	CreateBulk(ctx context.Context, permissions []*models.Permission) error
	Count(ctx context.Context) (int64, error)

	// Search and filtering
	Search(ctx context.Context, query string, filters *PermissionFilters) ([]*models.Permission, error)
	Exists(ctx context.Context, name string) (bool, error)
	ExistsByModuleResourceAction(ctx context.Context, module, resource, action string) (bool, error)

	// Module and resource discovery
	GetModules(ctx context.Context) ([]string, error)
	GetResourcesByModule(ctx context.Context, module string) ([]string, error)
	GetActionsByResource(ctx context.Context, resource string) ([]string, error)

	// Permission relationships
	GetPermissionRoles(ctx context.Context, permissionID uint) ([]*models.Role, error)
	GetPermissionsByPattern(ctx context.Context, pattern string) ([]*models.Permission, error)

	// Ownership and contextual permissions
	GetOwnershipRequiredPermissions(ctx context.Context) ([]*models.Permission, error)
	GetPermissionsByContext(ctx context.Context, contextType string) ([]*models.Permission, error)
}

// PermissionFilters represents filters for querying permissions
type PermissionFilters struct {
	Status             *models.PermissionStatus    `json:"status,omitempty"`
	Module             *string                     `json:"module,omitempty"`
	Resource           *string                     `json:"resource,omitempty"`
	Action             *string                     `json:"action,omitempty"`
	Scope              *models.PermissionScope     `json:"scope,omitempty"`
	RiskLevel          *models.PermissionRiskLevel `json:"risk_level,omitempty"`
	IsSystemPermission *bool                       `json:"is_system_permission,omitempty"`
	RequiresOwnership  *bool                       `json:"requires_ownership,omitempty"`
	CreatedAfter       *time.Time                  `json:"created_after,omitempty"`
	CreatedBefore      *time.Time                  `json:"created_before,omitempty"`
	SortBy             string                      `json:"sort_by,omitempty"`
	SortOrder          string                      `json:"sort_order,omitempty"`
	Limit              int                         `json:"limit,omitempty"`
	Offset             int                         `json:"offset,omitempty"`
}
