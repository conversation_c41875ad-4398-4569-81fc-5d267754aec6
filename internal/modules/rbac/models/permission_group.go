package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"
)

// PermissionGroupStatus represents the status of a permission group
// @Enum active,inactive,deprecated
type PermissionGroupStatus string

const (
	PermissionGroupStatusActive     PermissionGroupStatus = "active"
	PermissionGroupStatusInactive   PermissionGroupStatus = "inactive"
	PermissionGroupStatusDeprecated PermissionGroupStatus = "deprecated"
)

// Scan implements sql.Scanner interface
func (s *PermissionGroupStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = PermissionGroupStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s PermissionGroupStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// PermissionGroupCategory represents the category of a permission group
// @Enum core,feature,administrative,integration,general
type PermissionGroupCategory string

const (
	PermissionGroupCategoryCore           PermissionGroupCategory = "core"
	PermissionGroupCategoryFeature        PermissionGroupCategory = "feature"
	PermissionGroupCategoryAdministrative PermissionGroupCategory = "administrative"
	PermissionGroupCategoryIntegration    PermissionGroupCategory = "integration"
	PermissionGroupCategoryGeneral        PermissionGroupCategory = "general"
)

// Scan implements sql.Scanner interface
func (s *PermissionGroupCategory) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = PermissionGroupCategory(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s PermissionGroupCategory) Value() (driver.Value, error) {
	return string(s), nil
}

// PermissionGroupDefaultPermissions represents a JSON array of default permissions
type PermissionGroupDefaultPermissions []string

// Scan implements sql.Scanner interface
func (pgdp *PermissionGroupDefaultPermissions) Scan(value interface{}) error {
	if value == nil {
		*pgdp = make(PermissionGroupDefaultPermissions, 0)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(bytes, pgdp)
}

// Value implements driver.Valuer interface
func (pgdp PermissionGroupDefaultPermissions) Value() (driver.Value, error) {
	if pgdp == nil {
		return "[]", nil
	}
	return json.Marshal(pgdp)
}

// PermissionGroupMetadata represents a JSON object of permission group metadata
type PermissionGroupMetadata map[string]interface{}

// Scan implements sql.Scanner interface
func (pgm *PermissionGroupMetadata) Scan(value interface{}) error {
	if value == nil {
		*pgm = make(PermissionGroupMetadata)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(bytes, pgm)
}

// Value implements driver.Valuer interface
func (pgm PermissionGroupMetadata) Value() (driver.Value, error) {
	if pgm == nil {
		return "{}", nil
	}
	return json.Marshal(pgm)
}

// PermissionGroup represents a permission group in the RBAC system
type PermissionGroup struct {
	ID uint `json:"id" gorm:"primaryKey"`

	// Group Information
	Name        string  `json:"name" gorm:"not null;size:100" validate:"required,max=100,matches=^[a-z0-9_-]+$"`
	DisplayName string  `json:"display_name" gorm:"not null;size:255" validate:"required,max=255"`
	Description *string `json:"description,omitempty" gorm:"type:text"`

	// Group Hierarchy
	ParentGroupID *uint `json:"parent_group_id,omitempty" gorm:"index"`
	Level         uint  `json:"level" gorm:"default:0" validate:"min=0,max=10"`
	SortOrder     uint  `json:"sort_order" gorm:"default:0" validate:"min=0"`

	// Group Categorization
	Module   string                  `json:"module" gorm:"not null;size:50" validate:"required,max=50,matches=^[a-z0-9_-]+$"`
	Category PermissionGroupCategory `json:"category" gorm:"type:enum('core','feature','administrative','integration','general');default:'general';not null"`

	// Group Metadata
	Color *string `json:"color,omitempty" gorm:"size:7" validate:"omitempty,matches=^#[0-9A-Fa-f]{6}$"`
	Icon  *string `json:"icon,omitempty" gorm:"size:100"`

	// Group Configuration
	IsSystemGroup bool `json:"is_system_group" gorm:"default:false"`
	IsRequired    bool `json:"is_required" gorm:"default:false"`

	// Group Capabilities
	DefaultPermissions PermissionGroupDefaultPermissions `json:"default_permissions" gorm:"type:json;default:'[]'"`
	GroupMetadata      PermissionGroupMetadata           `json:"group_metadata" gorm:"type:json;default:'{}'"`

	// Status and Timestamps
	Status    PermissionGroupStatus `json:"status" gorm:"type:enum('active','inactive','deprecated');default:'active';not null"`
	CreatedAt time.Time             `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time             `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations (loaded separately to avoid circular references)
	// ParentGroup *PermissionGroup   `json:"parent_group,omitempty" gorm:"foreignKey:ParentGroupID"`
	// ChildGroups []PermissionGroup  `json:"child_groups,omitempty" gorm:"foreignKey:ParentGroupID"`
}

// TableName specifies the table name for PermissionGroup
func (PermissionGroup) TableName() string {
	return "rbac_permission_groups"
}

// IsActive checks if permission group is active
func (pg *PermissionGroup) IsActive() bool {
	return pg.Status == PermissionGroupStatusActive
}

// IsSystemGroupCheck checks if permission group is a system group
func (pg *PermissionGroup) IsSystemGroupCheck() bool {
	return pg.IsSystemGroup
}

// IsRequiredCheck checks if permission group is required
func (pg *PermissionGroup) IsRequiredCheck() bool {
	return pg.IsRequired
}

// HasParent checks if permission group has a parent
func (pg *PermissionGroup) HasParent() bool {
	return pg.ParentGroupID != nil
}

// HasDefaultPermission checks if permission group has a specific default permission
func (pg *PermissionGroup) HasDefaultPermission(permission string) bool {
	for _, perm := range pg.DefaultPermissions {
		if perm == permission {
			return true
		}
	}
	return false
}

// HasMetadata checks if permission group has a specific metadata key
func (pg *PermissionGroup) HasMetadata(key string) bool {
	_, exists := pg.GroupMetadata[key]
	return exists
}

// GetMetadataValue gets the value of a specific metadata key
func (pg *PermissionGroup) GetMetadataValue(key string) interface{} {
	return pg.GroupMetadata[key]
}

// BeforeCreate hook for PermissionGroup
func (pg *PermissionGroup) BeforeCreate() error {
	if pg.DefaultPermissions == nil {
		pg.DefaultPermissions = make(PermissionGroupDefaultPermissions, 0)
	}
	if pg.GroupMetadata == nil {
		pg.GroupMetadata = make(PermissionGroupMetadata)
	}
	return nil
}

// PermissionGroupFilter represents filters for querying permission groups
type PermissionGroupFilter struct {
	Status        PermissionGroupStatus   `json:"status,omitempty"`
	Module        string                  `json:"module,omitempty"`
	Category      PermissionGroupCategory `json:"category,omitempty"`
	ParentGroupID *uint                   `json:"parent_group_id,omitempty"`
	Level         *uint                   `json:"level,omitempty"`
	IsSystemGroup *bool                   `json:"is_system_group,omitempty"`
	IsRequired    *bool                   `json:"is_required,omitempty"`
	Search        string                  `json:"search,omitempty"`
	Page          int                     `json:"page,omitempty"`
	PageSize      int                     `json:"page_size,omitempty"`
	SortBy        string                  `json:"sort_by,omitempty"`
	SortOrder     string                  `json:"sort_order,omitempty"`
}

// PermissionGroupCreateRequest represents the request to create a permission group
type PermissionGroupCreateRequest struct {
	Name               string                            `json:"name" validate:"required,max=100,matches=^[a-z0-9_-]+$"`
	DisplayName        string                            `json:"display_name" validate:"required,max=255"`
	Description        *string                           `json:"description,omitempty"`
	ParentGroupID      *uint                             `json:"parent_group_id,omitempty"`
	Level              uint                              `json:"level,omitempty" validate:"min=0,max=10"`
	SortOrder          uint                              `json:"sort_order,omitempty" validate:"min=0"`
	Module             string                            `json:"module" validate:"required,max=50,matches=^[a-z0-9_-]+$"`
	Category           PermissionGroupCategory           `json:"category,omitempty" validate:"omitempty,oneof=core feature administrative integration general"`
	Color              *string                           `json:"color,omitempty" validate:"omitempty,matches=^#[0-9A-Fa-f]{6}$"`
	Icon               *string                           `json:"icon,omitempty" validate:"omitempty,max=100"`
	IsSystemGroup      bool                              `json:"is_system_group,omitempty"`
	IsRequired         bool                              `json:"is_required,omitempty"`
	DefaultPermissions PermissionGroupDefaultPermissions `json:"default_permissions,omitempty"`
	GroupMetadata      PermissionGroupMetadata           `json:"group_metadata,omitempty"`
}

// PermissionGroupUpdateRequest represents the request to update a permission group
type PermissionGroupUpdateRequest struct {
	Name               *string                            `json:"name,omitempty" validate:"omitempty,max=100,matches=^[a-z0-9_-]+$"`
	DisplayName        *string                            `json:"display_name,omitempty" validate:"omitempty,max=255"`
	Description        *string                            `json:"description,omitempty"`
	ParentGroupID      *uint                              `json:"parent_group_id,omitempty"`
	Level              *uint                              `json:"level,omitempty" validate:"omitempty,min=0,max=10"`
	SortOrder          *uint                              `json:"sort_order,omitempty" validate:"omitempty,min=0"`
	Module             *string                            `json:"module,omitempty" validate:"omitempty,max=50,matches=^[a-z0-9_-]+$"`
	Category           *PermissionGroupCategory           `json:"category,omitempty" validate:"omitempty,oneof=core feature administrative integration general"`
	Color              *string                            `json:"color,omitempty" validate:"omitempty,matches=^#[0-9A-Fa-f]{6}$"`
	Icon               *string                            `json:"icon,omitempty" validate:"omitempty,max=100"`
	IsSystemGroup      *bool                              `json:"is_system_group,omitempty"`
	IsRequired         *bool                              `json:"is_required,omitempty"`
	Status             *PermissionGroupStatus             `json:"status,omitempty" validate:"omitempty,oneof=active inactive deprecated"`
	DefaultPermissions *PermissionGroupDefaultPermissions `json:"default_permissions,omitempty"`
	GroupMetadata      *PermissionGroupMetadata           `json:"group_metadata,omitempty"`
}

// PermissionGroupBulkCreateRequest represents the request to bulk create permission groups
type PermissionGroupBulkCreateRequest struct {
	Groups []PermissionGroupCreateRequest `json:"groups" validate:"required,min=1,dive"`
}

// PermissionGroupBulkUpdateRequest represents the request to bulk update permission groups
type PermissionGroupBulkUpdateRequest struct {
	GroupIDs []uint                       `json:"group_ids" validate:"required,min=1,dive,min=1"`
	Updates  PermissionGroupUpdateRequest `json:"updates" validate:"required"`
}
