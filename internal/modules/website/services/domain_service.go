package services

import (
	"context"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories"
)

// domainService implements the DomainService interface
type domainService struct {
	websiteRepo     repositories.WebsiteRepository
	domainValidator *models.DomainValidator
}

// NewDomainService creates a new domain service
func NewDomainService(
	websiteRepo repositories.WebsiteRepository,
	baseDomain string,
) DomainService {
	return &domainService{
		websiteRepo:     websiteRepo,
		domainValidator: models.NewDomainValidator(baseDomain),
	}
}

// ValidateDomain validates domain format and availability
func (s *domainService) ValidateDomain(ctx context.Context, domain string, excludeWebsiteID uint) error {
	if domain == "" {
		return nil // Empty domain is allowed
	}

	// Validate domain format
	if err := s.domainValidator.ValidateDomain(domain); err != nil {
		return &WebsiteServiceError{
			Code:    ErrCodeInvalidDomain,
			Message: fmt.Sprintf("invalid domain: %v", err),
			Field:   "domain",
		}
	}

	// Check availability
	available, err := s.CheckDomainAvailability(ctx, domain, excludeWebsiteID)
	if err != nil {
		return &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: fmt.Sprintf("failed to check domain availability: %v", err),
		}
	}

	if !available {
		return &WebsiteServiceError{
			Code:    ErrCodeDomainAlreadyExists,
			Message: "domain already exists",
			Field:   "domain",
		}
	}

	return nil
}

// ValidateSubdomain validates subdomain format and availability
func (s *domainService) ValidateSubdomain(ctx context.Context, subdomain string, excludeWebsiteID uint) error {
	if subdomain == "" {
		return nil // Empty subdomain is allowed
	}

	// Validate subdomain format
	if err := s.domainValidator.ValidateSubdomain(subdomain); err != nil {
		return &WebsiteServiceError{
			Code:    ErrCodeInvalidSubdomain,
			Message: fmt.Sprintf("invalid subdomain: %v", err),
			Field:   "subdomain",
		}
	}

	// Check if subdomain is reserved
	if s.domainValidator.IsReservedSubdomain(subdomain) {
		return &WebsiteServiceError{
			Code:    ErrCodeReservedSubdomain,
			Message: fmt.Sprintf("subdomain '%s' is reserved", subdomain),
			Field:   "subdomain",
		}
	}

	// Check availability
	available, err := s.CheckSubdomainAvailability(ctx, subdomain, excludeWebsiteID)
	if err != nil {
		return &WebsiteServiceError{
			Code:    ErrCodeInternalError,
			Message: fmt.Sprintf("failed to check subdomain availability: %v", err),
		}
	}

	if !available {
		return &WebsiteServiceError{
			Code:    ErrCodeSubdomainAlreadyExists,
			Message: "subdomain already exists",
			Field:   "subdomain",
		}
	}

	return nil
}

// GetDomainInfo analyzes domain and returns information
func (s *domainService) GetDomainInfo(ctx context.Context, domain, subdomain string) (*models.DomainInfo, error) {
	return s.domainValidator.AnalyzeDomain(domain, subdomain), nil
}

// CheckDomainAvailability checks if domain is available
func (s *domainService) CheckDomainAvailability(ctx context.Context, domain string, excludeWebsiteID uint) (bool, error) {
	if domain == "" {
		return true, nil
	}

	normalizedDomain := s.domainValidator.NormalizeDomain(domain)
	exists, err := s.websiteRepo.CheckDomainExists(ctx, normalizedDomain, excludeWebsiteID)
	if err != nil {
		return false, err
	}

	return !exists, nil
}

// CheckSubdomainAvailability checks if subdomain is available
func (s *domainService) CheckSubdomainAvailability(ctx context.Context, subdomain string, excludeWebsiteID uint) (bool, error) {
	if subdomain == "" {
		return true, nil
	}

	normalizedSubdomain := s.domainValidator.NormalizeSubdomain(subdomain)
	exists, err := s.websiteRepo.CheckSubdomainExists(ctx, normalizedSubdomain, excludeWebsiteID)
	if err != nil {
		return false, err
	}

	return !exists, nil
}

// NormalizeDomain normalizes domain name
func (s *domainService) NormalizeDomain(domain string) string {
	return s.domainValidator.NormalizeDomain(domain)
}

// NormalizeSubdomain normalizes subdomain name
func (s *domainService) NormalizeSubdomain(subdomain string) string {
	return s.domainValidator.NormalizeSubdomain(subdomain)
}

// GetReservedSubdomains returns list of reserved subdomains
func (s *domainService) GetReservedSubdomains() []string {
	return models.ReservedSubdomains
}

// IsReservedSubdomain checks if subdomain is reserved
func (s *domainService) IsReservedSubdomain(subdomain string) bool {
	return s.domainValidator.IsReservedSubdomain(subdomain)
}

// VerifyDomainOwnership initiates domain ownership verification
func (s *domainService) VerifyDomainOwnership(ctx context.Context, websiteID uint, domain string) error {
	// TODO: Implement domain ownership verification
	// This would involve creating DNS records or uploading verification files
	return &WebsiteServiceError{
		Code:    ErrCodeInternalError,
		Message: "domain ownership verification not implemented",
	}
}

// CheckDomainVerification checks domain verification status
func (s *domainService) CheckDomainVerification(ctx context.Context, websiteID uint, domain string) (bool, error) {
	// TODO: Implement domain verification status check
	// This would check if the domain has been verified
	return false, &WebsiteServiceError{
		Code:    ErrCodeInternalError,
		Message: "domain verification status check not implemented",
	}
}

// GetDomainVerificationInstructions returns domain verification instructions
func (s *domainService) GetDomainVerificationInstructions(ctx context.Context, websiteID uint, domain string) (map[string]interface{}, error) {
	instructions := map[string]interface{}{
		"domain":     domain,
		"website_id": websiteID,
		"methods": []map[string]interface{}{
			{
				"type":         "dns",
				"name":         "DNS Record",
				"description":  "Add a TXT record to your domain's DNS settings",
				"record_type":  "TXT",
				"record_name":  fmt.Sprintf("_website_verification.%s", domain),
				"record_value": fmt.Sprintf("website-verification=%d", websiteID),
			},
			{
				"type":        "file",
				"name":        "HTML File",
				"description": "Upload a verification file to your domain's root directory",
				"file_name":   "website_verification.html",
				"file_path":   fmt.Sprintf("https://%s/website_verification.html", domain),
				"file_content": fmt.Sprintf(`<!DOCTYPE html>
<html>
<head>
    <title>Website Verification</title>
    <meta name="website-verification" content="website-verification=%d" />
</head>
<body>
    <h1>Website Verification</h1>
    <p>This file is used to verify domain ownership.</p>
</body>
</html>`, websiteID),
			},
			{
				"type":        "meta",
				"name":        "HTML Meta Tag",
				"description": "Add a meta tag to your website's homepage",
				"meta_tag":    fmt.Sprintf(`<meta name="website-verification" content="website-verification=%d" />`, websiteID),
			},
		},
		"verification_url": fmt.Sprintf("/api/v1/websites/%d/verify-domain", websiteID),
		"instructions": []string{
			"Choose one of the verification methods above",
			"Complete the verification process (add DNS record, upload file, or add meta tag)",
			"Click the verification URL to check if the domain is verified",
			"Once verified, you can start using your custom domain",
		},
	}

	return instructions, nil
}
