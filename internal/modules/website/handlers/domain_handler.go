package handlers

import (
	"errors"
	"strconv"

	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/response"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// DomainHandler handles domain-related HTTP requests
type DomainHandler struct {
	domainService  services.DomainService
	websiteService services.WebsiteService
	validator      validator.Validator
}

// NewDomainHandler creates a new domain handler
func NewDomainHandler(
	domainService services.DomainService,
	websiteService services.WebsiteService,
	validator validator.Validator,
) *DomainHandler {
	return &DomainHandler{
		domainService:  domainService,
		websiteService: websiteService,
		validator:      validator,
	}
}

// ValidateDomain handles POST /api/v1/domains/validate
func (h *DomainHandler) ValidateDomain(c *gin.Context) {
	var req struct {
		Domain           string `json:"domain,omitempty"`
		Subdomain        string `json:"subdomain,omitempty"`
		ExcludeWebsiteID uint   `json:"exclude_website_id,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	// Validate that either domain or subdomain is provided
	if req.Domain == "" && req.Subdomain == "" {
		response.BadRequest(c.Writer, "Either domain or subdomain must be provided", "")
		return
	}

	if req.Domain != "" && req.Subdomain != "" {
		response.BadRequest(c.Writer, "Cannot specify both domain and subdomain", "")
		return
	}

	var err error
	var available bool
	var info *models.DomainInfo

	if req.Domain != "" {
		// Validate domain
		err = h.domainService.ValidateDomain(c.Request.Context(), req.Domain, req.ExcludeWebsiteID)
		if err == nil {
			available, err = h.domainService.CheckDomainAvailability(c.Request.Context(), req.Domain, req.ExcludeWebsiteID)
		}
		if err == nil {
			info, err = h.domainService.GetDomainInfo(c.Request.Context(), req.Domain, "")
		}
	} else {
		// Validate subdomain
		err = h.domainService.ValidateSubdomain(c.Request.Context(), req.Subdomain, req.ExcludeWebsiteID)
		if err == nil {
			available, err = h.domainService.CheckSubdomainAvailability(c.Request.Context(), req.Subdomain, req.ExcludeWebsiteID)
		}
		if err == nil {
			info, err = h.domainService.GetDomainInfo(c.Request.Context(), "", req.Subdomain)
		}
	}

	if err != nil {
		var serviceErr *services.WebsiteServiceError
		if errors.As(err, &serviceErr) {
			switch serviceErr.Code {
			case services.ErrCodeInvalidDomain, services.ErrCodeInvalidSubdomain:
				response.Success(c.Writer, gin.H{
					"valid":     false,
					"available": false,
					"error":     serviceErr.Message,
					"info":      info,
				})
				return
			case services.ErrCodeDomainAlreadyExists, services.ErrCodeSubdomainAlreadyExists:
				response.Success(c.Writer, gin.H{
					"valid":     true,
					"available": false,
					"error":     serviceErr.Message,
					"info":      info,
				})
				return
			case services.ErrCodeReservedSubdomain:
				response.Success(c.Writer, gin.H{
					"valid":     false,
					"available": false,
					"error":     serviceErr.Message,
					"info":      info,
				})
				return
			}
		}
		h.handleServiceError(c, err)
		return
	}

	response.Success(c.Writer, gin.H{
		"valid":     true,
		"available": available,
		"info":      info,
	})
}

// CheckDomainAvailability handles GET /api/v1/domains/check-availability
func (h *DomainHandler) CheckDomainAvailability(c *gin.Context) {
	domain := c.Query("domain")
	subdomain := c.Query("subdomain")
	excludeWebsiteIDStr := c.Query("exclude_website_id")

	if domain == "" && subdomain == "" {
		response.BadRequest(c.Writer, "Either domain or subdomain query parameter must be provided", "")
		return
	}

	if domain != "" && subdomain != "" {
		response.BadRequest(c.Writer, "Cannot specify both domain and subdomain", "")
		return
	}

	var excludeWebsiteID uint
	if excludeWebsiteIDStr != "" {
		id, err := strconv.ParseUint(excludeWebsiteIDStr, 10, 32)
		if err != nil {
			response.BadRequest(c.Writer, "Invalid exclude_website_id format", err.Error())
			return
		}
		excludeWebsiteID = uint(id)
	}

	var available bool
	var err error

	if domain != "" {
		available, err = h.domainService.CheckDomainAvailability(c.Request.Context(), domain, excludeWebsiteID)
	} else {
		available, err = h.domainService.CheckSubdomainAvailability(c.Request.Context(), subdomain, excludeWebsiteID)
	}

	if err != nil {
		h.handleServiceError(c, err)
		return
	}

	response.Success(c.Writer, gin.H{
		"available": available,
		"domain":    domain,
		"subdomain": subdomain,
	})
}

// GetDomainInfo handles GET /api/v1/domains/info
func (h *DomainHandler) GetDomainInfo(c *gin.Context) {
	domain := c.Query("domain")
	subdomain := c.Query("subdomain")

	if domain == "" && subdomain == "" {
		response.BadRequest(c.Writer, "Either domain or subdomain query parameter must be provided", "")
		return
	}

	info, err := h.domainService.GetDomainInfo(c.Request.Context(), domain, subdomain)
	if err != nil {
		h.handleServiceError(c, err)
		return
	}

	response.Success(c.Writer, info)
}

// GetReservedSubdomains handles GET /api/v1/domains/reserved-subdomains
func (h *DomainHandler) GetReservedSubdomains(c *gin.Context) {
	reserved := h.domainService.GetReservedSubdomains()
	response.Success(c.Writer, gin.H{
		"reserved_subdomains": reserved,
		"count":               len(reserved),
	})
}

// VerifyDomainOwnership handles POST /api/v1/websites/:id/verify-domain
func (h *DomainHandler) VerifyDomainOwnership(c *gin.Context) {
	websiteID, err := h.parseWebsiteID(c)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid website ID", err.Error())
		return
	}

	var req struct {
		Domain string `json:"domain" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c.Writer, "Invalid request body", err.Error())
		return
	}

	// Validate tenant access to website
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant ID not found in context")
		return
	}

	if err := h.websiteService.ValidateWebsiteAccess(c.Request.Context(), tenantID.(uint), websiteID); err != nil {
		h.handleServiceError(c, err)
		return
	}

	if err := h.domainService.VerifyDomainOwnership(c.Request.Context(), websiteID, req.Domain); err != nil {
		h.handleServiceError(c, err)
		return
	}

	response.Success(c.Writer, gin.H{
		"message":    "Domain ownership verification initiated",
		"domain":     req.Domain,
		"website_id": websiteID,
	})
}

// CheckDomainVerification handles GET /api/v1/websites/:id/verify-domain/:domain
func (h *DomainHandler) CheckDomainVerification(c *gin.Context) {
	websiteID, err := h.parseWebsiteID(c)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid website ID", err.Error())
		return
	}

	domain := c.Param("domain")
	if domain == "" {
		response.BadRequest(c.Writer, "Domain is required", "")
		return
	}

	// Validate tenant access to website
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant ID not found in context")
		return
	}

	if err := h.websiteService.ValidateWebsiteAccess(c.Request.Context(), tenantID.(uint), websiteID); err != nil {
		h.handleServiceError(c, err)
		return
	}

	verified, err := h.domainService.CheckDomainVerification(c.Request.Context(), websiteID, domain)
	if err != nil {
		h.handleServiceError(c, err)
		return
	}

	response.Success(c.Writer, gin.H{
		"verified":   verified,
		"domain":     domain,
		"website_id": websiteID,
	})
}

// GetDomainVerificationInstructions handles GET /api/v1/websites/:id/domain-verification-instructions/:domain
func (h *DomainHandler) GetDomainVerificationInstructions(c *gin.Context) {
	websiteID, err := h.parseWebsiteID(c)
	if err != nil {
		response.BadRequest(c.Writer, "Invalid website ID", err.Error())
		return
	}

	domain := c.Param("domain")
	if domain == "" {
		response.BadRequest(c.Writer, "Domain is required", "")
		return
	}

	// Validate tenant access to website
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		response.Unauthorized(c.Writer, "Tenant ID not found in context")
		return
	}

	if err := h.websiteService.ValidateWebsiteAccess(c.Request.Context(), tenantID.(uint), websiteID); err != nil {
		h.handleServiceError(c, err)
		return
	}

	instructions, err := h.domainService.GetDomainVerificationInstructions(c.Request.Context(), websiteID, domain)
	if err != nil {
		h.handleServiceError(c, err)
		return
	}

	response.Success(c.Writer, instructions)
}

// Helper methods

// parseWebsiteID parses website ID from URL parameter
func (h *DomainHandler) parseWebsiteID(c *gin.Context) (uint, error) {
	idStr := c.Param("id")
	if idStr == "" {
		return 0, errors.New("website ID is required")
	}

	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		return 0, errors.New("invalid website ID format")
	}

	return uint(id), nil
}

// handleServiceError handles service errors and returns appropriate HTTP responses
func (h *DomainHandler) handleServiceError(c *gin.Context, err error) {
	var serviceErr *services.WebsiteServiceError
	if errors.As(err, &serviceErr) {
		switch serviceErr.Code {
		case services.ErrCodeWebsiteNotFound:
			response.NotFound(c.Writer, serviceErr.Message)
		case services.ErrCodeWebsiteAccessDenied:
			response.Forbidden(c.Writer, serviceErr.Message)
		case services.ErrCodeDomainAlreadyExists, services.ErrCodeSubdomainAlreadyExists:
			response.Conflict(c.Writer, serviceErr.Message)
		case services.ErrCodeInvalidDomain, services.ErrCodeInvalidSubdomain, services.ErrCodeReservedSubdomain:
			response.BadRequest(c.Writer, serviceErr.Message, serviceErr.Field)
		case services.ErrCodeValidationFailed:
			response.BadRequest(c.Writer, serviceErr.Message, serviceErr.Field)
		default:
			response.InternalServerError(c.Writer, serviceErr.Message)
		}
		return
	}

	// Default to internal server error
	response.InternalServerError(c.Writer, "Internal server error")
}
