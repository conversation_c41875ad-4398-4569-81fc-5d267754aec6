package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// WebsiteRepository defines the interface for website data operations
type WebsiteRepository interface {
	// Create creates a new website
	Create(ctx context.Context, website *models.Website) error

	// GetByID retrieves a website by ID
	GetByID(ctx context.Context, id uint) (*models.Website, error)

	// GetByTenantID retrieves a website by tenant ID and website ID
	GetByTenantID(ctx context.Context, tenantID, websiteID uint) (*models.Website, error)

	// GetByDomain retrieves a website by domain
	GetByDomain(ctx context.Context, domain string) (*models.Website, error)

	// GetBySubdomain retrieves a website by subdomain
	GetBySubdomain(ctx context.Context, subdomain string) (*models.Website, error)

	// GetByTenant retrieves all websites for a tenant
	GetByTenant(ctx context.Context, tenantID uint) ([]*models.Website, error)

	// Update updates a website
	Update(ctx context.Context, id uint, updates map[string]interface{}) error

	// Delete soft deletes a website (sets status to inactive)
	Delete(ctx context.Context, id uint) error

	// List retrieves websites based on filter
	List(ctx context.Context, filter models.WebsiteFilter) ([]*models.Website, int64, error)

	// ListWithCursor retrieves websites using cursor-based pagination
	ListWithCursor(ctx context.Context, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]*models.Website, *pagination.CursorResponse, error)

	// CheckDomainExists checks if a domain already exists
	CheckDomainExists(ctx context.Context, domain string, excludeID uint) (bool, error)

	// CheckSubdomainExists checks if a subdomain already exists
	CheckSubdomainExists(ctx context.Context, subdomain string, excludeID uint) (bool, error)

	// GetActiveWebsites retrieves all active websites for a tenant
	GetActiveWebsites(ctx context.Context, tenantID uint) ([]*models.Website, error)

	// CountByTenant counts websites for a tenant
	CountByTenant(ctx context.Context, tenantID uint) (int64, error)

	// GetWithSettings retrieves a website with its settings
	GetWithSettings(ctx context.Context, id uint) (*models.Website, error)

	// GetWithThemes retrieves a website with its themes
	GetWithThemes(ctx context.Context, id uint) (*models.Website, error)

	// GetWithAll retrieves a website with all associations
	GetWithAll(ctx context.Context, id uint) (*models.Website, error)

	// UpdateStatus updates website status
	UpdateStatus(ctx context.Context, id uint, status models.WebsiteStatus) error

	// ActivateWebsite activates a website
	ActivateWebsite(ctx context.Context, id uint) error

	// DeactivateWebsite deactivates a website
	DeactivateWebsite(ctx context.Context, id uint) error

	// SuspendWebsite suspends a website
	SuspendWebsite(ctx context.Context, id uint) error
}

// WebsiteSettingRepository defines the interface for website setting data operations
type WebsiteSettingRepository interface {
	// Create creates a new website setting
	Create(ctx context.Context, setting *models.WebsiteSetting) error

	// GetByID retrieves a setting by ID
	GetByID(ctx context.Context, id uint) (*models.WebsiteSetting, error)

	// GetByKey retrieves a setting by website ID and key
	GetByKey(ctx context.Context, websiteID uint, key string) (*models.WebsiteSetting, error)

	// GetByCategory retrieves all settings for a website in a category
	GetByCategory(ctx context.Context, websiteID uint, category models.WebsiteSettingCategory) ([]*models.WebsiteSetting, error)

	// Update updates a setting
	Update(ctx context.Context, id uint, updates map[string]interface{}) error

	// Upsert creates or updates a setting
	Upsert(ctx context.Context, setting *models.WebsiteSetting) error

	// Delete deletes a setting
	Delete(ctx context.Context, id uint) error

	// DeleteByKey deletes a setting by website ID and key
	DeleteByKey(ctx context.Context, websiteID uint, key string) error

	// List retrieves settings based on filter
	List(ctx context.Context, filter models.WebsiteSettingFilter) ([]*models.WebsiteSetting, int64, error)

	// GetByWebsite retrieves all settings for a website
	GetByWebsite(ctx context.Context, websiteID uint) ([]*models.WebsiteSetting, error)

	// GetPublicSettings retrieves all public settings for a website
	GetPublicSettings(ctx context.Context, websiteID uint) ([]*models.WebsiteSetting, error)

	// GetRequiredSettings retrieves all required settings for a website
	GetRequiredSettings(ctx context.Context, websiteID uint) ([]*models.WebsiteSetting, error)

	// BulkUpsert creates or updates multiple settings
	BulkUpsert(ctx context.Context, websiteID uint, settings []models.WebsiteSettingCreateRequest) error

	// BulkDelete deletes multiple settings by keys
	BulkDelete(ctx context.Context, websiteID uint, keys []string) error

	// GetSettingsByTenant retrieves all settings for websites of a tenant
	GetSettingsByTenant(ctx context.Context, tenantID uint) ([]*models.WebsiteSetting, error)

	// CheckKeyExists checks if a setting key already exists for a website
	CheckKeyExists(ctx context.Context, websiteID uint, key string, excludeID uint) (bool, error)

	// GetSettingValue retrieves a setting value (or default) by key
	GetSettingValue(ctx context.Context, websiteID uint, key string) (string, error)

	// CountByWebsite counts settings for a website
	CountByWebsite(ctx context.Context, websiteID uint) (int64, error)
}

// WebsiteThemeRepository defines the interface for website theme data operations
type WebsiteThemeRepository interface {
	// Create creates a new website theme
	Create(ctx context.Context, theme *models.WebsiteTheme) error

	// GetByID retrieves a theme by ID
	GetByID(ctx context.Context, id uint) (*models.WebsiteTheme, error)

	// GetByName retrieves a theme by website ID and name
	GetByName(ctx context.Context, websiteID uint, name string) (*models.WebsiteTheme, error)

	// GetByWebsite retrieves all themes for a website
	GetByWebsite(ctx context.Context, websiteID uint) ([]*models.WebsiteTheme, error)

	// GetActiveTheme retrieves the active theme for a website
	GetActiveTheme(ctx context.Context, websiteID uint) (*models.WebsiteTheme, error)

	// GetDefaultTheme retrieves the default theme for a website
	GetDefaultTheme(ctx context.Context, websiteID uint) (*models.WebsiteTheme, error)

	// Update updates a theme
	Update(ctx context.Context, id uint, updates map[string]interface{}) error

	// Delete soft deletes a theme (sets status to archived)
	Delete(ctx context.Context, id uint) error

	// List retrieves themes based on filter
	List(ctx context.Context, filter models.WebsiteThemeFilter) ([]*models.WebsiteTheme, int64, error)

	// ActivateTheme activates a theme and deactivates others
	ActivateTheme(ctx context.Context, websiteID, themeID uint) error

	// DeactivateTheme deactivates a theme
	DeactivateTheme(ctx context.Context, id uint) error

	// CloneTheme clones a theme
	CloneTheme(ctx context.Context, sourceID uint, newTheme *models.WebsiteTheme) error

	// GetChildThemes retrieves all child themes for a parent theme
	GetChildThemes(ctx context.Context, parentThemeID uint) ([]*models.WebsiteTheme, error)

	// GetParentTheme retrieves the parent theme for a theme
	GetParentTheme(ctx context.Context, themeID uint) (*models.WebsiteTheme, error)

	// UpdateStatus updates theme status
	UpdateStatus(ctx context.Context, id uint, status models.WebsiteThemeStatus) error

	// SetAsDefault sets a theme as default for a website
	SetAsDefault(ctx context.Context, websiteID, themeID uint) error

	// GetThemesByTenant retrieves all themes for websites of a tenant
	GetThemesByTenant(ctx context.Context, tenantID uint) ([]*models.WebsiteTheme, error)

	// CheckNameExists checks if a theme name already exists for a website
	CheckNameExists(ctx context.Context, websiteID uint, name string, excludeID uint) (bool, error)

	// CountByWebsite counts themes for a website
	CountByWebsite(ctx context.Context, websiteID uint) (int64, error)

	// GetThemeConfig retrieves theme configuration
	GetThemeConfig(ctx context.Context, id uint) (models.JSONMap, error)

	// UpdateThemeConfig updates theme configuration
	UpdateThemeConfig(ctx context.Context, id uint, config models.JSONMap) error

	// GetThemeCustomizations retrieves theme customizations
	GetThemeCustomizations(ctx context.Context, id uint) (map[string]interface{}, error)

	// UpdateThemeCustomizations updates theme customizations
	UpdateThemeCustomizations(ctx context.Context, id uint, customizations map[string]interface{}) error
}

// WebsiteTemplateRepository defines the interface for website template data operations
type WebsiteTemplateRepository interface {
	// Create creates a new website template
	Create(ctx context.Context, template *models.WebsiteTemplate) error

	// GetByID retrieves a template by ID
	GetByID(ctx context.Context, id uint) (*models.WebsiteTemplate, error)

	// GetBySlug retrieves a template by slug
	GetBySlug(ctx context.Context, slug string) (*models.WebsiteTemplate, error)

	// Update updates a template
	Update(ctx context.Context, id uint, updates map[string]interface{}) error

	// Delete soft deletes a template (sets status to deleted)
	Delete(ctx context.Context, id uint) error

	// List retrieves templates based on filter
	List(ctx context.Context, filter models.TemplateFilter) ([]*models.WebsiteTemplate, int64, error)

	// GetByCategory retrieves templates by category
	GetByCategory(ctx context.Context, categoryID uint) ([]*models.WebsiteTemplate, error)

	// GetByType retrieves templates by type
	GetByType(ctx context.Context, templateType models.TemplateType) ([]*models.WebsiteTemplate, error)

	// GetFeatured retrieves featured templates
	GetFeatured(ctx context.Context, limit int) ([]*models.WebsiteTemplate, error)

	// GetPopular retrieves popular templates (by usage count)
	GetPopular(ctx context.Context, limit int) ([]*models.WebsiteTemplate, error)

	// GetPublished retrieves all published templates
	GetPublished(ctx context.Context) ([]*models.WebsiteTemplate, error)

	// Search searches templates by name, description, tags
	Search(ctx context.Context, query string, limit int) ([]*models.WebsiteTemplate, error)

	// UpdateUsage increments usage count
	UpdateUsage(ctx context.Context, id uint) error

	// UpdateDownloadCount increments download count
	UpdateDownloadCount(ctx context.Context, id uint) error

	// UpdateRating updates template rating
	UpdateRating(ctx context.Context, id uint, rating float64) error

	// GetStats retrieves template statistics
	GetStats(ctx context.Context) (*models.TemplateStats, error)

	// CheckSlugExists checks if a slug already exists
	CheckSlugExists(ctx context.Context, slug string, excludeID uint) (bool, error)

	// UpdateStatus updates template status
	UpdateStatus(ctx context.Context, id uint, status models.TemplateStatus) error

	// GetWithCategory retrieves a template with its category
	GetWithCategory(ctx context.Context, id uint) (*models.WebsiteTemplate, error)

	// CountByCategory counts templates by category
	CountByCategory(ctx context.Context, categoryID uint) (int64, error)

	// GetTemplatesByIndustry retrieves templates by industry
	GetTemplatesByIndustry(ctx context.Context, industry string) ([]*models.WebsiteTemplate, error)
}

// TemplateCategoryRepository defines the interface for template category data operations
type TemplateCategoryRepository interface {
	// Create creates a new template category
	Create(ctx context.Context, category *models.TemplateCategory) error

	// GetByID retrieves a category by ID
	GetByID(ctx context.Context, id uint) (*models.TemplateCategory, error)

	// GetBySlug retrieves a category by slug
	GetBySlug(ctx context.Context, slug string) (*models.TemplateCategory, error)

	// Update updates a category
	Update(ctx context.Context, id uint, updates map[string]interface{}) error

	// Delete soft deletes a category (sets status to deleted)
	Delete(ctx context.Context, id uint) error

	// List retrieves categories based on filter
	List(ctx context.Context, filter models.CategoryFilter) ([]*models.TemplateCategory, int64, error)

	// GetMainCategories retrieves all main categories (level 0)
	GetMainCategories(ctx context.Context) ([]*models.TemplateCategory, error)

	// GetSubcategories retrieves subcategories by parent ID
	GetSubcategories(ctx context.Context, parentID uint) ([]*models.TemplateCategory, error)

	// GetCategoryTree retrieves the category tree
	GetCategoryTree(ctx context.Context) ([]*models.TemplateCategory, error)

	// GetFeatured retrieves featured categories
	GetFeatured(ctx context.Context) ([]*models.TemplateCategory, error)

	// GetWithChildren retrieves a category with its children
	GetWithChildren(ctx context.Context, id uint) (*models.TemplateCategory, error)

	// GetWithParent retrieves a category with its parent
	GetWithParent(ctx context.Context, id uint) (*models.TemplateCategory, error)

	// GetWithTemplates retrieves a category with its templates
	GetWithTemplates(ctx context.Context, id uint) (*models.TemplateCategory, error)

	// UpdateTemplateCount updates the template count for a category
	UpdateTemplateCount(ctx context.Context, id uint, count int) error

	// IncrementTemplateCount increments template count
	IncrementTemplateCount(ctx context.Context, id uint) error

	// DecrementTemplateCount decrements template count
	DecrementTemplateCount(ctx context.Context, id uint) error

	// CheckSlugExists checks if a slug already exists
	CheckSlugExists(ctx context.Context, slug string, excludeID uint) (bool, error)

	// UpdateStatus updates category status
	UpdateStatus(ctx context.Context, id uint, status models.CategoryStatus) error

	// GetStats retrieves category statistics
	GetStats(ctx context.Context) (*models.CategoryStats, error)

	// MoveCategoryToParent moves a category to a new parent
	MoveCategoryToParent(ctx context.Context, categoryID uint, newParentID *uint) error

	// GetCategoryPath retrieves the path from root to category
	GetCategoryPath(ctx context.Context, id uint) ([]*models.TemplateCategory, error)
}
