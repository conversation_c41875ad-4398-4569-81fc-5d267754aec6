package mysql

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories"
)

// websiteThemeRepository implements the WebsiteThemeRepository interface
type websiteThemeRepository struct {
	db *gorm.DB
}

// NewWebsiteThemeRepository creates a new website theme repository
func NewWebsiteThemeRepository(db *gorm.DB) repositories.WebsiteThemeRepository {
	return &websiteThemeRepository{db: db}
}

// Create creates a new website theme
func (r *websiteThemeRepository) Create(ctx context.Context, theme *models.WebsiteTheme) error {
	return r.db.WithContext(ctx).Create(theme).Error
}

// GetByID retrieves a theme by ID
func (r *websiteThemeRepository) GetByID(ctx context.Context, id uint) (*models.WebsiteTheme, error) {
	var theme models.WebsiteTheme
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&theme).Error
	if err != nil {
		return nil, err
	}
	return &theme, nil
}

// GetByName retrieves a theme by website ID and name
func (r *websiteThemeRepository) GetByName(ctx context.Context, websiteID uint, name string) (*models.WebsiteTheme, error) {
	var theme models.WebsiteTheme
	err := r.db.WithContext(ctx).Where("website_id = ? AND name = ?", websiteID, name).First(&theme).Error
	if err != nil {
		return nil, err
	}
	return &theme, nil
}

// GetByWebsite retrieves all themes for a website
func (r *websiteThemeRepository) GetByWebsite(ctx context.Context, websiteID uint) ([]*models.WebsiteTheme, error) {
	var themes []*models.WebsiteTheme
	err := r.db.WithContext(ctx).Where("website_id = ?", websiteID).Order("is_default DESC, is_active DESC, name ASC").Find(&themes).Error
	return themes, err
}

// GetActiveTheme retrieves the active theme for a website
func (r *websiteThemeRepository) GetActiveTheme(ctx context.Context, websiteID uint) (*models.WebsiteTheme, error) {
	var theme models.WebsiteTheme
	err := r.db.WithContext(ctx).Where("website_id = ? AND is_active = true", websiteID).First(&theme).Error
	if err != nil {
		return nil, err
	}
	return &theme, nil
}

// GetDefaultTheme retrieves the default theme for a website
func (r *websiteThemeRepository) GetDefaultTheme(ctx context.Context, websiteID uint) (*models.WebsiteTheme, error) {
	var theme models.WebsiteTheme
	err := r.db.WithContext(ctx).Where("website_id = ? AND is_default = true", websiteID).First(&theme).Error
	if err != nil {
		return nil, err
	}
	return &theme, nil
}

// Update updates a theme
func (r *websiteThemeRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&models.WebsiteTheme{}).Where("id = ?", id).Updates(updates).Error
}

// Delete soft deletes a theme (sets status to archived)
func (r *websiteThemeRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&models.WebsiteTheme{}).Where("id = ?", id).Update("status", models.WebsiteThemeStatusArchived).Error
}

// List retrieves themes based on filter
func (r *websiteThemeRepository) List(ctx context.Context, filter models.WebsiteThemeFilter) ([]*models.WebsiteTheme, int64, error) {
	var themes []*models.WebsiteTheme
	var total int64

	query := r.db.WithContext(ctx).Model(&models.WebsiteTheme{})

	// Apply filters
	if filter.WebsiteID > 0 {
		query = query.Where("website_id = ?", filter.WebsiteID)
	}

	if filter.TenantID > 0 {
		query = query.Where("tenant_id = ?", filter.TenantID)
	}

	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}

	if filter.IsDefault != nil {
		query = query.Where("is_default = ?", *filter.IsDefault)
	}

	if filter.IsActive != nil {
		query = query.Where("is_active = ?", *filter.IsActive)
	}

	if filter.ParentThemeID != nil {
		if *filter.ParentThemeID == 0 {
			query = query.Where("parent_theme_id IS NULL")
		} else {
			query = query.Where("parent_theme_id = ?", *filter.ParentThemeID)
		}
	}

	if filter.Author != "" {
		query = query.Where("author = ?", filter.Author)
	}

	if filter.Search != "" {
		searchTerm := "%" + filter.Search + "%"
		query = query.Where("name LIKE ? OR author LIKE ? OR description LIKE ?", searchTerm, searchTerm, searchTerm)
	}

	// Count total
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Apply sorting
	orderBy := "is_default DESC, is_active DESC, name ASC"
	if filter.SortBy != "" {
		direction := "ASC"
		if filter.SortOrder == "desc" {
			direction = "DESC"
		}
		orderBy = fmt.Sprintf("%s %s", filter.SortBy, direction)
	}
	query = query.Order(orderBy)

	// Apply pagination
	if filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	err = query.Find(&themes).Error
	return themes, total, err
}

// ActivateTheme activates a theme and deactivates others
func (r *websiteThemeRepository) ActivateTheme(ctx context.Context, websiteID, themeID uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Deactivate all themes for the website
		if err := tx.Model(&models.WebsiteTheme{}).Where("website_id = ?", websiteID).Update("is_active", false).Error; err != nil {
			return err
		}

		// Activate the selected theme
		if err := tx.Model(&models.WebsiteTheme{}).Where("id = ? AND website_id = ?", themeID, websiteID).Updates(map[string]interface{}{
			"is_active": true,
			"status":    models.WebsiteThemeStatusActive,
		}).Error; err != nil {
			return err
		}

		// Update website's active theme
		return tx.Model(&models.Website{}).Where("id = ?", websiteID).Update("active_theme",
			tx.Model(&models.WebsiteTheme{}).Select("name").Where("id = ?", themeID)).Error
	})
}

// DeactivateTheme deactivates a theme
func (r *websiteThemeRepository) DeactivateTheme(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&models.WebsiteTheme{}).Where("id = ?", id).Updates(map[string]interface{}{
		"is_active": false,
		"status":    models.WebsiteThemeStatusInactive,
	}).Error
}

// CloneTheme clones a theme
func (r *websiteThemeRepository) CloneTheme(ctx context.Context, sourceID uint, newTheme *models.WebsiteTheme) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Get source theme
		var sourceTheme models.WebsiteTheme
		if err := tx.Where("id = ?", sourceID).First(&sourceTheme).Error; err != nil {
			return err
		}

		// Copy properties from source theme
		newTheme.Version = sourceTheme.Version
		newTheme.Author = sourceTheme.Author
		newTheme.Config = sourceTheme.Config
		newTheme.CustomColors = sourceTheme.CustomColors
		newTheme.CustomFonts = sourceTheme.CustomFonts
		newTheme.CustomCSS = sourceTheme.CustomCSS
		newTheme.CustomJS = sourceTheme.CustomJS
		newTheme.StylesheetPath = sourceTheme.StylesheetPath
		newTheme.JavascriptPath = sourceTheme.JavascriptPath
		newTheme.AssetsPath = sourceTheme.AssetsPath
		newTheme.Templates = sourceTheme.Templates
		newTheme.SupportedBlocks = sourceTheme.SupportedBlocks
		newTheme.Features = sourceTheme.Features

		// Create the new theme
		return tx.Create(newTheme).Error
	})
}

// GetChildThemes retrieves all child themes for a parent theme
func (r *websiteThemeRepository) GetChildThemes(ctx context.Context, parentThemeID uint) ([]*models.WebsiteTheme, error) {
	var themes []*models.WebsiteTheme
	err := r.db.WithContext(ctx).Where("parent_theme_id = ?", parentThemeID).Order("name ASC").Find(&themes).Error
	return themes, err
}

// GetParentTheme retrieves the parent theme for a theme
func (r *websiteThemeRepository) GetParentTheme(ctx context.Context, themeID uint) (*models.WebsiteTheme, error) {
	var theme models.WebsiteTheme
	err := r.db.WithContext(ctx).
		Joins("JOIN website_themes pt ON pt.id = website_themes.parent_theme_id").
		Where("website_themes.id = ?", themeID).
		First(&theme).Error
	if err != nil {
		return nil, err
	}
	return &theme, nil
}

// UpdateStatus updates theme status
func (r *websiteThemeRepository) UpdateStatus(ctx context.Context, id uint, status models.WebsiteThemeStatus) error {
	return r.db.WithContext(ctx).Model(&models.WebsiteTheme{}).Where("id = ?", id).Update("status", status).Error
}

// SetAsDefault sets a theme as default for a website
func (r *websiteThemeRepository) SetAsDefault(ctx context.Context, websiteID, themeID uint) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// Remove default flag from all themes for the website
		if err := tx.Model(&models.WebsiteTheme{}).Where("website_id = ?", websiteID).Update("is_default", false).Error; err != nil {
			return err
		}

		// Set the selected theme as default
		return tx.Model(&models.WebsiteTheme{}).Where("id = ? AND website_id = ?", themeID, websiteID).Update("is_default", true).Error
	})
}

// GetThemesByTenant retrieves all themes for websites of a tenant
func (r *websiteThemeRepository) GetThemesByTenant(ctx context.Context, tenantID uint) ([]*models.WebsiteTheme, error) {
	var themes []*models.WebsiteTheme
	err := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID).Order("website_id ASC, name ASC").Find(&themes).Error
	return themes, err
}

// CheckNameExists checks if a theme name already exists for a website
func (r *websiteThemeRepository) CheckNameExists(ctx context.Context, websiteID uint, name string, excludeID uint) (bool, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.WebsiteTheme{}).Where("website_id = ? AND name = ?", websiteID, name)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	err := query.Count(&count).Error
	return count > 0, err
}

// CountByWebsite counts themes for a website
func (r *websiteThemeRepository) CountByWebsite(ctx context.Context, websiteID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.WebsiteTheme{}).Where("website_id = ?", websiteID).Count(&count).Error
	return count, err
}

// GetThemeConfig retrieves theme configuration
func (r *websiteThemeRepository) GetThemeConfig(ctx context.Context, id uint) (models.JSONMap, error) {
	var theme models.WebsiteTheme
	err := r.db.WithContext(ctx).Select("config").Where("id = ?", id).First(&theme).Error
	if err != nil {
		return nil, err
	}
	return theme.Config, nil
}

// UpdateThemeConfig updates theme configuration
func (r *websiteThemeRepository) UpdateThemeConfig(ctx context.Context, id uint, config models.JSONMap) error {
	return r.db.WithContext(ctx).Model(&models.WebsiteTheme{}).Where("id = ?", id).Update("config", config).Error
}

// GetThemeCustomizations retrieves theme customizations
func (r *websiteThemeRepository) GetThemeCustomizations(ctx context.Context, id uint) (map[string]interface{}, error) {
	var theme models.WebsiteTheme
	err := r.db.WithContext(ctx).Select("custom_colors, custom_fonts, custom_css, custom_js").Where("id = ?", id).First(&theme).Error
	if err != nil {
		return nil, err
	}

	customizations := make(map[string]interface{})
	customizations["custom_colors"] = theme.CustomColors
	customizations["custom_fonts"] = theme.CustomFonts
	customizations["custom_css"] = theme.CustomCSS
	customizations["custom_js"] = theme.CustomJS

	return customizations, nil
}

// UpdateThemeCustomizations updates theme customizations
func (r *websiteThemeRepository) UpdateThemeCustomizations(ctx context.Context, id uint, customizations map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&models.WebsiteTheme{}).Where("id = ?", id).Updates(customizations).Error
}
