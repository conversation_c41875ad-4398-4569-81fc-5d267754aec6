package mysql

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
)

// websiteRepository implements the WebsiteRepository interface
type websiteRepository struct {
	db *gorm.DB
}

// NewWebsiteRepository creates a new website repository
func NewWebsiteRepository(db *gorm.DB) repositories.WebsiteRepository {
	return &websiteRepository{db: db}
}

// <PERSON><PERSON> creates a new website
func (r *websiteRepository) Create(ctx context.Context, website *models.Website) error {
	return r.db.WithContext(ctx).Create(website).Error
}

// GetByID retrieves a website by ID
func (r *websiteRepository) GetByID(ctx context.Context, id uint) (*models.Website, error) {
	var website models.Website
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&website).Error
	if err != nil {
		return nil, err
	}
	return &website, nil
}

// GetByTenantID retrieves a website by tenant ID and website ID
func (r *websiteRepository) GetByTenantID(ctx context.Context, tenantID, websiteID uint) (*models.Website, error) {
	var website models.Website
	err := r.db.WithContext(ctx).Where("tenant_id = ? AND id = ?", tenantID, websiteID).First(&website).Error
	if err != nil {
		return nil, err
	}
	return &website, nil
}

// GetByDomain retrieves a website by domain
func (r *websiteRepository) GetByDomain(ctx context.Context, domain string) (*models.Website, error) {
	var website models.Website
	err := r.db.WithContext(ctx).Where("domain = ? AND status != ?", domain, models.WebsiteStatusInactive).First(&website).Error
	if err != nil {
		return nil, err
	}
	return &website, nil
}

// GetBySubdomain retrieves a website by subdomain
func (r *websiteRepository) GetBySubdomain(ctx context.Context, subdomain string) (*models.Website, error) {
	var website models.Website
	err := r.db.WithContext(ctx).Where("subdomain = ? AND status != ?", subdomain, models.WebsiteStatusInactive).First(&website).Error
	if err != nil {
		return nil, err
	}
	return &website, nil
}

// GetByTenant retrieves all websites for a tenant
func (r *websiteRepository) GetByTenant(ctx context.Context, tenantID uint) ([]*models.Website, error) {
	var websites []*models.Website
	err := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID).Find(&websites).Error
	return websites, err
}

// Update updates a website
func (r *websiteRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&models.Website{}).Where("id = ?", id).Updates(updates).Error
}

// Delete soft deletes a website (sets status to inactive)
func (r *websiteRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Model(&models.Website{}).Where("id = ?", id).Update("status", models.WebsiteStatusInactive).Error
}

// List retrieves websites based on filter
func (r *websiteRepository) List(ctx context.Context, filter models.WebsiteFilter) ([]*models.Website, int64, error) {
	var websites []*models.Website
	var total int64

	query := r.db.WithContext(ctx).Model(&models.Website{})

	// Apply filters
	if filter.TenantID > 0 {
		query = query.Where("tenant_id = ?", filter.TenantID)
	}

	if filter.Status != "" {
		query = query.Where("status = ?", filter.Status)
	}

	if filter.Domain != "" {
		query = query.Where("domain = ?", filter.Domain)
	}

	if filter.Subdomain != "" {
		query = query.Where("subdomain = ?", filter.Subdomain)
	}

	if filter.Search != "" {
		searchTerm := "%" + filter.Search + "%"
		query = query.Where("name LIKE ? OR domain LIKE ? OR subdomain LIKE ?", searchTerm, searchTerm, searchTerm)
	}

	// Exclude deleted websites unless explicitly included
	if !filter.IncludeDeleted {
		query = query.Where("status != ?", models.WebsiteStatusInactive)
	}

	// Count total
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Apply sorting
	orderBy := "created_at DESC"
	if filter.SortBy != "" {
		direction := "ASC"
		if filter.SortOrder == "desc" {
			direction = "DESC"
		}
		orderBy = fmt.Sprintf("%s %s", filter.SortBy, direction)
	}
	query = query.Order(orderBy)

	// Apply pagination
	if filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	err = query.Find(&websites).Error
	return websites, total, err
}

// ListWithCursor retrieves websites using cursor-based pagination
func (r *websiteRepository) ListWithCursor(ctx context.Context, tenantID uint, req *pagination.CursorRequest, filters map[string]interface{}) ([]*models.Website, *pagination.CursorResponse, error) {
	var websites []*models.Website

	// Build base query with tenant isolation
	query := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID)

	// Apply filters
	if name, ok := filters["name"]; ok && name != "" {
		query = query.Where("name LIKE ?", "%"+name.(string)+"%")
	}
	if domain, ok := filters["domain"]; ok && domain != "" {
		query = query.Where("domain = ?", domain)
	}
	if status, ok := filters["status"]; ok && status != "" {
		query = query.Where("status = ?", status)
	}
	if language, ok := filters["language"]; ok && language != "" {
		query = query.Where("language = ?", language)
	}

	// Exclude inactive websites by default
	query = query.Where("status != ?", models.WebsiteStatusInactive)

	// Apply cursor
	if req.Cursor != "" {
		cursor, err := pagination.ParseCursor(req.Cursor)
		if err == nil {
			query = query.Where("(created_at < ? OR (created_at = ? AND id < ?))",
				cursor.CreatedAt, cursor.CreatedAt, cursor.ID)
		}
	}

	// Apply sorting and limit
	sortBy := "created_at"
	sortOrder := "DESC"
	if sortByFilter, ok := filters["sort_by"]; ok && sortByFilter != "" {
		sortBy = sortByFilter.(string)
	}
	if sortOrderFilter, ok := filters["sort_order"]; ok && sortOrderFilter != "" {
		sortOrder = sortOrderFilter.(string)
	}

	query = query.Order(fmt.Sprintf("%s %s, id %s", sortBy, sortOrder, sortOrder))

	// Get one extra record to check if there are more
	limit := pagination.ValidateLimit(req.Limit)
	query = query.Limit(limit + 1)

	err := query.Find(&websites).Error
	if err != nil {
		return nil, nil, err
	}

	// Build cursor response
	hasNext := len(websites) > limit
	if hasNext {
		websites = websites[:limit]
	}

	response := &pagination.CursorResponse{
		HasNext: hasNext,
		Count:   len(websites),
		Limit:   limit,
	}

	// Generate next cursor if there are more records
	if hasNext && len(websites) > 0 {
		last := websites[len(websites)-1]
		response.NextCursor, _ = pagination.EncodeCursor(last.ID, last.CreatedAt)
	}

	return websites, response, nil
}

// CheckDomainExists checks if a domain already exists
func (r *websiteRepository) CheckDomainExists(ctx context.Context, domain string, excludeID uint) (bool, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.Website{}).Where("domain = ?", domain)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	err := query.Count(&count).Error
	return count > 0, err
}

// CheckSubdomainExists checks if a subdomain already exists
func (r *websiteRepository) CheckSubdomainExists(ctx context.Context, subdomain string, excludeID uint) (bool, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.Website{}).Where("subdomain = ?", subdomain)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	err := query.Count(&count).Error
	return count > 0, err
}

// GetActiveWebsites retrieves all active websites for a tenant
func (r *websiteRepository) GetActiveWebsites(ctx context.Context, tenantID uint) ([]*models.Website, error) {
	var websites []*models.Website
	err := r.db.WithContext(ctx).Where("tenant_id = ? AND status = ?", tenantID, models.WebsiteStatusActive).Find(&websites).Error
	return websites, err
}

// CountByTenant counts websites for a tenant
func (r *websiteRepository) CountByTenant(ctx context.Context, tenantID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.Website{}).Where("tenant_id = ?", tenantID).Count(&count).Error
	return count, err
}

// GetWithSettings retrieves a website with its settings
func (r *websiteRepository) GetWithSettings(ctx context.Context, id uint) (*models.Website, error) {
	var website models.Website
	err := r.db.WithContext(ctx).Preload("Settings").Where("id = ?", id).First(&website).Error
	if err != nil {
		return nil, err
	}
	return &website, nil
}

// GetWithThemes retrieves a website with its themes
func (r *websiteRepository) GetWithThemes(ctx context.Context, id uint) (*models.Website, error) {
	var website models.Website
	err := r.db.WithContext(ctx).Preload("Themes").Where("id = ?", id).First(&website).Error
	if err != nil {
		return nil, err
	}
	return &website, nil
}

// GetWithAll retrieves a website with all associations
func (r *websiteRepository) GetWithAll(ctx context.Context, id uint) (*models.Website, error) {
	var website models.Website
	err := r.db.WithContext(ctx).
		Preload("Tenant").
		Preload("Settings").
		Preload("Themes").
		Preload("ActiveThemeObj").
		Where("id = ?", id).
		First(&website).Error
	if err != nil {
		return nil, err
	}
	return &website, nil
}

// UpdateStatus updates website status
func (r *websiteRepository) UpdateStatus(ctx context.Context, id uint, status models.WebsiteStatus) error {
	return r.db.WithContext(ctx).Model(&models.Website{}).Where("id = ?", id).Update("status", status).Error
}

// ActivateWebsite activates a website
func (r *websiteRepository) ActivateWebsite(ctx context.Context, id uint) error {
	return r.UpdateStatus(ctx, id, models.WebsiteStatusActive)
}

// DeactivateWebsite deactivates a website
func (r *websiteRepository) DeactivateWebsite(ctx context.Context, id uint) error {
	return r.UpdateStatus(ctx, id, models.WebsiteStatusInactive)
}

// SuspendWebsite suspends a website
func (r *websiteRepository) SuspendWebsite(ctx context.Context, id uint) error {
	return r.UpdateStatus(ctx, id, models.WebsiteStatusSuspended)
}
