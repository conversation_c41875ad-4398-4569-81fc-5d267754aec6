package mysql

import (
	"context"
	"fmt"

	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/repositories"
)

// websiteSettingRepository implements the WebsiteSettingRepository interface
type websiteSettingRepository struct {
	db *gorm.DB
}

// NewWebsiteSettingRepository creates a new website setting repository
func NewWebsiteSettingRepository(db *gorm.DB) repositories.WebsiteSettingRepository {
	return &websiteSettingRepository{db: db}
}

// Create creates a new website setting
func (r *websiteSettingRepository) Create(ctx context.Context, setting *models.WebsiteSetting) error {
	return r.db.WithContext(ctx).Create(setting).Error
}

// GetByID retrieves a setting by ID
func (r *websiteSettingRepository) GetByID(ctx context.Context, id uint) (*models.WebsiteSetting, error) {
	var setting models.WebsiteSetting
	err := r.db.WithContext(ctx).Where("id = ?", id).First(&setting).Error
	if err != nil {
		return nil, err
	}
	return &setting, nil
}

// GetByKey retrieves a setting by website ID and key
func (r *websiteSettingRepository) GetByKey(ctx context.Context, websiteID uint, key string) (*models.WebsiteSetting, error) {
	var setting models.WebsiteSetting
	err := r.db.WithContext(ctx).Where("website_id = ? AND setting_key = ?", websiteID, key).First(&setting).Error
	if err != nil {
		return nil, err
	}
	return &setting, nil
}

// GetByCategory retrieves all settings for a website in a category
func (r *websiteSettingRepository) GetByCategory(ctx context.Context, websiteID uint, category models.WebsiteSettingCategory) ([]*models.WebsiteSetting, error) {
	var settings []*models.WebsiteSetting
	err := r.db.WithContext(ctx).Where("website_id = ? AND category = ?", websiteID, category).Find(&settings).Error
	return settings, err
}

// Update updates a setting
func (r *websiteSettingRepository) Update(ctx context.Context, id uint, updates map[string]interface{}) error {
	return r.db.WithContext(ctx).Model(&models.WebsiteSetting{}).Where("id = ?", id).Updates(updates).Error
}

// Upsert creates or updates a setting
func (r *websiteSettingRepository) Upsert(ctx context.Context, setting *models.WebsiteSetting) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var existing models.WebsiteSetting
		err := tx.Where("website_id = ? AND setting_key = ?", setting.WebsiteID, setting.SettingKey).First(&existing).Error

		if err == gorm.ErrRecordNotFound {
			// Create new setting
			return tx.Create(setting).Error
		} else if err != nil {
			return err
		}

		// Update existing setting
		setting.ID = existing.ID
		return tx.Save(setting).Error
	})
}

// Delete deletes a setting
func (r *websiteSettingRepository) Delete(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.WebsiteSetting{}, id).Error
}

// DeleteByKey deletes a setting by website ID and key
func (r *websiteSettingRepository) DeleteByKey(ctx context.Context, websiteID uint, key string) error {
	return r.db.WithContext(ctx).Where("website_id = ? AND setting_key = ?", websiteID, key).Delete(&models.WebsiteSetting{}).Error
}

// List retrieves settings based on filter
func (r *websiteSettingRepository) List(ctx context.Context, filter models.WebsiteSettingFilter) ([]*models.WebsiteSetting, int64, error) {
	var settings []*models.WebsiteSetting
	var total int64

	query := r.db.WithContext(ctx).Model(&models.WebsiteSetting{})

	// Apply filters
	if filter.WebsiteID > 0 {
		query = query.Where("website_id = ?", filter.WebsiteID)
	}

	if filter.TenantID > 0 {
		query = query.Where("tenant_id = ?", filter.TenantID)
	}

	if filter.Category != "" {
		query = query.Where("category = ?", filter.Category)
	}

	if filter.DataType != "" {
		query = query.Where("data_type = ?", filter.DataType)
	}

	if filter.IsPublic != nil {
		query = query.Where("is_public = ?", *filter.IsPublic)
	}

	if filter.IsRequired != nil {
		query = query.Where("is_required = ?", *filter.IsRequired)
	}

	if filter.IsEncrypted != nil {
		query = query.Where("is_encrypted = ?", *filter.IsEncrypted)
	}

	if filter.SettingKey != "" {
		query = query.Where("setting_key = ?", filter.SettingKey)
	}

	if filter.Search != "" {
		searchTerm := "%" + filter.Search + "%"
		query = query.Where("setting_name LIKE ? OR setting_key LIKE ? OR description LIKE ?", searchTerm, searchTerm, searchTerm)
	}

	// Count total
	err := query.Count(&total).Error
	if err != nil {
		return nil, 0, err
	}

	// Apply sorting
	orderBy := "category ASC, setting_key ASC"
	if filter.SortBy != "" {
		direction := "ASC"
		if filter.SortOrder == "desc" {
			direction = "DESC"
		}
		orderBy = fmt.Sprintf("%s %s", filter.SortBy, direction)
	}
	query = query.Order(orderBy)

	// Apply pagination
	if filter.Page > 0 && filter.PageSize > 0 {
		offset := (filter.Page - 1) * filter.PageSize
		query = query.Offset(offset).Limit(filter.PageSize)
	}

	err = query.Find(&settings).Error
	return settings, total, err
}

// GetByWebsite retrieves all settings for a website
func (r *websiteSettingRepository) GetByWebsite(ctx context.Context, websiteID uint) ([]*models.WebsiteSetting, error) {
	var settings []*models.WebsiteSetting
	err := r.db.WithContext(ctx).Where("website_id = ?", websiteID).Order("category ASC, setting_key ASC").Find(&settings).Error
	return settings, err
}

// GetPublicSettings retrieves all public settings for a website
func (r *websiteSettingRepository) GetPublicSettings(ctx context.Context, websiteID uint) ([]*models.WebsiteSetting, error) {
	var settings []*models.WebsiteSetting
	err := r.db.WithContext(ctx).Where("website_id = ? AND is_public = true", websiteID).Order("category ASC, setting_key ASC").Find(&settings).Error
	return settings, err
}

// GetRequiredSettings retrieves all required settings for a website
func (r *websiteSettingRepository) GetRequiredSettings(ctx context.Context, websiteID uint) ([]*models.WebsiteSetting, error) {
	var settings []*models.WebsiteSetting
	err := r.db.WithContext(ctx).Where("website_id = ? AND is_required = true", websiteID).Order("category ASC, setting_key ASC").Find(&settings).Error
	return settings, err
}

// BulkUpsert creates or updates multiple settings
func (r *websiteSettingRepository) BulkUpsert(ctx context.Context, websiteID uint, settings []models.WebsiteSettingCreateRequest) error {
	return r.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		for _, settingReq := range settings {
			setting := &models.WebsiteSetting{
				WebsiteID:       websiteID,
				SettingKey:      settingReq.SettingKey,
				SettingName:     settingReq.SettingName,
				Category:        settingReq.Category,
				SettingValue:    settingReq.SettingValue,
				DefaultValue:    settingReq.DefaultValue,
				DataType:        settingReq.DataType,
				IsPublic:        settingReq.IsPublic,
				IsRequired:      settingReq.IsRequired,
				IsEncrypted:     settingReq.IsEncrypted,
				ValidationRules: settingReq.ValidationRules,
				Description:     settingReq.Description,
				Options:         settingReq.Options,
			}

			var existing models.WebsiteSetting
			err := tx.Where("website_id = ? AND setting_key = ?", websiteID, settingReq.SettingKey).First(&existing).Error

			if err == gorm.ErrRecordNotFound {
				// Create new setting
				if err := tx.Create(setting).Error; err != nil {
					return err
				}
			} else if err != nil {
				return err
			} else {
				// Update existing setting
				setting.ID = existing.ID
				if err := tx.Save(setting).Error; err != nil {
					return err
				}
			}
		}
		return nil
	})
}

// BulkDelete deletes multiple settings by keys
func (r *websiteSettingRepository) BulkDelete(ctx context.Context, websiteID uint, keys []string) error {
	return r.db.WithContext(ctx).Where("website_id = ? AND setting_key IN ?", websiteID, keys).Delete(&models.WebsiteSetting{}).Error
}

// GetSettingsByTenant retrieves all settings for websites of a tenant
func (r *websiteSettingRepository) GetSettingsByTenant(ctx context.Context, tenantID uint) ([]*models.WebsiteSetting, error) {
	var settings []*models.WebsiteSetting
	err := r.db.WithContext(ctx).Where("tenant_id = ?", tenantID).Order("website_id ASC, category ASC, setting_key ASC").Find(&settings).Error
	return settings, err
}

// CheckKeyExists checks if a setting key already exists for a website
func (r *websiteSettingRepository) CheckKeyExists(ctx context.Context, websiteID uint, key string, excludeID uint) (bool, error) {
	var count int64
	query := r.db.WithContext(ctx).Model(&models.WebsiteSetting{}).Where("website_id = ? AND setting_key = ?", websiteID, key)

	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}

	err := query.Count(&count).Error
	return count > 0, err
}

// GetSettingValue retrieves a setting value (or default) by key
func (r *websiteSettingRepository) GetSettingValue(ctx context.Context, websiteID uint, key string) (string, error) {
	var setting models.WebsiteSetting
	err := r.db.WithContext(ctx).Where("website_id = ? AND setting_key = ?", websiteID, key).First(&setting).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return "", nil
		}
		return "", err
	}

	// Return setting value or default value
	if setting.SettingValue != "" {
		return setting.SettingValue, nil
	}
	return setting.DefaultValue, nil
}

// CountByWebsite counts settings for a website
func (r *websiteSettingRepository) CountByWebsite(ctx context.Context, websiteID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.WebsiteSetting{}).Where("website_id = ?", websiteID).Count(&count).Error
	return count, err
}
