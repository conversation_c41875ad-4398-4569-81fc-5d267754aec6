package website

import (
	"github.com/gin-gonic/gin"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/website/handlers"
	"github.com/tranthanhloi/wn-api-v3/pkg/http/middleware"
)

// RegisterWebsiteRoutes registers all website routes
func RegisterWebsiteRoutes(
	router *gin.RouterGroup,
	websiteHandler *handlers.WebsiteHandler,
	domainHandler *handlers.DomainHandler,
	templateHandler *handlers.WebsiteTemplateHandler,
	categoryHandler *handlers.TemplateCategoryHandler,
) {
	// Website management routes (require authentication and tenant context)
	websites := router.Group("/websites")
	websites.Use(middleware.RequireAuthentication())
	{
		// Website CRUD operations
		websites.POST("", websiteHandler.CreateWebsite)
		websites.GET("", websiteHandler.ListWebsites)
		websites.GET("/stats", websiteHandler.GetWebsiteStats)

		websites.GET("/:id", websiteHandler.GetWebsite)
		websites.GET("/:id/details", websiteHandler.GetWebsiteWithDetails)
		websites.PUT("/:id", websiteHandler.UpdateWebsite)
		websites.DELETE("/:id", websiteHandler.DeleteWebsite)

		// Website status management
		websites.POST("/:id/activate", websiteHandler.ActivateWebsite)
		websites.POST("/:id/deactivate", websiteHandler.DeactivateWebsite)
		websites.POST("/:id/suspend", websiteHandler.SuspendWebsite)

		// Website operations
		websites.POST("/:id/clone", websiteHandler.CloneWebsite)

		// Domain verification for websites
		websites.POST("/:id/verify-domain", domainHandler.VerifyDomainOwnership)
		websites.GET("/:id/verify-domain/:domain", domainHandler.CheckDomainVerification)
		websites.GET("/:id/domain-verification-instructions/:domain", domainHandler.GetDomainVerificationInstructions)
	}

	// Domain management routes (public utilities)
	domains := router.Group("/domains")
	{
		// Domain validation and availability
		domains.POST("/validate", domainHandler.ValidateDomain)
		domains.GET("/check-availability", domainHandler.CheckDomainAvailability)
		domains.GET("/info", domainHandler.GetDomainInfo)
		domains.GET("/reserved-subdomains", domainHandler.GetReservedSubdomains)
	}

	// Website template routes (public - no authentication required)
	templates := router.Group("/websites/templates")
	{
		// CRUD operations
		templates.POST("", templateHandler.CreateTemplate)
		templates.GET("/:id", templateHandler.GetTemplate)
		templates.PUT("/:id", templateHandler.UpdateTemplate)
		templates.DELETE("/:id", templateHandler.DeleteTemplate)
		templates.GET("", templateHandler.ListTemplates)

		// Special endpoints
		templates.GET("/featured", templateHandler.GetFeaturedTemplates)
		templates.GET("/popular", templateHandler.GetPopularTemplates)
		templates.GET("/search", templateHandler.SearchTemplates)
		templates.GET("/stats", templateHandler.GetTemplateStats)

		// Actions
		templates.POST("/:id/download", templateHandler.IncrementDownloadCount)
		templates.POST("/:id/use", templateHandler.IncrementUsageCount)
	}

	// Template category routes (public - no authentication required)
	categories := router.Group("/websites/template-categories")
	{
		// CRUD operations
		categories.POST("", categoryHandler.CreateCategory)
		categories.GET("", categoryHandler.ListCategories)

		// Special endpoints (must come before parameterized routes)
		categories.GET("/tree", categoryHandler.GetCategoryTree)
		categories.GET("/main", categoryHandler.GetMainCategories)
		categories.GET("/featured", categoryHandler.GetFeaturedCategories)
		categories.GET("/stats", categoryHandler.GetCategoryStats)

		// Parameterized routes (must come after specific routes)
		categories.GET("/:id", categoryHandler.GetCategory)
		categories.PUT("/:id", categoryHandler.UpdateCategory)
		categories.DELETE("/:id", categoryHandler.DeleteCategory)
		categories.GET("/:id/subcategories", categoryHandler.GetSubcategories)

		// Actions
		categories.POST("/:id/move", categoryHandler.MoveCategoryToParent)
	}
}

// RegisterPublicRoutes registers public website routes (for domain resolution)
func RegisterPublicRoutes(
	router *gin.RouterGroup,
	websiteHandler *handlers.WebsiteHandler,
) {
	// Public routes for website resolution by domain/subdomain
	// These routes are used for website context resolution and don't require authentication
	public := router.Group("/public/websites")
	{
		// These would be used by the website isolation middleware
		// to resolve website context from domain/subdomain
		public.GET("/resolve-by-domain", func(c *gin.Context) {
			// This endpoint would be used internally by middleware
			// Implementation would depend on specific requirements
			c.JSON(200, gin.H{"message": "Website resolution endpoint"})
		})
	}
}

// RouteInfo represents route information for documentation
type RouteInfo struct {
	Method      string `json:"method"`
	Path        string `json:"path"`
	Description string `json:"description"`
	Protected   bool   `json:"protected"`
}

// GetRouteInfo returns information about all website routes
func GetRouteInfo() []RouteInfo {
	return []RouteInfo{
		// Website management routes
		{
			Method:      "POST",
			Path:        "/api/v1/websites",
			Description: "Create a new website",
			Protected:   true,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/websites",
			Description: "List websites for tenant",
			Protected:   true,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/websites/stats",
			Description: "Get website statistics for tenant",
			Protected:   true,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/websites/:id",
			Description: "Get website by ID",
			Protected:   true,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/websites/:id/details",
			Description: "Get website with all details",
			Protected:   true,
		},
		{
			Method:      "PUT",
			Path:        "/api/v1/websites/:id",
			Description: "Update website",
			Protected:   true,
		},
		{
			Method:      "DELETE",
			Path:        "/api/v1/websites/:id",
			Description: "Delete website",
			Protected:   true,
		},
		{
			Method:      "POST",
			Path:        "/api/v1/websites/:id/activate",
			Description: "Activate website",
			Protected:   true,
		},
		{
			Method:      "POST",
			Path:        "/api/v1/websites/:id/deactivate",
			Description: "Deactivate website",
			Protected:   true,
		},
		{
			Method:      "POST",
			Path:        "/api/v1/websites/:id/suspend",
			Description: "Suspend website",
			Protected:   true,
		},
		{
			Method:      "POST",
			Path:        "/api/v1/websites/:id/clone",
			Description: "Clone website",
			Protected:   true,
		},
		{
			Method:      "POST",
			Path:        "/api/v1/websites/:id/verify-domain",
			Description: "Verify domain ownership",
			Protected:   true,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/websites/:id/verify-domain/:domain",
			Description: "Check domain verification status",
			Protected:   true,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/websites/:id/domain-verification-instructions/:domain",
			Description: "Get domain verification instructions",
			Protected:   true,
		},

		// Domain management routes
		{
			Method:      "POST",
			Path:        "/api/v1/domains/validate",
			Description: "Validate domain or subdomain",
			Protected:   false,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/domains/check-availability",
			Description: "Check domain/subdomain availability",
			Protected:   false,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/domains/info",
			Description: "Get domain information",
			Protected:   false,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/domains/reserved-subdomains",
			Description: "Get list of reserved subdomains",
			Protected:   false,
		},

		// Public routes
		{
			Method:      "GET",
			Path:        "/api/v1/public/websites/resolve-by-domain",
			Description: "Resolve website by domain (internal use)",
			Protected:   false,
		},

		// Website template routes
		{
			Method:      "POST",
			Path:        "/api/v1/templates",
			Description: "Create a new website template",
			Protected:   false,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/templates/:id",
			Description: "Get website template by ID",
			Protected:   false,
		},
		{
			Method:      "PUT",
			Path:        "/api/v1/templates/:id",
			Description: "Update website template",
			Protected:   false,
		},
		{
			Method:      "DELETE",
			Path:        "/api/v1/templates/:id",
			Description: "Delete website template",
			Protected:   false,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/templates",
			Description: "List website templates with filtering",
			Protected:   false,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/templates/featured",
			Description: "Get featured website templates",
			Protected:   false,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/templates/popular",
			Description: "Get popular website templates",
			Protected:   false,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/templates/search",
			Description: "Search website templates",
			Protected:   false,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/templates/stats",
			Description: "Get website template statistics",
			Protected:   false,
		},
		{
			Method:      "POST",
			Path:        "/api/v1/templates/:id/download",
			Description: "Increment template download count",
			Protected:   false,
		},
		{
			Method:      "POST",
			Path:        "/api/v1/templates/:id/use",
			Description: "Increment template usage count",
			Protected:   false,
		},

		// Template category routes
		{
			Method:      "POST",
			Path:        "/api/v1/template-categories",
			Description: "Create a new template category",
			Protected:   false,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/template-categories/:id",
			Description: "Get template category by ID",
			Protected:   false,
		},
		{
			Method:      "PUT",
			Path:        "/api/v1/template-categories/:id",
			Description: "Update template category",
			Protected:   false,
		},
		{
			Method:      "DELETE",
			Path:        "/api/v1/template-categories/:id",
			Description: "Delete template category",
			Protected:   false,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/template-categories",
			Description: "List template categories with filtering",
			Protected:   false,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/template-categories/tree",
			Description: "Get template category tree structure",
			Protected:   false,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/template-categories/main",
			Description: "Get main template categories",
			Protected:   false,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/template-categories/featured",
			Description: "Get featured template categories",
			Protected:   false,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/template-categories/stats",
			Description: "Get template category statistics",
			Protected:   false,
		},
		{
			Method:      "GET",
			Path:        "/api/v1/template-categories/:parent_id/subcategories",
			Description: "Get subcategories for parent category",
			Protected:   false,
		},
		{
			Method:      "POST",
			Path:        "/api/v1/template-categories/:id/move",
			Description: "Move category to new parent",
			Protected:   false,
		},
	}
}
