package models

import (
	"time"

	tenantModels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"gorm.io/gorm"
)

// WebsiteSettingCategory represents the category of a website setting
type WebsiteSettingCategory string

const (
	WebsiteSettingCategoryGeneral   WebsiteSettingCategory = "general"
	WebsiteSettingCategorySEO       WebsiteSettingCategory = "seo"
	WebsiteSettingCategorySocial    WebsiteSettingCategory = "social"
	WebsiteSettingCategorySecurity  WebsiteSettingCategory = "security"
	WebsiteSettingCategoryTheme     WebsiteSettingCategory = "theme"
	WebsiteSettingCategoryAnalytics WebsiteSettingCategory = "analytics"
	WebsiteSettingCategoryComments  WebsiteSettingCategory = "comments"
	WebsiteSettingCategoryMedia     WebsiteSettingCategory = "media"
)

// WebsiteSettingDataType represents the data type of a website setting
type WebsiteSettingDataType string

const (
	WebsiteSettingDataTypeString  WebsiteSettingDataType = "string"
	WebsiteSettingDataTypeNumber  WebsiteSettingDataType = "number"
	WebsiteSettingDataTypeBoolean WebsiteSettingDataType = "boolean"
	WebsiteSettingDataTypeJSON    WebsiteSettingDataType = "json"
	WebsiteSettingDataTypeArray   WebsiteSettingDataType = "array"
)

// WebsiteSetting represents a website setting in the system
type WebsiteSetting struct {
	ID              uint                   `json:"id" gorm:"primaryKey"`
	WebsiteID       uint                   `json:"website_id" gorm:"not null" validate:"required,min=1"`
	TenantID        uint                   `json:"tenant_id" gorm:"not null" validate:"required,min=1"`
	SettingKey      string                 `json:"setting_key" gorm:"not null" validate:"required,min=1,max=255"`
	SettingName     string                 `json:"setting_name" gorm:"not null" validate:"required,min=1,max=255"`
	Category        WebsiteSettingCategory `json:"category" gorm:"type:enum('general','seo','social','security','theme','analytics','comments','media');not null" validate:"required"`
	SettingValue    string                 `json:"setting_value,omitempty"`
	DefaultValue    string                 `json:"default_value,omitempty"`
	DataType        WebsiteSettingDataType `json:"data_type" gorm:"type:enum('string','number','boolean','json','array');default:'string';not null" validate:"required"`
	IsPublic        bool                   `json:"is_public" gorm:"default:true"`
	IsRequired      bool                   `json:"is_required" gorm:"default:false"`
	IsEncrypted     bool                   `json:"is_encrypted" gorm:"default:false"`
	ValidationRules JSONMap                `json:"validation_rules,omitempty" gorm:"type:json"`
	Description     string                 `json:"description,omitempty"`
	Options         JSONMap                `json:"options,omitempty" gorm:"type:json"`
	CreatedAt       time.Time              `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt       time.Time              `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations
	Website *Website             `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
	Tenant  *tenantModels.Tenant `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
}

// TableName specifies the table name for WebsiteSetting
func (WebsiteSetting) TableName() string {
	return "website_settings"
}

// GetEffectiveValue returns the setting value or default value if setting value is empty
func (ws *WebsiteSetting) GetEffectiveValue() string {
	if ws.SettingValue != "" {
		return ws.SettingValue
	}
	return ws.DefaultValue
}

// IsStringType checks if the setting is a string type
func (ws *WebsiteSetting) IsStringType() bool {
	return ws.DataType == WebsiteSettingDataTypeString
}

// IsNumberType checks if the setting is a number type
func (ws *WebsiteSetting) IsNumberType() bool {
	return ws.DataType == WebsiteSettingDataTypeNumber
}

// IsBooleanType checks if the setting is a boolean type
func (ws *WebsiteSetting) IsBooleanType() bool {
	return ws.DataType == WebsiteSettingDataTypeBoolean
}

// IsJSONType checks if the setting is a JSON type
func (ws *WebsiteSetting) IsJSONType() bool {
	return ws.DataType == WebsiteSettingDataTypeJSON
}

// IsArrayType checks if the setting is an array type
func (ws *WebsiteSetting) IsArrayType() bool {
	return ws.DataType == WebsiteSettingDataTypeArray
}

// BeforeCreate hook for WebsiteSetting
func (ws *WebsiteSetting) BeforeCreate(tx *gorm.DB) error {
	if ws.ValidationRules == nil {
		ws.ValidationRules = make(JSONMap)
	}
	if ws.Options == nil {
		ws.Options = make(JSONMap)
	}
	return nil
}

// WebsiteSettingFilter represents filters for querying website settings
type WebsiteSettingFilter struct {
	WebsiteID   uint                   `json:"website_id,omitempty"`
	TenantID    uint                   `json:"tenant_id,omitempty"`
	Category    WebsiteSettingCategory `json:"category,omitempty"`
	DataType    WebsiteSettingDataType `json:"data_type,omitempty"`
	IsPublic    *bool                  `json:"is_public,omitempty"`
	IsRequired  *bool                  `json:"is_required,omitempty"`
	IsEncrypted *bool                  `json:"is_encrypted,omitempty"`
	SettingKey  string                 `json:"setting_key,omitempty"`
	Search      string                 `json:"search,omitempty"`
	Page        int                    `json:"page,omitempty"`
	PageSize    int                    `json:"page_size,omitempty"`
	SortBy      string                 `json:"sort_by,omitempty"`
	SortOrder   string                 `json:"sort_order,omitempty"`
}

// WebsiteSettingCreateRequest represents the request to create a website setting
type WebsiteSettingCreateRequest struct {
	SettingKey      string                 `json:"setting_key" validate:"required,min=1,max=255"`
	SettingName     string                 `json:"setting_name" validate:"required,min=1,max=255"`
	Category        WebsiteSettingCategory `json:"category" validate:"required,oneof=general seo social security theme analytics comments media"`
	SettingValue    string                 `json:"setting_value,omitempty"`
	DefaultValue    string                 `json:"default_value,omitempty"`
	DataType        WebsiteSettingDataType `json:"data_type" validate:"required,oneof=string number boolean json array"`
	IsPublic        bool                   `json:"is_public"`
	IsRequired      bool                   `json:"is_required"`
	IsEncrypted     bool                   `json:"is_encrypted"`
	ValidationRules JSONMap                `json:"validation_rules,omitempty"`
	Description     string                 `json:"description,omitempty"`
	Options         JSONMap                `json:"options,omitempty"`
}

// WebsiteSettingUpdateRequest represents the request to update a website setting
type WebsiteSettingUpdateRequest struct {
	SettingName     string                 `json:"setting_name,omitempty" validate:"omitempty,min=1,max=255"`
	Category        WebsiteSettingCategory `json:"category,omitempty" validate:"omitempty,oneof=general seo social security theme analytics comments media"`
	SettingValue    string                 `json:"setting_value,omitempty"`
	DefaultValue    string                 `json:"default_value,omitempty"`
	DataType        WebsiteSettingDataType `json:"data_type,omitempty" validate:"omitempty,oneof=string number boolean json array"`
	IsPublic        *bool                  `json:"is_public,omitempty"`
	IsRequired      *bool                  `json:"is_required,omitempty"`
	IsEncrypted     *bool                  `json:"is_encrypted,omitempty"`
	ValidationRules JSONMap                `json:"validation_rules,omitempty"`
	Description     string                 `json:"description,omitempty"`
	Options         JSONMap                `json:"options,omitempty"`
}

// WebsiteSettingResponse represents the response for website setting operations
type WebsiteSettingResponse struct {
	ID              uint                   `json:"id"`
	WebsiteID       uint                   `json:"website_id"`
	TenantID        uint                   `json:"tenant_id"`
	SettingKey      string                 `json:"setting_key"`
	SettingName     string                 `json:"setting_name"`
	Category        WebsiteSettingCategory `json:"category"`
	SettingValue    string                 `json:"setting_value,omitempty"`
	DefaultValue    string                 `json:"default_value,omitempty"`
	DataType        WebsiteSettingDataType `json:"data_type"`
	IsPublic        bool                   `json:"is_public"`
	IsRequired      bool                   `json:"is_required"`
	IsEncrypted     bool                   `json:"is_encrypted"`
	ValidationRules JSONMap                `json:"validation_rules,omitempty"`
	Description     string                 `json:"description,omitempty"`
	Options         JSONMap                `json:"options,omitempty"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
}

// WebsiteSettingListResponse represents the response for website setting listing
type WebsiteSettingListResponse struct {
	Settings   []WebsiteSettingResponse `json:"settings"`
	TotalCount int64                    `json:"total_count"`
	Page       int                      `json:"page"`
	PageSize   int                      `json:"page_size"`
	TotalPages int                      `json:"total_pages"`
}

// WebsiteSettingBulkUpdateRequest represents the request to bulk update website settings
type WebsiteSettingBulkUpdateRequest struct {
	Settings []struct {
		SettingKey   string `json:"setting_key" validate:"required"`
		SettingValue string `json:"setting_value"`
	} `json:"settings" validate:"required,min=1"`
}
