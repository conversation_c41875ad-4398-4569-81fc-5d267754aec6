package models

import (
	"fmt"
	"net/url"
	"regexp"
	"strings"
)

const (
	// MaxDomainLength is the maximum allowed domain length
	MaxDomainLength = 253
	// MaxLabelLength is the maximum allowed label length in a domain
	MaxLabelLength = 63
	// MinDomainLength is the minimum allowed domain length
	MinDomainLength = 3
	// DefaultBaseDomain is the default base domain for subdomains
	DefaultBaseDomain = "example.com"
)

var (
	// DomainRegex is the regex pattern for validating domain names
	DomainRegex = regexp.MustCompile(`^([a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?\.)+[a-zA-Z]{2,}$`)
	// SubdomainRegex is the regex pattern for validating subdomain names
	SubdomainRegex = regexp.MustCompile(`^[a-z0-9]([a-z0-9\-]{0,61}[a-z0-9])?$`)
	// ReservedSubdomains are subdomain names that cannot be used
	ReservedSubdomains = []string{
		"www", "api", "admin", "app", "blog", "mail", "email", "ftp", "sftp",
		"ssh", "ssl", "dev", "test", "staging", "prod", "production", "demo",
		"help", "support", "docs", "cdn", "assets", "static", "media", "files",
		"download", "upload", "secure", "auth", "login", "register", "signup",
		"dashboard", "console", "control", "manage", "portal", "gateway",
		"ns1", "ns2", "ns3", "ns4", "mx", "mx1", "mx2", "pop", "imap", "smtp",
		"webmail", "cpanel", "whm", "plesk", "directadmin", "cloudflare",
		"google", "facebook", "twitter", "github", "gitlab", "bitbucket",
	}
)

// DomainValidator handles domain validation operations
type DomainValidator struct {
	BaseDomain string
}

// NewDomainValidator creates a new domain validator
func NewDomainValidator(baseDomain string) *DomainValidator {
	if baseDomain == "" {
		baseDomain = DefaultBaseDomain
	}
	return &DomainValidator{
		BaseDomain: baseDomain,
	}
}

// ValidateDomain validates a full domain name
func (dv *DomainValidator) ValidateDomain(domain string) error {
	if domain == "" {
		return nil // Empty domain is allowed
	}

	// Check length
	if len(domain) < MinDomainLength || len(domain) > MaxDomainLength {
		return fmt.Errorf("domain length must be between %d and %d characters", MinDomainLength, MaxDomainLength)
	}

	// Convert to lowercase for validation
	domain = strings.ToLower(domain)

	// Remove protocol if present
	if strings.HasPrefix(domain, "http://") || strings.HasPrefix(domain, "https://") {
		parsedURL, err := url.Parse(domain)
		if err != nil {
			return fmt.Errorf("invalid domain format: %v", err)
		}
		domain = parsedURL.Host
	}

	// Check format using regex
	if !DomainRegex.MatchString(domain) {
		return fmt.Errorf("invalid domain format")
	}

	// Check each label length
	labels := strings.Split(domain, ".")
	for _, label := range labels {
		if len(label) > MaxLabelLength {
			return fmt.Errorf("domain label '%s' exceeds maximum length of %d characters", label, MaxLabelLength)
		}
		if len(label) == 0 {
			return fmt.Errorf("domain contains empty label")
		}
	}

	return nil
}

// ValidateSubdomain validates a subdomain name
func (dv *DomainValidator) ValidateSubdomain(subdomain string) error {
	if subdomain == "" {
		return nil // Empty subdomain is allowed
	}

	// Convert to lowercase for validation
	subdomain = strings.ToLower(subdomain)

	// Check length
	if len(subdomain) < 2 || len(subdomain) > MaxLabelLength {
		return fmt.Errorf("subdomain length must be between 2 and %d characters", MaxLabelLength)
	}

	// Check format using regex
	if !SubdomainRegex.MatchString(subdomain) {
		return fmt.Errorf("invalid subdomain format: must contain only lowercase letters, numbers, and hyphens")
	}

	// Check for reserved subdomains
	if dv.IsReservedSubdomain(subdomain) {
		return fmt.Errorf("subdomain '%s' is reserved and cannot be used", subdomain)
	}

	return nil
}

// IsReservedSubdomain checks if a subdomain is reserved
func (dv *DomainValidator) IsReservedSubdomain(subdomain string) bool {
	subdomain = strings.ToLower(subdomain)
	for _, reserved := range ReservedSubdomains {
		if subdomain == reserved {
			return true
		}
	}
	return false
}

// BuildFullDomain builds a full domain from subdomain and base domain
func (dv *DomainValidator) BuildFullDomain(subdomain string) string {
	if subdomain == "" {
		return ""
	}
	return fmt.Sprintf("%s.%s", strings.ToLower(subdomain), dv.BaseDomain)
}

// ExtractSubdomain extracts subdomain from a full domain
func (dv *DomainValidator) ExtractSubdomain(domain string) string {
	if domain == "" {
		return ""
	}

	domain = strings.ToLower(domain)
	baseDomainLower := strings.ToLower(dv.BaseDomain)

	if strings.HasSuffix(domain, "."+baseDomainLower) {
		subdomain := strings.TrimSuffix(domain, "."+baseDomainLower)
		if subdomain != "" && !strings.Contains(subdomain, ".") {
			return subdomain
		}
	}

	return ""
}

// IsDomainAvailable checks if a domain or subdomain is available (not reserved)
func (dv *DomainValidator) IsDomainAvailable(domain, subdomain string) error {
	if domain != "" {
		return dv.ValidateDomain(domain)
	}

	if subdomain != "" {
		return dv.ValidateSubdomain(subdomain)
	}

	return fmt.Errorf("either domain or subdomain must be provided")
}

// NormalizeDomain normalizes a domain name (converts to lowercase, removes protocol)
func (dv *DomainValidator) NormalizeDomain(domain string) string {
	if domain == "" {
		return ""
	}

	// Remove protocol if present
	if strings.HasPrefix(domain, "http://") || strings.HasPrefix(domain, "https://") {
		parsedURL, err := url.Parse(domain)
		if err == nil {
			domain = parsedURL.Host
		}
	}

	// Convert to lowercase and trim spaces
	return strings.ToLower(strings.TrimSpace(domain))
}

// NormalizeSubdomain normalizes a subdomain name (converts to lowercase, removes spaces)
func (dv *DomainValidator) NormalizeSubdomain(subdomain string) string {
	if subdomain == "" {
		return ""
	}

	// Convert to lowercase and trim spaces
	return strings.ToLower(strings.TrimSpace(subdomain))
}

// DomainInfo contains information about a domain
type DomainInfo struct {
	FullDomain    string `json:"full_domain"`
	Subdomain     string `json:"subdomain,omitempty"`
	CustomDomain  string `json:"custom_domain,omitempty"`
	IsCustom      bool   `json:"is_custom"`
	IsSubdomain   bool   `json:"is_subdomain"`
	IsValid       bool   `json:"is_valid"`
	ValidationErr string `json:"validation_error,omitempty"`
}

// AnalyzeDomain analyzes a domain and returns domain information
func (dv *DomainValidator) AnalyzeDomain(domain, subdomain string) *DomainInfo {
	info := &DomainInfo{}

	if domain != "" {
		info.CustomDomain = dv.NormalizeDomain(domain)
		info.IsCustom = true
		info.FullDomain = info.CustomDomain

		if err := dv.ValidateDomain(info.CustomDomain); err != nil {
			info.ValidationErr = err.Error()
		} else {
			info.IsValid = true
		}
	} else if subdomain != "" {
		info.Subdomain = dv.NormalizeSubdomain(subdomain)
		info.IsSubdomain = true
		info.FullDomain = dv.BuildFullDomain(info.Subdomain)

		if err := dv.ValidateSubdomain(info.Subdomain); err != nil {
			info.ValidationErr = err.Error()
		} else {
			info.IsValid = true
		}
	} else {
		info.ValidationErr = "either domain or subdomain must be provided"
	}

	return info
}
