package models

import (
	"time"

	tenantModels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"gorm.io/gorm"
)

// WebsiteThemeStatus represents the status of a website theme
// @Enum active,inactive,archived
type WebsiteThemeStatus string

const (
	WebsiteThemeStatusActive   WebsiteThemeStatus = "active"
	WebsiteThemeStatusInactive WebsiteThemeStatus = "inactive"
	WebsiteThemeStatusArchived WebsiteThemeStatus = "archived"
)

// WebsiteTheme represents a website theme in the system
type WebsiteTheme struct {
	ID              uint               `json:"id" gorm:"primaryKey"`
	WebsiteID       uint               `json:"website_id" gorm:"not null" validate:"required,min=1"`
	TenantID        uint               `json:"tenant_id" gorm:"not null" validate:"required,min=1"`
	Name            string             `json:"name" gorm:"not null" validate:"required,min=1,max=100"`
	Version         string             `json:"version,omitempty" validate:"omitempty,max=20"`
	Author          string             `json:"author,omitempty" validate:"omitempty,max=255"`
	Description     string             `json:"description,omitempty"`
	Config          JSONMap            `json:"config,omitempty" gorm:"type:json"`
	CustomColors    JSONMap            `json:"custom_colors,omitempty" gorm:"type:json"`
	CustomFonts     JSONMap            `json:"custom_fonts,omitempty" gorm:"type:json"`
	CustomCSS       string             `json:"custom_css,omitempty"`
	CustomJS        string             `json:"custom_js,omitempty"`
	StylesheetPath  string             `json:"stylesheet_path,omitempty" validate:"omitempty,max=500"`
	JavascriptPath  string             `json:"javascript_path,omitempty" validate:"omitempty,max=500"`
	PreviewImage    string             `json:"preview_image,omitempty" validate:"omitempty,url,max=500"`
	AssetsPath      string             `json:"assets_path,omitempty" validate:"omitempty,max=500"`
	Templates       JSONMap            `json:"templates,omitempty" gorm:"type:json"`
	SupportedBlocks JSONMap            `json:"supported_blocks,omitempty" gorm:"type:json"`
	Features        JSONMap            `json:"features,omitempty" gorm:"type:json"`
	IsDefault       bool               `json:"is_default" gorm:"default:false"`
	IsActive        bool               `json:"is_active" gorm:"default:false"`
	ParentThemeID   *uint              `json:"parent_theme_id,omitempty"`
	Status          WebsiteThemeStatus `json:"status" gorm:"type:enum('active','inactive','archived');default:'inactive';not null"`
	CreatedAt       time.Time          `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt       time.Time          `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations
	Website     *Website             `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
	Tenant      *tenantModels.Tenant `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	ParentTheme *WebsiteTheme        `json:"parent_theme,omitempty" gorm:"foreignKey:ParentThemeID"`
	ChildThemes *[]WebsiteTheme      `json:"child_themes,omitempty" gorm:"foreignKey:ParentThemeID"`
}

// TableName specifies the table name for WebsiteTheme
func (WebsiteTheme) TableName() string {
	return "website_themes"
}

// IsActiveStatus checks if theme is active
func (wt *WebsiteTheme) IsActiveStatus() bool {
	return wt.Status == WebsiteThemeStatusActive
}

// IsInactiveStatus checks if theme is inactive
func (wt *WebsiteTheme) IsInactiveStatus() bool {
	return wt.Status == WebsiteThemeStatusInactive
}

// IsArchivedStatus checks if theme is archived
func (wt *WebsiteTheme) IsArchivedStatus() bool {
	return wt.Status == WebsiteThemeStatusArchived
}

// IsChildTheme checks if this theme is a child theme
func (wt *WebsiteTheme) IsChildTheme() bool {
	return wt.ParentThemeID != nil && *wt.ParentThemeID > 0
}

// HasCustomizations checks if theme has any customizations
func (wt *WebsiteTheme) HasCustomizations() bool {
	return len(wt.CustomColors) > 0 || len(wt.CustomFonts) > 0 || wt.CustomCSS != "" || wt.CustomJS != ""
}

// BeforeCreate hook for WebsiteTheme
func (wt *WebsiteTheme) BeforeCreate(tx *gorm.DB) error {
	if wt.Config == nil {
		wt.Config = make(JSONMap)
	}
	if wt.CustomColors == nil {
		wt.CustomColors = make(JSONMap)
	}
	if wt.CustomFonts == nil {
		wt.CustomFonts = make(JSONMap)
	}
	if wt.Templates == nil {
		wt.Templates = make(JSONMap)
	}
	if wt.SupportedBlocks == nil {
		wt.SupportedBlocks = make(JSONMap)
	}
	if wt.Features == nil {
		wt.Features = make(JSONMap)
	}
	return nil
}

// WebsiteThemeFilter represents filters for querying website themes
type WebsiteThemeFilter struct {
	WebsiteID     uint               `json:"website_id,omitempty"`
	TenantID      uint               `json:"tenant_id,omitempty"`
	Status        WebsiteThemeStatus `json:"status,omitempty"`
	IsDefault     *bool              `json:"is_default,omitempty"`
	IsActive      *bool              `json:"is_active,omitempty"`
	ParentThemeID *uint              `json:"parent_theme_id,omitempty"`
	Author        string             `json:"author,omitempty"`
	Search        string             `json:"search,omitempty"`
	Page          int                `json:"page,omitempty"`
	PageSize      int                `json:"page_size,omitempty"`
	SortBy        string             `json:"sort_by,omitempty"`
	SortOrder     string             `json:"sort_order,omitempty"`
}

// WebsiteThemeCreateRequest represents the request to create a website theme
type WebsiteThemeCreateRequest struct {
	Name            string  `json:"name" validate:"required,min=1,max=100"`
	Version         string  `json:"version,omitempty" validate:"omitempty,max=20"`
	Author          string  `json:"author,omitempty" validate:"omitempty,max=255"`
	Description     string  `json:"description,omitempty"`
	Config          JSONMap `json:"config,omitempty"`
	CustomColors    JSONMap `json:"custom_colors,omitempty"`
	CustomFonts     JSONMap `json:"custom_fonts,omitempty"`
	CustomCSS       string  `json:"custom_css,omitempty"`
	CustomJS        string  `json:"custom_js,omitempty"`
	StylesheetPath  string  `json:"stylesheet_path,omitempty" validate:"omitempty,max=500"`
	JavascriptPath  string  `json:"javascript_path,omitempty" validate:"omitempty,max=500"`
	PreviewImage    string  `json:"preview_image,omitempty" validate:"omitempty,url,max=500"`
	AssetsPath      string  `json:"assets_path,omitempty" validate:"omitempty,max=500"`
	Templates       JSONMap `json:"templates,omitempty"`
	SupportedBlocks JSONMap `json:"supported_blocks,omitempty"`
	Features        JSONMap `json:"features,omitempty"`
	IsDefault       bool    `json:"is_default"`
	ParentThemeID   *uint   `json:"parent_theme_id,omitempty"`
}

// WebsiteThemeUpdateRequest represents the request to update a website theme
type WebsiteThemeUpdateRequest struct {
	Name            string             `json:"name,omitempty" validate:"omitempty,min=1,max=100"`
	Version         string             `json:"version,omitempty" validate:"omitempty,max=20"`
	Author          string             `json:"author,omitempty" validate:"omitempty,max=255"`
	Description     string             `json:"description,omitempty"`
	Config          JSONMap            `json:"config,omitempty"`
	CustomColors    JSONMap            `json:"custom_colors,omitempty"`
	CustomFonts     JSONMap            `json:"custom_fonts,omitempty"`
	CustomCSS       string             `json:"custom_css,omitempty"`
	CustomJS        string             `json:"custom_js,omitempty"`
	StylesheetPath  string             `json:"stylesheet_path,omitempty" validate:"omitempty,max=500"`
	JavascriptPath  string             `json:"javascript_path,omitempty" validate:"omitempty,max=500"`
	PreviewImage    string             `json:"preview_image,omitempty" validate:"omitempty,url,max=500"`
	AssetsPath      string             `json:"assets_path,omitempty" validate:"omitempty,max=500"`
	Templates       JSONMap            `json:"templates,omitempty"`
	SupportedBlocks JSONMap            `json:"supported_blocks,omitempty"`
	Features        JSONMap            `json:"features,omitempty"`
	IsDefault       *bool              `json:"is_default,omitempty"`
	IsActive        *bool              `json:"is_active,omitempty"`
	ParentThemeID   *uint              `json:"parent_theme_id,omitempty"`
	Status          WebsiteThemeStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive archived"`
}

// WebsiteThemeResponse represents the response for website theme operations
type WebsiteThemeResponse struct {
	ID              uint               `json:"id"`
	WebsiteID       uint               `json:"website_id"`
	TenantID        uint               `json:"tenant_id"`
	Name            string             `json:"name"`
	Version         string             `json:"version,omitempty"`
	Author          string             `json:"author,omitempty"`
	Description     string             `json:"description,omitempty"`
	Config          JSONMap            `json:"config,omitempty"`
	CustomColors    JSONMap            `json:"custom_colors,omitempty"`
	CustomFonts     JSONMap            `json:"custom_fonts,omitempty"`
	CustomCSS       string             `json:"custom_css,omitempty"`
	CustomJS        string             `json:"custom_js,omitempty"`
	StylesheetPath  string             `json:"stylesheet_path,omitempty"`
	JavascriptPath  string             `json:"javascript_path,omitempty"`
	PreviewImage    string             `json:"preview_image,omitempty"`
	AssetsPath      string             `json:"assets_path,omitempty"`
	Templates       JSONMap            `json:"templates,omitempty"`
	SupportedBlocks JSONMap            `json:"supported_blocks,omitempty"`
	Features        JSONMap            `json:"features,omitempty"`
	IsDefault       bool               `json:"is_default"`
	IsActive        bool               `json:"is_active"`
	ParentThemeID   *uint              `json:"parent_theme_id,omitempty"`
	Status          WebsiteThemeStatus `json:"status"`
	CreatedAt       time.Time          `json:"created_at"`
	UpdatedAt       time.Time          `json:"updated_at"`
}

// WebsiteThemeListResponse represents the response for website theme listing
type WebsiteThemeListResponse struct {
	Themes     []WebsiteThemeResponse `json:"themes"`
	TotalCount int64                  `json:"total_count"`
	Page       int                    `json:"page"`
	PageSize   int                    `json:"page_size"`
	TotalPages int                    `json:"total_pages"`
}

// WebsiteThemeActivateRequest represents the request to activate a theme
type WebsiteThemeActivateRequest struct {
	ThemeID uint `json:"theme_id" validate:"required,min=1"`
}

// WebsiteThemeCloneRequest represents the request to clone a theme
type WebsiteThemeCloneRequest struct {
	SourceThemeID uint   `json:"source_theme_id" validate:"required,min=1"`
	NewName       string `json:"new_name" validate:"required,min=1,max=100"`
	CloneAsChild  bool   `json:"clone_as_child"`
}
