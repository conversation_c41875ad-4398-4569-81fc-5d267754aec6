package models

import (
	"context"
	"errors"
)

// ContextKey is a custom type for context keys
type Context<PERSON><PERSON> string

const (
	// WebsiteContextKey is the key for storing website in context
	WebsiteContextKey ContextKey = "website"
	// WebsiteIDContextKey is the key for storing website ID in context
	WebsiteIDContextKey ContextKey = "website_id"
	// DomainContextKey is the key for storing domain in context
	DomainContextKey ContextKey = "domain"
)

// WebsiteContext contains website-related information for request context
type WebsiteContext struct {
	Website   *Website `json:"website"`
	WebsiteID uint     `json:"website_id"`
	Domain    string   `json:"domain"`
	TenantID  uint     `json:"tenant_id"`
}

// SetWebsiteContext sets website context in the given context
func SetWebsiteContext(ctx context.Context, websiteCtx *WebsiteContext) context.Context {
	ctx = context.WithValue(ctx, WebsiteContextKey, websiteCtx)
	ctx = context.WithValue(ctx, WebsiteIDContextKey, websiteCtx.WebsiteID)
	ctx = context.WithValue(ctx, DomainContextKey, websiteCtx.Domain)
	return ctx
}

// GetWebsiteContext retrieves website context from the given context
func GetWebsiteContext(ctx context.Context) (*WebsiteContext, error) {
	websiteCtx, ok := ctx.Value(WebsiteContextKey).(*WebsiteContext)
	if !ok || websiteCtx == nil {
		return nil, errors.New("website context not found")
	}
	return websiteCtx, nil
}

// GetWebsiteID retrieves website ID from the given context
func GetWebsiteID(ctx context.Context) (uint, error) {
	websiteID, ok := ctx.Value(WebsiteIDContextKey).(uint)
	if !ok || websiteID == 0 {
		return 0, errors.New("website ID not found in context")
	}
	return websiteID, nil
}

// GetDomain retrieves domain from the given context
func GetDomain(ctx context.Context) (string, error) {
	domain, ok := ctx.Value(DomainContextKey).(string)
	if !ok || domain == "" {
		return "", errors.New("domain not found in context")
	}
	return domain, nil
}

// MustGetWebsiteContext retrieves website context from the given context, panics if not found
func MustGetWebsiteContext(ctx context.Context) *WebsiteContext {
	websiteCtx, err := GetWebsiteContext(ctx)
	if err != nil {
		panic(err)
	}
	return websiteCtx
}

// MustGetWebsiteID retrieves website ID from the given context, panics if not found
func MustGetWebsiteID(ctx context.Context) uint {
	websiteID, err := GetWebsiteID(ctx)
	if err != nil {
		panic(err)
	}
	return websiteID
}

// MustGetDomain retrieves domain from the given context, panics if not found
func MustGetDomain(ctx context.Context) string {
	domain, err := GetDomain(ctx)
	if err != nil {
		panic(err)
	}
	return domain
}
