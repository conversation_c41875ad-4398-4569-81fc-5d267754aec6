package services

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// RateLimitingService handles rate limiting for authentication attempts
type RateLimitingService interface {
	// Check if account is locked due to too many failed attempts
	IsAccountLocked(ctx context.Context, identifier string) (locked bool, unlocksAt time.Time, err error)

	// Record a failed login attempt
	RecordFailedAttempt(ctx context.Context, identifier string, ipAddress string) error

	// Record a successful login
	RecordSuccessfulLogin(ctx context.Context, userID uint, ipAddress string) error

	// Reset failed attempts for an account
	ResetFailedAttempts(ctx context.Context, identifier string) error

	// Get failed attempt count
	GetFailedAttemptCount(ctx context.Context, identifier string) (int, error)
}

// rateLimitingService implements RateLimitingService
type rateLimitingService struct {
	maxAttempts      int
	lockoutDuration  time.Duration
	attemptWindow    time.Duration
	loginAttemptRepo repositories.LoginAttemptRepository
	logger           utils.Logger
}

// NewRateLimitingService creates a new rate limiting service
func NewRateLimitingService(
	maxAttempts int,
	lockoutDuration time.Duration,
	attemptWindow time.Duration,
	loginAttemptRepo repositories.LoginAttemptRepository,
	logger utils.Logger,
) RateLimitingService {
	return &rateLimitingService{
		maxAttempts:      maxAttempts,
		lockoutDuration:  lockoutDuration,
		attemptWindow:    attemptWindow,
		loginAttemptRepo: loginAttemptRepo,
		logger:           logger,
	}
}

// IsAccountLocked checks if an account is locked
func (s *rateLimitingService) IsAccountLocked(ctx context.Context, identifier string) (bool, time.Time, error) {
	// Get recent failed attempts
	attempts, err := s.loginAttemptRepo.GetRecentAttempts(ctx, identifier, s.attemptWindow)
	if err != nil {
		return false, time.Time{}, err
	}

	// Check if account is locked
	failedCount := 0
	var lastAttemptTime time.Time

	for _, attempt := range attempts {
		if attempt.IsFailure() {
			failedCount++
			if attempt.CreatedAt.After(lastAttemptTime) {
				lastAttemptTime = attempt.CreatedAt
			}
		}
	}

	if failedCount >= s.maxAttempts {
		unlocksAt := lastAttemptTime.Add(s.lockoutDuration)
		if time.Now().Before(unlocksAt) {
			return true, unlocksAt, nil
		}
	}

	return false, time.Time{}, nil
}

// RecordFailedAttempt records a failed login attempt
func (s *rateLimitingService) RecordFailedAttempt(ctx context.Context, identifier string, ipAddress string) error {
	ipAddr := ipAddress // Convert to pointer
	attempt := &models.LoginAttempt{
		Email:     identifier,
		IPAddress: &ipAddr,
		Result:    models.AttemptResultFailure,
	}

	return s.loginAttemptRepo.Create(ctx, attempt)
}

// RecordSuccessfulLogin records a successful login
func (s *rateLimitingService) RecordSuccessfulLogin(ctx context.Context, userID uint, ipAddress string) error {
	// We'll record successful attempts by user ID
	// First get user email for consistent tracking
	// For now, we'll just log success
	s.logger.WithFields(map[string]interface{}{
		"user_id": userID,
		"ip":      ipAddress,
	}).Info("Successful login recorded")

	return nil
}

// ResetFailedAttempts resets failed attempts for an account
func (s *rateLimitingService) ResetFailedAttempts(ctx context.Context, identifier string) error {
	// In practice, we might want to mark old attempts as resolved
	// For now, we'll just log
	s.logger.WithField("identifier", identifier).Info("Reset failed attempts")
	return nil
}

// GetFailedAttemptCount gets the number of failed attempts
func (s *rateLimitingService) GetFailedAttemptCount(ctx context.Context, identifier string) (int, error) {
	attempts, err := s.loginAttemptRepo.GetRecentAttempts(ctx, identifier, s.attemptWindow)
	if err != nil {
		return 0, err
	}

	count := 0
	for _, attempt := range attempts {
		if attempt.IsFailure() {
			count++
		}
	}

	return count, nil
}
