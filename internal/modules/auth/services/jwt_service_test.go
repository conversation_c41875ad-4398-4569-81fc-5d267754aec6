package services

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/tranthanhloi/wn-api-v3/internal/config"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
)

func TestJWTService(t *testing.T) {
	// Create config
	cfg := &config.Config{
		Auth: config.AuthConfig{
			JWTSecret:       "test-secret-key",
			JWTIssuer:       "test-issuer",
			AccessTokenTTL:  1 * time.Hour,
			RefreshTokenTTL: 24 * time.Hour,
		},
	}

	// Create JWT service (without blacklist for now)
	jwtService, err := NewJWTService(cfg, nil, nil)
	require.NoError(t, err)

	t.Run("GenerateAccessToken", func(t *testing.T) {
		claims := &models.JWTClaims{
			UserID: 1,
			Email:  "<EMAIL>",
			Scopes: models.TokenScopes{"read", "write"},
		}

		token, err := jwtService.GenerateAccessToken(claims)
		assert.NoError(t, err)
		assert.NotEmpty(t, token)
	})

	t.Run("GenerateTokenPair", func(t *testing.T) {
		claims := &models.JWTClaims{
			UserID: 1,
			Email:  "<EMAIL>",
		}

		accessToken, refreshToken, err := jwtService.GenerateTokenPair(claims)
		assert.NoError(t, err)
		assert.NotEmpty(t, accessToken)
		assert.NotEmpty(t, refreshToken)
		assert.NotEqual(t, accessToken, refreshToken)
	})

	t.Run("ValidateAccessToken", func(t *testing.T) {
		claims := &models.JWTClaims{
			UserID: 1,
			Email:  "<EMAIL>",
		}

		token, err := jwtService.GenerateAccessToken(claims)
		require.NoError(t, err)

		validatedClaims, err := jwtService.ValidateAccessToken(token)
		assert.NoError(t, err)
		assert.Equal(t, claims.UserID, validatedClaims.UserID)
		assert.Equal(t, claims.Email, validatedClaims.Email)
	})

	t.Run("RefreshAccessToken", func(t *testing.T) {
		claims := &models.JWTClaims{
			UserID: 1,
			Email:  "<EMAIL>",
		}

		_, refreshToken, err := jwtService.GenerateTokenPair(claims)
		require.NoError(t, err)

		newAccessToken, err := jwtService.RefreshAccessToken(refreshToken)
		assert.NoError(t, err)
		assert.NotEmpty(t, newAccessToken)

		// Validate new access token
		validatedClaims, err := jwtService.ValidateAccessToken(newAccessToken)
		assert.NoError(t, err)
		assert.Equal(t, claims.UserID, validatedClaims.UserID)
		assert.Equal(t, claims.Email, validatedClaims.Email)
	})

	t.Run("ValidateInvalidToken", func(t *testing.T) {
		_, err := jwtService.ValidateAccessToken("invalid-token")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid token")
	})

	t.Run("ValidateRefreshTokenAsAccess", func(t *testing.T) {
		claims := &models.JWTClaims{
			UserID: 1,
			Email:  "<EMAIL>",
		}

		refreshToken, err := jwtService.GenerateRefreshToken(claims)
		require.NoError(t, err)

		_, err = jwtService.ValidateAccessToken(refreshToken)
		assert.Error(t, err)
		assert.Equal(t, ErrInvalidTokenType, err)
	})
}
