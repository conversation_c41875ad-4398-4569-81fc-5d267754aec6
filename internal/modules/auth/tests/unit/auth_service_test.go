package unit

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/tests/mocks"
	userModels "github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
)

// MockLogger for testing
type MockLogger struct {
	mock.Mock
}

func (m *MockLogger) Info(msg string, fields ...map[string]interface{}) {
	m.Called(msg, fields)
}

func (m *MockLogger) Error(msg string, fields ...map[string]interface{}) {
	m.Called(msg, fields)
}

func (m *MockLogger) Warn(msg string, fields ...map[string]interface{}) {
	m.Called(msg, fields)
}

func (m *MockLogger) Debug(msg string, fields ...map[string]interface{}) {
	m.Called(msg, fields)
}

func (m *MockLogger) WithError(err error) utils.Logger {
	return m
}

func (m *MockLogger) WithFields(fields map[string]interface{}) utils.Logger {
	return m
}

func (m *MockLogger) WithField(key string, value interface{}) utils.Logger {
	return m
}

func (m *MockLogger) GetLevel() string {
	return "debug"
}

func (m *MockLogger) SetLevel(level string) error {
	return nil
}

func (m *MockLogger) Close() error {
	return nil
}

// Test suite for AuthService
type AuthServiceTestSuite struct {
	authService                  services.AuthService
	mockUserRepo                 *mocks.MockUserRepository
	mockSessionRepo              *mocks.MockSessionRepository
	mockLoginAttemptRepo         *mocks.MockLoginAttemptRepository
	mockJWTService               *mocks.MockJWTService
	mockPasswordService          *mocks.MockPasswordService
	mockRateLimiter              *mocks.MockRateLimitingService
	mockTenantService            *mocks.MockTenantService
	mockMembershipRepo           *mocks.MockTenantMembershipRepository
	mockOnboardingService        *mocks.MockOnboardingIntegrationService
	mockNotificationService      *mocks.MockNotificationService
	mockEmailVerificationService *mocks.MockEmailVerificationService
	mockLogger                   *MockLogger
}

func setupAuthServiceTest() *AuthServiceTestSuite {
	suite := &AuthServiceTestSuite{
		mockUserRepo:                 &mocks.MockUserRepository{},
		mockSessionRepo:              &mocks.MockSessionRepository{},
		mockLoginAttemptRepo:         &mocks.MockLoginAttemptRepository{},
		mockJWTService:               &mocks.MockJWTService{},
		mockPasswordService:          &mocks.MockPasswordService{},
		mockRateLimiter:              &mocks.MockRateLimitingService{},
		mockTenantService:            &mocks.MockTenantService{},
		mockMembershipRepo:           &mocks.MockTenantMembershipRepository{},
		mockOnboardingService:        &mocks.MockOnboardingIntegrationService{},
		mockNotificationService:      &mocks.MockNotificationService{},
		mockEmailVerificationService: &mocks.MockEmailVerificationService{},
		mockLogger:                   &MockLogger{},
	}

	config := &services.AuthConfig{
		RequireEmailVerification: true,
		AllowRegistration:        true,
		MaxLoginAttempts:         5,
		LockoutDuration:          15 * time.Minute,
		SessionTimeout:           24 * time.Hour,
		RefreshTokenTTL:          7 * 24 * time.Hour,
		TwoFactorIssuer:          "Blog API",
	}

	suite.authService = services.NewAuthService(
		suite.mockUserRepo,
		suite.mockSessionRepo,
		suite.mockLoginAttemptRepo,
		suite.mockJWTService,
		suite.mockPasswordService,
		suite.mockRateLimiter,
		suite.mockLogger,
		config,
		suite.mockTenantService,
		nil, // invitationRepo
		suite.mockMembershipRepo,
		suite.mockNotificationService,
		suite.mockEmailVerificationService,
	)

	return suite
}

func TestAuthService_Login_Success(t *testing.T) {
	suite := setupAuthServiceTest()
	ctx := context.Background()

	// Test data
	email := "<EMAIL>"
	password := "password123"
	user := &userModels.User{
		ID:     1,
		Email:  email,
		Status: userModels.UserStatusActive,
	}

	loginReq := &services.LoginRequest{
		Email:     email,
		Password:  password,
		IPAddress: "127.0.0.1",
		UserAgent: "test-agent",
	}

	// Mock expectations
	suite.mockUserRepo.On("GetByEmail", ctx, email).Return(user, nil)
	suite.mockPasswordService.On("VerifyPassword", mock.AnythingOfType("string"), password).Return(true, nil)
	suite.mockRateLimiter.On("CheckLoginAttempt", ctx, user.ID, "127.0.0.1").Return(false, nil)
	suite.mockSessionRepo.On("Create", ctx, mock.AnythingOfType("*models.Session")).Return(nil)
	suite.mockJWTService.On("GenerateTokenPair", mock.AnythingOfType("*models.JWTClaims")).Return("access_token", "refresh_token", nil)
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Execute
	response, err := suite.authService.Login(ctx, loginReq)

	// Assertions
	require.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, user, response.User)
	assert.Equal(t, "access_token", response.AccessToken)
	assert.Equal(t, "refresh_token", response.RefreshToken)
	assert.Equal(t, "Bearer", response.TokenType)
	assert.False(t, response.RequiresTwoFactor)

	// Verify all expectations
	suite.mockUserRepo.AssertExpectations(t)
	suite.mockPasswordService.AssertExpectations(t)
	suite.mockRateLimiter.AssertExpectations(t)
	suite.mockSessionRepo.AssertExpectations(t)
	suite.mockJWTService.AssertExpectations(t)
}

func TestAuthService_Login_InvalidCredentials(t *testing.T) {
	suite := setupAuthServiceTest()
	ctx := context.Background()

	email := "<EMAIL>"
	password := "wrongpassword"
	user := &userModels.User{
		ID:     1,
		Email:  email,
		Status: userModels.UserStatusActive,
	}

	loginReq := &services.LoginRequest{
		Email:     email,
		Password:  password,
		IPAddress: "127.0.0.1",
		UserAgent: "test-agent",
	}

	// Mock expectations
	suite.mockUserRepo.On("GetByEmail", ctx, email).Return(user, nil)
	suite.mockPasswordService.On("VerifyPassword", mock.AnythingOfType("string"), password).Return(false, nil)
	suite.mockRateLimiter.On("RecordFailedLoginAttempt", ctx, user.ID, "127.0.0.1").Return(nil)
	suite.mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithError", mock.AnythingOfType("error")).Return(suite.mockLogger)
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Execute
	response, err := suite.authService.Login(ctx, loginReq)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, services.ErrInvalidCredentials, err)
	assert.Nil(t, response)

	// Verify expectations
	suite.mockUserRepo.AssertExpectations(t)
	suite.mockPasswordService.AssertExpectations(t)
	suite.mockRateLimiter.AssertExpectations(t)
}

func TestAuthService_Login_UserNotFound(t *testing.T) {
	suite := setupAuthServiceTest()
	ctx := context.Background()

	email := "<EMAIL>"
	password := "password123"

	loginReq := &services.LoginRequest{
		Email:     email,
		Password:  password,
		IPAddress: "127.0.0.1",
		UserAgent: "test-agent",
	}

	// Mock expectations
	suite.mockUserRepo.On("GetByEmail", ctx, email).Return(nil, services.ErrUserNotFound)
	suite.mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithError", mock.AnythingOfType("error")).Return(suite.mockLogger)
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Execute
	response, err := suite.authService.Login(ctx, loginReq)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, services.ErrInvalidCredentials, err)
	assert.Nil(t, response)

	// Verify expectations
	suite.mockUserRepo.AssertExpectations(t)
}

func TestAuthService_Login_EmailNotVerified(t *testing.T) {
	suite := setupAuthServiceTest()
	ctx := context.Background()

	email := "<EMAIL>"
	password := "password123"
	user := &userModels.User{
		ID:     1,
		Email:  email,
		Status: userModels.UserStatusPendingVerification,
	}

	loginReq := &services.LoginRequest{
		Email:     email,
		Password:  password,
		IPAddress: "127.0.0.1",
		UserAgent: "test-agent",
	}

	// Mock expectations
	suite.mockUserRepo.On("GetByEmail", ctx, email).Return(user, nil)
	suite.mockPasswordService.On("VerifyPassword", mock.AnythingOfType("string"), password).Return(true, nil)
	suite.mockRateLimiter.On("CheckLoginAttempt", ctx, user.ID, "127.0.0.1").Return(false, nil)
	suite.mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithError", mock.AnythingOfType("error")).Return(suite.mockLogger)
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Execute
	response, err := suite.authService.Login(ctx, loginReq)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, services.ErrEmailNotVerified, err)
	assert.Nil(t, response)

	// Verify expectations
	suite.mockUserRepo.AssertExpectations(t)
	suite.mockPasswordService.AssertExpectations(t)
	suite.mockRateLimiter.AssertExpectations(t)
}

func TestAuthService_Register_Success(t *testing.T) {
	suite := setupAuthServiceTest()
	ctx := context.Background()

	registerReq := &services.RegisterRequest{
		Email:     "<EMAIL>",
		Password:  "password123",
		FirstName: "John",
		LastName:  "Doe",
		IPAddress: "127.0.0.1",
		UserAgent: "test-agent",
	}

	hashedPassword := "$2a$12$hashedpassword"
	newUser := &userModels.User{
		ID:        1,
		Email:     registerReq.Email,
		FirstName: registerReq.FirstName,
		LastName:  registerReq.LastName,
		Status:    userModels.UserStatusPendingVerification,
	}

	// Mock expectations
	suite.mockUserRepo.On("GetByEmail", ctx, registerReq.Email).Return(nil, services.ErrUserNotFound)
	suite.mockPasswordService.On("HashPassword", registerReq.Password).Return(hashedPassword, nil)
	suite.mockUserRepo.On("Create", ctx, mock.AnythingOfType("*userModels.User")).Return(nil).Run(func(args mock.Arguments) {
		user := args.Get(1).(*userModels.User)
		user.ID = 1
	})
	suite.mockEmailVerificationService.On("SendVerificationEmail", ctx, newUser.ID, registerReq.Email).Return(nil)
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Execute
	response, err := suite.authService.Register(ctx, registerReq)

	// Assertions
	require.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, registerReq.Email, response.User.Email)
	assert.Equal(t, registerReq.FirstName, response.User.FirstName)
	assert.Equal(t, registerReq.LastName, response.User.LastName)
	assert.True(t, response.RequiresEmailVerification)
	assert.Equal(t, "Registration successful. Please check your email for verification.", response.Message)

	// Verify expectations
	suite.mockUserRepo.AssertExpectations(t)
	suite.mockPasswordService.AssertExpectations(t)
	suite.mockEmailVerificationService.AssertExpectations(t)
}

func TestAuthService_Register_EmailAlreadyExists(t *testing.T) {
	suite := setupAuthServiceTest()
	ctx := context.Background()

	registerReq := &services.RegisterRequest{
		Email:     "<EMAIL>",
		Password:  "password123",
		FirstName: "John",
		LastName:  "Doe",
		IPAddress: "127.0.0.1",
		UserAgent: "test-agent",
	}

	existingUser := &userModels.User{
		ID:    1,
		Email: registerReq.Email,
	}

	// Mock expectations
	suite.mockUserRepo.On("GetByEmail", ctx, registerReq.Email).Return(existingUser, nil)
	suite.mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithError", mock.AnythingOfType("error")).Return(suite.mockLogger)
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Execute
	response, err := suite.authService.Register(ctx, registerReq)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, services.ErrEmailAlreadyExists, err)
	assert.Nil(t, response)

	// Verify expectations
	suite.mockUserRepo.AssertExpectations(t)
}

func TestAuthService_Register_WeakPassword(t *testing.T) {
	suite := setupAuthServiceTest()
	ctx := context.Background()

	registerReq := &services.RegisterRequest{
		Email:     "<EMAIL>",
		Password:  "weak",
		FirstName: "John",
		LastName:  "Doe",
		IPAddress: "127.0.0.1",
		UserAgent: "test-agent",
	}

	// Mock expectations
	suite.mockUserRepo.On("GetByEmail", ctx, registerReq.Email).Return(nil, services.ErrUserNotFound)
	suite.mockPasswordService.On("HashPassword", registerReq.Password).Return("", services.ErrWeakPassword)
	suite.mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithError", mock.AnythingOfType("error")).Return(suite.mockLogger)
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Execute
	response, err := suite.authService.Register(ctx, registerReq)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, services.ErrWeakPassword, err)
	assert.Nil(t, response)

	// Verify expectations
	suite.mockUserRepo.AssertExpectations(t)
	suite.mockPasswordService.AssertExpectations(t)
}

func TestAuthService_Logout_Success(t *testing.T) {
	suite := setupAuthServiceTest()
	ctx := context.Background()

	userID := uint(1)
	sessionID := uint(100)

	// Mock expectations
	suite.mockSessionRepo.On("InvalidateSession", ctx, sessionID).Return(nil)
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Execute
	err := suite.authService.Logout(ctx, userID, sessionID)

	// Assertions
	assert.NoError(t, err)

	// Verify expectations
	suite.mockSessionRepo.AssertExpectations(t)
}

func TestAuthService_Logout_SessionNotFound(t *testing.T) {
	suite := setupAuthServiceTest()
	ctx := context.Background()

	userID := uint(1)
	sessionID := uint(999)

	// Mock expectations
	suite.mockSessionRepo.On("InvalidateSession", ctx, sessionID).Return(errors.New("session not found"))
	suite.mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithError", mock.AnythingOfType("error")).Return(suite.mockLogger)
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Execute
	err := suite.authService.Logout(ctx, userID, sessionID)

	// Assertions
	assert.Error(t, err)

	// Verify expectations
	suite.mockSessionRepo.AssertExpectations(t)
}

func TestAuthService_RefreshToken_Success(t *testing.T) {
	suite := setupAuthServiceTest()
	ctx := context.Background()

	refreshToken := "valid_refresh_token"
	claims := &models.JWTClaims{
		UserID: 1,
		Email:  "<EMAIL>",
	}

	user := &userModels.User{
		ID:     1,
		Email:  "<EMAIL>",
		Status: userModels.UserStatusActive,
	}

	// Mock expectations
	suite.mockJWTService.On("ValidateRefreshToken", refreshToken).Return(claims, nil)
	suite.mockUserRepo.On("GetByID", ctx, claims.UserID).Return(user, nil)
	suite.mockJWTService.On("GenerateTokenPair", mock.AnythingOfType("*models.JWTClaims")).Return("new_access_token", "new_refresh_token", nil)
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Execute
	response, err := suite.authService.RefreshToken(ctx, refreshToken)

	// Assertions
	require.NoError(t, err)
	assert.NotNil(t, response)
	assert.Equal(t, "new_access_token", response.AccessToken)
	assert.Equal(t, "new_refresh_token", response.RefreshToken)
	assert.Equal(t, user, response.User)

	// Verify expectations
	suite.mockJWTService.AssertExpectations(t)
	suite.mockUserRepo.AssertExpectations(t)
}

func TestAuthService_RefreshToken_InvalidToken(t *testing.T) {
	suite := setupAuthServiceTest()
	ctx := context.Background()

	refreshToken := "invalid_refresh_token"

	// Mock expectations
	suite.mockJWTService.On("ValidateRefreshToken", refreshToken).Return(nil, services.ErrInvalidRefreshToken)
	suite.mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithError", mock.AnythingOfType("error")).Return(suite.mockLogger)
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Execute
	response, err := suite.authService.RefreshToken(ctx, refreshToken)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, services.ErrInvalidRefreshToken, err)
	assert.Nil(t, response)

	// Verify expectations
	suite.mockJWTService.AssertExpectations(t)
}

func TestAuthService_GetActiveSessions_Success(t *testing.T) {
	suite := setupAuthServiceTest()
	ctx := context.Background()

	userID := uint(1)
	sessions := []*models.Session{
		{
			ID:         1,
			UserID:     userID,
			DeviceType: models.DeviceTypeWeb,
			IPAddress:  strPtr("127.0.0.1"),
			IsActive:   true,
			ExpiresAt:  time.Now().Add(24 * time.Hour),
			LastUsedAt: time.Now(),
		},
		{
			ID:         2,
			UserID:     userID,
			DeviceType: models.DeviceTypeMobile,
			IPAddress:  strPtr("***********"),
			IsActive:   true,
			ExpiresAt:  time.Now().Add(24 * time.Hour),
			LastUsedAt: time.Now(),
		},
	}

	// Mock expectations
	suite.mockSessionRepo.On("GetActiveSessionsByUser", ctx, userID).Return(sessions, nil)

	// Execute
	result, err := suite.authService.GetActiveSessions(ctx, userID)

	// Assertions
	require.NoError(t, err)
	assert.Len(t, result, 2)
	assert.Equal(t, sessions, result)

	// Verify expectations
	suite.mockSessionRepo.AssertExpectations(t)
}

func TestAuthService_RevokeSession_Success(t *testing.T) {
	suite := setupAuthServiceTest()
	ctx := context.Background()

	userID := uint(1)
	sessionID := uint(100)

	session := &models.Session{
		ID:     sessionID,
		UserID: userID,
	}

	// Mock expectations
	suite.mockSessionRepo.On("GetByID", ctx, sessionID).Return(session, nil)
	suite.mockSessionRepo.On("InvalidateSession", ctx, sessionID).Return(nil)
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Execute
	err := suite.authService.RevokeSession(ctx, userID, sessionID)

	// Assertions
	assert.NoError(t, err)

	// Verify expectations
	suite.mockSessionRepo.AssertExpectations(t)
}

func TestAuthService_RevokeSession_Unauthorized(t *testing.T) {
	suite := setupAuthServiceTest()
	ctx := context.Background()

	userID := uint(1)
	sessionID := uint(100)

	// Session belongs to different user
	session := &models.Session{
		ID:     sessionID,
		UserID: 999,
	}

	// Mock expectations
	suite.mockSessionRepo.On("GetByID", ctx, sessionID).Return(session, nil)
	suite.mockLogger.On("Error", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithError", mock.AnythingOfType("error")).Return(suite.mockLogger)
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Execute
	err := suite.authService.RevokeSession(ctx, userID, sessionID)

	// Assertions
	assert.Error(t, err)
	assert.Equal(t, services.ErrUnauthorized, err)

	// Verify expectations
	suite.mockSessionRepo.AssertExpectations(t)
}

func TestAuthService_LogoutAllDevices_Success(t *testing.T) {
	suite := setupAuthServiceTest()
	ctx := context.Background()

	userID := uint(1)

	// Mock expectations
	suite.mockSessionRepo.On("InvalidateAllUserSessions", ctx, userID).Return(nil)
	suite.mockLogger.On("Info", mock.AnythingOfType("string"), mock.Anything).Return()
	suite.mockLogger.On("WithFields", mock.Anything).Return(suite.mockLogger)

	// Execute
	err := suite.authService.LogoutAllDevices(ctx, userID)

	// Assertions
	assert.NoError(t, err)

	// Verify expectations
	suite.mockSessionRepo.AssertExpectations(t)
}

// Helper functions
func strPtr(s string) *string {
	return &s
}
