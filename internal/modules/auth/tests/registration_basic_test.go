package tests

import (
	"context"
	"fmt"
	"testing"
	"time"

	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories/mysql"
	tenantModels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/tests/helpers"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

func TestRegistrationBasic(t *testing.T) {
	RegisterFailHandler(Fail)
	RunSpecs(t, "Registration Basic Suite")
}

var (
	testDB     *gorm.DB
	testHelper *helpers.TestHelper
	logger     utils.Logger
)

var _ = BeforeSuite(func() {
	// Setup test database
	var err error
	testDB, err = helpers.SetupTestDatabase()
	Expect(err).NotTo(HaveOccurred())

	// Run migrations
	err = helpers.RunMigrations(testDB)
	Expect(err).NotTo(HaveOccurred())

	// Initialize test helper
	testHelper = helpers.NewTestHelper(testDB)

	// Initialize logger
	logger = utils.NewLogger()
})

var _ = AfterSuite(func() {
	// Cleanup
	if testDB != nil {
		sqlDB, _ := testDB.DB()
		sqlDB.Close()
	}
})

// Reset database between tests
var _ = BeforeEach(func() {
	testHelper.CleanDatabase()
	testHelper.SeedBasicData()
})

var _ = Describe("Registration Basic Tests", func() {
	var (
		userRepo repositories.UserRepository
		tenant   *tenantModels.Tenant
		ctx      context.Context
	)

	BeforeEach(func() {
		ctx = context.Background()

		// Create test tenant
		tenant = testHelper.CreateTenant("Test Tenant")

		// Initialize repositories
		userRepo = mysql.NewUserRepository(testDB)
	})

	Describe("User Repository", func() {
		It("should create a user successfully", func() {
			// Arrange
			user := &models.User{
				TenantID:      tenant.ID,
				Email:         "<EMAIL>",
				PasswordHash:  "$2a$10$test.password.hash",
				EmailVerified: true,
				Status:        models.UserStatusActive,
				Role:          models.UserRoleUser,
				Language:      "en",
				Timezone:      "UTC",
			}

			// Act
			err := userRepo.Create(ctx, user)

			// Assert
			Expect(err).NotTo(HaveOccurred())
			Expect(user.ID).NotTo(BeZero())
			Expect(user.CreatedAt).NotTo(BeZero())
			Expect(user.UpdatedAt).NotTo(BeZero())

			// Verify in database
			var dbUser models.User
			err = testDB.Where("email = ?", "<EMAIL>").First(&dbUser).Error
			Expect(err).NotTo(HaveOccurred())
			Expect(dbUser.TenantID).To(Equal(tenant.ID))
			Expect(dbUser.Status).To(Equal(models.UserStatusActive))
			Expect(dbUser.EmailVerified).To(BeTrue())
		})

		It("should check if email exists", func() {
			// Arrange - create existing user
			existingUser := testHelper.CreateUser("<EMAIL>", tenant.ID)
			Expect(existingUser).NotTo(BeNil())

			// Act & Assert - existing email
			exists, err := userRepo.Exists(ctx, "<EMAIL>")
			Expect(err).NotTo(HaveOccurred())
			Expect(exists).To(BeTrue())

			// Act & Assert - non-existing email
			exists, err = userRepo.Exists(ctx, "<EMAIL>")
			Expect(err).NotTo(HaveOccurred())
			Expect(exists).To(BeFalse())
		})

		It("should check if username exists", func() {
			// Arrange - create existing user with username
			existingUser := testHelper.CreateUser("<EMAIL>", tenant.ID)
			username := "testuser"
			existingUser.Username = &username
			err := testDB.Save(existingUser).Error
			Expect(err).NotTo(HaveOccurred())

			// Act & Assert - existing username
			exists, err := userRepo.ExistsByUsername(ctx, "testuser")
			Expect(err).NotTo(HaveOccurred())
			Expect(exists).To(BeTrue())

			// Act & Assert - non-existing username
			exists, err = userRepo.ExistsByUsername(ctx, "nonexisting")
			Expect(err).NotTo(HaveOccurred())
			Expect(exists).To(BeFalse())
		})

		It("should get user by email", func() {
			// Arrange
			existingUser := testHelper.CreateUser("<EMAIL>", tenant.ID)
			Expect(existingUser).NotTo(BeNil())

			// Act
			user, err := userRepo.GetByEmail(ctx, "<EMAIL>")

			// Assert
			Expect(err).NotTo(HaveOccurred())
			Expect(user).NotTo(BeNil())
			Expect(user.ID).To(Equal(existingUser.ID))
			Expect(user.Email).To(Equal("<EMAIL>"))
			Expect(user.TenantID).To(Equal(tenant.ID))
		})

		It("should get user by email or username", func() {
			// Arrange
			existingUser := testHelper.CreateUser("<EMAIL>", tenant.ID)
			username := "testuser123"
			existingUser.Username = &username
			err := testDB.Save(existingUser).Error
			Expect(err).NotTo(HaveOccurred())

			// Act & Assert - by email
			user, err := userRepo.GetByEmailOrUsername(ctx, "<EMAIL>")
			Expect(err).NotTo(HaveOccurred())
			Expect(user).NotTo(BeNil())
			Expect(user.ID).To(Equal(existingUser.ID))

			// Act & Assert - by username
			user, err = userRepo.GetByEmailOrUsername(ctx, "testuser123")
			Expect(err).NotTo(HaveOccurred())
			Expect(user).NotTo(BeNil())
			Expect(user.ID).To(Equal(existingUser.ID))
		})

		It("should update last login", func() {
			// Arrange
			existingUser := testHelper.CreateUser("<EMAIL>", tenant.ID)
			Expect(existingUser).NotTo(BeNil())

			// Act
			err := userRepo.UpdateLastLogin(ctx, existingUser.ID, "***********")

			// Assert
			Expect(err).NotTo(HaveOccurred())

			// Verify in database
			var dbUser models.User
			err = testDB.Where("id = ?", existingUser.ID).First(&dbUser).Error
			Expect(err).NotTo(HaveOccurred())
			Expect(dbUser.LastLoginAt).NotTo(BeNil())
			Expect(dbUser.LastLoginIP).NotTo(BeNil())
			Expect(*dbUser.LastLoginIP).To(Equal("***********"))
			Expect(dbUser.LoginCount).To(Equal(uint(1)))
		})

		It("should count users by tenant", func() {
			// Arrange - create multiple users
			for i := 0; i < 5; i++ {
				testHelper.CreateUser(fmt.Sprintf("<EMAIL>", i), tenant.ID)
			}

			// Act
			count, err := userRepo.CountByTenant(ctx, tenant.ID)

			// Assert
			Expect(err).NotTo(HaveOccurred())
			Expect(count).To(Equal(int64(5)))
		})
	})

	Describe("User Model", func() {
		It("should have correct default values", func() {
			// Arrange
			user := &models.User{
				TenantID:     tenant.ID,
				Email:        "<EMAIL>",
				PasswordHash: "$2a$10$test.password.hash",
				Language:     "en",
				Timezone:     "UTC",
			}

			// Act
			err := testDB.Create(user).Error

			// Assert
			Expect(err).NotTo(HaveOccurred())
			Expect(user.Status).To(Equal(models.UserStatusActive))
			Expect(user.Role).To(Equal(models.UserRoleUser))
			Expect(user.EmailVerified).To(BeFalse())
			Expect(user.TwoFactorEnabled).To(BeFalse())
			Expect(user.LoginCount).To(Equal(uint(0)))
		})

		It("should validate user methods", func() {
			// Arrange
			user := &models.User{
				TenantID:      tenant.ID,
				Email:         "<EMAIL>",
				Status:        models.UserStatusActive,
				EmailVerified: true,
				Role:          models.UserRoleUser,
			}

			// Act & Assert
			Expect(user.IsActive()).To(BeTrue())
			Expect(user.IsVerified()).To(BeTrue())
			Expect(user.IsDeleted()).To(BeFalse())
			Expect(user.CanLogin()).To(BeTrue())
			Expect(user.HasTwoFactorEnabled()).To(BeFalse())

			// Test inactive user
			user.Status = models.UserStatusInactive
			Expect(user.IsActive()).To(BeFalse())
			Expect(user.CanLogin()).To(BeFalse())

			// Test deleted user
			user.Status = models.UserStatusDeleted
			Expect(user.IsDeleted()).To(BeTrue())
		})

		It("should handle optional fields correctly", func() {
			// Arrange
			user := &models.User{
				TenantID:     tenant.ID,
				Email:        "<EMAIL>",
				PasswordHash: "$2a$10$test.password.hash",
				Language:     "en",
				Timezone:     "UTC",
			}

			firstName := "John"
			lastName := "Doe"
			username := "johndoe"
			displayName := "John Doe"
			phone := "+1234567890"

			user.FirstName = &firstName
			user.LastName = &lastName
			user.Username = &username
			user.DisplayName = &displayName
			user.Phone = &phone

			// Act
			err := testDB.Create(user).Error

			// Assert
			Expect(err).NotTo(HaveOccurred())
			Expect(user.GetFullName()).To(Equal("John Doe"))

			// Test with display name
			displayName = "Johnny D"
			user.DisplayName = &displayName
			Expect(user.GetFullName()).To(Equal("Johnny D"))
		})
	})

	Describe("Test Helpers", func() {
		It("should create test tenant correctly", func() {
			// Act
			newTenant := testHelper.CreateTenant("Helper Test Tenant")

			// Assert
			Expect(newTenant).NotTo(BeNil())
			Expect(newTenant.ID).NotTo(BeZero())
			Expect(newTenant.Name).To(Equal("Helper Test Tenant"))
			Expect(newTenant.Status).To(Equal(tenantModels.TenantStatusActive))
		})

		It("should create test user correctly", func() {
			// Act
			user := testHelper.CreateUser("<EMAIL>", tenant.ID)

			// Assert
			Expect(user).NotTo(BeNil())
			Expect(user.ID).NotTo(BeZero())
			Expect(user.Email).To(Equal("<EMAIL>"))
			Expect(user.TenantID).To(Equal(tenant.ID))
			Expect(user.Status).To(Equal(models.UserStatusActive))
			Expect(user.EmailVerified).To(BeTrue())
		})

		It("should create test session correctly", func() {
			// Arrange
			user := testHelper.CreateUser("<EMAIL>", tenant.ID)

			// Act
			session := testHelper.CreateSession(user.ID, 1)

			// Assert
			Expect(session).NotTo(BeNil())
			Expect(session.ID).NotTo(BeZero())
			Expect(session.UserID).To(Equal(user.ID))
			Expect(session.IsRevoked).To(BeFalse())
			Expect(session.ExpiresAt).To(BeTemporally(">", time.Now()))
		})

		It("should assert user properties correctly", func() {
			// Arrange
			user := testHelper.CreateUser("<EMAIL>", tenant.ID)

			// Act & Assert
			testHelper.AssertUserExists(user.ID)
			testHelper.AssertUserHasStatus(user.ID, models.UserStatusActive)
			testHelper.AssertUserEmailVerified(user.ID, true)
		})
	})
})
