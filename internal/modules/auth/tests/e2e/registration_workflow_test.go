package e2e

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"time"

	"github.com/gin-gonic/gin"
	. "github.com/onsi/ginkgo/v2"
	. "github.com/onsi/gomega"
	"github.com/onsi/gomega/gmeasure"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/services"
	tenantModels "github.com/tranthanhloi/wn-api-v3/internal/modules/tenant/models"
	"github.com/tranthanhloi/wn-api-v3/internal/tests/helpers"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

var _ = Describe("Registration E2E Workflow Tests", func() {
	var (
		router      *gin.Engine
		authHandler *handlers.AuthHandler
		testHelper  *helpers.TestHelper
		tenant      *tenantModels.Tenant
		experiment  *gmeasure.Experiment
		ctx         context.Context
	)

	BeforeEach(func() {
		// Initialize test context
		ctx = context.Background()

		// Initialize experiment for performance tracking
		experiment = gmeasure.NewExperiment("Registration Performance")
		AddReportEntry(experiment.Name, experiment)

		// Create test helper
		testHelper = helpers.NewTestHelper(db)

		// Create test tenant
		tenant = testHelper.CreateTenant("E2E Test Tenant")

		// Initialize real repositories
		userRepo := mysql.NewUserRepository(db)
		sessionRepo := mysql.NewSessionRepository(db)
		loginAttemptRepo := mysql.NewLoginAttemptRepository(db)

		// Initialize real services
		jwtService := services.NewJWTService(&services.JWTConfig{
			SecretKey:       "test-secret-key-32-bytes-long-for-jwt",
			AccessTokenTTL:  time.Hour,
			RefreshTokenTTL: time.Hour * 24 * 7,
			Issuer:          "test-issuer",
		}, nil)

		passwordService := services.NewPasswordService(&services.PasswordConfig{
			MinLength:        8,
			RequireUppercase: true,
			RequireLowercase: true,
			RequireNumbers:   true,
			RequireSymbols:   false,
		})

		emailService := services.NewEmailService(&services.EmailConfig{
			SMTPHost:     "localhost",
			SMTPPort:     587,
			SMTPUsername: "<EMAIL>",
			SMTPPassword: "test",
			FromAddress:  "<EMAIL>",
			FromName:     "Test",
		})

		rateLimiter := services.NewRateLimitingService(loginAttemptRepo, &services.RateLimitConfig{
			MaxAttempts:     5,
			LockoutDuration: time.Minute * 15,
			WindowDuration:  time.Minute * 5,
		})

		authService := services.NewAuthService(
			userRepo,
			sessionRepo,
			loginAttemptRepo,
			jwtService,
			passwordService,
			rateLimiter,
			emailService,
			logger,
			&services.AuthConfig{
				RequireEmailVerification: false,
				AllowRegistration:        true,
				MaxLoginAttempts:         5,
				LockoutDuration:          time.Minute * 15,
				SessionTimeout:           time.Hour * 24,
				RefreshTokenTTL:          time.Hour * 24 * 7,
				TwoFactorIssuer:          "test-app",
			},
		)

		// Initialize validator
		validatorInstance := validator.NewValidator()

		// Initialize handler
		authHandler = handlers.NewAuthHandler(
			authService,
			jwtService,
			passwordService,
			validatorInstance,
			logger,
		)

		// Setup router
		gin.SetMode(gin.TestMode)
		router = gin.New()

		// Add middleware to set tenant_id in context
		router.Use(func(c *gin.Context) {
			c.Set("tenant_id", tenant.ID)
			c.Next()
		})

		// Setup routes
		v1 := router.Group("/api/cms/v1")
		{
			v1.POST("/auth/register", authHandler.Register)
			v1.POST("/auth/login", authHandler.Login)
			v1.GET("/auth/profile", authHandler.GetProfile)
			v1.GET("/auth/sessions", authHandler.GetActiveSessions)
			v1.POST("/auth/logout", authHandler.Logout)
			v1.POST("/auth/refresh", authHandler.RefreshToken)
		}
	})

	Describe("Complete Registration to Login Workflow", func() {
		It("should complete full user registration and login workflow within performance targets", func() {
			experiment.Sample(func(idx int) {
				userEmail := fmt.Sprintf("<EMAIL>", idx)
				userPassword := "SecurePass123"

				startTime := time.Now()

				By("Step 1: Register new user")
				registrationData := map[string]interface{}{
					"email":      userEmail,
					"username":   fmt.Sprintf("user%d", idx),
					"password":   userPassword,
					"first_name": "Test",
					"last_name":  "User",
					"tenant_id":  tenant.ID,
					"website_id": 1,
				}

				body, _ := json.Marshal(registrationData)
				req := httptest.NewRequest("POST", "/api/cms/v1/auth/register", bytes.NewBuffer(body))
				req.Header.Set("Content-Type", "application/json")
				req.Header.Set("User-Agent", "Test Browser E2E")

				w := httptest.NewRecorder()
				router.ServeHTTP(w, req)

				Expect(w.Code).To(Equal(http.StatusCreated))

				var registerResponse map[string]interface{}
				err := json.Unmarshal(w.Body.Bytes(), &registerResponse)
				Expect(err).NotTo(HaveOccurred())
				Expect(registerResponse["success"]).To(BeTrue())

				registrationDuration := time.Since(startTime)
				experiment.RecordDuration("user_registration", registrationDuration)

				// Extract user data and tokens
				data := registerResponse["data"].(map[string]interface{})
				user := data["user"].(map[string]interface{})
				userID := uint(user["id"].(float64))
				accessToken := data["access_token"].(string)
				refreshToken := data["refresh_token"].(string)
				sessionID := uint(data["session_id"].(float64))

				By("Step 2: Verify user exists in database")
				verifyStartTime := time.Now()

				var dbUser models.User
				err = db.Where("email = ?", userEmail).First(&dbUser).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(dbUser.ID).To(Equal(userID))
				Expect(dbUser.TenantID).To(Equal(tenant.ID))
				Expect(dbUser.Status).To(Equal(models.UserStatusActive))
				Expect(dbUser.EmailVerified).To(BeTrue())

				verifyDuration := time.Since(verifyStartTime)
				experiment.RecordDuration("user_verification", verifyDuration)

				By("Step 3: Verify session was created")
				var session models.Session
				err = db.Where("id = ?", sessionID).First(&session).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(session.UserID).To(Equal(userID))
				Expect(session.Status).To(Equal(models.SessionStatusActive))
				Expect(session.ExpiresAt).To(BeTemporally(">", time.Now()))

				By("Step 4: Test immediate login with same credentials")
				loginStartTime := time.Now()

				loginData := map[string]interface{}{
					"email":      userEmail,
					"password":   userPassword,
					"website_id": 1,
				}

				body, _ = json.Marshal(loginData)
				req = httptest.NewRequest("POST", "/api/cms/v1/auth/login", bytes.NewBuffer(body))
				req.Header.Set("Content-Type", "application/json")
				req.Header.Set("User-Agent", "Test Browser E2E")

				w = httptest.NewRecorder()
				router.ServeHTTP(w, req)

				Expect(w.Code).To(Equal(http.StatusOK))

				var loginResponse map[string]interface{}
				err = json.Unmarshal(w.Body.Bytes(), &loginResponse)
				Expect(err).NotTo(HaveOccurred())
				Expect(loginResponse["success"]).To(BeTrue())

				loginDuration := time.Since(loginStartTime)
				experiment.RecordDuration("user_login", loginDuration)

				By("Step 5: Test token refresh")
				refreshStartTime := time.Now()

				refreshData := map[string]interface{}{
					"refresh_token": refreshToken,
				}

				body, _ = json.Marshal(refreshData)
				req = httptest.NewRequest("POST", "/api/cms/v1/auth/refresh", bytes.NewBuffer(body))
				req.Header.Set("Content-Type", "application/json")

				w = httptest.NewRecorder()
				router.ServeHTTP(w, req)

				Expect(w.Code).To(Equal(http.StatusOK))

				var refreshResponse map[string]interface{}
				err = json.Unmarshal(w.Body.Bytes(), &refreshResponse)
				Expect(err).NotTo(HaveOccurred())
				Expect(refreshResponse["success"]).To(BeTrue())

				refreshDuration := time.Since(refreshStartTime)
				experiment.RecordDuration("token_refresh", refreshDuration)

				By("Step 6: Test profile access with new token")
				profileStartTime := time.Now()

				// Use new access token from refresh response
				newAccessToken := refreshResponse["data"].(map[string]interface{})["access_token"].(string)

				req = httptest.NewRequest("GET", "/api/cms/v1/auth/profile", nil)
				req.Header.Set("Authorization", "Bearer "+newAccessToken)

				w = httptest.NewRecorder()
				router.ServeHTTP(w, req)

				Expect(w.Code).To(Equal(http.StatusOK))

				var profileResponse map[string]interface{}
				err = json.Unmarshal(w.Body.Bytes(), &profileResponse)
				Expect(err).NotTo(HaveOccurred())
				Expect(profileResponse["success"]).To(BeTrue())

				profileData := profileResponse["data"].(map[string]interface{})
				Expect(profileData["email"]).To(Equal(userEmail))
				Expect(profileData["tenant_id"]).To(Equal(float64(tenant.ID)))

				profileDuration := time.Since(profileStartTime)
				experiment.RecordDuration("profile_access", profileDuration)

				By("Step 7: Test logout")
				logoutStartTime := time.Now()

				req = httptest.NewRequest("POST", "/api/cms/v1/auth/logout", nil)
				req.Header.Set("Authorization", "Bearer "+newAccessToken)

				w = httptest.NewRecorder()
				router.ServeHTTP(w, req)

				Expect(w.Code).To(Equal(http.StatusOK))

				var logoutResponse map[string]interface{}
				err = json.Unmarshal(w.Body.Bytes(), &logoutResponse)
				Expect(err).NotTo(HaveOccurred())
				Expect(logoutResponse["success"]).To(BeTrue())

				logoutDuration := time.Since(logoutStartTime)
				experiment.RecordDuration("user_logout", logoutDuration)

				By("Step 8: Verify session is invalidated")
				var invalidatedSession models.Session
				err = db.Where("id = ?", sessionID).First(&invalidatedSession).Error
				Expect(err).NotTo(HaveOccurred())
				Expect(invalidatedSession.Status).To(Equal(models.SessionStatusInactive))

				By("Step 9: Verify token is no longer valid")
				req = httptest.NewRequest("GET", "/api/cms/v1/auth/profile", nil)
				req.Header.Set("Authorization", "Bearer "+newAccessToken)

				w = httptest.NewRecorder()
				router.ServeHTTP(w, req)

				Expect(w.Code).To(Equal(http.StatusUnauthorized))

				totalDuration := time.Since(startTime)
				experiment.RecordDuration("total_workflow", totalDuration)

				// Performance assertions
				Expect(totalDuration).To(BeNumerically("<", 5*time.Second), "Total workflow should complete within 5 seconds")
				Expect(registrationDuration).To(BeNumerically("<", 2*time.Second), "Registration should complete within 2 seconds")
				Expect(loginDuration).To(BeNumerically("<", 1*time.Second), "Login should complete within 1 second")

			}, gmeasure.SamplingConfig{N: 5}) // Test with 5 concurrent users

			// Analyze performance results
			stats := experiment.GetStats("total_workflow")
			Expect(stats.Mean).To(BeNumerically("<", 3*time.Second))
			Expect(stats.StdDeviation).To(BeNumerically("<", 500*time.Millisecond))
		})
	})

	Describe("Registration with Email Verification Workflow", func() {
		var authServiceWithVerification services.AuthService

		BeforeEach(func() {
			// Create auth service with email verification enabled
			userRepo := mysql.NewUserRepository(db)
			sessionRepo := mysql.NewSessionRepository(db)
			loginAttemptRepo := mysql.NewLoginAttemptRepository(db)

			jwtService := services.NewJWTService(&services.JWTConfig{
				SecretKey:       "test-secret-key-32-bytes-long-for-jwt",
				AccessTokenTTL:  time.Hour,
				RefreshTokenTTL: time.Hour * 24 * 7,
				Issuer:          "test-issuer",
			}, nil)

			passwordService := services.NewPasswordService(&services.PasswordConfig{
				MinLength:        8,
				RequireUppercase: true,
				RequireLowercase: true,
				RequireNumbers:   true,
				RequireSymbols:   false,
			})

			emailService := services.NewEmailService(&services.EmailConfig{
				SMTPHost:     "localhost",
				SMTPPort:     587,
				SMTPUsername: "<EMAIL>",
				SMTPPassword: "test",
				FromAddress:  "<EMAIL>",
				FromName:     "Test",
			})

			rateLimiter := services.NewRateLimitingService(loginAttemptRepo, &services.RateLimitConfig{
				MaxAttempts:     5,
				LockoutDuration: time.Minute * 15,
				WindowDuration:  time.Minute * 5,
			})

			authServiceWithVerification = services.NewAuthService(
				userRepo,
				sessionRepo,
				loginAttemptRepo,
				jwtService,
				passwordService,
				rateLimiter,
				emailService,
				logger,
				&services.AuthConfig{
					RequireEmailVerification: true, // Enable email verification
					AllowRegistration:        true,
					MaxLoginAttempts:         5,
					LockoutDuration:          time.Minute * 15,
					SessionTimeout:           time.Hour * 24,
					RefreshTokenTTL:          time.Hour * 24 * 7,
					TwoFactorIssuer:          "test-app",
				},
			)

			// Update handler with new service
			authHandler = handlers.NewAuthHandler(
				authServiceWithVerification,
				jwtService,
				passwordService,
				validator.NewValidator(),
				logger,
			)

			// Update router routes
			v1 := router.Group("/api/cms/v1")
			{
				v1.POST("/auth/register", authHandler.Register)
				v1.POST("/auth/login", authHandler.Login)
			}
		})

		It("should handle registration with email verification requirement", func() {
			// Arrange
			registrationData := map[string]interface{}{
				"email":      "<EMAIL>",
				"password":   "SecurePass123",
				"tenant_id":  tenant.ID,
				"website_id": 1,
			}

			body, _ := json.Marshal(registrationData)
			req := httptest.NewRequest("POST", "/api/cms/v1/auth/register", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")
			req.Header.Set("User-Agent", "Test Browser E2E")

			// Act
			w := httptest.NewRecorder()
			router.ServeHTTP(w, req)

			// Assert - Registration should fail due to email verification requirement
			Expect(w.Code).To(Equal(http.StatusForbidden))

			var response map[string]interface{}
			err := json.Unmarshal(w.Body.Bytes(), &response)
			Expect(err).NotTo(HaveOccurred())
			Expect(response["success"]).To(BeFalse())
			Expect(response["error"].(map[string]interface{})["message"]).To(Equal("Email not verified"))

			// Verify user was created but not verified
			var user models.User
			err = db.Where("email = ?", "<EMAIL>").First(&user).Error
			Expect(err).NotTo(HaveOccurred())
			Expect(user.EmailVerified).To(BeFalse())
			Expect(user.Status).To(Equal(models.UserStatusActive))
		})
	})

	Describe("Concurrent Registration Stress Test", func() {
		It("should handle concurrent registrations without race conditions", func() {
			const numConcurrent = 10
			results := make(chan error, numConcurrent)

			for i := 0; i < numConcurrent; i++ {
				go func(idx int) {
					defer GinkgoRecover()

					userEmail := fmt.Sprintf("<EMAIL>", idx)

					registrationData := map[string]interface{}{
						"email":      userEmail,
						"username":   fmt.Sprintf("concurrent%d", idx),
						"password":   "SecurePass123",
						"tenant_id":  tenant.ID,
						"website_id": 1,
					}

					body, _ := json.Marshal(registrationData)
					req := httptest.NewRequest("POST", "/api/cms/v1/auth/register", bytes.NewBuffer(body))
					req.Header.Set("Content-Type", "application/json")
					req.Header.Set("User-Agent", "Test Browser Concurrent")

					w := httptest.NewRecorder()
					router.ServeHTTP(w, req)

					if w.Code != http.StatusCreated {
						results <- fmt.Errorf("registration failed for user %d: status %d", idx, w.Code)
						return
					}

					// Verify user was created
					var user models.User
					err := db.Where("email = ?", userEmail).First(&user).Error
					if err != nil {
						results <- fmt.Errorf("user not found in database: %v", err)
						return
					}

					results <- nil
				}(i)
			}

			// Wait for all goroutines to complete
			for i := 0; i < numConcurrent; i++ {
				Eventually(results, 10*time.Second).Should(Receive(BeNil()))
			}

			// Verify all users were created
			var userCount int64
			db.Model(&models.User{}).Where("tenant_id = ?", tenant.ID).Count(&userCount)
			Expect(userCount).To(Equal(int64(numConcurrent)))

			// Verify all sessions were created
			var sessionCount int64
			db.Model(&models.Session{}).Count(&sessionCount)
			Expect(sessionCount).To(Equal(int64(numConcurrent)))
		})

		It("should handle duplicate email registrations gracefully", func() {
			const numConcurrent = 5
			results := make(chan int, numConcurrent)

			// Try to register the same email multiple times concurrently
			for i := 0; i < numConcurrent; i++ {
				go func(idx int) {
					defer GinkgoRecover()

					registrationData := map[string]interface{}{
						"email":      "<EMAIL>",
						"username":   fmt.Sprintf("duplicate%d", idx),
						"password":   "SecurePass123",
						"tenant_id":  tenant.ID,
						"website_id": 1,
					}

					body, _ := json.Marshal(registrationData)
					req := httptest.NewRequest("POST", "/api/cms/v1/auth/register", bytes.NewBuffer(body))
					req.Header.Set("Content-Type", "application/json")
					req.Header.Set("User-Agent", "Test Browser Duplicate")

					w := httptest.NewRecorder()
					router.ServeHTTP(w, req)

					results <- w.Code
				}(i)
			}

			// Collect all results
			var statusCodes []int
			for i := 0; i < numConcurrent; i++ {
				Eventually(results, 10*time.Second).Should(Receive(&statusCodes))
			}

			// Verify only one succeeded (201) and others failed (409)
			successCount := 0
			conflictCount := 0

			for _, code := range statusCodes {
				if code == http.StatusCreated {
					successCount++
				} else if code == http.StatusConflict {
					conflictCount++
				}
			}

			Expect(successCount).To(Equal(1), "Only one registration should succeed")
			Expect(conflictCount).To(Equal(numConcurrent-1), "All other registrations should fail with conflict")

			// Verify only one user was created
			var userCount int64
			db.Model(&models.User{}).Where("email = ?", "<EMAIL>").Count(&userCount)
			Expect(userCount).To(Equal(int64(1)))
		})
	})
})
