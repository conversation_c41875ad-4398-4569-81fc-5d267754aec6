package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/user/models"
)

// UserRepository defines the interface for user-related database operations
// This interface works with global users (no tenant-specific operations)
type UserRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, user *models.User) error
	GetByID(ctx context.Context, id uint) (*models.User, error)
	GetByEmail(ctx context.Context, email string) (*models.User, error)
	GetByUsername(ctx context.Context, username string) (*models.User, error)
	Update(ctx context.Context, user *models.User) error
	Delete(ctx context.Context, id uint) error

	// Authentication specific
	GetByEmailOrUsername(ctx context.Context, identifier string) (*models.User, error)
	UpdateLastLogin(ctx context.Context, userID uint, ip string) error
	UpdatePassword(ctx context.Context, userID uint, hashedPassword string) error

	// Email verification
	VerifyEmail(ctx context.Context, userID uint) error
	UpdateEmailVerificationToken(ctx context.Context, userID uint, token string) error
	GetByEmailVerificationToken(ctx context.Context, token string) (*models.User, error)

	// Two-factor authentication
	EnableTwoFactor(ctx context.Context, userID uint, secret string) error
	DisableTwoFactor(ctx context.Context, userID uint) error
	GetTwoFactorSecret(ctx context.Context, userID uint) (string, error)
	UpdateTwoFactorBackupCodes(ctx context.Context, userID uint, codes []string) error

	// Status management
	UpdateStatus(ctx context.Context, userID uint, status models.UserStatus) error

	// Bulk operations
	GetByIDs(ctx context.Context, ids []uint) ([]*models.User, error)

	// Search and filtering (global search)
	Search(ctx context.Context, query string, filters *UserFilters) ([]*models.User, error)
	Exists(ctx context.Context, email string) (bool, error)
	ExistsByUsername(ctx context.Context, username string) (bool, error)
}

// UserFilters represents filters for querying users (global search)
type UserFilters struct {
	Status           *models.UserStatus `json:"status,omitempty"`
	EmailVerified    *bool              `json:"email_verified,omitempty"`
	TwoFactorEnabled *bool              `json:"two_factor_enabled,omitempty"`
	CreatedAfter     *time.Time         `json:"created_after,omitempty"`
	CreatedBefore    *time.Time         `json:"created_before,omitempty"`
	SortBy           string             `json:"sort_by,omitempty"`
	SortOrder        string             `json:"sort_order,omitempty"`
	Limit            int                `json:"limit,omitempty"`
	Offset           int                `json:"offset,omitempty"`
}
