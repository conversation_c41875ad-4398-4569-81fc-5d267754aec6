package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
)

// LoginAttemptRepository defines the interface for login attempt tracking
type LoginAttemptRepository interface {
	// Basic operations
	Create(ctx context.Context, attempt *models.LoginAttempt) error
	GetByID(ctx context.Context, id uint) (*models.LoginAttempt, error)

	// Rate limiting queries
	CountRecentAttempts(ctx context.Context, identifier string, duration time.Duration) (int64, error)
	CountRecentAttemptsbyIP(ctx context.Context, ip string, duration time.Duration) (int64, error)
	GetRecentAttempts(ctx context.Context, identifier string, duration time.Duration) ([]*models.LoginAttempt, error)

	// Lock management
	IsAccountLocked(ctx context.Context, identifier string) (bool, time.Time, error)
	LockAccount(ctx context.Context, identifier string, until time.Time) error
	UnlockAccount(ctx context.Context, identifier string) error

	// Success tracking
	RecordSuccessfulLogin(ctx context.Context, userID uint, ip string) error
	GetLastSuccessfulLogin(ctx context.Context, userID uint) (*models.LoginAttempt, error)

	// Analytics
	GetFailedAttemptsByUser(ctx context.Context, userID uint, limit int) ([]*models.LoginAttempt, error)
	GetFailedAttemptsByIP(ctx context.Context, ip string, limit int) ([]*models.LoginAttempt, error)
	CountFailedAttemptsByUser(ctx context.Context, userID uint, duration time.Duration) (int64, error)

	// Cleanup
	CleanupOldAttempts(ctx context.Context, olderThan time.Time) (int64, error)

	// Security monitoring
	GetSuspiciousIPs(ctx context.Context, threshold int, duration time.Duration) ([]string, error)
	GetSuspiciousPatterns(ctx context.Context, websiteID uint) ([]*LoginPattern, error)
}

// LoginPattern represents suspicious login patterns
type LoginPattern struct {
	Pattern     string    `json:"pattern"`
	Count       int       `json:"count"`
	FirstSeen   time.Time `json:"first_seen"`
	LastSeen    time.Time `json:"last_seen"`
	IPAddresses []string  `json:"ip_addresses"`
}
