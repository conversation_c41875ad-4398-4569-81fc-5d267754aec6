package repositories

import (
	"context"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
)

// SessionRepository defines the interface for session-related database operations
type SessionRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, session *models.Session) error
	GetByID(ctx context.Context, id uint) (*models.Session, error)
	GetByToken(ctx context.Context, token string) (*models.Session, error)
	Update(ctx context.Context, session *models.Session) error
	Delete(ctx context.Context, id uint) error

	// Session management
	GetActiveSessionsByUser(ctx context.Context, userID uint) ([]*models.Session, error)
	GetActiveSessionsByWebsite(ctx context.Context, websiteID uint) ([]*models.Session, error)
	InvalidateSession(ctx context.Context, sessionID uint) error
	InvalidateAllUserSessions(ctx context.Context, userID uint) error
	InvalidateAllUserSessionsExcept(ctx context.Context, userID uint, exceptSessionID uint) error

	// Activity tracking
	UpdateLastActivity(ctx context.Context, sessionID uint, ip string, userAgent string) error
	ExtendExpiration(ctx context.Context, sessionID uint, newExpiresAt time.Time) error

	// Cleanup operations
	CleanupExpiredSessions(ctx context.Context) (int64, error)
	CleanupInactiveSessions(ctx context.Context, inactiveDuration time.Duration) (int64, error)

	// Search and statistics
	CountActiveSessions(ctx context.Context, websiteID uint) (int64, error)
	CountActiveUserSessions(ctx context.Context, userID uint) (int64, error)
	GetSessionsByFilters(ctx context.Context, filters *SessionFilters) ([]*models.Session, error)

	// Security operations
	GetSessionsByIP(ctx context.Context, ip string, limit int) ([]*models.Session, error)
	GetRecentSessions(ctx context.Context, userID uint, limit int) ([]*models.Session, error)
}

// SessionFilters represents filters for querying sessions
type SessionFilters struct {
	UserID        *uint      `json:"user_id,omitempty"`
	WebsiteID     *uint      `json:"website_id,omitempty"`
	IsActive      *bool      `json:"is_active,omitempty"`
	IPAddress     *string    `json:"ip_address,omitempty"`
	DeviceType    *string    `json:"device_type,omitempty"`
	CreatedAfter  *time.Time `json:"created_after,omitempty"`
	CreatedBefore *time.Time `json:"created_before,omitempty"`
	ExpiresAfter  *time.Time `json:"expires_after,omitempty"`
	ExpiresBefore *time.Time `json:"expires_before,omitempty"`
	Limit         int        `json:"limit,omitempty"`
	Offset        int        `json:"offset,omitempty"`
}
