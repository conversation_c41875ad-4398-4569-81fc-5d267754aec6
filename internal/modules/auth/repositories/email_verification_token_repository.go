package repositories

import (
	"context"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"gorm.io/gorm"
)

// EmailVerificationTokenRepository interface defines methods for email verification token operations
type EmailVerificationTokenRepository interface {
	// Core CRUD operations
	CreateToken(ctx context.Context, token *models.EmailVerificationToken) error
	GetTokenByID(ctx context.Context, id uint) (*models.EmailVerificationToken, error)
	GetTokenByToken(ctx context.Context, tokenValue string) (*models.EmailVerificationToken, error)
	GetTokenByUserID(ctx context.Context, userID uint) (*models.EmailVerificationToken, error)
	GetTokenByEmail(ctx context.Context, email string) (*models.EmailVerificationToken, error)
	UpdateToken(ctx context.Context, token *models.EmailVerificationToken) error
	DeleteToken(ctx context.Context, id uint) error

	// Verification operations
	VerifyToken(ctx context.Context, tokenValue string) (*models.EmailVerificationToken, error)
	MarkTokenAsUsed(ctx context.Context, id uint) error

	// Rate limiting and cleanup
	GetActiveTokensForUser(ctx context.Context, userID uint) ([]*models.EmailVerificationToken, error)
	GetActiveTokensForEmail(ctx context.Context, email string) ([]*models.EmailVerificationToken, error)
	InvalidateTokensForUser(ctx context.Context, userID uint) error
	InvalidateTokensForEmail(ctx context.Context, email string) error
	CleanupExpiredTokens(ctx context.Context) (int64, error)

	// Analytics and monitoring
	GetTokenStats(ctx context.Context, userID uint) (*models.TokenStats, error)
	GetRecentTokensForUser(ctx context.Context, userID uint, limit int) ([]*models.EmailVerificationToken, error)
}

// emailVerificationTokenRepository implements EmailVerificationTokenRepository
type emailVerificationTokenRepository struct {
	db *gorm.DB
}

// NewEmailVerificationTokenRepository creates a new email verification token repository
func NewEmailVerificationTokenRepository(db *gorm.DB) EmailVerificationTokenRepository {
	return &emailVerificationTokenRepository{
		db: db,
	}
}

// CreateToken creates a new email verification token
func (r *emailVerificationTokenRepository) CreateToken(ctx context.Context, token *models.EmailVerificationToken) error {
	return r.db.WithContext(ctx).Create(token).Error
}

// GetTokenByID retrieves a token by its ID
func (r *emailVerificationTokenRepository) GetTokenByID(ctx context.Context, id uint) (*models.EmailVerificationToken, error) {
	var token models.EmailVerificationToken
	err := r.db.WithContext(ctx).
		Preload("User").
		First(&token, id).Error
	if err != nil {
		return nil, err
	}
	return &token, nil
}

// GetTokenByToken retrieves a token by its token value
func (r *emailVerificationTokenRepository) GetTokenByToken(ctx context.Context, tokenValue string) (*models.EmailVerificationToken, error) {
	// Hash the provided token to match against stored hash
	hash := sha256.Sum256([]byte(tokenValue))
	tokenHash := hex.EncodeToString(hash[:])

	var token models.EmailVerificationToken
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("token_hash = ?", tokenHash).
		First(&token).Error
	if err != nil {
		return nil, err
	}
	return &token, nil
}

// GetTokenByUserID retrieves the most recent active token for a user
func (r *emailVerificationTokenRepository) GetTokenByUserID(ctx context.Context, userID uint) (*models.EmailVerificationToken, error) {
	var token models.EmailVerificationToken
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("user_id = ? AND is_used = ? AND expires_at > ?", userID, false, time.Now()).
		Order("created_at DESC").
		First(&token).Error
	if err != nil {
		return nil, err
	}
	return &token, nil
}

// GetTokenByEmail retrieves the most recent active token for an email
func (r *emailVerificationTokenRepository) GetTokenByEmail(ctx context.Context, email string) (*models.EmailVerificationToken, error) {
	var token models.EmailVerificationToken
	err := r.db.WithContext(ctx).
		Preload("User").
		Where("email = ? AND is_used = ? AND expires_at > ?", email, false, time.Now()).
		Order("created_at DESC").
		First(&token).Error
	if err != nil {
		return nil, err
	}
	return &token, nil
}

// UpdateToken updates an existing token
func (r *emailVerificationTokenRepository) UpdateToken(ctx context.Context, token *models.EmailVerificationToken) error {
	return r.db.WithContext(ctx).Save(token).Error
}

// DeleteToken deletes a token by ID
func (r *emailVerificationTokenRepository) DeleteToken(ctx context.Context, id uint) error {
	return r.db.WithContext(ctx).Delete(&models.EmailVerificationToken{}, id).Error
}

// VerifyToken verifies a token and marks it as used if valid
func (r *emailVerificationTokenRepository) VerifyToken(ctx context.Context, tokenValue string) (*models.EmailVerificationToken, error) {
	token, err := r.GetTokenByToken(ctx, tokenValue)
	if err != nil {
		return nil, err
	}

	// Check if token is valid
	if !token.IsValid() {
		if token.IsExpired() {
			return nil, fmt.Errorf("verification token has expired")
		}
		if token.IsUsed {
			return nil, fmt.Errorf("verification token has already been used")
		}
	}

	return token, nil
}

// MarkTokenAsUsed marks a token as used
func (r *emailVerificationTokenRepository) MarkTokenAsUsed(ctx context.Context, id uint) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&models.EmailVerificationToken{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"is_used": true,
			"used_at": now,
		}).Error
}

// GetActiveTokensForUser retrieves all active tokens for a user
func (r *emailVerificationTokenRepository) GetActiveTokensForUser(ctx context.Context, userID uint) ([]*models.EmailVerificationToken, error) {
	var tokens []*models.EmailVerificationToken
	err := r.db.WithContext(ctx).
		Where("user_id = ? AND is_used = ? AND expires_at > ?", userID, false, time.Now()).
		Order("created_at DESC").
		Find(&tokens).Error
	return tokens, err
}

// GetActiveTokensForEmail retrieves all active tokens for an email
func (r *emailVerificationTokenRepository) GetActiveTokensForEmail(ctx context.Context, email string) ([]*models.EmailVerificationToken, error) {
	var tokens []*models.EmailVerificationToken
	err := r.db.WithContext(ctx).
		Where("email = ? AND is_used = ? AND expires_at > ?", email, false, time.Now()).
		Order("created_at DESC").
		Find(&tokens).Error
	return tokens, err
}

// InvalidateTokensForUser marks all active tokens for a user as used
func (r *emailVerificationTokenRepository) InvalidateTokensForUser(ctx context.Context, userID uint) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&models.EmailVerificationToken{}).
		Where("user_id = ? AND is_used = ?", userID, false).
		Updates(map[string]interface{}{
			"is_used": true,
			"used_at": now,
		}).Error
}

// InvalidateTokensForEmail marks all active tokens for an email as used
func (r *emailVerificationTokenRepository) InvalidateTokensForEmail(ctx context.Context, email string) error {
	now := time.Now()
	return r.db.WithContext(ctx).
		Model(&models.EmailVerificationToken{}).
		Where("email = ? AND is_used = ?", email, false).
		Updates(map[string]interface{}{
			"is_used": true,
			"used_at": now,
		}).Error
}

// CleanupExpiredTokens removes expired tokens from the database
func (r *emailVerificationTokenRepository) CleanupExpiredTokens(ctx context.Context) (int64, error) {
	result := r.db.WithContext(ctx).
		Where("expires_at < ?", time.Now()).
		Delete(&models.EmailVerificationToken{})
	return result.RowsAffected, result.Error
}

// GetTokenStats returns statistics about tokens for a user
func (r *emailVerificationTokenRepository) GetTokenStats(ctx context.Context, userID uint) (*models.TokenStats, error) {
	var stats models.TokenStats

	// Total tokens created
	err := r.db.WithContext(ctx).
		Model(&models.EmailVerificationToken{}).
		Where("user_id = ?", userID).
		Count(&stats.TotalCreated).Error
	if err != nil {
		return nil, err
	}

	// Used tokens
	err = r.db.WithContext(ctx).
		Model(&models.EmailVerificationToken{}).
		Where("user_id = ? AND is_used = ?", userID, true).
		Count(&stats.TotalUsed).Error
	if err != nil {
		return nil, err
	}

	// Expired tokens
	err = r.db.WithContext(ctx).
		Model(&models.EmailVerificationToken{}).
		Where("user_id = ? AND expires_at < ?", userID, time.Now()).
		Count(&stats.TotalExpired).Error
	if err != nil {
		return nil, err
	}

	// Active tokens
	err = r.db.WithContext(ctx).
		Model(&models.EmailVerificationToken{}).
		Where("user_id = ? AND is_used = ? AND expires_at > ?", userID, false, time.Now()).
		Count(&stats.TotalActive).Error
	if err != nil {
		return nil, err
	}

	// Total resends
	err = r.db.WithContext(ctx).
		Model(&models.EmailVerificationToken{}).
		Where("user_id = ?", userID).
		Select("COALESCE(SUM(resend_count), 0)").
		Scan(&stats.TotalResends).Error
	if err != nil {
		return nil, err
	}

	return &stats, nil
}

// GetRecentTokensForUser retrieves recent tokens for a user
func (r *emailVerificationTokenRepository) GetRecentTokensForUser(ctx context.Context, userID uint, limit int) ([]*models.EmailVerificationToken, error) {
	var tokens []*models.EmailVerificationToken
	err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Limit(limit).
		Find(&tokens).Error
	return tokens, err
}
