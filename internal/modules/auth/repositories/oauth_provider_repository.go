package repositories

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
)

// OAuthProviderRepository defines the interface for OAuth provider operations
type OAuthProviderRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, provider *models.OAuthProvider) error
	GetByID(ctx context.Context, id uint) (*models.OAuthProvider, error)
	GetByName(ctx context.Context, websiteID uint, name string) (*models.OAuthProvider, error)
	Update(ctx context.Context, provider *models.OAuthProvider) error
	Delete(ctx context.Context, id uint) error

	// Query operations
	GetByWebsite(ctx context.Context, websiteID uint) ([]*models.OAuthProvider, error)
	GetEnabledProviders(ctx context.Context, websiteID uint) ([]*models.OAuthProvider, error)
	IsProviderEnabled(ctx context.Context, websiteID uint, providerName string) (bool, error)

	// Configuration
	UpdateConfiguration(ctx context.Context, id uint, config models.OAuthProviderConfig) error
	UpdateStatus(ctx context.Context, id uint, enabled bool) error
}
