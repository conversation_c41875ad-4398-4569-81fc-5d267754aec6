package mysql

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	"gorm.io/gorm"
)

type passwordResetRepository struct {
	db *gorm.DB
}

// NewPasswordResetRepository creates a new MySQL password reset repository
func NewPasswordResetRepository(db *gorm.DB) repositories.PasswordResetRepository {
	return &passwordResetRepository{db: db}
}

// Create creates a new password reset record
func (r *passwordResetRepository) Create(ctx context.Context, reset *models.PasswordReset) error {
	if err := r.db.WithContext(ctx).Create(reset).Error; err != nil {
		return fmt.Errorf("failed to create password reset: %w", err)
	}
	return nil
}

// GetByToken retrieves a password reset by token
func (r *passwordResetRepository) GetByToken(ctx context.Context, token string) (*models.PasswordReset, error) {
	var reset models.PasswordReset
	if err := r.db.WithContext(ctx).
		Where("token = ? AND is_used = ? AND expires_at > ?", token, false, time.Now()).
		First(&reset).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get password reset by token: %w", err)
	}
	return &reset, nil
}

// GetByID retrieves a password reset by ID
func (r *passwordResetRepository) GetByID(ctx context.Context, id uint) (*models.PasswordReset, error) {
	var reset models.PasswordReset
	if err := r.db.WithContext(ctx).First(&reset, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get password reset by id: %w", err)
	}
	return &reset, nil
}

// Update updates a password reset record
func (r *passwordResetRepository) Update(ctx context.Context, reset *models.PasswordReset) error {
	if err := r.db.WithContext(ctx).Save(reset).Error; err != nil {
		return fmt.Errorf("failed to update password reset: %w", err)
	}
	return nil
}

// Delete deletes a password reset record
func (r *passwordResetRepository) Delete(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).Delete(&models.PasswordReset{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete password reset: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("password reset not found")
	}
	return nil
}

// InvalidateToken marks a token as used
func (r *passwordResetRepository) InvalidateToken(ctx context.Context, token string) error {
	now := time.Now()
	result := r.db.WithContext(ctx).
		Model(&models.PasswordReset{}).
		Where("token = ? AND is_used = ?", token, false).
		Updates(map[string]interface{}{
			"is_used": true,
			"used_at": &now,
		})

	if result.Error != nil {
		return fmt.Errorf("failed to invalidate token: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("token not found or already used")
	}
	return nil
}

// InvalidateAllUserTokens invalidates all tokens for a user
func (r *passwordResetRepository) InvalidateAllUserTokens(ctx context.Context, userID uint) error {
	if err := r.db.WithContext(ctx).
		Model(&models.PasswordReset{}).
		Where("user_id = ? AND is_used = ?", userID, false).
		Update("is_used", true).Error; err != nil {
		return fmt.Errorf("failed to invalidate all user tokens: %w", err)
	}
	return nil
}

// IsTokenValid checks if a token is valid and not expired
func (r *passwordResetRepository) IsTokenValid(ctx context.Context, token string) (bool, error) {
	var count int64
	now := time.Now()

	if err := r.db.WithContext(ctx).
		Model(&models.PasswordReset{}).
		Where("token = ? AND is_used = ? AND expires_at > ?", token, false, now).
		Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check token validity: %w", err)
	}

	return count > 0, nil
}

// GetActiveTokenByUser retrieves the active password reset token for a user
func (r *passwordResetRepository) GetActiveTokenByUser(ctx context.Context, userID uint) (*models.PasswordReset, error) {
	var reset models.PasswordReset
	now := time.Now()

	if err := r.db.WithContext(ctx).
		Where("user_id = ? AND is_used = ? AND expires_at > ?", userID, false, now).
		Order("created_at DESC").
		First(&reset).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get active token by user: %w", err)
	}
	return &reset, nil
}

// CountRecentRequests counts recent password reset requests for a user
func (r *passwordResetRepository) CountRecentRequests(ctx context.Context, userID uint, duration time.Duration) (int64, error) {
	var count int64
	since := time.Now().Add(-duration)

	if err := r.db.WithContext(ctx).
		Model(&models.PasswordReset{}).
		Where("user_id = ? AND created_at > ?", userID, since).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count recent requests: %w", err)
	}
	return count, nil
}

// GetRecentRequests retrieves recent password reset requests for a user
func (r *passwordResetRepository) GetRecentRequests(ctx context.Context, userID uint, limit int) ([]*models.PasswordReset, error) {
	var resets []*models.PasswordReset

	query := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&resets).Error; err != nil {
		return nil, fmt.Errorf("failed to get recent requests: %w", err)
	}
	return resets, nil
}

// CleanupExpiredTokens removes expired password reset tokens
func (r *passwordResetRepository) CleanupExpiredTokens(ctx context.Context) (int64, error) {
	now := time.Now()

	// Delete old expired or used tokens (older than 30 days)
	oldDate := now.AddDate(0, 0, -30)
	result := r.db.WithContext(ctx).
		Where("created_at < ? AND (expires_at <= ? OR is_used = ?)", oldDate, now, true).
		Delete(&models.PasswordReset{})

	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup expired tokens: %w", result.Error)
	}
	return result.RowsAffected, nil
}

// HasRecentRequest checks if there's a recent password reset request for an email
func (r *passwordResetRepository) HasRecentRequest(ctx context.Context, email string, duration time.Duration) (bool, error) {
	var count int64
	since := time.Now().Add(-duration)

	if err := r.db.WithContext(ctx).
		Model(&models.PasswordReset{}).
		Where("email = ? AND created_at > ?", email, since).
		Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check recent request: %w", err)
	}

	return count > 0, nil
}

// GetRequestsByIP retrieves password reset requests from a specific IP
func (r *passwordResetRepository) GetRequestsByIP(ctx context.Context, ip string, duration time.Duration) ([]*models.PasswordReset, error) {
	var resets []*models.PasswordReset
	since := time.Now().Add(-duration)

	if err := r.db.WithContext(ctx).
		Where("ip_address = ? AND created_at > ?", ip, since).
		Order("created_at DESC").
		Find(&resets).Error; err != nil {
		return nil, fmt.Errorf("failed to get requests by IP: %w", err)
	}
	return resets, nil
}
