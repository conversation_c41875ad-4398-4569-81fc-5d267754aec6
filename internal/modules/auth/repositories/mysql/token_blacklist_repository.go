package mysql

import (
	"context"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	"gorm.io/gorm"
)

type tokenBlacklistRepository struct {
	db *gorm.DB
}

// NewTokenBlacklistRepository creates a new MySQL token blacklist repository
func NewTokenBlacklistRepository(db *gorm.DB) repositories.TokenBlacklistRepository {
	return &tokenBlacklistRepository{db: db}
}

// Add adds a token to the blacklist with context information
func (r *tokenBlacklistRepository) Add(ctx context.Context, tokenID string, expiresAt time.Time, userID uint, reason models.TokenBlacklistReason) error {
	// Extract tenant/website ID from context
	tenantID := r.extractTenantIDFromContext(ctx)

	blacklist := &models.TokenBlacklist{
		TokenJTI:  tokenID,
		TokenType: "jwt",
		Reason:    reason,
		ExpiresAt: expiresAt,
		WebsiteID: tenantID, // Using tenant ID as website ID for consistency
		UserID:    userID,
	}

	result := r.db.WithContext(ctx).Create(blacklist)
	if result.Error != nil {
		return fmt.Errorf("failed to blacklist token: %w", result.Error)
	}

	return nil
}

// AddWithDetails adds a token to the blacklist with full details
func (r *tokenBlacklistRepository) AddWithDetails(ctx context.Context, blacklist *models.TokenBlacklist) error {
	// If website ID is not set, try to get it from context
	if blacklist.WebsiteID == 0 {
		blacklist.WebsiteID = r.extractTenantIDFromContext(ctx)
	}

	result := r.db.WithContext(ctx).Create(blacklist)
	if result.Error != nil {
		return fmt.Errorf("failed to blacklist token: %w", result.Error)
	}
	return nil
}

// Exists checks if a token is blacklisted
func (r *tokenBlacklistRepository) Exists(ctx context.Context, tokenID string) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).Model(&models.TokenBlacklist{}).
		Where("token_jti = ? AND expires_at > ?", tokenID, time.Now()).
		Count(&count).Error

	if err != nil {
		return false, fmt.Errorf("failed to check blacklist: %w", err)
	}

	return count > 0, nil
}

// Get retrieves a blacklist entry by token ID
func (r *tokenBlacklistRepository) Get(ctx context.Context, tokenID string) (*models.TokenBlacklist, error) {
	var blacklist models.TokenBlacklist
	err := r.db.WithContext(ctx).Where("token_jti = ?", tokenID).First(&blacklist).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get blacklist entry: %w", err)
	}

	return &blacklist, nil
}

// CleanupExpired removes expired tokens from the blacklist
func (r *tokenBlacklistRepository) CleanupExpired(ctx context.Context) (int64, error) {
	result := r.db.WithContext(ctx).Where("expires_at <= ?", time.Now()).Delete(&models.TokenBlacklist{})
	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup expired tokens: %w", result.Error)
	}

	return result.RowsAffected, nil
}

// GetAll retrieves all blacklisted tokens (for debugging/admin purposes)
func (r *tokenBlacklistRepository) GetAll(ctx context.Context) ([]*models.TokenBlacklist, error) {
	var blacklist []*models.TokenBlacklist
	err := r.db.WithContext(ctx).Where("expires_at > ?", time.Now()).
		Order("created_at DESC").
		Find(&blacklist).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get all blacklisted tokens: %w", err)
	}

	return blacklist, nil
}

// GetByUser retrieves all blacklisted tokens for a user
func (r *tokenBlacklistRepository) GetByUser(ctx context.Context, userID uint) ([]*models.TokenBlacklist, error) {
	var blacklist []*models.TokenBlacklist
	err := r.db.WithContext(ctx).Where("user_id = ? AND expires_at > ?", userID, time.Now()).
		Order("created_at DESC").
		Find(&blacklist).Error

	if err != nil {
		return nil, fmt.Errorf("failed to get user blacklisted tokens: %w", err)
	}

	return blacklist, nil
}

// Remove removes a token from the blacklist
func (r *tokenBlacklistRepository) Remove(ctx context.Context, tokenID string) error {
	result := r.db.WithContext(ctx).Where("token_jti = ?", tokenID).Delete(&models.TokenBlacklist{})
	if result.Error != nil {
		return fmt.Errorf("failed to remove from blacklist: %w", result.Error)
	}

	return nil
}

// extractTenantIDFromContext extracts tenant ID from context with fallback
func (r *tokenBlacklistRepository) extractTenantIDFromContext(ctx context.Context) uint {
	// Try to get tenant ID from context
	if tenantID, ok := ctx.Value("tenant_id").(uint); ok && tenantID > 0 {
		return tenantID
	}

	// Try to get tenant scope from context
	if tenantScope, ok := ctx.Value("tenant_scope").(uint); ok && tenantScope > 0 {
		return tenantScope
	}

	// Fallback to default tenant ID
	return 1
}
