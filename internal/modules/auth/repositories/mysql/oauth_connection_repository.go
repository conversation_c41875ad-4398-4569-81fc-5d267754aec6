package mysql

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	"gorm.io/gorm"
)

type oauthConnectionRepository struct {
	db *gorm.DB
}

// NewOAuthConnectionRepository creates a new MySQL OAuth connection repository
func NewOAuthConnectionRepository(db *gorm.DB) repositories.OAuthConnectionRepository {
	return &oauthConnectionRepository{db: db}
}

// Create creates a new OAuth connection
func (r *oauthConnectionRepository) Create(ctx context.Context, connection *models.OAuthConnection) error {
	if err := r.db.WithContext(ctx).Create(connection).Error; err != nil {
		return fmt.Errorf("failed to create OAuth connection: %w", err)
	}
	return nil
}

// GetByID retrieves an OAuth connection by ID
func (r *oauthConnectionRepository) GetByID(ctx context.Context, id uint) (*models.OAuthConnection, error) {
	var connection models.OAuthConnection
	if err := r.db.WithContext(ctx).First(&connection, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get OAuth connection by id: %w", err)
	}
	return &connection, nil
}

// GetByProviderID retrieves a connection by provider and provider user ID
func (r *oauthConnectionRepository) GetByProviderID(ctx context.Context, providerName, providerUserID string) (*models.OAuthConnection, error) {
	var connection models.OAuthConnection
	if err := r.db.WithContext(ctx).
		Where("provider_name = ? AND provider_user_id = ?", providerName, providerUserID).
		First(&connection).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get OAuth connection by provider ID: %w", err)
	}
	return &connection, nil
}

// Update updates an OAuth connection
func (r *oauthConnectionRepository) Update(ctx context.Context, connection *models.OAuthConnection) error {
	if err := r.db.WithContext(ctx).Save(connection).Error; err != nil {
		return fmt.Errorf("failed to update OAuth connection: %w", err)
	}
	return nil
}

// Delete deletes an OAuth connection
func (r *oauthConnectionRepository) Delete(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).Delete(&models.OAuthConnection{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete OAuth connection: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("OAuth connection not found")
	}
	return nil
}

// GetByUser retrieves all OAuth connections for a user
func (r *oauthConnectionRepository) GetByUser(ctx context.Context, userID uint) ([]*models.OAuthConnection, error) {
	var connections []*models.OAuthConnection
	if err := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC").
		Find(&connections).Error; err != nil {
		return nil, fmt.Errorf("failed to get OAuth connections by user: %w", err)
	}
	return connections, nil
}

// GetByUserAndProvider retrieves a user's connection for a specific provider
func (r *oauthConnectionRepository) GetByUserAndProvider(ctx context.Context, userID uint, providerName string) (*models.OAuthConnection, error) {
	var connection models.OAuthConnection
	if err := r.db.WithContext(ctx).
		Where("user_id = ? AND provider_name = ?", userID, providerName).
		First(&connection).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get OAuth connection by user and provider: %w", err)
	}
	return &connection, nil
}

// DisconnectProvider disconnects a provider for a user
func (r *oauthConnectionRepository) DisconnectProvider(ctx context.Context, userID uint, providerName string) error {
	result := r.db.WithContext(ctx).
		Where("user_id = ? AND provider_name = ?", userID, providerName).
		Delete(&models.OAuthConnection{})

	if result.Error != nil {
		return fmt.Errorf("failed to disconnect provider: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("connection not found")
	}
	return nil
}

// UpdateTokens updates OAuth tokens
func (r *oauthConnectionRepository) UpdateTokens(ctx context.Context, id uint, accessToken, refreshToken string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"access_token": accessToken,
		"last_used_at": now,
	}

	if refreshToken != "" {
		updates["refresh_token"] = refreshToken
	}

	if err := r.db.WithContext(ctx).
		Model(&models.OAuthConnection{}).
		Where("id = ?", id).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update tokens: %w", err)
	}
	return nil
}

// GetActiveConnection retrieves an active connection for a user and provider
func (r *oauthConnectionRepository) GetActiveConnection(ctx context.Context, userID uint, providerName string) (*models.OAuthConnection, error) {
	var connection models.OAuthConnection
	if err := r.db.WithContext(ctx).
		Where("user_id = ? AND provider_name = ? AND is_active = ?", userID, providerName, true).
		First(&connection).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get active connection: %w", err)
	}
	return &connection, nil
}

// CountUserConnections counts OAuth connections for a user
func (r *oauthConnectionRepository) CountUserConnections(ctx context.Context, userID uint) (int64, error) {
	var count int64
	if err := r.db.WithContext(ctx).
		Model(&models.OAuthConnection{}).
		Where("user_id = ?", userID).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count user connections: %w", err)
	}
	return count, nil
}

// HasConnection checks if a user has a connection with a provider
func (r *oauthConnectionRepository) HasConnection(ctx context.Context, userID uint, providerName string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).
		Model(&models.OAuthConnection{}).
		Where("user_id = ? AND provider_name = ?", userID, providerName).
		Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check connection: %w", err)
	}
	return count > 0, nil
}

// RevokeConnection revokes an OAuth connection
func (r *oauthConnectionRepository) RevokeConnection(ctx context.Context, id uint) error {
	now := time.Now()
	updates := map[string]interface{}{
		"is_active":  false,
		"revoked_at": &now,
	}

	if err := r.db.WithContext(ctx).
		Model(&models.OAuthConnection{}).
		Where("id = ?", id).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to revoke connection: %w", err)
	}
	return nil
}

// GetConnectionsByEmail retrieves all OAuth connections by email
func (r *oauthConnectionRepository) GetConnectionsByEmail(ctx context.Context, email string) ([]*models.OAuthConnection, error) {
	var connections []*models.OAuthConnection
	if err := r.db.WithContext(ctx).
		Where("provider_email = ?", email).
		Find(&connections).Error; err != nil {
		return nil, fmt.Errorf("failed to get connections by email: %w", err)
	}
	return connections, nil
}
