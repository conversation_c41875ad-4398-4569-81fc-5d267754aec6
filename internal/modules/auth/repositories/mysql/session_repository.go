package mysql

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	"gorm.io/gorm"
)

type sessionRepository struct {
	db *gorm.DB
}

// NewSessionRepository creates a new MySQL session repository
func NewSessionRepository(db *gorm.DB) repositories.SessionRepository {
	return &sessionRepository{db: db}
}

// Create creates a new session
func (r *sessionRepository) Create(ctx context.Context, session *models.Session) error {
	if err := r.db.WithContext(ctx).Create(session).Error; err != nil {
		return fmt.Errorf("failed to create session: %w", err)
	}
	return nil
}

// GetByID retrieves a session by ID
func (r *sessionRepository) GetByID(ctx context.Context, id uint) (*models.Session, error) {
	var session models.Session
	if err := r.db.WithContext(ctx).First(&session, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get session by id: %w", err)
	}
	return &session, nil
}

// GetByToken retrieves a session by token
func (r *sessionRepository) GetByToken(ctx context.Context, token string) (*models.Session, error) {
	var session models.Session
	if err := r.db.WithContext(ctx).Where("token = ?", token).First(&session).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get session by token: %w", err)
	}
	return &session, nil
}

// Update updates a session
func (r *sessionRepository) Update(ctx context.Context, session *models.Session) error {
	if err := r.db.WithContext(ctx).Save(session).Error; err != nil {
		return fmt.Errorf("failed to update session: %w", err)
	}
	return nil
}

// Delete deletes a session
func (r *sessionRepository) Delete(ctx context.Context, id uint) error {
	result := r.db.WithContext(ctx).Delete(&models.Session{}, id)
	if result.Error != nil {
		return fmt.Errorf("failed to delete session: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("session not found")
	}
	return nil
}

// GetActiveSessionsByUser retrieves all active sessions for a user
func (r *sessionRepository) GetActiveSessionsByUser(ctx context.Context, userID uint) ([]*models.Session, error) {
	var sessions []*models.Session
	now := time.Now()

	if err := r.db.WithContext(ctx).
		Where("user_id = ? AND is_revoked = ? AND expires_at > ?", userID, false, now).
		Order("last_used_at DESC").
		Find(&sessions).Error; err != nil {
		return nil, fmt.Errorf("failed to get active sessions by user: %w", err)
	}
	return sessions, nil
}

// GetActiveSessionsByWebsite retrieves all active sessions for a website
func (r *sessionRepository) GetActiveSessionsByWebsite(ctx context.Context, websiteID uint) ([]*models.Session, error) {
	var sessions []*models.Session
	now := time.Now()

	if err := r.db.WithContext(ctx).
		Where("website_id = ? AND is_revoked = ? AND expires_at > ?", websiteID, false, now).
		Order("last_used_at DESC").
		Find(&sessions).Error; err != nil {
		return nil, fmt.Errorf("failed to get active sessions by website: %w", err)
	}
	return sessions, nil
}

// InvalidateSession marks a session as revoked
func (r *sessionRepository) InvalidateSession(ctx context.Context, sessionID uint) error {
	result := r.db.WithContext(ctx).
		Model(&models.Session{}).
		Where("id = ?", sessionID).
		Update("is_revoked", true)

	if result.Error != nil {
		return fmt.Errorf("failed to invalidate session: %w", result.Error)
	}
	if result.RowsAffected == 0 {
		return fmt.Errorf("session not found")
	}
	return nil
}

// InvalidateAllUserSessions invalidates all sessions for a user
func (r *sessionRepository) InvalidateAllUserSessions(ctx context.Context, userID uint) error {
	if err := r.db.WithContext(ctx).
		Model(&models.Session{}).
		Where("user_id = ?", userID).
		Update("is_revoked", true).Error; err != nil {
		return fmt.Errorf("failed to invalidate all user sessions: %w", err)
	}
	return nil
}

// InvalidateAllUserSessionsExcept invalidates all user sessions except one
func (r *sessionRepository) InvalidateAllUserSessionsExcept(ctx context.Context, userID uint, exceptSessionID uint) error {
	if err := r.db.WithContext(ctx).
		Model(&models.Session{}).
		Where("user_id = ? AND id != ?", userID, exceptSessionID).
		Update("is_revoked", true).Error; err != nil {
		return fmt.Errorf("failed to invalidate user sessions: %w", err)
	}
	return nil
}

// UpdateLastActivity updates session's last activity
func (r *sessionRepository) UpdateLastActivity(ctx context.Context, sessionID uint, ip string, userAgent string) error {
	now := time.Now()
	updates := map[string]interface{}{
		"last_used_at": now,
		"ip_address":   ip,
	}

	if err := r.db.WithContext(ctx).
		Model(&models.Session{}).
		Where("id = ?", sessionID).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to update last activity: %w", err)
	}
	return nil
}

// ExtendExpiration extends session expiration time
func (r *sessionRepository) ExtendExpiration(ctx context.Context, sessionID uint, newExpiresAt time.Time) error {
	if err := r.db.WithContext(ctx).
		Model(&models.Session{}).
		Where("id = ?", sessionID).
		Update("expires_at", newExpiresAt).Error; err != nil {
		return fmt.Errorf("failed to extend expiration: %w", err)
	}
	return nil
}

// CleanupExpiredSessions removes expired sessions
func (r *sessionRepository) CleanupExpiredSessions(ctx context.Context) (int64, error) {
	now := time.Now()
	result := r.db.WithContext(ctx).
		Where("expires_at <= ?", now).
		Delete(&models.Session{})

	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup expired sessions: %w", result.Error)
	}
	return result.RowsAffected, nil
}

// CleanupInactiveSessions removes sessions that have been inactive for a duration
func (r *sessionRepository) CleanupInactiveSessions(ctx context.Context, inactiveDuration time.Duration) (int64, error) {
	cutoffTime := time.Now().Add(-inactiveDuration)
	result := r.db.WithContext(ctx).
		Where("last_used_at < ? OR (last_used_at IS NULL AND created_at < ?)", cutoffTime, cutoffTime).
		Delete(&models.Session{})

	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup inactive sessions: %w", result.Error)
	}
	return result.RowsAffected, nil
}

// CountActiveSessions counts active sessions for a website
func (r *sessionRepository) CountActiveSessions(ctx context.Context, websiteID uint) (int64, error) {
	var count int64
	now := time.Now()

	if err := r.db.WithContext(ctx).
		Model(&models.Session{}).
		Where("website_id = ? AND is_revoked = ? AND expires_at > ?", websiteID, false, now).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count active sessions: %w", err)
	}
	return count, nil
}

// CountActiveUserSessions counts active sessions for a user
func (r *sessionRepository) CountActiveUserSessions(ctx context.Context, userID uint) (int64, error) {
	var count int64
	now := time.Now()

	if err := r.db.WithContext(ctx).
		Model(&models.Session{}).
		Where("user_id = ? AND is_revoked = ? AND expires_at > ?", userID, false, now).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count active user sessions: %w", err)
	}
	return count, nil
}

// GetSessionsByFilters retrieves sessions based on filters
func (r *sessionRepository) GetSessionsByFilters(ctx context.Context, filters *repositories.SessionFilters) ([]*models.Session, error) {
	var sessions []*models.Session

	db := r.db.WithContext(ctx).Model(&models.Session{})

	// Apply filters
	if filters != nil {
		if filters.UserID != nil {
			db = db.Where("user_id = ?", *filters.UserID)
		}
		if filters.WebsiteID != nil {
			db = db.Where("website_id = ?", *filters.WebsiteID)
		}
		if filters.IsActive != nil {
			// Convert IsActive logic to IsRevoked logic
			db = db.Where("is_revoked = ?", !*filters.IsActive)
		}
		if filters.IPAddress != nil {
			db = db.Where("ip_address = ?", *filters.IPAddress)
		}
		if filters.DeviceType != nil {
			db = db.Where("device_type = ?", *filters.DeviceType)
		}
		if filters.CreatedAfter != nil {
			db = db.Where("created_at > ?", *filters.CreatedAfter)
		}
		if filters.CreatedBefore != nil {
			db = db.Where("created_at < ?", *filters.CreatedBefore)
		}
		if filters.ExpiresAfter != nil {
			db = db.Where("expires_at > ?", *filters.ExpiresAfter)
		}
		if filters.ExpiresBefore != nil {
			db = db.Where("expires_at < ?", *filters.ExpiresBefore)
		}

		// Apply pagination
		if filters.Limit > 0 {
			db = db.Limit(filters.Limit)
		}
		if filters.Offset > 0 {
			db = db.Offset(filters.Offset)
		}
	}

	// Default ordering
	db = db.Order("created_at DESC")

	if err := db.Find(&sessions).Error; err != nil {
		return nil, fmt.Errorf("failed to get sessions by filters: %w", err)
	}

	return sessions, nil
}

// GetSessionsByIP retrieves sessions by IP address
func (r *sessionRepository) GetSessionsByIP(ctx context.Context, ip string, limit int) ([]*models.Session, error) {
	var sessions []*models.Session

	query := r.db.WithContext(ctx).
		Where("ip_address = ?", ip).
		Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&sessions).Error; err != nil {
		return nil, fmt.Errorf("failed to get sessions by IP: %w", err)
	}
	return sessions, nil
}

// GetRecentSessions retrieves recent sessions for a user
func (r *sessionRepository) GetRecentSessions(ctx context.Context, userID uint, limit int) ([]*models.Session, error) {
	var sessions []*models.Session

	query := r.db.WithContext(ctx).
		Where("user_id = ?", userID).
		Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&sessions).Error; err != nil {
		return nil, fmt.Errorf("failed to get recent sessions: %w", err)
	}
	return sessions, nil
}
