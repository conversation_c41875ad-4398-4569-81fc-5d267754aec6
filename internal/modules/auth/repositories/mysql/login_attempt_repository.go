package mysql

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/auth/repositories"
	"gorm.io/gorm"
)

type loginAttemptRepository struct {
	db *gorm.DB
}

// NewLoginAttemptRepository creates a new MySQL login attempt repository
func NewLoginAttemptRepository(db *gorm.DB) repositories.LoginAttemptRepository {
	return &loginAttemptRepository{db: db}
}

// Create creates a new login attempt record
func (r *loginAttemptRepository) Create(ctx context.Context, attempt *models.LoginAttempt) error {
	if err := r.db.WithContext(ctx).Create(attempt).Error; err != nil {
		return fmt.Errorf("failed to create login attempt: %w", err)
	}
	return nil
}

// GetByID retrieves a login attempt by ID
func (r *loginAttemptRepository) GetByID(ctx context.Context, id uint) (*models.LoginAttempt, error) {
	var attempt models.LoginAttempt
	if err := r.db.WithContext(ctx).First(&attempt, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get login attempt: %w", err)
	}
	return &attempt, nil
}

// CountRecentAttempts counts recent login attempts for an identifier (email/username)
func (r *loginAttemptRepository) CountRecentAttempts(ctx context.Context, identifier string, duration time.Duration) (int64, error) {
	var count int64
	since := time.Now().Add(-duration)

	if err := r.db.WithContext(ctx).
		Model(&models.LoginAttempt{}).
		Where("email = ? AND created_at > ? AND result = ?", identifier, since, models.AttemptResultFailure).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count recent attempts: %w", err)
	}
	return count, nil
}

// CountRecentAttemptsbyIP counts recent login attempts from an IP
func (r *loginAttemptRepository) CountRecentAttemptsbyIP(ctx context.Context, ip string, duration time.Duration) (int64, error) {
	var count int64
	since := time.Now().Add(-duration)

	if err := r.db.WithContext(ctx).
		Model(&models.LoginAttempt{}).
		Where("ip_address = ? AND created_at > ? AND result = ?", ip, since, models.AttemptResultFailure).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count recent attempts by IP: %w", err)
	}
	return count, nil
}

// GetRecentAttempts retrieves recent login attempts for an identifier
func (r *loginAttemptRepository) GetRecentAttempts(ctx context.Context, identifier string, duration time.Duration) ([]*models.LoginAttempt, error) {
	var attempts []*models.LoginAttempt
	since := time.Now().Add(-duration)

	if err := r.db.WithContext(ctx).
		Where("email = ? AND created_at > ?", identifier, since).
		Order("created_at DESC").
		Find(&attempts).Error; err != nil {
		return nil, fmt.Errorf("failed to get recent attempts: %w", err)
	}
	return attempts, nil
}

// IsAccountLocked checks if an account is locked
func (r *loginAttemptRepository) IsAccountLocked(ctx context.Context, identifier string) (bool, time.Time, error) {
	// Check for account lock by counting recent failed attempts
	// This is a simplified implementation - in production, you might want a separate locks table
	count, err := r.CountRecentAttempts(ctx, identifier, 15*time.Minute)
	if err != nil {
		return false, time.Time{}, fmt.Errorf("failed to count recent attempts: %w", err)
	}

	// If there are 5 or more failed attempts in the last 15 minutes, consider account locked
	if count >= 5 {
		return true, time.Now().Add(15 * time.Minute), nil
	}

	return false, time.Time{}, nil
}

// LockAccount locks an account until a specific time
func (r *loginAttemptRepository) LockAccount(ctx context.Context, identifier string, until time.Time) error {
	// Create a blocked attempt record to indicate account is locked
	attempt := &models.LoginAttempt{
		Email:  identifier,
		Result: models.AttemptResultBlocked,
	}

	if err := r.db.WithContext(ctx).Create(attempt).Error; err != nil {
		return fmt.Errorf("failed to lock account: %w", err)
	}
	return nil
}

// UnlockAccount unlocks an account
func (r *loginAttemptRepository) UnlockAccount(ctx context.Context, identifier string) error {
	// This is a no-op in our current implementation since locks are time-based
	// In a more complex system, you might delete blocked records or update a separate locks table
	return nil
}

// RecordSuccessfulLogin records a successful login
func (r *loginAttemptRepository) RecordSuccessfulLogin(ctx context.Context, userID uint, ip string) error {
	attempt := &models.LoginAttempt{
		UserID:    &userID,
		IPAddress: &ip,
		Email:     "", // Email would need to be passed or looked up
		Result:    models.AttemptResultSuccess,
	}

	if err := r.db.WithContext(ctx).Create(attempt).Error; err != nil {
		return fmt.Errorf("failed to record successful login: %w", err)
	}
	return nil
}

// GetLastSuccessfulLogin gets the last successful login for a user
func (r *loginAttemptRepository) GetLastSuccessfulLogin(ctx context.Context, userID uint) (*models.LoginAttempt, error) {
	var attempt models.LoginAttempt
	if err := r.db.WithContext(ctx).
		Where("user_id = ? AND success = ?", userID, true).
		Order("attempted_at DESC").
		First(&attempt).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, fmt.Errorf("failed to get last successful login: %w", err)
	}
	return &attempt, nil
}

// GetFailedAttemptsByUser retrieves failed login attempts for a user
func (r *loginAttemptRepository) GetFailedAttemptsByUser(ctx context.Context, userID uint, limit int) ([]*models.LoginAttempt, error) {
	var attempts []*models.LoginAttempt

	query := r.db.WithContext(ctx).
		Where("user_id = ? AND success = ?", userID, false).
		Order("attempted_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&attempts).Error; err != nil {
		return nil, fmt.Errorf("failed to get failed attempts by user: %w", err)
	}
	return attempts, nil
}

// GetFailedAttemptsByIP retrieves failed login attempts from an IP
func (r *loginAttemptRepository) GetFailedAttemptsByIP(ctx context.Context, ip string, limit int) ([]*models.LoginAttempt, error) {
	var attempts []*models.LoginAttempt

	query := r.db.WithContext(ctx).
		Where("ip_address = ? AND success = ?", ip, false).
		Order("attempted_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&attempts).Error; err != nil {
		return nil, fmt.Errorf("failed to get failed attempts by IP: %w", err)
	}
	return attempts, nil
}

// CountFailedAttemptsByUser counts failed login attempts for a user within a duration
func (r *loginAttemptRepository) CountFailedAttemptsByUser(ctx context.Context, userID uint, duration time.Duration) (int64, error) {
	var count int64
	since := time.Now().Add(-duration)

	if err := r.db.WithContext(ctx).
		Model(&models.LoginAttempt{}).
		Where("user_id = ? AND attempted_at > ? AND success = ?", userID, since, false).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("failed to count failed attempts by user: %w", err)
	}
	return count, nil
}

// CleanupOldAttempts removes old login attempt records
func (r *loginAttemptRepository) CleanupOldAttempts(ctx context.Context, olderThan time.Time) (int64, error) {
	result := r.db.WithContext(ctx).
		Where("attempted_at < ?", olderThan).
		Delete(&models.LoginAttempt{})

	if result.Error != nil {
		return 0, fmt.Errorf("failed to cleanup old attempts: %w", result.Error)
	}
	return result.RowsAffected, nil
}

// GetSuspiciousIPs retrieves IPs with suspicious activity
func (r *loginAttemptRepository) GetSuspiciousIPs(ctx context.Context, threshold int, duration time.Duration) ([]string, error) {
	var ips []string
	since := time.Now().Add(-duration)

	// Query to find IPs with more than threshold failed attempts
	if err := r.db.WithContext(ctx).
		Model(&models.LoginAttempt{}).
		Select("ip_address").
		Where("attempted_at > ? AND success = ?", since, false).
		Group("ip_address").
		Having("COUNT(*) > ?", threshold).
		Pluck("ip_address", &ips).Error; err != nil {
		return nil, fmt.Errorf("failed to get suspicious IPs: %w", err)
	}

	return ips, nil
}

// GetSuspiciousPatterns analyzes login patterns for suspicious activity
func (r *loginAttemptRepository) GetSuspiciousPatterns(ctx context.Context, websiteID uint) ([]*repositories.LoginPattern, error) {
	// This is a simplified implementation
	// In production, you might want more sophisticated pattern detection

	var patterns []*repositories.LoginPattern

	// Example: Find IPs with multiple failed attempts across different identifiers
	type ipPattern struct {
		IPAddress string
		Count     int
		FirstSeen time.Time
		LastSeen  time.Time
	}

	var ipPatterns []ipPattern

	// Get IPs with multiple failed attempts in the last 24 hours
	since := time.Now().Add(-24 * time.Hour)

	err := r.db.WithContext(ctx).
		Model(&models.LoginAttempt{}).
		Select("ip_address, COUNT(DISTINCT identifier) as count, MIN(attempted_at) as first_seen, MAX(attempted_at) as last_seen").
		Where("website_id = ? AND attempted_at > ? AND success = ?", websiteID, since, false).
		Group("ip_address").
		Having("COUNT(DISTINCT identifier) > ?", 3). // More than 3 different identifiers
		Scan(&ipPatterns).Error

	if err != nil {
		return nil, fmt.Errorf("failed to analyze suspicious patterns: %w", err)
	}

	// Convert to LoginPattern
	for _, ip := range ipPatterns {
		pattern := &repositories.LoginPattern{
			Pattern:     fmt.Sprintf("Multiple identifier attempts from IP %s", ip.IPAddress),
			Count:       ip.Count,
			FirstSeen:   ip.FirstSeen,
			LastSeen:    ip.LastSeen,
			IPAddresses: []string{ip.IPAddress},
		}
		patterns = append(patterns, pattern)
	}

	return patterns, nil
}
