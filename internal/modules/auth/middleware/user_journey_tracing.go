package middleware

import (
	"context"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/trace"
)

const (
	// Context keys for user journey tracking
	UserJourneyIDKey   = "user_journey_id"
	UserJourneyStepKey = "user_journey_step"
	UserJourneyTypeKey = "user_journey_type"

	// Journey types
	JourneyTypeRegistration = "registration"
	JourneyTypeOnboarding   = "onboarding"

	// Journey steps
	StepRegister           = "register"
	StepEmailVerification  = "email_verification"
	StepOnboardingStart    = "onboarding_start"
	StepCreateOrganization = "create_organization"
	StepOnboardingComplete = "onboarding_complete"
)

// UserJourneyTracingMiddleware creates a middleware that tracks user journey with distributed tracing
func UserJourneyTracingMiddleware() gin.HandlerFunc {
	tracer := otel.Tracer("user-journey-middleware")

	return func(c *gin.Context) {
		ctx := c.Request.Context()

		// Generate or extract user journey ID
		journeyID := extractOrGenerateJourneyID(c)

		// Determine journey type and step based on endpoint
		journeyType, journeyStep := determineJourneyTypeAndStep(c.Request.URL.Path)

		// Start a new span for this step of the journey
		spanName := fmt.Sprintf("user_journey.%s.%s", journeyType, journeyStep)
		ctx, span := tracer.Start(ctx, spanName)
		defer span.End()

		// Add journey attributes to span
		span.SetAttributes(
			attribute.String("user_journey.id", journeyID),
			attribute.String("user_journey.type", journeyType),
			attribute.String("user_journey.step", journeyStep),
			attribute.String("user_journey.endpoint", c.Request.URL.Path),
			attribute.String("http.method", c.Request.Method),
			attribute.String("http.url", c.Request.URL.String()),
			attribute.String("http.user_agent", c.Request.UserAgent()),
			attribute.String("http.remote_addr", c.ClientIP()),
		)

		// Add journey ID to context for handlers to use
		ctx = context.WithValue(ctx, UserJourneyIDKey, journeyID)
		ctx = context.WithValue(ctx, UserJourneyStepKey, journeyStep)
		ctx = context.WithValue(ctx, UserJourneyTypeKey, journeyType)

		// Update gin context with the new context
		c.Request = c.Request.WithContext(ctx)

		// Set response headers for journey tracking
		c.Header("X-User-Journey-ID", journeyID)
		c.Header("X-User-Journey-Step", journeyStep)
		c.Header("X-User-Journey-Type", journeyType)

		// Continue with the request
		c.Next()

		// Add response status to span
		span.SetAttributes(
			attribute.Int("http.status_code", c.Writer.Status()),
			attribute.String("http.status_class", fmt.Sprintf("%dxx", c.Writer.Status()/100)),
		)

		// Add success/failure information
		if c.Writer.Status() >= 200 && c.Writer.Status() < 300 {
			span.SetAttributes(attribute.Bool("user_journey.step_success", true))
		} else {
			span.SetAttributes(attribute.Bool("user_journey.step_success", false))
		}
	}
}

// extractOrGenerateJourneyID extracts journey ID from headers or generates a new one
func extractOrGenerateJourneyID(c *gin.Context) string {
	// Try to get journey ID from header (for continuing journeys)
	journeyID := c.GetHeader("X-User-Journey-ID")
	if journeyID != "" {
		return journeyID
	}

	// Try to get from query parameter
	journeyID = c.Query("journey_id")
	if journeyID != "" {
		return journeyID
	}

	// Generate new journey ID for new journeys
	return uuid.New().String()
}

// determineJourneyTypeAndStep determines the journey type and step based on the endpoint
func determineJourneyTypeAndStep(path string) (string, string) {
	switch {
	case strings.HasPrefix(path, "/auth/register"):
		return JourneyTypeRegistration, StepRegister
	case strings.HasPrefix(path, "/auth/verify-email"):
		return JourneyTypeRegistration, StepEmailVerification
	case strings.HasPrefix(path, "/auth/resend-verification"):
		return JourneyTypeRegistration, StepEmailVerification
	case strings.HasPrefix(path, "/onboarding/create-organization"):
		return JourneyTypeOnboarding, StepCreateOrganization
	case strings.HasPrefix(path, "/onboarding/organization-status"):
		return JourneyTypeOnboarding, StepOnboardingStart
	case strings.HasPrefix(path, "/onboarding/"):
		return JourneyTypeOnboarding, StepOnboardingStart
	default:
		return "unknown", "unknown"
	}
}

// GetUserJourneyID extracts user journey ID from context
func GetUserJourneyID(ctx context.Context) string {
	if journeyID, ok := ctx.Value(UserJourneyIDKey).(string); ok {
		return journeyID
	}
	return ""
}

// GetUserJourneyStep extracts user journey step from context
func GetUserJourneyStep(ctx context.Context) string {
	if step, ok := ctx.Value(UserJourneyStepKey).(string); ok {
		return step
	}
	return ""
}

// GetUserJourneyType extracts user journey type from context
func GetUserJourneyType(ctx context.Context) string {
	if journeyType, ok := ctx.Value(UserJourneyTypeKey).(string); ok {
		return journeyType
	}
	return ""
}

// AddJourneyEvent adds a custom event to the current span with journey context
func AddJourneyEvent(ctx context.Context, eventName string, attributes ...attribute.KeyValue) {
	span := trace.SpanFromContext(ctx)
	if span.IsRecording() {
		// Add journey context to event attributes
		journeyAttrs := []attribute.KeyValue{
			attribute.String("user_journey.id", GetUserJourneyID(ctx)),
			attribute.String("user_journey.step", GetUserJourneyStep(ctx)),
			attribute.String("user_journey.type", GetUserJourneyType(ctx)),
		}

		// Combine journey attributes with custom attributes
		allAttrs := append(journeyAttrs, attributes...)
		span.AddEvent(eventName, trace.WithAttributes(allAttrs...))
	}
}
