package models

import (
	"database/sql/driver"
	"time"
)

// DeviceType represents the type of device
// @Enum mobile,desktop,tablet,unknown
type DeviceType string

const (
	DeviceTypeMobile  DeviceType = "mobile"
	DeviceTypeDesktop DeviceType = "desktop"
	DeviceTypeTablet  DeviceType = "tablet"
	DeviceTypeUnknown DeviceType = "unknown"
)

// Scan implements sql.Scanner interface
func (d *DeviceType) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*d = DeviceType(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (d DeviceType) Value() (driver.Value, error) {
	return string(d), nil
}

// Session represents a user session
type Session struct {
	ID       uint `json:"id" gorm:"primaryKey"`
	TenantID uint `json:"tenant_id" gorm:"not null;index"`
	UserID   uint `json:"user_id" gorm:"not null;index"`

	// Session Information
	Token string `json:"-" gorm:"type:varchar(255);not null;uniqueIndex"` // Don't expose token in JSON

	// Device Information
	DeviceName *string    `json:"device_name,omitempty"`
	DeviceType DeviceType `json:"device_type" gorm:"type:enum('mobile','desktop','tablet','unknown');default:'unknown'"`
	OS         *string    `json:"os,omitempty"`
	Browser    *string    `json:"browser,omitempty"`
	DeviceID   *string    `json:"device_id,omitempty" gorm:"index"`

	// Location Information
	IPAddress *string `json:"ip_address,omitempty"`
	Country   *string `json:"country,omitempty"`
	City      *string `json:"city,omitempty"`

	// Session State
	IsRevoked  bool       `json:"is_revoked" gorm:"default:false;index"`
	LastUsedAt *time.Time `json:"last_used_at" gorm:"index"`
	ExpiresAt  time.Time  `json:"expires_at" gorm:"not null;index"`

	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations (loaded separately to avoid circular references)
	// Website *models.Website `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
	// User    *User           `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableName specifies the table name for Session
func (Session) TableName() string {
	return "auth_sessions"
}

// IsExpired checks if session is expired
func (s *Session) IsExpired() bool {
	return time.Now().After(s.ExpiresAt)
}

// IsActive checks if session is active (not revoked and not expired)
func (s *Session) IsActive() bool {
	return !s.IsRevoked && !s.IsExpired()
}

// IsValid checks if session is valid for use
func (s *Session) IsValid() bool {
	return s.IsActive()
}

// GetDeviceDescription returns a human-readable device description
func (s *Session) GetDeviceDescription() string {
	if s.DeviceName != nil && *s.DeviceName != "" {
		return *s.DeviceName
	}

	description := ""
	if s.OS != nil && *s.OS != "" {
		description = *s.OS
	}
	if s.Browser != nil && *s.Browser != "" {
		if description != "" {
			description += " - "
		}
		description += *s.Browser
	}
	if description == "" {
		description = string(s.DeviceType)
	}

	return description
}

// UpdateLastUsed updates the last used timestamp
func (s *Session) UpdateLastUsed() {
	now := time.Now()
	s.LastUsedAt = &now
}

// Revoke marks the session as revoked
func (s *Session) Revoke() {
	s.IsRevoked = true
}

// SessionFilter represents filters for querying sessions
type SessionFilter struct {
	TenantID   uint       `json:"tenant_id,omitempty"`
	UserID     uint       `json:"user_id,omitempty"`
	DeviceType DeviceType `json:"device_type,omitempty"`
	IsRevoked  *bool      `json:"is_revoked,omitempty"`
	IsExpired  *bool      `json:"is_expired,omitempty"`
	DeviceID   string     `json:"device_id,omitempty"`
	IPAddress  string     `json:"ip_address,omitempty"`
	Page       int        `json:"page,omitempty"`
	PageSize   int        `json:"page_size,omitempty"`
	SortBy     string     `json:"sort_by,omitempty"`
	SortOrder  string     `json:"sort_order,omitempty"`
}

// CreateSessionRequest represents the request to create a session
type CreateSessionRequest struct {
	TenantID   uint        `json:"tenant_id" validate:"required,min=1"`
	UserID     uint        `json:"user_id" validate:"required,min=1"`
	DeviceInfo *DeviceInfo `json:"device_info,omitempty"`
	IPAddress  *string     `json:"ip_address,omitempty" validate:"omitempty,ip"`
	Country    *string     `json:"country,omitempty"`
	City       *string     `json:"city,omitempty"`
	RememberMe bool        `json:"remember_me"` // For setting expiration time
}

// SessionData represents the session response with safe information
type SessionData struct {
	ID         uint       `json:"id"`
	DeviceName *string    `json:"device_name,omitempty"`
	DeviceType DeviceType `json:"device_type"`
	OS         *string    `json:"os,omitempty"`
	Browser    *string    `json:"browser,omitempty"`
	IPAddress  *string    `json:"ip_address,omitempty"`
	Country    *string    `json:"country,omitempty"`
	City       *string    `json:"city,omitempty"`
	IsRevoked  bool       `json:"is_revoked"`
	LastUsedAt *time.Time `json:"last_used_at,omitempty"`
	ExpiresAt  time.Time  `json:"expires_at"`
	CreatedAt  time.Time  `json:"created_at"`
	IsCurrent  bool       `json:"is_current,omitempty"` // Set when this is the current session
	IsExpired  bool       `json:"is_expired"`           // Computed field
	DeviceDesc string     `json:"device_description"`   // Computed field
}

// ToResponse converts a Session to SessionData
func (s *Session) ToResponse(isCurrent bool) *SessionData {
	return &SessionData{
		ID:         s.ID,
		DeviceName: s.DeviceName,
		DeviceType: s.DeviceType,
		OS:         s.OS,
		Browser:    s.Browser,
		IPAddress:  s.IPAddress,
		Country:    s.Country,
		City:       s.City,
		IsRevoked:  s.IsRevoked,
		LastUsedAt: s.LastUsedAt,
		ExpiresAt:  s.ExpiresAt,
		CreatedAt:  s.CreatedAt,
		IsCurrent:  isCurrent,
		IsExpired:  s.IsExpired(),
		DeviceDesc: s.GetDeviceDescription(),
	}
}
