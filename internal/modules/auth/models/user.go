package models

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// UserStatus represents the status of a user
// @Enum active,suspended,inactive,pending_verification,deleted
type UserStatus string

const (
	UserStatusActive              UserStatus = "active"
	UserStatusSuspended           UserStatus = "suspended"
	UserStatusInactive            UserStatus = "inactive"
	UserStatusPendingVerification UserStatus = "pending_verification"
	UserStatusDeleted             UserStatus = "deleted"
)

// Scan implements sql.Scanner interface
func (s *UserStatus) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*s = UserStatus(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (s UserStatus) Value() (driver.Value, error) {
	return string(s), nil
}

// UserRole represents the basic role of a user
// @Enum admin,user,guest
type UserRole string

const (
	UserRoleAdmin UserRole = "admin"
	UserRoleUser  UserRole = "user"
	UserRoleGuest UserRole = "guest"
)

// Scan implements sql.Scanner interface
func (r *UserRole) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*r = UserRole(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (r UserRole) Value() (driver.Value, error) {
	return string(r), nil
}

// RecoveryCodes represents an array of recovery codes
type RecoveryCodes []string

// Scan implements sql.Scanner interface
func (rc *RecoveryCodes) Scan(value interface{}) error {
	if value == nil {
		*rc = make(RecoveryCodes, 0)
		return nil
	}

	bytes, ok := value.([]byte)
	if !ok {
		return nil
	}

	return json.Unmarshal(bytes, rc)
}

// Value implements driver.Valuer interface
func (rc RecoveryCodes) Value() (driver.Value, error) {
	if rc == nil {
		return "[]", nil
	}
	return json.Marshal(rc)
}

// User represents a user in the system
type User struct {
	ID       uint `json:"id" gorm:"primaryKey"`
	TenantID uint `json:"tenant_id" gorm:"not null;index"`

	// Basic Information
	Email       string  `json:"email" gorm:"not null" validate:"required,email"`
	Username    *string `json:"username,omitempty" validate:"omitempty,min=3,max=30,alphanum"`
	FirstName   *string `json:"first_name,omitempty" validate:"omitempty,max=255"`
	LastName    *string `json:"last_name,omitempty" validate:"omitempty,max=255"`
	DisplayName *string `json:"display_name,omitempty" validate:"omitempty,max=255"`

	// Authentication
	PasswordHash    string     `json:"-" gorm:"not null"`
	EmailVerified   bool       `json:"email_verified" gorm:"default:false"`
	EmailVerifiedAt *time.Time `json:"email_verified_at,omitempty"`

	// Status and Configuration
	Status UserStatus `json:"status" gorm:"type:enum('active','suspended','inactive','pending_verification','deleted');default:'active';not null"`
	Role   UserRole   `json:"role" gorm:"type:enum('admin','user','guest');default:'user';not null"`

	// Contact Information
	Phone           *string    `json:"phone,omitempty" validate:"omitempty,e164"`
	PhoneVerified   bool       `json:"phone_verified" gorm:"default:false"`
	PhoneVerifiedAt *time.Time `json:"phone_verified_at,omitempty"`

	// Profile Information
	AvatarURL *string `json:"avatar_url,omitempty" validate:"omitempty,url"`
	Timezone  string  `json:"timezone" gorm:"default:'UTC';not null" validate:"timezone"`
	Language  string  `json:"language" gorm:"default:'en';not null" validate:"required,len=2"`

	// Security
	TwoFactorEnabled bool          `json:"two_factor_enabled" gorm:"default:false"`
	TwoFactorSecret  *string       `json:"-" gorm:"column:two_factor_secret"`
	RecoveryCodes    RecoveryCodes `json:"-" gorm:"type:json"`

	// Activity Tracking
	LastLoginAt *time.Time `json:"last_login_at,omitempty"`
	LastLoginIP *string    `json:"last_login_ip,omitempty"`
	LoginCount  uint       `json:"login_count" gorm:"default:0"`

	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// Associations (loaded separately to avoid circular references)
	// Tenant     *models.Tenant     `json:"tenant,omitempty" gorm:"foreignKey:TenantID"`
	// Sessions   []Session          `json:"sessions,omitempty" gorm:"foreignKey:UserID"`
	// Tokens     []Token            `json:"tokens,omitempty" gorm:"foreignKey:UserID"`
}

// TableName specifies the table name for User
func (User) TableName() string {
	return "users"
}

// IsActive checks if user is active
func (u *User) IsActive() bool {
	return u.Status == UserStatusActive
}

// IsVerified checks if user's email is verified
func (u *User) IsVerified() bool {
	return u.EmailVerified
}

// IsDeleted checks if user is soft deleted
func (u *User) IsDeleted() bool {
	return u.Status == UserStatusDeleted
}

// CanLogin checks if user can login
func (u *User) CanLogin() bool {
	return u.IsActive() && u.IsVerified()
}

// HasTwoFactorEnabled checks if two-factor authentication is enabled
func (u *User) HasTwoFactorEnabled() bool {
	return u.TwoFactorEnabled && u.TwoFactorSecret != nil
}

// GetFullName returns the user's full name
func (u *User) GetFullName() string {
	if u.DisplayName != nil && *u.DisplayName != "" {
		return *u.DisplayName
	}

	var fullName string
	if u.FirstName != nil {
		fullName = *u.FirstName
	}
	if u.LastName != nil {
		if fullName != "" {
			fullName += " "
		}
		fullName += *u.LastName
	}

	if fullName == "" {
		if u.Username != nil {
			return *u.Username
		}
		return u.Email
	}

	return fullName
}

// BeforeCreate hook for User
func (u *User) BeforeCreate(tx *gorm.DB) error {
	if u.RecoveryCodes == nil {
		u.RecoveryCodes = make(RecoveryCodes, 0)
	}
	return nil
}

// UserFilter represents filters for querying users
type UserFilter struct {
	TenantID         uint       `json:"tenant_id,omitempty"`
	Status           UserStatus `json:"status,omitempty"`
	Role             UserRole   `json:"role,omitempty"`
	EmailVerified    *bool      `json:"email_verified,omitempty"`
	TwoFactorEnabled *bool      `json:"two_factor_enabled,omitempty"`
	Search           string     `json:"search,omitempty"`
	Page             int        `json:"page,omitempty"`
	PageSize         int        `json:"page_size,omitempty"`
	SortBy           string     `json:"sort_by,omitempty"`
	SortOrder        string     `json:"sort_order,omitempty"`
}

// UserCreateRequest represents the request to create a user
type UserCreateRequest struct {
	TenantID    uint     `json:"tenant_id" validate:"required,min=1"`
	Email       string   `json:"email" validate:"required,email"`
	Username    *string  `json:"username,omitempty" validate:"omitempty,min=3,max=30,alphanum"`
	FirstName   *string  `json:"first_name,omitempty" validate:"omitempty,max=255"`
	LastName    *string  `json:"last_name,omitempty" validate:"omitempty,max=255"`
	DisplayName *string  `json:"display_name,omitempty" validate:"omitempty,max=255"`
	Password    string   `json:"password" validate:"required,min=8,max=128"`
	Role        UserRole `json:"role,omitempty" validate:"omitempty,oneof=admin user guest"`
	Phone       *string  `json:"phone,omitempty" validate:"omitempty,e164"`
	AvatarURL   *string  `json:"avatar_url,omitempty" validate:"omitempty,url"`
	Timezone    string   `json:"timezone,omitempty" validate:"omitempty,timezone"`
	Language    string   `json:"language,omitempty" validate:"omitempty,len=2"`
}

// UserUpdateRequest represents the request to update a user
type UserUpdateRequest struct {
	Email       *string     `json:"email,omitempty" validate:"omitempty,email"`
	Username    *string     `json:"username,omitempty" validate:"omitempty,min=3,max=30,alphanum"`
	FirstName   *string     `json:"first_name,omitempty" validate:"omitempty,max=255"`
	LastName    *string     `json:"last_name,omitempty" validate:"omitempty,max=255"`
	DisplayName *string     `json:"display_name,omitempty" validate:"omitempty,max=255"`
	Status      *UserStatus `json:"status,omitempty" validate:"omitempty,oneof=active suspended inactive"`
	Role        *UserRole   `json:"role,omitempty" validate:"omitempty,oneof=admin user guest"`
	Phone       *string     `json:"phone,omitempty" validate:"omitempty,e164"`
	AvatarURL   *string     `json:"avatar_url,omitempty" validate:"omitempty,url"`
	Timezone    *string     `json:"timezone,omitempty" validate:"omitempty,timezone"`
	Language    *string     `json:"language,omitempty" validate:"omitempty,len=2"`
}

// ChangePasswordRequest represents the request to change password
type ChangePasswordRequest struct {
	CurrentPassword string `json:"current_password" validate:"required"`
	NewPassword     string `json:"new_password" validate:"required,min=8,max=128"`
	ConfirmPassword string `json:"confirm_password" validate:"required,eqfield=NewPassword"`
}

// UserLoginRequest represents the login request
type UserLoginRequest struct {
	Email      string      `json:"email" validate:"required,email"`
	Password   string      `json:"password" validate:"required"`
	RememberMe bool        `json:"remember_me"`
	DeviceInfo *DeviceInfo `json:"device_info,omitempty"`
}

// DeviceInfo represents device information for login tracking
type DeviceInfo struct {
	Name     *string `json:"name,omitempty"`
	Type     *string `json:"type,omitempty" validate:"omitempty,oneof=mobile desktop tablet"`
	OS       *string `json:"os,omitempty"`
	Browser  *string `json:"browser,omitempty"`
	DeviceID *string `json:"device_id,omitempty"`
}

// UserRegistrationRequest represents the registration request
type UserRegistrationRequest struct {
	TenantID        uint    `json:"tenant_id" validate:"required,min=1"`
	Email           string  `json:"email" validate:"required,email"`
	Username        *string `json:"username,omitempty" validate:"omitempty,min=3,max=30,alphanum"`
	FirstName       *string `json:"first_name,omitempty" validate:"omitempty,max=255"`
	LastName        *string `json:"last_name,omitempty" validate:"omitempty,max=255"`
	Password        string  `json:"password" validate:"required,min=8,max=128"`
	ConfirmPassword string  `json:"confirm_password" validate:"required,eqfield=Password"`
	Phone           *string `json:"phone,omitempty" validate:"omitempty,e164"`
	Timezone        string  `json:"timezone,omitempty" validate:"omitempty,timezone"`
	Language        string  `json:"language,omitempty" validate:"omitempty,len=2"`
	AcceptTerms     bool    `json:"accept_terms" validate:"required,eq=true"`
}
