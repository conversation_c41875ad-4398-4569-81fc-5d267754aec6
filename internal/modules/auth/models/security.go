package models

import (
	"database/sql/driver"
	"time"
)

// TokenBlacklistReason represents the reason for blacklisting a token
// @Enum logout,revoked,compromised,expired,password_changed,account_suspended
type TokenBlacklistReason string

const (
	TokenBlacklistReasonLogout           TokenBlacklistReason = "logout"
	TokenBlacklistReasonRevoked          TokenBlacklistReason = "revoked"
	TokenBlacklistReasonCompromised      TokenBlacklistReason = "compromised"
	TokenBlacklistReasonExpired          TokenBlacklistReason = "expired"
	TokenBlacklistReasonPasswordChanged  TokenBlacklistReason = "password_changed"
	TokenBlacklistReasonAccountSuspended TokenBlacklistReason = "account_suspended"
)

// Scan implements sql.Scanner interface
func (tbr *TokenBlacklistReason) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*tbr = TokenBlacklistReason(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (tbr TokenBlacklistReason) Value() (driver.Value, error) {
	return string(tbr), nil
}

// TokenBlacklist represents a blacklisted token
type TokenBlacklist struct {
	ID        uint `json:"id" gorm:"primaryKey"`
	TenantID  uint `json:"tenant_id" gorm:"not null;index"`
	WebsiteID uint `json:"website_id" gorm:"not null;index"`
	UserID    uint `json:"user_id" gorm:"not null;index"`

	// Token Information
	TokenJTI  string               `json:"token_jti" gorm:"type:varchar(255);not null;uniqueIndex"` // JWT ID from token claims
	TokenType string               `json:"token_type" gorm:"not null;index"`                        // jwt, session, api
	Reason    TokenBlacklistReason `json:"reason" gorm:"type:enum('logout','revoked','compromised','expired','password_changed','account_suspended');not null;index"`

	// Blacklist Information
	BlacklistedBy *uint     `json:"blacklisted_by,omitempty"` // User who blacklisted the token
	Notes         *string   `json:"notes,omitempty"`
	ExpiresAt     time.Time `json:"expires_at" gorm:"not null;index"` // When to clean up this blacklist entry

	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime"`

	// Associations
	// Website      *models.Website `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
	// User         *User           `json:"user,omitempty" gorm:"foreignKey:UserID"`
	// BlacklistedByUser *User      `json:"blacklisted_by_user,omitempty" gorm:"foreignKey:BlacklistedBy"`
}

// TableName specifies the table name for TokenBlacklist
func (TokenBlacklist) TableName() string {
	return "auth_token_blacklist"
}

// IsExpired checks if the blacklist entry can be cleaned up
func (tb *TokenBlacklist) IsExpired() bool {
	return time.Now().After(tb.ExpiresAt)
}

// TokenBlacklistFilter represents filters for querying token blacklist
type TokenBlacklistFilter struct {
	TenantID      uint                 `json:"tenant_id,omitempty"`
	WebsiteID     uint                 `json:"website_id,omitempty"`
	UserID        uint                 `json:"user_id,omitempty"`
	TokenType     string               `json:"token_type,omitempty"`
	Reason        TokenBlacklistReason `json:"reason,omitempty"`
	BlacklistedBy uint                 `json:"blacklisted_by,omitempty"`
	IsExpired     *bool                `json:"is_expired,omitempty"`
	Page          int                  `json:"page,omitempty"`
	PageSize      int                  `json:"page_size,omitempty"`
	SortBy        string               `json:"sort_by,omitempty"`
	SortOrder     string               `json:"sort_order,omitempty"`
}

// CreateTokenBlacklistRequest represents the request to blacklist a token
type CreateTokenBlacklistRequest struct {
	TenantID      uint                 `json:"tenant_id" validate:"required,min=1"`
	WebsiteID     uint                 `json:"website_id" validate:"required,min=1"`
	UserID        uint                 `json:"user_id" validate:"required,min=1"`
	TokenJTI      string               `json:"token_jti" validate:"required"`
	TokenType     string               `json:"token_type" validate:"required,oneof=jwt session api"`
	Reason        TokenBlacklistReason `json:"reason" validate:"required"`
	BlacklistedBy *uint                `json:"blacklisted_by,omitempty"`
	Notes         *string              `json:"notes,omitempty"`
	ExpiresAt     time.Time            `json:"expires_at" validate:"required"`
}

// PasswordHistory represents a user's password history for security
type PasswordHistory struct {
	ID        uint `json:"id" gorm:"primaryKey"`
	TenantID  uint `json:"tenant_id" gorm:"not null;index"`
	WebsiteID uint `json:"website_id" gorm:"not null;index"`
	UserID    uint `json:"user_id" gorm:"not null;index"`

	// Password Information
	PasswordHash string `json:"-" gorm:"not null"` // Don't expose in JSON

	// Change Information
	ChangedBy *uint   `json:"changed_by,omitempty"` // User who changed the password (for admin changes)
	IPAddress *string `json:"ip_address,omitempty"`
	UserAgent *string `json:"user_agent,omitempty"`
	Reason    *string `json:"reason,omitempty"` // e.g., "user_requested", "admin_reset", "security_policy"

	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime;index"`

	// Associations
	// Website   *models.Website `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
	// User      *User           `json:"user,omitempty" gorm:"foreignKey:UserID"`
	// ChangedByUser *User       `json:"changed_by_user,omitempty" gorm:"foreignKey:ChangedBy"`
}

// TableName specifies the table name for PasswordHistory
func (PasswordHistory) TableName() string {
	return "auth_password_history"
}

// IsRecentChange checks if the password was changed recently (within specified duration)
func (ph *PasswordHistory) IsRecentChange(duration time.Duration) bool {
	return time.Since(ph.CreatedAt) < duration
}

// PasswordHistoryFilter represents filters for querying password history
type PasswordHistoryFilter struct {
	TenantID  uint       `json:"tenant_id,omitempty"`
	WebsiteID uint       `json:"website_id,omitempty"`
	UserID    uint       `json:"user_id,omitempty"`
	ChangedBy uint       `json:"changed_by,omitempty"`
	Reason    string     `json:"reason,omitempty"`
	DateFrom  *time.Time `json:"date_from,omitempty"`
	DateTo    *time.Time `json:"date_to,omitempty"`
	Page      int        `json:"page,omitempty"`
	PageSize  int        `json:"page_size,omitempty"`
	SortBy    string     `json:"sort_by,omitempty"`
	SortOrder string     `json:"sort_order,omitempty"`
}

// CreatePasswordHistoryRequest represents the request to create a password history entry
type CreatePasswordHistoryRequest struct {
	TenantID     uint    `json:"tenant_id" validate:"required,min=1"`
	WebsiteID    uint    `json:"website_id" validate:"required,min=1"`
	UserID       uint    `json:"user_id" validate:"required,min=1"`
	PasswordHash string  `json:"password_hash" validate:"required"`
	ChangedBy    *uint   `json:"changed_by,omitempty"`
	IPAddress    *string `json:"ip_address,omitempty" validate:"omitempty,ip"`
	UserAgent    *string `json:"user_agent,omitempty"`
	Reason       *string `json:"reason,omitempty"`
}

// PasswordHistoryResponse represents a password history response
type PasswordHistoryResponse struct {
	ID        uint      `json:"id"`
	IPAddress *string   `json:"ip_address,omitempty"`
	UserAgent *string   `json:"user_agent,omitempty"`
	Reason    *string   `json:"reason,omitempty"`
	CreatedAt time.Time `json:"created_at"`
	IsRecent  bool      `json:"is_recent"` // Computed field (last 24 hours)
}

// ToResponse converts a PasswordHistory to PasswordHistoryResponse
func (ph *PasswordHistory) ToResponse() *PasswordHistoryResponse {
	return &PasswordHistoryResponse{
		ID:        ph.ID,
		IPAddress: ph.IPAddress,
		UserAgent: ph.UserAgent,
		Reason:    ph.Reason,
		CreatedAt: ph.CreatedAt,
		IsRecent:  ph.IsRecentChange(24 * time.Hour),
	}
}

// SecurityEvent represents a security-related event for logging and monitoring
type SecurityEvent struct {
	ID        uint  `json:"id" gorm:"primaryKey"`
	WebsiteID uint  `json:"website_id" gorm:"not null;index"`
	UserID    *uint `json:"user_id,omitempty" gorm:"index"` // Nullable for events without user context

	// Event Information
	EventType   string `json:"event_type" gorm:"not null;index"`   // e.g., "login_attempt", "password_change", "token_created"
	EventAction string `json:"event_action" gorm:"not null;index"` // e.g., "success", "failure", "blocked"
	Description string `json:"description" gorm:"not null"`

	// Request Information
	IPAddress *string `json:"ip_address,omitempty" gorm:"index"`
	UserAgent *string `json:"user_agent,omitempty"`
	Country   *string `json:"country,omitempty"`
	City      *string `json:"city,omitempty"`

	// Additional Context
	Metadata map[string]interface{} `json:"metadata,omitempty" gorm:"type:json"`

	// Risk Assessment
	RiskScore int    `json:"risk_score" gorm:"default:0;index"`     // 0-100
	RiskLevel string `json:"risk_level" gorm:"default:'low';index"` // low, medium, high, critical

	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime;index"`

	// Associations
	// Website *models.Website `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
	// User    *User           `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableName specifies the table name for SecurityEvent
func (SecurityEvent) TableName() string {
	return "auth_security_events"
}

// IsHighRisk checks if the security event is high risk
func (se *SecurityEvent) IsHighRisk() bool {
	return se.RiskScore >= 70 || se.RiskLevel == "high" || se.RiskLevel == "critical"
}

// BeforeCreate hook for SecurityEvent
func (se *SecurityEvent) BeforeCreate() error {
	if se.Metadata == nil {
		se.Metadata = make(map[string]interface{})
	}
	return nil
}

// SecurityEventFilter represents filters for querying security events
type SecurityEventFilter struct {
	WebsiteID    uint       `json:"website_id,omitempty"`
	UserID       uint       `json:"user_id,omitempty"`
	EventType    string     `json:"event_type,omitempty"`
	EventAction  string     `json:"event_action,omitempty"`
	RiskLevel    string     `json:"risk_level,omitempty"`
	IPAddress    string     `json:"ip_address,omitempty"`
	MinRiskScore int        `json:"min_risk_score,omitempty"`
	DateFrom     *time.Time `json:"date_from,omitempty"`
	DateTo       *time.Time `json:"date_to,omitempty"`
	Page         int        `json:"page,omitempty"`
	PageSize     int        `json:"page_size,omitempty"`
	SortBy       string     `json:"sort_by,omitempty"`
	SortOrder    string     `json:"sort_order,omitempty"`
}
