package models

import (
	"time"
)

// TenantMembership represents a user's membership in a tenant for JWT claims
type TenantMembership struct {
	TenantID      uint   `json:"tenant_id"`
	LocalUsername string `json:"local_username,omitempty"`
	DisplayName   string `json:"display_name,omitempty"`
	Status        string `json:"status"`
	Role          string `json:"role,omitempty"`
	JoinedAt      int64  `json:"joined_at,omitempty"`
}

// JWTClaims represents JWT token claims with user-only information
type JWTClaims struct {
	// User identification
	UserID uint   `json:"user_id"`
	Email  string `json:"email"`

	// Token metadata
	Scopes    TokenScopes `json:"scopes,omitempty"`
	TokenType TokenType   `json:"token_type,omitempty"`
	SessionID *uint       `json:"session_id,omitempty"`

	// Standard JWT claims
	Issuer    string `json:"iss,omitempty"`
	Subject   string `json:"sub,omitempty"`
	Audience  string `json:"aud,omitempty"`
	ExpiresAt int64  `json:"exp,omitempty"`
	NotBefore int64  `json:"nbf,omitempty"`
	IssuedAt  int64  `json:"iat,omitempty"`
	ID        string `json:"jti,omitempty"`
}

// IsExpiredJWT checks if JWT token is expired
func (c *JWTClaims) IsExpiredJWT() bool {
	return c.ExpiresAt > 0 && time.Now().Unix() > c.ExpiresAt
}

// HasScopeJWT checks if JWT token has a specific scope
func (c *JWTClaims) HasScopeJWT(scope string) bool {
	return c.Scopes.HasScope(scope) || c.Scopes.HasScope("*")
}

// SwitchTenantRequest represents a request to switch active tenant
type SwitchTenantRequest struct {
	TenantID uint `json:"tenant_id" validate:"required,min=1"`
}

// SwitchTenantResponse represents the response after switching tenant
type SwitchTenantResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresAt    int64  `json:"expires_at"`
}

// TokenRefreshRequest represents a request to refresh tokens
type TokenRefreshRequest struct {
	RefreshToken string `json:"refresh_token" validate:"required"`
}

// TokenRefreshResponse represents the response after token refresh
type TokenRefreshResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresAt    int64  `json:"expires_at"`
}

// LoginResponse represents the response after successful login
type LoginResponse struct {
	AccessToken  string        `json:"access_token"`
	RefreshToken string        `json:"refresh_token"`
	TokenType    string        `json:"token_type"`
	ExpiresIn    int64         `json:"expires_in"`
	User         *UserResponse `json:"user"`
}

// UserResponse represents user information in authentication responses
type UserResponse struct {
	ID       uint   `json:"id"`
	Email    string `json:"email"`
	Username string `json:"username"`
}
