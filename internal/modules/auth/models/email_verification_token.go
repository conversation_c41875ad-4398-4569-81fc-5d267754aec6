package models

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// EmailVerificationToken represents an email verification token
type EmailVerificationToken struct {
	ID     uint `gorm:"primaryKey;autoIncrement" json:"id"`
	UserID uint `gorm:"not null;index" json:"user_id"`

	// Token Information
	Token     string `gorm:"type:varchar(255);not null;uniqueIndex" json:"token"`
	TokenHash string `gorm:"type:varchar(255);not null;index" json:"-"` // Hidden from JSON

	// Email and User Context
	Email     string  `gorm:"type:varchar(255);not null;index" json:"email"`
	UserAgent *string `gorm:"type:text" json:"user_agent,omitempty"`
	IPAddress *string `gorm:"type:varchar(45)" json:"ip_address,omitempty"`

	// Status and Tracking
	IsUsed bool       `gorm:"default:false;index" json:"is_used"`
	UsedAt *time.Time `json:"used_at,omitempty"`

	// Rate Limiting
	ResendCount  uint       `gorm:"default:0" json:"resend_count"`
	LastResentAt *time.Time `json:"last_resent_at,omitempty"`

	// Expiration
	ExpiresAt time.Time `gorm:"not null;index" json:"expires_at"`

	// Timestamps
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`

	// Relationships
	User *User `gorm:"foreignKey:UserID" json:"user,omitempty"`
}

// TableName returns the table name for the EmailVerificationToken model
func (EmailVerificationToken) TableName() string {
	return "auth_email_verification_tokens"
}

// GenerateToken generates a new verification token
func (evt *EmailVerificationToken) GenerateToken() error {
	// Generate a 32-byte random token
	tokenBytes := make([]byte, 32)
	_, err := rand.Read(tokenBytes)
	if err != nil {
		return fmt.Errorf("failed to generate token: %w", err)
	}

	// Convert to hex string
	evt.Token = hex.EncodeToString(tokenBytes)

	// Create hash for storage
	hash := sha256.Sum256([]byte(evt.Token))
	evt.TokenHash = hex.EncodeToString(hash[:])

	return nil
}

// IsExpired checks if the token has expired
func (evt *EmailVerificationToken) IsExpired() bool {
	return time.Now().After(evt.ExpiresAt)
}

// IsValid checks if the token is valid for use
func (evt *EmailVerificationToken) IsValid() bool {
	return !evt.IsUsed && !evt.IsExpired()
}

// CanResend checks if a new token can be sent (rate limiting)
func (evt *EmailVerificationToken) CanResend(maxResends uint, resendInterval time.Duration) bool {
	// Check resend count limit
	if evt.ResendCount >= maxResends {
		return false
	}

	// Check resend interval if this is not the first send
	if evt.LastResentAt != nil && time.Since(*evt.LastResentAt) < resendInterval {
		return false
	}

	return true
}

// MarkAsUsed marks the token as used
func (evt *EmailVerificationToken) MarkAsUsed() {
	evt.IsUsed = true
	now := time.Now()
	evt.UsedAt = &now
}

// IncrementResendCount increments the resend counter
func (evt *EmailVerificationToken) IncrementResendCount() {
	evt.ResendCount++
	now := time.Now()
	evt.LastResentAt = &now
}

// BeforeCreate hook to generate token and set defaults
func (evt *EmailVerificationToken) BeforeCreate(tx *gorm.DB) error {
	// Generate token if not already set
	if evt.Token == "" {
		if err := evt.GenerateToken(); err != nil {
			return err
		}
	}

	// Set default expiration if not set (24 hours)
	if evt.ExpiresAt.IsZero() {
		evt.ExpiresAt = time.Now().Add(24 * time.Hour)
	}

	return nil
}

// CreateEmailVerificationTokenRequest represents the request to create a new verification token
type CreateEmailVerificationTokenRequest struct {
	UserID    uint    `json:"user_id" validate:"required"`
	Email     string  `json:"email" validate:"required,email"`
	UserAgent *string `json:"user_agent,omitempty"`
	IPAddress *string `json:"ip_address,omitempty"`
	ExpiresIn *int    `json:"expires_in,omitempty" validate:"omitempty,min=300,max=86400"` // 5 minutes to 24 hours
}

// VerifyEmailRequest represents the request to verify an email
type VerifyEmailRequest struct {
	Token     string  `json:"token" validate:"required,min=32"`
	UserAgent *string `json:"user_agent,omitempty"`
	IPAddress *string `json:"ip_address,omitempty"`
}

// ResendVerificationEmailRequest represents the request to resend verification email
type ResendVerificationEmailRequest struct {
	Email     string  `json:"email" validate:"required,email"`
	UserAgent *string `json:"user_agent,omitempty"`
	IPAddress *string `json:"ip_address,omitempty"`
}

// EmailVerificationTokenResponse represents the response when returning token data
type EmailVerificationTokenResponse struct {
	ID            uint       `json:"id"`
	UserID        uint       `json:"user_id"`
	Email         string     `json:"email"`
	IsUsed        bool       `json:"is_used"`
	UsedAt        *time.Time `json:"used_at,omitempty"`
	ResendCount   uint       `json:"resend_count"`
	LastResentAt  *time.Time `json:"last_resent_at,omitempty"`
	ExpiresAt     time.Time  `json:"expires_at"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
	IsValid       bool       `json:"is_valid"`
	IsExpired     bool       `json:"is_expired"`
	TimeRemaining string     `json:"time_remaining"`
}

// FromEmailVerificationToken converts a EmailVerificationToken model to response
func (evtr *EmailVerificationTokenResponse) FromEmailVerificationToken(token *EmailVerificationToken) {
	evtr.ID = token.ID
	evtr.UserID = token.UserID
	evtr.Email = token.Email
	evtr.IsUsed = token.IsUsed
	evtr.UsedAt = token.UsedAt
	evtr.ResendCount = token.ResendCount
	evtr.LastResentAt = token.LastResentAt
	evtr.ExpiresAt = token.ExpiresAt
	evtr.CreatedAt = token.CreatedAt
	evtr.UpdatedAt = token.UpdatedAt
	evtr.IsValid = token.IsValid()
	evtr.IsExpired = token.IsExpired()

	// Calculate time remaining
	if !token.IsExpired() {
		remaining := time.Until(token.ExpiresAt)
		if remaining > time.Hour {
			evtr.TimeRemaining = fmt.Sprintf("%.1f hours", remaining.Hours())
		} else if remaining > time.Minute {
			evtr.TimeRemaining = fmt.Sprintf("%.0f minutes", remaining.Minutes())
		} else {
			evtr.TimeRemaining = fmt.Sprintf("%.0f seconds", remaining.Seconds())
		}
	} else {
		evtr.TimeRemaining = "expired"
	}
}

// EmailVerificationResult represents the result of email verification
type EmailVerificationResult struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	UserID    uint   `json:"user_id,omitempty"`
	Email     string `json:"email,omitempty"`
	TokenUsed bool   `json:"token_used"`
}

// TokenStats represents statistics about email verification tokens
type TokenStats struct {
	TotalCreated int64 `json:"total_created"`
	TotalUsed    int64 `json:"total_used"`
	TotalExpired int64 `json:"total_expired"`
	TotalActive  int64 `json:"total_active"`
	TotalResends int64 `json:"total_resends"`
}
