package models

import (
	"database/sql/driver"
	"time"
)

// AttemptResult represents the result of a login attempt
// @Enum success,failure,blocked,two_factor_required,email_not_verified,account_suspended
type AttemptResult string

const (
	AttemptResultSuccess           AttemptResult = "success"
	AttemptResultFailure           AttemptResult = "failure"
	AttemptResultBlocked           AttemptResult = "blocked"
	AttemptResultTwoFactorRequired AttemptResult = "two_factor_required"
	AttemptResultEmailNotVerified  AttemptResult = "email_not_verified"
	AttemptResultAccountSuspended  AttemptResult = "account_suspended"
)

// Scan implements sql.Scanner interface
func (ar *AttemptResult) Scan(value interface{}) error {
	if value == nil {
		return nil
	}
	*ar = AttemptResult(value.([]byte))
	return nil
}

// Value implements driver.Valuer interface
func (ar AttemptResult) Value() (driver.Value, error) {
	return string(ar), nil
}

// LoginAttempt represents a login attempt for rate limiting and security
type LoginAttempt struct {
	ID        uint  `json:"id" gorm:"primaryKey"`
	TenantID  uint  `json:"tenant_id" gorm:"not null;index"`
	WebsiteID uint  `json:"website_id" gorm:"not null;index"`
	UserID    *uint `json:"user_id,omitempty" gorm:"index"` // Nullable for failed attempts with invalid email

	// Attempt Information
	Email  string        `json:"email" gorm:"not null;index"`
	Result AttemptResult `json:"result" gorm:"type:enum('success','failure','blocked','two_factor_required','email_not_verified','account_suspended');not null;index"`

	// Request Information
	IPAddress *string `json:"ip_address,omitempty" gorm:"index"`
	UserAgent *string `json:"user_agent,omitempty"`
	Country   *string `json:"country,omitempty"`
	City      *string `json:"city,omitempty"`

	// Device Information
	DeviceInfo *string `json:"device_info,omitempty"`

	// Failure Details (only for failed attempts)
	FailureReason *string `json:"failure_reason,omitempty"`

	// Timestamps
	CreatedAt time.Time `json:"created_at" gorm:"autoCreateTime;index"`

	// Associations (loaded separately to avoid circular references)
	// Website *models.Website `json:"website,omitempty" gorm:"foreignKey:WebsiteID"`
	// User    *User           `json:"user,omitempty" gorm:"foreignKey:UserID"`
}

// TableName specifies the table name for LoginAttempt
func (LoginAttempt) TableName() string {
	return "auth_login_attempts"
}

// IsSuccess checks if the login attempt was successful
func (la *LoginAttempt) IsSuccess() bool {
	return la.Result == AttemptResultSuccess
}

// IsFailure checks if the login attempt failed
func (la *LoginAttempt) IsFailure() bool {
	return la.Result == AttemptResultFailure
}

// IsBlocked checks if the login attempt was blocked
func (la *LoginAttempt) IsBlocked() bool {
	return la.Result == AttemptResultBlocked
}

// RequiresTwoFactor checks if the attempt requires two-factor authentication
func (la *LoginAttempt) RequiresTwoFactor() bool {
	return la.Result == AttemptResultTwoFactorRequired
}

// IsFromSameLocation checks if two attempts are from the same general location
func (la *LoginAttempt) IsFromSameLocation(other *LoginAttempt) bool {
	return la.Country != nil && other.Country != nil &&
		*la.Country == *other.Country &&
		la.City != nil && other.City != nil &&
		*la.City == *other.City
}

// LoginAttemptFilter represents filters for querying login attempts
type LoginAttemptFilter struct {
	TenantID  uint          `json:"tenant_id,omitempty"`
	WebsiteID uint          `json:"website_id,omitempty"`
	UserID    uint          `json:"user_id,omitempty"`
	Email     string        `json:"email,omitempty"`
	Result    AttemptResult `json:"result,omitempty"`
	IPAddress string        `json:"ip_address,omitempty"`
	Country   string        `json:"country,omitempty"`
	DateFrom  *time.Time    `json:"date_from,omitempty"`
	DateTo    *time.Time    `json:"date_to,omitempty"`
	Page      int           `json:"page,omitempty"`
	PageSize  int           `json:"page_size,omitempty"`
	SortBy    string        `json:"sort_by,omitempty"`
	SortOrder string        `json:"sort_order,omitempty"`
}

// CreateLoginAttemptRequest represents the request to create a login attempt
type CreateLoginAttemptRequest struct {
	TenantID      uint          `json:"tenant_id" validate:"required,min=1"`
	WebsiteID     uint          `json:"website_id" validate:"required,min=1"`
	UserID        *uint         `json:"user_id,omitempty"`
	Email         string        `json:"email" validate:"required,email"`
	Result        AttemptResult `json:"result" validate:"required,oneof=success failure blocked two_factor_required email_not_verified account_suspended"`
	IPAddress     *string       `json:"ip_address,omitempty" validate:"omitempty,ip"`
	UserAgent     *string       `json:"user_agent,omitempty"`
	Country       *string       `json:"country,omitempty"`
	City          *string       `json:"city,omitempty"`
	DeviceInfo    *string       `json:"device_info,omitempty"`
	FailureReason *string       `json:"failure_reason,omitempty"`
}

// LoginAttemptResponse represents a login attempt response
type LoginAttemptResponse struct {
	ID            uint          `json:"id"`
	Email         string        `json:"email"`
	Result        AttemptResult `json:"result"`
	IPAddress     *string       `json:"ip_address,omitempty"`
	UserAgent     *string       `json:"user_agent,omitempty"`
	Country       *string       `json:"country,omitempty"`
	City          *string       `json:"city,omitempty"`
	DeviceInfo    *string       `json:"device_info,omitempty"`
	FailureReason *string       `json:"failure_reason,omitempty"`
	CreatedAt     time.Time     `json:"created_at"`
	IsSuccess     bool          `json:"is_success"`                     // Computed field
	LocationDesc  string        `json:"location_description,omitempty"` // Computed field
}

// ToResponse converts a LoginAttempt to LoginAttemptResponse
func (la *LoginAttempt) ToResponse() *LoginAttemptResponse {
	resp := &LoginAttemptResponse{
		ID:            la.ID,
		Email:         la.Email,
		Result:        la.Result,
		IPAddress:     la.IPAddress,
		UserAgent:     la.UserAgent,
		Country:       la.Country,
		City:          la.City,
		DeviceInfo:    la.DeviceInfo,
		FailureReason: la.FailureReason,
		CreatedAt:     la.CreatedAt,
		IsSuccess:     la.IsSuccess(),
	}

	// Build location description
	if la.City != nil && la.Country != nil {
		resp.LocationDesc = *la.City + ", " + *la.Country
	} else if la.Country != nil {
		resp.LocationDesc = *la.Country
	} else if la.IPAddress != nil {
		resp.LocationDesc = *la.IPAddress
	}

	return resp
}

// RateLimitInfo represents rate limiting information for login attempts
type RateLimitInfo struct {
	IsBlocked          bool       `json:"is_blocked"`
	AttemptsRemaining  int        `json:"attempts_remaining"`
	BlockedUntil       *time.Time `json:"blocked_until,omitempty"`
	RecentFailures     int        `json:"recent_failures"`
	WindowStart        time.Time  `json:"window_start"`
	NextAttemptAllowed time.Time  `json:"next_attempt_allowed"`
}

// LoginSecurityInfo represents security information for a login
type LoginSecurityInfo struct {
	IsNewLocation        bool `json:"is_new_location"`
	IsNewDevice          bool `json:"is_new_device"`
	IsSuspiciousLogin    bool `json:"is_suspicious_login"`
	RequiresVerification bool `json:"requires_verification"`
	RiskScore            int  `json:"risk_score"` // 0-100
}
