package models

import (
	"time"
)

// TrelloChecklist represents a checklist within a card
type TrelloChecklist struct {
	ID                     uint         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID               uint         `gorm:"not null;index:idx_trello_checklists_tenant_id" json:"tenant_id"`
	CardID                 uint         `gorm:"not null;index:idx_trello_checklists_card_id" json:"card_id"`
	Name                   string       `gorm:"type:varchar(255);not null" json:"name"`
	Position               float64      `gorm:"type:decimal(10,5);not null" json:"position"`
	CheckItemsCount        uint         `gorm:"default:0;not null" json:"check_items_count"`
	CheckItemsCheckedCount uint         `gorm:"default:0;not null" json:"check_items_checked_count"`
	CreatedBy              uint         `gorm:"not null;index" json:"created_by"`
	Status                 TrelloStatus `gorm:"type:varchar(50);default:'active';not null;index" json:"status"`
	CreatedAt              time.Time    `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt              time.Time    `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Card  *TrelloCard           `gorm:"foreignKey:CardID" json:"card,omitempty"`
	Items []TrelloChecklistItem `gorm:"foreignKey:ChecklistID" json:"items,omitempty"`
}

// TableName returns the table name for GORM
func (TrelloChecklist) TableName() string {
	return "trello_checklists"
}

// TrelloChecklistItem represents an individual item within a checklist
type TrelloChecklistItem struct {
	ID             uint         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID       uint         `gorm:"not null;index:idx_trello_checklist_items_tenant_id" json:"tenant_id"`
	ChecklistID    uint         `gorm:"not null;index:idx_trello_checklist_items_checklist_id" json:"checklist_id"`
	CardID         uint         `gorm:"not null;index:idx_trello_checklist_items_card_id" json:"card_id"`
	Name           string       `gorm:"type:varchar(512);not null" json:"name"`
	Position       float64      `gorm:"type:decimal(10,5);not null" json:"position"`
	IsChecked      bool         `gorm:"default:false;not null" json:"is_checked"`
	DueDate        *time.Time   `json:"due_date,omitempty"`
	AssignedUserID *uint        `gorm:"index" json:"assigned_user_id,omitempty"`
	CreatedBy      uint         `gorm:"not null;index" json:"created_by"`
	CheckedBy      *uint        `gorm:"index" json:"checked_by,omitempty"`
	CheckedAt      *time.Time   `json:"checked_at,omitempty"`
	Status         TrelloStatus `gorm:"type:varchar(50);default:'active';not null;index" json:"status"`
	CreatedAt      time.Time    `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt      time.Time    `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Checklist *TrelloChecklist `gorm:"foreignKey:ChecklistID" json:"checklist,omitempty"`
	Card      *TrelloCard      `gorm:"foreignKey:CardID" json:"card,omitempty"`
}

// TableName returns the table name for GORM
func (TrelloChecklistItem) TableName() string {
	return "trello_checklist_items"
}
