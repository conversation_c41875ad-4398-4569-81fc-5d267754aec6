package models

import (
	"time"

	"gorm.io/datatypes"
)

// Workspace Requests
type CreateWorkspaceRequest struct {
	Name        string         `json:"name" validate:"required,max=255"`
	Description *string        `json:"description,omitempty"`
	Visibility  VisibilityType `json:"visibility" validate:"oneof=private workspace public"`
	LogoURL     *string        `json:"logo_url,omitempty" validate:"omitempty,url"`
	WebsiteURL  *string        `json:"website_url,omitempty" validate:"omitempty,url"`
	Settings    datatypes.JSON `json:"settings,omitempty"`
}

type UpdateWorkspaceRequest struct {
	Name        *string        `json:"name,omitempty" validate:"omitempty,max=255"`
	Description *string        `json:"description,omitempty"`
	Visibility  VisibilityType `json:"visibility,omitempty" validate:"omitempty,oneof=private workspace public"`
	LogoURL     *string        `json:"logo_url,omitempty" validate:"omitempty,url"`
	WebsiteURL  *string        `json:"website_url,omitempty" validate:"omitempty,url"`
	Settings    datatypes.JSON `json:"settings,omitempty"`
}

// Board Requests
type CreateBoardRequest struct {
	WorkspaceID     uint           `json:"workspace_id" validate:"required"`
	Name            string         `json:"name" validate:"required,max=255"`
	Description     *string        `json:"description,omitempty"`
	BackgroundType  BackgroundType `json:"background_type" validate:"oneof=color image gradient"`
	BackgroundValue *string        `json:"background_value,omitempty"`
	Visibility      VisibilityType `json:"visibility" validate:"oneof=private workspace public"`
	Preferences     datatypes.JSON `json:"preferences,omitempty"`
}

type UpdateBoardRequest struct {
	Name            *string        `json:"name,omitempty" validate:"omitempty,max=255"`
	Description     *string        `json:"description,omitempty"`
	BackgroundType  BackgroundType `json:"background_type,omitempty" validate:"omitempty,oneof=color image gradient"`
	BackgroundValue *string        `json:"background_value,omitempty"`
	Visibility      VisibilityType `json:"visibility,omitempty" validate:"omitempty,oneof=private workspace public"`
	IsClosed        *bool          `json:"is_closed,omitempty"`
	IsStarred       *bool          `json:"is_starred,omitempty"`
	Preferences     datatypes.JSON `json:"preferences,omitempty"`
}

// List Requests
type CreateListRequest struct {
	BoardID  uint    `json:"board_id" validate:"required"`
	Name     string  `json:"name" validate:"required,max=255"`
	Position float64 `json:"position"`
}

type UpdateListRequest struct {
	Name         *string  `json:"name,omitempty" validate:"omitempty,max=255"`
	Position     *float64 `json:"position,omitempty"`
	IsClosed     *bool    `json:"is_closed,omitempty"`
	IsSubscribed *bool    `json:"is_subscribed,omitempty"`
}

type MoveListRequest struct {
	BoardID  *uint   `json:"board_id,omitempty"`
	Position float64 `json:"position" validate:"required"`
}

// Card Requests
type CreateCardRequest struct {
	ListID       uint           `json:"list_id" validate:"required"`
	Title        string         `json:"title" validate:"required,max=512"`
	Description  *string        `json:"description,omitempty"`
	Position     float64        `json:"position"`
	DueDate      *time.Time     `json:"due_date,omitempty"`
	StartDate    *time.Time     `json:"start_date,omitempty"`
	CoverType    CoverType      `json:"cover_type" validate:"oneof=none color image"`
	CoverValue   *string        `json:"cover_value,omitempty"`
	CoverColor   *string        `json:"cover_color,omitempty"`
	CoverSize    CoverSize      `json:"cover_size" validate:"oneof=normal full"`
	CustomFields datatypes.JSON `json:"custom_fields,omitempty"`
}

type UpdateCardRequest struct {
	Title        *string        `json:"title,omitempty" validate:"omitempty,max=512"`
	Description  *string        `json:"description,omitempty"`
	Position     *float64       `json:"position,omitempty"`
	DueDate      *time.Time     `json:"due_date,omitempty"`
	StartDate    *time.Time     `json:"start_date,omitempty"`
	DueComplete  *bool          `json:"due_complete,omitempty"`
	IsClosed     *bool          `json:"is_closed,omitempty"`
	IsSubscribed *bool          `json:"is_subscribed,omitempty"`
	CoverType    CoverType      `json:"cover_type,omitempty" validate:"omitempty,oneof=none color image"`
	CoverValue   *string        `json:"cover_value,omitempty"`
	CoverColor   *string        `json:"cover_color,omitempty"`
	CoverSize    CoverSize      `json:"cover_size,omitempty" validate:"omitempty,oneof=normal full"`
	CustomFields datatypes.JSON `json:"custom_fields,omitempty"`
}

type MoveCardRequest struct {
	ListID   *uint   `json:"list_id,omitempty"`
	Position float64 `json:"position" validate:"required"`
}

// Label Requests
type CreateLabelRequest struct {
	BoardID uint   `json:"board_id" validate:"required"`
	Name    string `json:"name" validate:"required,max=255"`
	Color   string `json:"color" validate:"required,hexcolor"`
}

type UpdateLabelRequest struct {
	Name  *string `json:"name,omitempty" validate:"omitempty,max=255"`
	Color *string `json:"color,omitempty" validate:"omitempty,hexcolor"`
}

// Checklist Requests
type CreateChecklistRequest struct {
	CardID   uint    `json:"card_id" validate:"required"`
	Name     string  `json:"name" validate:"required,max=255"`
	Position float64 `json:"position"`
}

type UpdateChecklistRequest struct {
	Name     *string  `json:"name,omitempty" validate:"omitempty,max=255"`
	Position *float64 `json:"position,omitempty"`
}

// Checklist Item Requests
type CreateChecklistItemRequest struct {
	ChecklistID    uint       `json:"checklist_id" validate:"required"`
	Name           string     `json:"name" validate:"required,max=512"`
	Position       float64    `json:"position"`
	DueDate        *time.Time `json:"due_date,omitempty"`
	AssignedUserID *uint      `json:"assigned_user_id,omitempty"`
}

type UpdateChecklistItemRequest struct {
	Name           *string    `json:"name,omitempty" validate:"omitempty,max=512"`
	Position       *float64   `json:"position,omitempty"`
	IsChecked      *bool      `json:"is_checked,omitempty"`
	DueDate        *time.Time `json:"due_date,omitempty"`
	AssignedUserID *uint      `json:"assigned_user_id,omitempty"`
}

// Member Requests
type AddWorkspaceMemberRequest struct {
	UserID uint       `json:"user_id" validate:"required"`
	Role   MemberRole `json:"role" validate:"oneof=owner admin member observer guest"`
}

type UpdateWorkspaceMemberRequest struct {
	Role   *MemberRole   `json:"role,omitempty" validate:"omitempty,oneof=owner admin member observer guest"`
	Status *MemberStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive pending suspended"`
}

type AddBoardMemberRequest struct {
	UserID uint       `json:"user_id" validate:"required"`
	Role   MemberRole `json:"role" validate:"oneof=owner admin member observer"`
}

type UpdateBoardMemberRequest struct {
	Role   *MemberRole   `json:"role,omitempty" validate:"omitempty,oneof=owner admin member observer"`
	Status *MemberStatus `json:"status,omitempty" validate:"omitempty,oneof=active inactive pending suspended"`
}

type AssignCardMemberRequest struct {
	UserID uint `json:"user_id" validate:"required"`
}

type ApplyCardLabelRequest struct {
	LabelID uint `json:"label_id" validate:"required"`
}

// Comment Requests
type CreateCommentRequest struct {
	CardID  uint        `json:"card_id" validate:"required"`
	Content string      `json:"content" validate:"required"`
	Type    CommentType `json:"type" validate:"oneof=comment action"`
}
