package models

import (
	"time"

	"gorm.io/datatypes"
)

// TrelloBoard represents a Trello board entity
type TrelloBoard struct {
	ID               uint           `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID         uint           `gorm:"not null;index:idx_trello_boards_tenant_id" json:"tenant_id"`
	WorkspaceID      uint           `gorm:"not null;index:idx_trello_boards_workspace_id" json:"workspace_id"`
	Name             string         `gorm:"type:varchar(255);not null" json:"name"`
	Description      *string        `gorm:"type:text" json:"description,omitempty"`
	BackgroundType   BackgroundType `gorm:"type:varchar(50);default:'color';not null" json:"background_type"`
	BackgroundValue  string         `gorm:"type:varchar(255);default:'#0079bf';not null" json:"background_value"`
	Visibility       VisibilityType `gorm:"type:varchar(50);default:'private';not null" json:"visibility"`
	IsClosed         bool           `gorm:"default:false;not null" json:"is_closed"`
	IsStarred        bool           `gorm:"default:false;not null" json:"is_starred"`
	Preferences      datatypes.JSON `gorm:"type:json;default:'{}'" json:"preferences"`
	LabelsNormalized datatypes.JSON `gorm:"type:json;default:'[]'" json:"labels_normalized"`
	CreatedBy        uint           `gorm:"not null;index" json:"created_by"`
	Status           TrelloStatus   `gorm:"type:varchar(50);default:'active';not null;index" json:"status"`
	CreatedAt        time.Time      `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt        time.Time      `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Workspace *TrelloWorkspace    `gorm:"foreignKey:WorkspaceID" json:"workspace,omitempty"`
	Lists     []TrelloList        `gorm:"foreignKey:BoardID" json:"lists,omitempty"`
	Labels    []TrelloBoardLabel  `gorm:"foreignKey:BoardID" json:"labels,omitempty"`
	Members   []TrelloBoardMember `gorm:"foreignKey:BoardID" json:"members,omitempty"`
}

// TableName returns the table name for GORM
func (TrelloBoard) TableName() string {
	return "trello_boards"
}

// TrelloBoardMember represents board membership
type TrelloBoardMember struct {
	ID        uint         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint         `gorm:"not null;index:idx_trello_board_members_tenant_id" json:"tenant_id"`
	BoardID   uint         `gorm:"not null;index:idx_trello_board_members_board_id" json:"board_id"`
	UserID    uint         `gorm:"not null;index:idx_trello_board_members_user_id" json:"user_id"`
	Role      MemberRole   `gorm:"type:varchar(50);default:'member';not null" json:"role"`
	InvitedBy uint         `gorm:"not null;index" json:"invited_by"`
	JoinedAt  time.Time    `gorm:"autoCreateTime" json:"joined_at"`
	Status    MemberStatus `gorm:"type:varchar(50);default:'active';not null;index" json:"status"`
	CreatedAt time.Time    `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time    `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Board *TrelloBoard `gorm:"foreignKey:BoardID" json:"board,omitempty"`
}

// TableName returns the table name for GORM
func (TrelloBoardMember) TableName() string {
	return "trello_board_members"
}

// TrelloBoardLabel represents board labels
type TrelloBoardLabel struct {
	ID        uint         `gorm:"primaryKey;autoIncrement" json:"id"`
	TenantID  uint         `gorm:"not null;index:idx_trello_board_labels_tenant_id" json:"tenant_id"`
	BoardID   uint         `gorm:"not null;index:idx_trello_board_labels_board_id" json:"board_id"`
	Name      string       `gorm:"type:varchar(255);not null" json:"name"`
	Color     string       `gorm:"type:varchar(7);default:'#61bd4f';not null" json:"color"`
	Uses      uint         `gorm:"default:0;not null" json:"uses"`
	CreatedBy uint         `gorm:"not null;index" json:"created_by"`
	Status    TrelloStatus `gorm:"type:varchar(50);default:'active';not null;index" json:"status"`
	CreatedAt time.Time    `gorm:"autoCreateTime" json:"created_at"`
	UpdatedAt time.Time    `gorm:"autoUpdateTime" json:"updated_at"`

	// Relationships
	Board *TrelloBoard `gorm:"foreignKey:BoardID" json:"board,omitempty"`
}

// TableName returns the table name for GORM
func (TrelloBoardLabel) TableName() string {
	return "trello_board_labels"
}
