package services

import (
	"context"
	"errors"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

type cardService struct {
	repo      repositories.CardRepository
	listRepo  repositories.ListRepository
	boardRepo repositories.BoardRepository
	logger    utils.Logger
}

func NewCardService(repo repositories.CardRepository, listRepo repositories.ListRepository, boardRepo repositories.BoardRepository, logger utils.Logger) CardService {
	return &cardService{
		repo:      repo,
		listRepo:  listRepo,
		boardRepo: boardRepo,
		logger:    logger,
	}
}

func (s *cardService) Create(ctx context.Context, tenantID, userID uint, request *models.CreateCardRequest) (*models.CardResponse, error) {
	list, err := s.listRepo.GetByID(ctx, tenantID, request.ListID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("list not found")
		}
		return nil, fmt.Errorf("failed to get list: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, list.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	position := request.Position
	if position == 0 {
		nextPos, err := s.repo.GetNextPosition(ctx, tenantID, request.ListID, nil)
		if err != nil {
			return nil, fmt.Errorf("failed to get next position: %w", err)
		}
		position = nextPos
	}

	card := &models.TrelloCard{
		TenantID:     tenantID,
		BoardID:      list.BoardID,
		ListID:       request.ListID,
		Title:        request.Title,
		Description:  request.Description,
		Position:     position,
		DueDate:      request.DueDate,
		StartDate:    request.StartDate,
		CoverType:    request.CoverType,
		CoverValue:   request.CoverValue,
		CoverColor:   request.CoverColor,
		CoverSize:    request.CoverSize,
		CustomFields: request.CustomFields,
		CreatedBy:    userID,
		Status:       models.TrelloStatusActive,
	}

	if err := s.repo.Create(ctx, card); err != nil {
		s.logger.Error("failed to create card", utils.WithError(err))
		return nil, fmt.Errorf("failed to create card: %w", err)
	}

	return s.modelToResponse(card), nil
}

func (s *cardService) GetByID(ctx context.Context, tenantID, userID, id uint) (*models.CardResponse, error) {
	card, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("card not found")
		}
		return nil, fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, card.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	return s.modelToResponse(card), nil
}

func (s *cardService) Update(ctx context.Context, tenantID, userID, id uint, request *models.UpdateCardRequest) (*models.CardResponse, error) {
	existing, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("card not found")
		}
		return nil, fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, existing.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	card := &models.TrelloCard{}
	if request.Title != nil {
		card.Title = *request.Title
	}
	if request.Description != nil {
		card.Description = request.Description
	}
	if request.Position != nil {
		card.Position = *request.Position
	}
	if request.DueDate != nil {
		card.DueDate = request.DueDate
	}
	if request.StartDate != nil {
		card.StartDate = request.StartDate
	}
	if request.DueComplete != nil {
		card.DueComplete = *request.DueComplete
	}
	if request.IsClosed != nil {
		card.IsClosed = *request.IsClosed
	}
	if request.IsSubscribed != nil {
		card.IsSubscribed = *request.IsSubscribed
	}
	if request.CoverType != "" {
		card.CoverType = request.CoverType
	}
	if request.CoverValue != nil {
		card.CoverValue = request.CoverValue
	}
	if request.CoverColor != nil {
		card.CoverColor = request.CoverColor
	}
	if request.CoverSize != "" {
		card.CoverSize = request.CoverSize
	}
	if request.CustomFields != nil {
		card.CustomFields = request.CustomFields
	}

	if err := s.repo.Update(ctx, tenantID, id, card); err != nil {
		s.logger.Error("failed to update card", utils.WithError(err))
		return nil, fmt.Errorf("failed to update card: %w", err)
	}

	return s.GetByID(ctx, tenantID, userID, id)
}

func (s *cardService) Delete(ctx context.Context, tenantID, userID, id uint) error {
	existing, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("card not found")
		}
		return fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, existing.BoardID, userID) {
		return errors.New("access denied")
	}

	if err := s.repo.Delete(ctx, tenantID, id); err != nil {
		s.logger.Error("failed to delete card", utils.WithError(err))
		return fmt.Errorf("failed to delete card: %w", err)
	}

	return nil
}

func (s *cardService) ListByList(ctx context.Context, tenantID, userID, listID uint, pagination *pagination.CursorPagination) ([]models.CardResponse, *pagination.CursorResponse, error) {
	list, err := s.listRepo.GetByID(ctx, tenantID, listID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil, errors.New("list not found")
		}
		return nil, nil, fmt.Errorf("failed to get list: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, list.BoardID, userID) {
		return nil, nil, errors.New("access denied")
	}

	cards, cursor, err := s.repo.ListByList(ctx, tenantID, listID, pagination)
	if err != nil {
		s.logger.Error("failed to list cards by list", utils.WithError(err))
		return nil, nil, fmt.Errorf("failed to list cards by list: %w", err)
	}

	responses := make([]models.CardResponse, len(cards))
	for i, card := range cards {
		responses[i] = *s.modelToResponse(&card)
	}

	return responses, cursor, nil
}

func (s *cardService) ListByBoard(ctx context.Context, tenantID, userID, boardID uint, pagination *pagination.CursorPagination) ([]models.CardResponse, *pagination.CursorResponse, error) {
	if !s.checkBoardAccess(ctx, tenantID, boardID, userID) {
		return nil, nil, errors.New("access denied")
	}

	cards, cursor, err := s.repo.ListByBoard(ctx, tenantID, boardID, pagination)
	if err != nil {
		s.logger.Error("failed to list cards by board", utils.WithError(err))
		return nil, nil, fmt.Errorf("failed to list cards by board: %w", err)
	}

	responses := make([]models.CardResponse, len(cards))
	for i, card := range cards {
		responses[i] = *s.modelToResponse(&card)
	}

	return responses, cursor, nil
}

func (s *cardService) GetDetail(ctx context.Context, tenantID, userID, id uint) (*models.CardDetailResponse, error) {
	card, err := s.repo.GetDetail(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("card not found")
		}
		return nil, fmt.Errorf("failed to get card detail: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, card.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	response := &models.CardDetailResponse{
		CardResponse: *s.modelToResponse(card),
		Members:      make([]models.CardMemberResponse, len(card.Members)),
		Labels:       make([]models.CardLabelResponse, len(card.Labels)),
		Checklists:   make([]models.ChecklistResponse, len(card.Checklists)),
		Comments:     make([]models.CardCommentResponse, len(card.Comments)),
		Activities:   make([]models.CardActivityResponse, len(card.Activities)),
	}

	for i, member := range card.Members {
		response.Members[i] = models.CardMemberResponse{
			ID:         member.ID,
			UserID:     member.UserID,
			AssignedBy: member.AssignedBy,
			AssignedAt: member.AssignedAt,
		}
	}

	for i, label := range card.Labels {
		response.Labels[i] = models.CardLabelResponse{
			ID:        label.ID,
			LabelID:   label.LabelID,
			AppliedBy: label.AppliedBy,
			AppliedAt: label.AppliedAt,
		}
	}

	for i, checklist := range card.Checklists {
		response.Checklists[i] = models.ChecklistResponse{
			ID:                     checklist.ID,
			TenantID:               checklist.TenantID,
			CardID:                 checklist.CardID,
			Name:                   checklist.Name,
			Position:               checklist.Position,
			CheckItemsCount:        checklist.CheckItemsCount,
			CheckItemsCheckedCount: checklist.CheckItemsCheckedCount,
			CreatedBy:              checklist.CreatedBy,
			Status:                 checklist.Status,
			CreatedAt:              checklist.CreatedAt,
			UpdatedAt:              checklist.UpdatedAt,
			Items:                  make([]models.ChecklistItemResponse, len(checklist.Items)),
		}

		for j, item := range checklist.Items {
			response.Checklists[i].Items[j] = models.ChecklistItemResponse{
				ID:             item.ID,
				TenantID:       item.TenantID,
				ChecklistID:    item.ChecklistID,
				CardID:         item.CardID,
				Name:           item.Name,
				Position:       item.Position,
				IsChecked:      item.IsChecked,
				DueDate:        item.DueDate,
				AssignedUserID: item.AssignedUserID,
				CreatedBy:      item.CreatedBy,
				CheckedBy:      item.CheckedBy,
				CheckedAt:      item.CheckedAt,
				Status:         item.Status,
				CreatedAt:      item.CreatedAt,
				UpdatedAt:      item.UpdatedAt,
			}
		}
	}

	for i, comment := range card.Comments {
		response.Comments[i] = models.CardCommentResponse{
			ID:        comment.ID,
			TenantID:  comment.TenantID,
			CardID:    comment.CardID,
			BoardID:   comment.BoardID,
			UserID:    comment.UserID,
			Content:   comment.Content,
			Type:      comment.Type,
			CreatedAt: comment.CreatedAt,
			UpdatedAt: comment.UpdatedAt,
		}
	}

	for i, activity := range card.Activities {
		response.Activities[i] = models.CardActivityResponse{
			ID:        activity.ID,
			TenantID:  activity.TenantID,
			BoardID:   activity.BoardID,
			CardID:    activity.CardID,
			ListID:    activity.ListID,
			UserID:    activity.UserID,
			Type:      activity.Type,
			Data:      activity.Data,
			OldValue:  activity.OldValue,
			NewValue:  activity.NewValue,
			CreatedAt: activity.CreatedAt,
		}
	}

	return response, nil
}

func (s *cardService) Move(ctx context.Context, tenantID, userID, id uint, request *models.MoveCardRequest) (*models.CardResponse, error) {
	existing, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("card not found")
		}
		return nil, fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, existing.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	if request.ListID != nil {
		list, err := s.listRepo.GetByID(ctx, tenantID, *request.ListID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("target list not found")
			}
			return nil, fmt.Errorf("failed to get target list: %w", err)
		}

		if !s.checkBoardAccess(ctx, tenantID, list.BoardID, userID) {
			return nil, errors.New("access denied to target list")
		}
	}

	if err := s.repo.Move(ctx, tenantID, id, request.ListID, request.Position); err != nil {
		s.logger.Error("failed to move card", utils.WithError(err))
		return nil, fmt.Errorf("failed to move card: %w", err)
	}

	return s.GetByID(ctx, tenantID, userID, id)
}

func (s *cardService) AssignMember(ctx context.Context, tenantID, userID, cardID uint, request *models.AssignCardMemberRequest) (*models.CardMemberResponse, error) {
	card, err := s.repo.GetByID(ctx, tenantID, cardID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("card not found")
		}
		return nil, fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, card.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	existing, err := s.repo.GetMember(ctx, tenantID, cardID, request.UserID)
	if err == nil && existing != nil {
		return nil, errors.New("user is already assigned to this card")
	}

	member := &models.TrelloCardMember{
		TenantID:   tenantID,
		CardID:     cardID,
		UserID:     request.UserID,
		AssignedBy: userID,
	}

	if err := s.repo.AssignMember(ctx, member); err != nil {
		s.logger.Error("failed to assign card member", utils.WithError(err))
		return nil, fmt.Errorf("failed to assign card member: %w", err)
	}

	return &models.CardMemberResponse{
		ID:         member.ID,
		UserID:     member.UserID,
		AssignedBy: member.AssignedBy,
		AssignedAt: member.AssignedAt,
	}, nil
}

func (s *cardService) UnassignMember(ctx context.Context, tenantID, userID, cardID, memberUserID uint) error {
	card, err := s.repo.GetByID(ctx, tenantID, cardID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("card not found")
		}
		return fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, card.BoardID, userID) {
		return errors.New("access denied")
	}

	if err := s.repo.UnassignMember(ctx, tenantID, cardID, memberUserID); err != nil {
		s.logger.Error("failed to unassign card member", utils.WithError(err))
		return fmt.Errorf("failed to unassign card member: %w", err)
	}

	return nil
}

func (s *cardService) ApplyLabel(ctx context.Context, tenantID, userID, cardID uint, request *models.ApplyCardLabelRequest) (*models.CardLabelResponse, error) {
	card, err := s.repo.GetByID(ctx, tenantID, cardID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("card not found")
		}
		return nil, fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, card.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	existing, err := s.repo.GetLabel(ctx, tenantID, cardID, request.LabelID)
	if err == nil && existing != nil {
		return nil, errors.New("label is already applied to this card")
	}

	label := &models.TrelloCardLabel{
		TenantID:  tenantID,
		CardID:    cardID,
		LabelID:   request.LabelID,
		AppliedBy: userID,
	}

	if err := s.repo.ApplyLabel(ctx, label); err != nil {
		s.logger.Error("failed to apply card label", utils.WithError(err))
		return nil, fmt.Errorf("failed to apply card label: %w", err)
	}

	return &models.CardLabelResponse{
		ID:        label.ID,
		LabelID:   label.LabelID,
		AppliedBy: label.AppliedBy,
		AppliedAt: label.AppliedAt,
	}, nil
}

func (s *cardService) RemoveLabel(ctx context.Context, tenantID, userID, cardID, labelID uint) error {
	card, err := s.repo.GetByID(ctx, tenantID, cardID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("card not found")
		}
		return fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, card.BoardID, userID) {
		return errors.New("access denied")
	}

	if err := s.repo.RemoveLabel(ctx, tenantID, cardID, labelID); err != nil {
		s.logger.Error("failed to remove card label", utils.WithError(err))
		return fmt.Errorf("failed to remove card label: %w", err)
	}

	return nil
}

func (s *cardService) CreateComment(ctx context.Context, tenantID, userID uint, request *models.CreateCommentRequest) (*models.CardCommentResponse, error) {
	card, err := s.repo.GetByID(ctx, tenantID, request.CardID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("card not found")
		}
		return nil, fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, card.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	comment := &models.TrelloCardComment{
		TenantID: tenantID,
		CardID:   request.CardID,
		BoardID:  card.BoardID,
		UserID:   userID,
		Content:  request.Content,
		Type:     request.Type,
	}

	if err := s.repo.CreateComment(ctx, comment); err != nil {
		s.logger.Error("failed to create comment", utils.WithError(err))
		return nil, fmt.Errorf("failed to create comment: %w", err)
	}

	return &models.CardCommentResponse{
		ID:        comment.ID,
		TenantID:  comment.TenantID,
		CardID:    comment.CardID,
		BoardID:   comment.BoardID,
		UserID:    comment.UserID,
		Content:   comment.Content,
		Type:      comment.Type,
		CreatedAt: comment.CreatedAt,
		UpdatedAt: comment.UpdatedAt,
	}, nil
}

func (s *cardService) Archive(ctx context.Context, tenantID, userID, id uint) error {
	existing, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("card not found")
		}
		return fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, existing.BoardID, userID) {
		return errors.New("access denied")
	}

	if err := s.repo.Archive(ctx, tenantID, id); err != nil {
		s.logger.Error("failed to archive card", utils.WithError(err))
		return fmt.Errorf("failed to archive card: %w", err)
	}

	return nil
}

func (s *cardService) Unarchive(ctx context.Context, tenantID, userID, id uint) error {
	existing, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("card not found")
		}
		return fmt.Errorf("failed to get card: %w", err)
	}

	if !s.checkBoardAccess(ctx, tenantID, existing.BoardID, userID) {
		return errors.New("access denied")
	}

	if err := s.repo.Unarchive(ctx, tenantID, id); err != nil {
		s.logger.Error("failed to unarchive card", utils.WithError(err))
		return fmt.Errorf("failed to unarchive card: %w", err)
	}

	return nil
}

func (s *cardService) Search(ctx context.Context, tenantID, userID, boardID uint, query string, pagination *pagination.CursorPagination) ([]models.CardResponse, *pagination.CursorResponse, error) {
	if !s.checkBoardAccess(ctx, tenantID, boardID, userID) {
		return nil, nil, errors.New("access denied")
	}

	cards, cursor, err := s.repo.Search(ctx, tenantID, boardID, query, pagination)
	if err != nil {
		s.logger.Error("failed to search cards", utils.WithError(err))
		return nil, nil, fmt.Errorf("failed to search cards: %w", err)
	}

	responses := make([]models.CardResponse, len(cards))
	for i, card := range cards {
		responses[i] = *s.modelToResponse(&card)
	}

	return responses, cursor, nil
}

func (s *cardService) checkBoardAccess(ctx context.Context, tenantID, boardID, userID uint) bool {
	isMember, err := s.boardRepo.IsMember(ctx, tenantID, boardID, userID)
	if err != nil {
		s.logger.Error("failed to check board access", utils.WithError(err))
		return false
	}
	return isMember
}

func (s *cardService) modelToResponse(card *models.TrelloCard) *models.CardResponse {
	return &models.CardResponse{
		ID:           card.ID,
		TenantID:     card.TenantID,
		BoardID:      card.BoardID,
		ListID:       card.ListID,
		Title:        card.Title,
		Description:  card.Description,
		Position:     card.Position,
		DueDate:      card.DueDate,
		StartDate:    card.StartDate,
		DueComplete:  card.DueComplete,
		IsClosed:     card.IsClosed,
		IsSubscribed: card.IsSubscribed,
		CoverType:    card.CoverType,
		CoverValue:   card.CoverValue,
		CoverColor:   card.CoverColor,
		CoverSize:    card.CoverSize,
		Badges:       card.Badges,
		CustomFields: card.CustomFields,
		CreatedBy:    card.CreatedBy,
		Status:       card.Status,
		CreatedAt:    card.CreatedAt,
		UpdatedAt:    card.UpdatedAt,
	}
}
