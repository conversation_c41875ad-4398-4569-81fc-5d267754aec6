package services

import (
	"context"
	"errors"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

type workspaceService struct {
	repo   repositories.WorkspaceRepository
	logger utils.Logger
}

func NewWorkspaceService(repo repositories.WorkspaceRepository, logger utils.Logger) WorkspaceService {
	return &workspaceService{
		repo:   repo,
		logger: logger,
	}
}

func (s *workspaceService) Create(ctx context.Context, tenantID, userID uint, request *models.CreateWorkspaceRequest) (*models.WorkspaceResponse, error) {
	workspace := &models.TrelloWorkspace{
		TenantID:    tenantID,
		Name:        request.Name,
		Description: request.Description,
		Visibility:  request.Visibility,
		LogoURL:     request.LogoURL,
		WebsiteURL:  request.WebsiteURL,
		Settings:    request.Settings,
		CreatedBy:   userID,
		Status:      models.TrelloStatusActive,
	}

	if err := s.repo.Create(ctx, workspace); err != nil {
		s.logger.Error("failed to create workspace", utils.WithError(err))
		return nil, fmt.Errorf("failed to create workspace: %w", err)
	}

	member := &models.TrelloWorkspaceMember{
		TenantID:    tenantID,
		WorkspaceID: workspace.ID,
		UserID:      userID,
		Role:        models.MemberRoleOwner,
		InvitedBy:   userID,
		Status:      models.MemberStatusActive,
	}

	if err := s.repo.AddMember(ctx, member); err != nil {
		s.logger.Error("failed to add workspace owner", utils.WithError(err))
		return nil, fmt.Errorf("failed to add workspace owner: %w", err)
	}

	return s.modelToResponse(workspace), nil
}

func (s *workspaceService) GetByID(ctx context.Context, tenantID, userID, id uint) (*models.WorkspaceResponse, error) {
	if !s.checkAccess(ctx, tenantID, id, userID) {
		return nil, errors.New("access denied")
	}

	workspace, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("workspace not found")
		}
		s.logger.Error("failed to get workspace", utils.WithError(err))
		return nil, fmt.Errorf("failed to get workspace: %w", err)
	}

	return s.modelToResponse(workspace), nil
}

func (s *workspaceService) Update(ctx context.Context, tenantID, userID, id uint, request *models.UpdateWorkspaceRequest) (*models.WorkspaceResponse, error) {
	role, err := s.repo.GetMemberRole(ctx, tenantID, id, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("access denied")
		}
		return nil, fmt.Errorf("failed to check permissions: %w", err)
	}

	if role != models.MemberRoleOwner && role != models.MemberRoleAdmin {
		return nil, errors.New("insufficient permissions")
	}

	workspace := &models.TrelloWorkspace{}
	if request.Name != nil {
		workspace.Name = *request.Name
	}
	if request.Description != nil {
		workspace.Description = request.Description
	}
	if request.Visibility != "" {
		workspace.Visibility = request.Visibility
	}
	if request.LogoURL != nil {
		workspace.LogoURL = request.LogoURL
	}
	if request.WebsiteURL != nil {
		workspace.WebsiteURL = request.WebsiteURL
	}
	if request.Settings != nil {
		workspace.Settings = request.Settings
	}

	if err := s.repo.Update(ctx, tenantID, id, workspace); err != nil {
		s.logger.Error("failed to update workspace", utils.WithError(err))
		return nil, fmt.Errorf("failed to update workspace: %w", err)
	}

	return s.GetByID(ctx, tenantID, userID, id)
}

func (s *workspaceService) Delete(ctx context.Context, tenantID, userID, id uint) error {
	role, err := s.repo.GetMemberRole(ctx, tenantID, id, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("access denied")
		}
		return fmt.Errorf("failed to check permissions: %w", err)
	}

	if role != models.MemberRoleOwner {
		return errors.New("only workspace owner can delete workspace")
	}

	if err := s.repo.Delete(ctx, tenantID, id); err != nil {
		s.logger.Error("failed to delete workspace", utils.WithError(err))
		return fmt.Errorf("failed to delete workspace: %w", err)
	}

	return nil
}

func (s *workspaceService) List(ctx context.Context, tenantID, userID uint, pagination *pagination.CursorPagination) ([]models.WorkspaceResponse, *pagination.CursorResponse, error) {
	workspaces, cursor, err := s.repo.GetUserWorkspaces(ctx, tenantID, userID, pagination)
	if err != nil {
		s.logger.Error("failed to list workspaces", utils.WithError(err))
		return nil, nil, fmt.Errorf("failed to list workspaces: %w", err)
	}

	responses := make([]models.WorkspaceResponse, len(workspaces))
	for i, workspace := range workspaces {
		responses[i] = *s.modelToResponse(&workspace)
	}

	return responses, cursor, nil
}

func (s *workspaceService) GetUserWorkspaces(ctx context.Context, tenantID, userID uint, pagination *pagination.CursorPagination) ([]models.WorkspaceResponse, *pagination.CursorResponse, error) {
	return s.List(ctx, tenantID, userID, pagination)
}

func (s *workspaceService) GetWithBoards(ctx context.Context, tenantID, userID, id uint) (*models.WorkspaceWithBoardsResponse, error) {
	if !s.checkAccess(ctx, tenantID, id, userID) {
		return nil, errors.New("access denied")
	}

	workspace, err := s.repo.GetWithBoards(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("workspace not found")
		}
		s.logger.Error("failed to get workspace with boards", utils.WithError(err))
		return nil, fmt.Errorf("failed to get workspace with boards: %w", err)
	}

	response := &models.WorkspaceWithBoardsResponse{
		WorkspaceResponse: *s.modelToResponse(workspace),
		Boards:            make([]models.BoardSummaryResponse, len(workspace.Boards)),
	}

	for i, board := range workspace.Boards {
		response.Boards[i] = models.BoardSummaryResponse{
			ID:              board.ID,
			Name:            board.Name,
			BackgroundType:  board.BackgroundType,
			BackgroundValue: board.BackgroundValue,
			Visibility:      board.Visibility,
			IsClosed:        board.IsClosed,
			IsStarred:       board.IsStarred,
			UpdatedAt:       board.UpdatedAt,
		}
	}

	return response, nil
}

func (s *workspaceService) GetWithMembers(ctx context.Context, tenantID, userID, id uint) (*models.WorkspaceWithMembersResponse, error) {
	if !s.checkAccess(ctx, tenantID, id, userID) {
		return nil, errors.New("access denied")
	}

	workspace, err := s.repo.GetWithMembers(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("workspace not found")
		}
		s.logger.Error("failed to get workspace with members", utils.WithError(err))
		return nil, fmt.Errorf("failed to get workspace with members: %w", err)
	}

	response := &models.WorkspaceWithMembersResponse{
		WorkspaceResponse: *s.modelToResponse(workspace),
		Members:           make([]models.WorkspaceMemberResponse, len(workspace.Members)),
	}

	for i, member := range workspace.Members {
		response.Members[i] = models.WorkspaceMemberResponse{
			ID:          member.ID,
			TenantID:    member.TenantID,
			WorkspaceID: member.WorkspaceID,
			UserID:      member.UserID,
			Role:        member.Role,
			InvitedBy:   member.InvitedBy,
			JoinedAt:    member.JoinedAt,
			Status:      member.Status,
		}
	}

	return response, nil
}

func (s *workspaceService) AddMember(ctx context.Context, tenantID, userID, workspaceID uint, request *models.AddWorkspaceMemberRequest) (*models.WorkspaceMemberResponse, error) {
	role, err := s.repo.GetMemberRole(ctx, tenantID, workspaceID, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("access denied")
		}
		return nil, fmt.Errorf("failed to check permissions: %w", err)
	}

	if role != models.MemberRoleOwner && role != models.MemberRoleAdmin {
		return nil, errors.New("insufficient permissions")
	}

	exists, err := s.repo.IsMember(ctx, tenantID, workspaceID, request.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing membership: %w", err)
	}
	if exists {
		return nil, errors.New("user is already a member")
	}

	member := &models.TrelloWorkspaceMember{
		TenantID:    tenantID,
		WorkspaceID: workspaceID,
		UserID:      request.UserID,
		Role:        request.Role,
		InvitedBy:   userID,
		Status:      models.MemberStatusActive,
	}

	if err := s.repo.AddMember(ctx, member); err != nil {
		s.logger.Error("failed to add workspace member", utils.WithError(err))
		return nil, fmt.Errorf("failed to add workspace member: %w", err)
	}

	return &models.WorkspaceMemberResponse{
		ID:          member.ID,
		TenantID:    member.TenantID,
		WorkspaceID: member.WorkspaceID,
		UserID:      member.UserID,
		Role:        member.Role,
		InvitedBy:   member.InvitedBy,
		JoinedAt:    member.JoinedAt,
		Status:      member.Status,
	}, nil
}

func (s *workspaceService) RemoveMember(ctx context.Context, tenantID, userID, workspaceID, memberUserID uint) error {
	role, err := s.repo.GetMemberRole(ctx, tenantID, workspaceID, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("access denied")
		}
		return fmt.Errorf("failed to check permissions: %w", err)
	}

	if role != models.MemberRoleOwner && role != models.MemberRoleAdmin {
		return errors.New("insufficient permissions")
	}

	if userID == memberUserID {
		return errors.New("cannot remove yourself")
	}

	if err := s.repo.RemoveMember(ctx, tenantID, workspaceID, memberUserID); err != nil {
		s.logger.Error("failed to remove workspace member", utils.WithError(err))
		return fmt.Errorf("failed to remove workspace member: %w", err)
	}

	return nil
}

func (s *workspaceService) UpdateMember(ctx context.Context, tenantID, userID, workspaceID, memberUserID uint, request *models.UpdateWorkspaceMemberRequest) (*models.WorkspaceMemberResponse, error) {
	role, err := s.repo.GetMemberRole(ctx, tenantID, workspaceID, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("access denied")
		}
		return nil, fmt.Errorf("failed to check permissions: %w", err)
	}

	if role != models.MemberRoleOwner && role != models.MemberRoleAdmin {
		return nil, errors.New("insufficient permissions")
	}

	member := &models.TrelloWorkspaceMember{}
	if request.Role != nil {
		member.Role = *request.Role
	}
	if request.Status != nil {
		member.Status = *request.Status
	}

	if err := s.repo.UpdateMember(ctx, tenantID, workspaceID, memberUserID, member); err != nil {
		s.logger.Error("failed to update workspace member", utils.WithError(err))
		return nil, fmt.Errorf("failed to update workspace member: %w", err)
	}

	updated, err := s.repo.GetMember(ctx, tenantID, workspaceID, memberUserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated member: %w", err)
	}

	return &models.WorkspaceMemberResponse{
		ID:          updated.ID,
		TenantID:    updated.TenantID,
		WorkspaceID: updated.WorkspaceID,
		UserID:      updated.UserID,
		Role:        updated.Role,
		InvitedBy:   updated.InvitedBy,
		JoinedAt:    updated.JoinedAt,
		Status:      updated.Status,
	}, nil
}

func (s *workspaceService) ListMembers(ctx context.Context, tenantID, userID, workspaceID uint) ([]models.WorkspaceMemberResponse, error) {
	if !s.checkAccess(ctx, tenantID, workspaceID, userID) {
		return nil, errors.New("access denied")
	}

	members, err := s.repo.ListMembers(ctx, tenantID, workspaceID)
	if err != nil {
		s.logger.Error("failed to list workspace members", utils.WithError(err))
		return nil, fmt.Errorf("failed to list workspace members: %w", err)
	}

	responses := make([]models.WorkspaceMemberResponse, len(members))
	for i, member := range members {
		responses[i] = models.WorkspaceMemberResponse{
			ID:          member.ID,
			TenantID:    member.TenantID,
			WorkspaceID: member.WorkspaceID,
			UserID:      member.UserID,
			Role:        member.Role,
			InvitedBy:   member.InvitedBy,
			JoinedAt:    member.JoinedAt,
			Status:      member.Status,
		}
	}

	return responses, nil
}

func (s *workspaceService) checkAccess(ctx context.Context, tenantID, workspaceID, userID uint) bool {
	isMember, err := s.repo.IsMember(ctx, tenantID, workspaceID, userID)
	if err != nil {
		s.logger.Error("failed to check workspace access", utils.WithError(err))
		return false
	}
	return isMember
}

func (s *workspaceService) modelToResponse(workspace *models.TrelloWorkspace) *models.WorkspaceResponse {
	return &models.WorkspaceResponse{
		ID:          workspace.ID,
		TenantID:    workspace.TenantID,
		Name:        workspace.Name,
		Description: workspace.Description,
		Visibility:  workspace.Visibility,
		LogoURL:     workspace.LogoURL,
		WebsiteURL:  workspace.WebsiteURL,
		Settings:    workspace.Settings,
		CreatedBy:   workspace.CreatedBy,
		Status:      workspace.Status,
		CreatedAt:   workspace.CreatedAt,
		UpdatedAt:   workspace.UpdatedAt,
	}
}
