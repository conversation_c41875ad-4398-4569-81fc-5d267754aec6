package services

import (
	"context"
	"errors"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"gorm.io/gorm"
)

type boardService struct {
	repo          repositories.BoardRepository
	workspaceRepo repositories.WorkspaceRepository
	logger        utils.Logger
}

func NewBoardService(repo repositories.BoardRepository, workspaceRepo repositories.WorkspaceRepository, logger utils.Logger) BoardService {
	return &boardService{
		repo:          repo,
		workspaceRepo: workspaceRepo,
		logger:        logger,
	}
}

func (s *boardService) Create(ctx context.Context, tenantID, userID uint, request *models.CreateBoardRequest) (*models.BoardResponse, error) {
	isMember, err := s.workspaceRepo.IsMember(ctx, tenantID, request.WorkspaceID, userID)
	if err != nil {
		return nil, fmt.Errorf("failed to check workspace membership: %w", err)
	}
	if !isMember {
		return nil, errors.New("access denied: not a workspace member")
	}

	backgroundValue := "#0079bf" // Default background color
	if request.BackgroundValue != nil {
		backgroundValue = *request.BackgroundValue
	}

	board := &models.TrelloBoard{
		TenantID:        tenantID,
		WorkspaceID:     request.WorkspaceID,
		Name:            request.Name,
		Description:     request.Description,
		BackgroundType:  request.BackgroundType,
		BackgroundValue: backgroundValue,
		Visibility:      request.Visibility,
		Preferences:     request.Preferences,
		CreatedBy:       userID,
		Status:          models.TrelloStatusActive,
	}

	if err := s.repo.Create(ctx, board); err != nil {
		s.logger.Error("failed to create board", utils.WithError(err))
		return nil, fmt.Errorf("failed to create board: %w", err)
	}

	member := &models.TrelloBoardMember{
		TenantID:  tenantID,
		BoardID:   board.ID,
		UserID:    userID,
		Role:      models.MemberRoleOwner,
		InvitedBy: userID,
		Status:    models.MemberStatusActive,
	}

	if err := s.repo.AddMember(ctx, member); err != nil {
		s.logger.Error("failed to add board owner", utils.WithError(err))
		return nil, fmt.Errorf("failed to add board owner: %w", err)
	}

	return s.modelToResponse(board), nil
}

func (s *boardService) GetByID(ctx context.Context, tenantID, userID, id uint) (*models.BoardResponse, error) {
	if !s.checkAccess(ctx, tenantID, id, userID) {
		return nil, errors.New("access denied")
	}

	board, err := s.repo.GetByID(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("board not found")
		}
		s.logger.Error("failed to get board", utils.WithError(err))
		return nil, fmt.Errorf("failed to get board: %w", err)
	}

	return s.modelToResponse(board), nil
}

func (s *boardService) Update(ctx context.Context, tenantID, userID, id uint, request *models.UpdateBoardRequest) (*models.BoardResponse, error) {
	role, err := s.repo.GetMemberRole(ctx, tenantID, id, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("access denied")
		}
		return nil, fmt.Errorf("failed to check permissions: %w", err)
	}

	if role != models.MemberRoleOwner && role != models.MemberRoleAdmin {
		return nil, errors.New("insufficient permissions")
	}

	board := &models.TrelloBoard{}
	if request.Name != nil {
		board.Name = *request.Name
	}
	if request.Description != nil {
		board.Description = request.Description
	}
	if request.BackgroundType != "" {
		board.BackgroundType = request.BackgroundType
	}
	if request.BackgroundValue != nil {
		board.BackgroundValue = *request.BackgroundValue
	}
	if request.Visibility != "" {
		board.Visibility = request.Visibility
	}
	if request.IsClosed != nil {
		board.IsClosed = *request.IsClosed
	}
	if request.IsStarred != nil {
		board.IsStarred = *request.IsStarred
	}
	if request.Preferences != nil {
		board.Preferences = request.Preferences
	}

	if err := s.repo.Update(ctx, tenantID, id, board); err != nil {
		s.logger.Error("failed to update board", utils.WithError(err))
		return nil, fmt.Errorf("failed to update board: %w", err)
	}

	return s.GetByID(ctx, tenantID, userID, id)
}

func (s *boardService) Delete(ctx context.Context, tenantID, userID, id uint) error {
	role, err := s.repo.GetMemberRole(ctx, tenantID, id, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("access denied")
		}
		return fmt.Errorf("failed to check permissions: %w", err)
	}

	if role != models.MemberRoleOwner {
		return errors.New("only board owner can delete board")
	}

	if err := s.repo.Delete(ctx, tenantID, id); err != nil {
		s.logger.Error("failed to delete board", utils.WithError(err))
		return fmt.Errorf("failed to delete board: %w", err)
	}

	return nil
}

func (s *boardService) List(ctx context.Context, tenantID, userID uint, pagination *pagination.CursorPagination) ([]models.BoardResponse, *pagination.CursorResponse, error) {
	boards, cursor, err := s.repo.GetUserBoards(ctx, tenantID, userID, pagination)
	if err != nil {
		s.logger.Error("failed to list boards", utils.WithError(err))
		return nil, nil, fmt.Errorf("failed to list boards: %w", err)
	}

	responses := make([]models.BoardResponse, len(boards))
	for i, board := range boards {
		responses[i] = *s.modelToResponse(&board)
	}

	return responses, cursor, nil
}

func (s *boardService) ListByWorkspace(ctx context.Context, tenantID, userID, workspaceID uint, pagination *pagination.CursorPagination) ([]models.BoardResponse, *pagination.CursorResponse, error) {
	isMember, err := s.workspaceRepo.IsMember(ctx, tenantID, workspaceID, userID)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to check workspace membership: %w", err)
	}
	if !isMember {
		return nil, nil, errors.New("access denied: not a workspace member")
	}

	boards, cursor, err := s.repo.ListByWorkspace(ctx, tenantID, workspaceID, pagination)
	if err != nil {
		s.logger.Error("failed to list boards by workspace", utils.WithError(err))
		return nil, nil, fmt.Errorf("failed to list boards by workspace: %w", err)
	}

	responses := make([]models.BoardResponse, len(boards))
	for i, board := range boards {
		responses[i] = *s.modelToResponse(&board)
	}

	return responses, cursor, nil
}

func (s *boardService) GetUserBoards(ctx context.Context, tenantID, userID uint, pagination *pagination.CursorPagination) ([]models.BoardResponse, *pagination.CursorResponse, error) {
	return s.List(ctx, tenantID, userID, pagination)
}

func (s *boardService) GetStarredBoards(ctx context.Context, tenantID, userID uint, pagination *pagination.CursorPagination) ([]models.BoardResponse, *pagination.CursorResponse, error) {
	boards, cursor, err := s.repo.GetStarredBoards(ctx, tenantID, userID, pagination)
	if err != nil {
		s.logger.Error("failed to get starred boards", utils.WithError(err))
		return nil, nil, fmt.Errorf("failed to get starred boards: %w", err)
	}

	responses := make([]models.BoardResponse, len(boards))
	for i, board := range boards {
		responses[i] = *s.modelToResponse(&board)
	}

	return responses, cursor, nil
}

func (s *boardService) GetWithLists(ctx context.Context, tenantID, userID, id uint) (*models.BoardWithListsResponse, error) {
	if !s.checkAccess(ctx, tenantID, id, userID) {
		return nil, errors.New("access denied")
	}

	board, err := s.repo.GetWithLists(ctx, tenantID, id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("board not found")
		}
		s.logger.Error("failed to get board with lists", utils.WithError(err))
		return nil, fmt.Errorf("failed to get board with lists: %w", err)
	}

	response := &models.BoardWithListsResponse{
		BoardResponse: *s.modelToResponse(board),
		Lists:         make([]models.ListWithCardsResponse, len(board.Lists)),
	}

	for i, list := range board.Lists {
		response.Lists[i] = models.ListWithCardsResponse{
			ListResponse: models.ListResponse{
				ID:           list.ID,
				TenantID:     list.TenantID,
				BoardID:      list.BoardID,
				Name:         list.Name,
				Position:     list.Position,
				IsClosed:     list.IsClosed,
				IsSubscribed: list.IsSubscribed,
				Settings:     list.Settings,
				Status:       list.Status,
				CreatedAt:    list.CreatedAt,
				UpdatedAt:    list.UpdatedAt,
			},
			Cards: make([]models.CardSummaryResponse, len(list.Cards)),
		}

		for j, card := range list.Cards {
			response.Lists[i].Cards[j] = models.CardSummaryResponse{
				ID:          card.ID,
				Title:       card.Title,
				Position:    card.Position,
				DueDate:     card.DueDate,
				DueComplete: card.DueComplete,
				IsClosed:    card.IsClosed,
				CoverType:   card.CoverType,
				CoverValue:  card.CoverValue,
				CoverColor:  card.CoverColor,
				UpdatedAt:   card.UpdatedAt,
			}
		}
	}

	return response, nil
}

func (s *boardService) AddMember(ctx context.Context, tenantID, userID, boardID uint, request *models.AddBoardMemberRequest) (*models.BoardMemberResponse, error) {
	role, err := s.repo.GetMemberRole(ctx, tenantID, boardID, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("access denied")
		}
		return nil, fmt.Errorf("failed to check permissions: %w", err)
	}

	if role != models.MemberRoleOwner && role != models.MemberRoleAdmin {
		return nil, errors.New("insufficient permissions")
	}

	exists, err := s.repo.IsMember(ctx, tenantID, boardID, request.UserID)
	if err != nil {
		return nil, fmt.Errorf("failed to check existing membership: %w", err)
	}
	if exists {
		return nil, errors.New("user is already a member")
	}

	member := &models.TrelloBoardMember{
		TenantID:  tenantID,
		BoardID:   boardID,
		UserID:    request.UserID,
		Role:      request.Role,
		InvitedBy: userID,
		Status:    models.MemberStatusActive,
	}

	if err := s.repo.AddMember(ctx, member); err != nil {
		s.logger.Error("failed to add board member", utils.WithError(err))
		return nil, fmt.Errorf("failed to add board member: %w", err)
	}

	return &models.BoardMemberResponse{
		ID:        member.ID,
		TenantID:  member.TenantID,
		BoardID:   member.BoardID,
		UserID:    member.UserID,
		Role:      member.Role,
		InvitedBy: member.InvitedBy,
		JoinedAt:  member.JoinedAt,
		Status:    member.Status,
	}, nil
}

func (s *boardService) RemoveMember(ctx context.Context, tenantID, userID, boardID, memberUserID uint) error {
	role, err := s.repo.GetMemberRole(ctx, tenantID, boardID, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("access denied")
		}
		return fmt.Errorf("failed to check permissions: %w", err)
	}

	if role != models.MemberRoleOwner && role != models.MemberRoleAdmin {
		return errors.New("insufficient permissions")
	}

	if userID == memberUserID {
		return errors.New("cannot remove yourself")
	}

	if err := s.repo.RemoveMember(ctx, tenantID, boardID, memberUserID); err != nil {
		s.logger.Error("failed to remove board member", utils.WithError(err))
		return fmt.Errorf("failed to remove board member: %w", err)
	}

	return nil
}

func (s *boardService) UpdateMember(ctx context.Context, tenantID, userID, boardID, memberUserID uint, request *models.UpdateBoardMemberRequest) (*models.BoardMemberResponse, error) {
	role, err := s.repo.GetMemberRole(ctx, tenantID, boardID, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("access denied")
		}
		return nil, fmt.Errorf("failed to check permissions: %w", err)
	}

	if role != models.MemberRoleOwner && role != models.MemberRoleAdmin {
		return nil, errors.New("insufficient permissions")
	}

	member := &models.TrelloBoardMember{}
	if request.Role != nil {
		member.Role = *request.Role
	}
	if request.Status != nil {
		member.Status = *request.Status
	}

	if err := s.repo.UpdateMember(ctx, tenantID, boardID, memberUserID, member); err != nil {
		s.logger.Error("failed to update board member", utils.WithError(err))
		return nil, fmt.Errorf("failed to update board member: %w", err)
	}

	updated, err := s.repo.GetMember(ctx, tenantID, boardID, memberUserID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated member: %w", err)
	}

	return &models.BoardMemberResponse{
		ID:        updated.ID,
		TenantID:  updated.TenantID,
		BoardID:   updated.BoardID,
		UserID:    updated.UserID,
		Role:      updated.Role,
		InvitedBy: updated.InvitedBy,
		JoinedAt:  updated.JoinedAt,
		Status:    updated.Status,
	}, nil
}

func (s *boardService) ListMembers(ctx context.Context, tenantID, userID, boardID uint) ([]models.BoardMemberResponse, error) {
	if !s.checkAccess(ctx, tenantID, boardID, userID) {
		return nil, errors.New("access denied")
	}

	members, err := s.repo.ListMembers(ctx, tenantID, boardID)
	if err != nil {
		s.logger.Error("failed to list board members", utils.WithError(err))
		return nil, fmt.Errorf("failed to list board members: %w", err)
	}

	responses := make([]models.BoardMemberResponse, len(members))
	for i, member := range members {
		responses[i] = models.BoardMemberResponse{
			ID:        member.ID,
			TenantID:  member.TenantID,
			BoardID:   member.BoardID,
			UserID:    member.UserID,
			Role:      member.Role,
			InvitedBy: member.InvitedBy,
			JoinedAt:  member.JoinedAt,
			Status:    member.Status,
		}
	}

	return responses, nil
}

func (s *boardService) CreateLabel(ctx context.Context, tenantID, userID uint, request *models.CreateLabelRequest) (*models.LabelResponse, error) {
	if !s.checkAccess(ctx, tenantID, request.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	label := &models.TrelloBoardLabel{
		TenantID:  tenantID,
		BoardID:   request.BoardID,
		Name:      request.Name,
		Color:     request.Color,
		Uses:      0,
		CreatedBy: userID,
		Status:    models.TrelloStatusActive,
	}

	if err := s.repo.CreateLabel(ctx, label); err != nil {
		s.logger.Error("failed to create label", utils.WithError(err))
		return nil, fmt.Errorf("failed to create label: %w", err)
	}

	return &models.LabelResponse{
		ID:        label.ID,
		TenantID:  label.TenantID,
		BoardID:   label.BoardID,
		Name:      label.Name,
		Color:     label.Color,
		Uses:      label.Uses,
		CreatedBy: label.CreatedBy,
		Status:    label.Status,
		CreatedAt: label.CreatedAt,
		UpdatedAt: label.UpdatedAt,
	}, nil
}

func (s *boardService) UpdateLabel(ctx context.Context, tenantID, userID, labelID uint, request *models.UpdateLabelRequest) (*models.LabelResponse, error) {
	existing, err := s.repo.GetLabel(ctx, tenantID, labelID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("label not found")
		}
		return nil, fmt.Errorf("failed to get label: %w", err)
	}

	if !s.checkAccess(ctx, tenantID, existing.BoardID, userID) {
		return nil, errors.New("access denied")
	}

	label := &models.TrelloBoardLabel{}
	if request.Name != nil {
		label.Name = *request.Name
	}
	if request.Color != nil {
		label.Color = *request.Color
	}

	if err := s.repo.UpdateLabel(ctx, tenantID, labelID, label); err != nil {
		s.logger.Error("failed to update label", utils.WithError(err))
		return nil, fmt.Errorf("failed to update label: %w", err)
	}

	updated, err := s.repo.GetLabel(ctx, tenantID, labelID)
	if err != nil {
		return nil, fmt.Errorf("failed to get updated label: %w", err)
	}

	return &models.LabelResponse{
		ID:        updated.ID,
		TenantID:  updated.TenantID,
		BoardID:   updated.BoardID,
		Name:      updated.Name,
		Color:     updated.Color,
		Uses:      updated.Uses,
		CreatedBy: updated.CreatedBy,
		Status:    updated.Status,
		CreatedAt: updated.CreatedAt,
		UpdatedAt: updated.UpdatedAt,
	}, nil
}

func (s *boardService) DeleteLabel(ctx context.Context, tenantID, userID, labelID uint) error {
	existing, err := s.repo.GetLabel(ctx, tenantID, labelID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("label not found")
		}
		return fmt.Errorf("failed to get label: %w", err)
	}

	if !s.checkAccess(ctx, tenantID, existing.BoardID, userID) {
		return errors.New("access denied")
	}

	if err := s.repo.DeleteLabel(ctx, tenantID, labelID); err != nil {
		s.logger.Error("failed to delete label", utils.WithError(err))
		return fmt.Errorf("failed to delete label: %w", err)
	}

	return nil
}

func (s *boardService) ListLabels(ctx context.Context, tenantID, userID, boardID uint) ([]models.LabelResponse, error) {
	if !s.checkAccess(ctx, tenantID, boardID, userID) {
		return nil, errors.New("access denied")
	}

	labels, err := s.repo.ListLabels(ctx, tenantID, boardID)
	if err != nil {
		s.logger.Error("failed to list labels", utils.WithError(err))
		return nil, fmt.Errorf("failed to list labels: %w", err)
	}

	responses := make([]models.LabelResponse, len(labels))
	for i, label := range labels {
		responses[i] = models.LabelResponse{
			ID:        label.ID,
			TenantID:  label.TenantID,
			BoardID:   label.BoardID,
			Name:      label.Name,
			Color:     label.Color,
			Uses:      label.Uses,
			CreatedBy: label.CreatedBy,
			Status:    label.Status,
			CreatedAt: label.CreatedAt,
			UpdatedAt: label.UpdatedAt,
		}
	}

	return responses, nil
}

func (s *boardService) checkAccess(ctx context.Context, tenantID, boardID, userID uint) bool {
	isMember, err := s.repo.IsMember(ctx, tenantID, boardID, userID)
	if err != nil {
		s.logger.Error("failed to check board access", utils.WithError(err))
		return false
	}
	return isMember
}

func (s *boardService) modelToResponse(board *models.TrelloBoard) *models.BoardResponse {
	return &models.BoardResponse{
		ID:               board.ID,
		TenantID:         board.TenantID,
		WorkspaceID:      board.WorkspaceID,
		Name:             board.Name,
		Description:      board.Description,
		BackgroundType:   board.BackgroundType,
		BackgroundValue:  board.BackgroundValue,
		Visibility:       board.Visibility,
		IsClosed:         board.IsClosed,
		IsStarred:        board.IsStarred,
		Preferences:      board.Preferences,
		LabelsNormalized: board.LabelsNormalized,
		CreatedBy:        board.CreatedBy,
		Status:           board.Status,
		CreatedAt:        board.CreatedAt,
		UpdatedAt:        board.UpdatedAt,
	}
}
