package mysql

import (
	"context"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"gorm.io/gorm"
)

type boardRepository struct {
	db *gorm.DB
}

func NewBoardRepository(db *gorm.DB) repositories.BoardRepository {
	return &boardRepository{db: db}
}

func (r *boardRepository) Create(ctx context.Context, board *models.TrelloBoard) error {
	return r.db.WithContext(ctx).Create(board).Error
}

func (r *boardRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.TrelloBoard, error) {
	var board models.TrelloBoard
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, id, models.TrelloStatusDeleted).
		First(&board).Error
	if err != nil {
		return nil, err
	}
	return &board, nil
}

func (r *boardRepository) Update(ctx context.Context, tenantID, id uint, board *models.TrelloBoard) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Updates(board).Error
}

func (r *boardRepository) Delete(ctx context.Context, tenantID, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloBoard{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Update("status", models.TrelloStatusDeleted).Error
}

func (r *boardRepository) List(ctx context.Context, tenantID uint, pag *pagination.CursorPagination) ([]models.TrelloBoard, *pagination.CursorResponse, error) {
	var boards []models.TrelloBoard
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status != ?", tenantID, models.TrelloStatusDeleted).
		Order("created_at DESC")

	if pag.Cursor != "" {
		query = query.Where("id < ?", pag.Cursor)
	}
	query = query.Limit(int(pag.Limit) + 1)

	if err := query.Find(&boards).Error; err != nil {
		return nil, nil, err
	}

	response := &pagination.CursorResponse{
		HasNext: len(boards) > int(pag.Limit),
		Limit:   int(pag.Limit),
	}

	if response.HasNext {
		boards = boards[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", boards[len(boards)-1].ID)
	}

	return boards, response, nil
}

func (r *boardRepository) ListByWorkspace(ctx context.Context, tenantID, workspaceID uint, pag *pagination.CursorPagination) ([]models.TrelloBoard, *pagination.CursorResponse, error) {
	var boards []models.TrelloBoard
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND workspace_id = ? AND status != ?", tenantID, workspaceID, models.TrelloStatusDeleted).
		Order("created_at DESC")

	if pag.Cursor != "" {
		query = query.Where("id < ?", pag.Cursor)
	}
	query = query.Limit(int(pag.Limit) + 1)

	if err := query.Find(&boards).Error; err != nil {
		return nil, nil, err
	}

	response := &pagination.CursorResponse{
		HasNext: len(boards) > int(pag.Limit),
		Limit:   int(pag.Limit),
	}

	if response.HasNext {
		boards = boards[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", boards[len(boards)-1].ID)
	}

	return boards, response, nil
}

func (r *boardRepository) GetByName(ctx context.Context, tenantID, workspaceID uint, name string) (*models.TrelloBoard, error) {
	var board models.TrelloBoard
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND workspace_id = ? AND name = ? AND status != ?", tenantID, workspaceID, name, models.TrelloStatusDeleted).
		First(&board).Error
	if err != nil {
		return nil, err
	}
	return &board, nil
}

func (r *boardRepository) GetWithLists(ctx context.Context, tenantID, id uint) (*models.TrelloBoard, error) {
	var board models.TrelloBoard
	err := r.db.WithContext(ctx).
		Preload("Lists", "status != ?", models.TrelloStatusDeleted).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, id, models.TrelloStatusDeleted).
		First(&board).Error
	if err != nil {
		return nil, err
	}
	return &board, nil
}

func (r *boardRepository) GetWithMembers(ctx context.Context, tenantID, id uint) (*models.TrelloBoard, error) {
	var board models.TrelloBoard
	err := r.db.WithContext(ctx).
		Preload("Members", "status != ?", models.MemberStatusDeleted).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, id, models.TrelloStatusDeleted).
		First(&board).Error
	if err != nil {
		return nil, err
	}
	return &board, nil
}

func (r *boardRepository) GetUserBoards(ctx context.Context, tenantID, userID uint, pag *pagination.CursorPagination) ([]models.TrelloBoard, *pagination.CursorResponse, error) {
	var boards []models.TrelloBoard
	query := r.db.WithContext(ctx).
		Joins("JOIN trello_board_members ON trello_boards.id = trello_board_members.board_id").
		Where("trello_boards.tenant_id = ? AND trello_board_members.user_id = ? AND trello_boards.status != ? AND trello_board_members.status = ?",
			tenantID, userID, models.TrelloStatusDeleted, models.MemberStatusActive).
		Order("trello_board_members.joined_at DESC")

	if pag.Cursor != "" {
		query = query.Where("trello_boards.id < ?", pag.Cursor)
	}
	query = query.Limit(int(pag.Limit) + 1)

	if err := query.Find(&boards).Error; err != nil {
		return nil, nil, err
	}

	response := &pagination.CursorResponse{
		HasNext: len(boards) > int(pag.Limit),
		Limit:   int(pag.Limit),
	}

	if response.HasNext {
		boards = boards[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", boards[len(boards)-1].ID)
	}

	return boards, response, nil
}

func (r *boardRepository) GetStarredBoards(ctx context.Context, tenantID, userID uint, pag *pagination.CursorPagination) ([]models.TrelloBoard, *pagination.CursorResponse, error) {
	var boards []models.TrelloBoard
	query := r.db.WithContext(ctx).
		Joins("JOIN trello_board_members ON trello_boards.id = trello_board_members.board_id").
		Where("trello_boards.tenant_id = ? AND trello_board_members.user_id = ? AND trello_boards.is_starred = ? AND trello_boards.status != ? AND trello_board_members.status = ?",
			tenantID, userID, true, models.TrelloStatusDeleted, models.MemberStatusActive).
		Order("trello_boards.updated_at DESC")

	if pag.Cursor != "" {
		query = query.Where("trello_boards.id < ?", pag.Cursor)
	}
	query = query.Limit(int(pag.Limit) + 1)

	if err := query.Find(&boards).Error; err != nil {
		return nil, nil, err
	}

	response := &pagination.CursorResponse{
		HasNext: len(boards) > int(pag.Limit),
		Limit:   int(pag.Limit),
	}

	if response.HasNext {
		boards = boards[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", boards[len(boards)-1].ID)
	}

	return boards, response, nil
}

func (r *boardRepository) AddMember(ctx context.Context, member *models.TrelloBoardMember) error {
	return r.db.WithContext(ctx).Create(member).Error
}

func (r *boardRepository) RemoveMember(ctx context.Context, tenantID, boardID, userID uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloBoardMember{}).
		Where("tenant_id = ? AND board_id = ? AND user_id = ?", tenantID, boardID, userID).
		Update("status", models.MemberStatusDeleted).Error
}

func (r *boardRepository) UpdateMember(ctx context.Context, tenantID, boardID, userID uint, member *models.TrelloBoardMember) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloBoardMember{}).
		Where("tenant_id = ? AND board_id = ? AND user_id = ?", tenantID, boardID, userID).
		Updates(member).Error
}

func (r *boardRepository) GetMember(ctx context.Context, tenantID, boardID, userID uint) (*models.TrelloBoardMember, error) {
	var member models.TrelloBoardMember
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND board_id = ? AND user_id = ? AND status != ?",
			tenantID, boardID, userID, models.MemberStatusDeleted).
		First(&member).Error
	if err != nil {
		return nil, err
	}
	return &member, nil
}

func (r *boardRepository) ListMembers(ctx context.Context, tenantID, boardID uint) ([]models.TrelloBoardMember, error) {
	var members []models.TrelloBoardMember
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND board_id = ? AND status != ?",
			tenantID, boardID, models.MemberStatusDeleted).
		Order("joined_at ASC").
		Find(&members).Error
	return members, err
}

func (r *boardRepository) IsMember(ctx context.Context, tenantID, boardID, userID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloBoardMember{}).
		Where("tenant_id = ? AND board_id = ? AND user_id = ? AND status = ?",
			tenantID, boardID, userID, models.MemberStatusActive).
		Count(&count).Error
	return count > 0, err
}

func (r *boardRepository) GetMemberRole(ctx context.Context, tenantID, boardID, userID uint) (models.MemberRole, error) {
	var member models.TrelloBoardMember
	err := r.db.WithContext(ctx).
		Select("role").
		Where("tenant_id = ? AND board_id = ? AND user_id = ? AND status = ?",
			tenantID, boardID, userID, models.MemberStatusActive).
		First(&member).Error
	if err != nil {
		return "", err
	}
	return member.Role, nil
}

func (r *boardRepository) CreateLabel(ctx context.Context, label *models.TrelloBoardLabel) error {
	return r.db.WithContext(ctx).Create(label).Error
}

func (r *boardRepository) UpdateLabel(ctx context.Context, tenantID, labelID uint, label *models.TrelloBoardLabel) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, labelID).
		Updates(label).Error
}

func (r *boardRepository) DeleteLabel(ctx context.Context, tenantID, labelID uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloBoardLabel{}).
		Where("tenant_id = ? AND id = ?", tenantID, labelID).
		Update("status", models.TrelloStatusDeleted).Error
}

func (r *boardRepository) GetLabel(ctx context.Context, tenantID, labelID uint) (*models.TrelloBoardLabel, error) {
	var label models.TrelloBoardLabel
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, labelID, models.TrelloStatusDeleted).
		First(&label).Error
	if err != nil {
		return nil, err
	}
	return &label, nil
}

func (r *boardRepository) ListLabels(ctx context.Context, tenantID, boardID uint) ([]models.TrelloBoardLabel, error) {
	var labels []models.TrelloBoardLabel
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND board_id = ? AND status != ?",
			tenantID, boardID, models.TrelloStatusDeleted).
		Order("created_at ASC").
		Find(&labels).Error
	return labels, err
}

func (r *boardRepository) ListByStatus(ctx context.Context, tenantID uint, status models.TrelloStatus, pag *pagination.CursorPagination) ([]models.TrelloBoard, *pagination.CursorResponse, error) {
	var boards []models.TrelloBoard
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Order("created_at DESC")

	if pag.Cursor != "" {
		query = query.Where("id < ?", pag.Cursor)
	}
	query = query.Limit(int(pag.Limit) + 1)

	if err := query.Find(&boards).Error; err != nil {
		return nil, nil, err
	}

	response := &pagination.CursorResponse{
		HasNext: len(boards) > int(pag.Limit),
		Limit:   int(pag.Limit),
	}

	if response.HasNext {
		boards = boards[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", boards[len(boards)-1].ID)
	}

	return boards, response, nil
}

func (r *boardRepository) GetWithLabels(ctx context.Context, tenantID, id uint) (*models.TrelloBoard, error) {
	var board models.TrelloBoard
	err := r.db.WithContext(ctx).
		Preload("Labels", "status != ?", models.TrelloStatusDeleted).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, id, models.TrelloStatusDeleted).
		First(&board).Error
	if err != nil {
		return nil, err
	}
	return &board, nil
}

func (r *boardRepository) GetRecentBoards(ctx context.Context, tenantID, userID uint, limit int) ([]models.TrelloBoard, error) {
	var boards []models.TrelloBoard
	err := r.db.WithContext(ctx).
		Joins("JOIN trello_board_members ON trello_boards.id = trello_board_members.board_id").
		Where("trello_boards.tenant_id = ? AND trello_board_members.user_id = ? AND trello_boards.status != ? AND trello_board_members.status = ?",
			tenantID, userID, models.TrelloStatusDeleted, models.MemberStatusActive).
		Order("trello_boards.updated_at DESC").
		Limit(limit).
		Find(&boards).Error
	return boards, err
}

func (r *boardRepository) SearchByName(ctx context.Context, tenantID uint, query string, pag *pagination.CursorPagination) ([]models.TrelloBoard, *pagination.CursorResponse, error) {
	var boards []models.TrelloBoard
	dbQuery := r.db.WithContext(ctx).
		Where("tenant_id = ? AND name LIKE ? AND status != ?", tenantID, "%"+query+"%", models.TrelloStatusDeleted).
		Order("created_at DESC")

	if pag.Cursor != "" {
		dbQuery = dbQuery.Where("id < ?", pag.Cursor)
	}
	dbQuery = dbQuery.Limit(int(pag.Limit) + 1)

	if err := dbQuery.Find(&boards).Error; err != nil {
		return nil, nil, err
	}

	response := &pagination.CursorResponse{
		HasNext: len(boards) > int(pag.Limit),
		Limit:   int(pag.Limit),
	}

	if response.HasNext {
		boards = boards[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", boards[len(boards)-1].ID)
	}

	return boards, response, nil
}
