package mysql

import (
	"context"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/repositories"
	"gorm.io/gorm"
)

type checklistRepository struct {
	db *gorm.DB
}

func NewChecklistRepository(db *gorm.DB) repositories.ChecklistRepository {
	return &checklistRepository{db: db}
}

func (r *checklistRepository) Create(ctx context.Context, checklist *models.TrelloChecklist) error {
	return r.db.WithContext(ctx).Create(checklist).Error
}

func (r *checklistRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.TrelloChecklist, error) {
	var checklist models.TrelloChecklist
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, id, models.TrelloStatusDeleted).
		First(&checklist).Error
	if err != nil {
		return nil, err
	}
	return &checklist, nil
}

func (r *checklistRepository) Update(ctx context.Context, tenantID, id uint, checklist *models.TrelloChecklist) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Updates(checklist).Error
}

func (r *checklistRepository) Delete(ctx context.Context, tenantID, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloChecklist{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Update("status", models.TrelloStatusDeleted).Error
}

func (r *checklistRepository) ListByCard(ctx context.Context, tenantID, cardID uint) ([]models.TrelloChecklist, error) {
	var checklists []models.TrelloChecklist
	err := r.db.WithContext(ctx).
		Preload("Items", "status != ?", models.TrelloStatusDeleted).
		Where("tenant_id = ? AND card_id = ? AND status != ?",
			tenantID, cardID, models.TrelloStatusDeleted).
		Order("position ASC").
		Find(&checklists).Error
	return checklists, err
}

func (r *checklistRepository) GetByName(ctx context.Context, tenantID, cardID uint, name string) (*models.TrelloChecklist, error) {
	var checklist models.TrelloChecklist
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND card_id = ? AND name = ? AND status != ?",
			tenantID, cardID, name, models.TrelloStatusDeleted).
		First(&checklist).Error
	if err != nil {
		return nil, err
	}
	return &checklist, nil
}

func (r *checklistRepository) GetWithItems(ctx context.Context, tenantID, id uint) (*models.TrelloChecklist, error) {
	var checklist models.TrelloChecklist
	err := r.db.WithContext(ctx).
		Preload("Items", func(db *gorm.DB) *gorm.DB {
			return db.Where("status != ?", models.TrelloStatusDeleted).Order("position ASC")
		}).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, id, models.TrelloStatusDeleted).
		First(&checklist).Error
	if err != nil {
		return nil, err
	}
	return &checklist, nil
}

func (r *checklistRepository) GetNextPosition(ctx context.Context, tenantID, cardID uint, afterPosition *float64) (float64, error) {
	var maxPosition float64
	query := r.db.WithContext(ctx).
		Model(&models.TrelloChecklist{}).
		Where("tenant_id = ? AND card_id = ? AND status != ?", tenantID, cardID, models.TrelloStatusDeleted)

	if afterPosition != nil {
		query = query.Where("position > ?", *afterPosition)
	}

	err := query.Select("COALESCE(MAX(position), 0)").Scan(&maxPosition).Error
	if err != nil {
		return 0, err
	}
	return maxPosition + 1, nil
}

func (r *checklistRepository) UpdatePosition(ctx context.Context, tenantID, id uint, position float64) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloChecklist{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Update("position", position).Error
}

func (r *checklistRepository) GetItemCount(ctx context.Context, tenantID, checklistID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloChecklistItem{}).
		Where("tenant_id = ? AND checklist_id = ? AND status != ?",
			tenantID, checklistID, models.TrelloStatusDeleted).
		Count(&count).Error
	return count, err
}

func (r *checklistRepository) GetCheckedItemCount(ctx context.Context, tenantID, checklistID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloChecklistItem{}).
		Where("tenant_id = ? AND checklist_id = ? AND is_checked = ? AND status != ?",
			tenantID, checklistID, true, models.TrelloStatusDeleted).
		Count(&count).Error
	return count, err
}

func (r *checklistRepository) CreateItem(ctx context.Context, item *models.TrelloChecklistItem) error {
	return r.db.WithContext(ctx).Create(item).Error
}

func (r *checklistRepository) GetItem(ctx context.Context, tenantID, itemID uint) (*models.TrelloChecklistItem, error) {
	var item models.TrelloChecklistItem
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, itemID, models.TrelloStatusDeleted).
		First(&item).Error
	if err != nil {
		return nil, err
	}
	return &item, nil
}

func (r *checklistRepository) UpdateItem(ctx context.Context, tenantID, itemID uint, item *models.TrelloChecklistItem) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, itemID).
		Updates(item).Error
}

func (r *checklistRepository) DeleteItem(ctx context.Context, tenantID, itemID uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloChecklistItem{}).
		Where("tenant_id = ? AND id = ?", tenantID, itemID).
		Update("status", models.TrelloStatusDeleted).Error
}

func (r *checklistRepository) ListItems(ctx context.Context, tenantID, checklistID uint) ([]models.TrelloChecklistItem, error) {
	var items []models.TrelloChecklistItem
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND checklist_id = ? AND status != ?",
			tenantID, checklistID, models.TrelloStatusDeleted).
		Order("position ASC").
		Find(&items).Error
	return items, err
}

func (r *checklistRepository) GetNextItemPosition(ctx context.Context, tenantID, checklistID uint, afterPosition *float64) (float64, error) {
	var maxPosition float64
	query := r.db.WithContext(ctx).
		Model(&models.TrelloChecklistItem{}).
		Where("tenant_id = ? AND checklist_id = ? AND status != ?", tenantID, checklistID, models.TrelloStatusDeleted)

	if afterPosition != nil {
		query = query.Where("position > ?", *afterPosition)
	}

	err := query.Select("COALESCE(MAX(position), 0)").Scan(&maxPosition).Error
	if err != nil {
		return 0, err
	}
	return maxPosition + 1, nil
}

func (r *checklistRepository) CheckItem(ctx context.Context, tenantID, itemID uint, userID uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloChecklistItem{}).
		Where("tenant_id = ? AND id = ?", tenantID, itemID).
		Updates(map[string]interface{}{
			"is_checked": true,
			"checked_by": userID,
			"checked_at": gorm.Expr("NOW()"),
		}).Error
}

func (r *checklistRepository) UncheckItem(ctx context.Context, tenantID, itemID uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloChecklistItem{}).
		Where("tenant_id = ? AND id = ?", tenantID, itemID).
		Updates(map[string]interface{}{
			"is_checked": false,
			"checked_by": nil,
			"checked_at": nil,
		}).Error
}

func (r *checklistRepository) GetItemsByDueDate(ctx context.Context, tenantID, cardID uint) ([]models.TrelloChecklistItem, error) {
	var items []models.TrelloChecklistItem
	err := r.db.WithContext(ctx).
		Joins("JOIN trello_checklists ON trello_checklist_items.checklist_id = trello_checklists.id").
		Where("trello_checklist_items.tenant_id = ? AND trello_checklists.card_id = ? AND trello_checklist_items.due_date IS NOT NULL AND trello_checklist_items.status != ?",
			tenantID, cardID, models.TrelloStatusDeleted).
		Order("trello_checklist_items.due_date ASC").
		Find(&items).Error
	return items, err
}

func (r *checklistRepository) GetItemsByAssignee(ctx context.Context, tenantID, cardID, userID uint) ([]models.TrelloChecklistItem, error) {
	var items []models.TrelloChecklistItem
	err := r.db.WithContext(ctx).
		Joins("JOIN trello_checklists ON trello_checklist_items.checklist_id = trello_checklists.id").
		Where("trello_checklist_items.tenant_id = ? AND trello_checklists.card_id = ? AND trello_checklist_items.assigned_user_id = ? AND trello_checklist_items.status != ?",
			tenantID, cardID, userID, models.TrelloStatusDeleted).
		Order("trello_checklist_items.position ASC").
		Find(&items).Error
	return items, err
}

func (r *checklistRepository) CheckAllItems(ctx context.Context, tenantID, checklistID, userID uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloChecklistItem{}).
		Where("tenant_id = ? AND checklist_id = ? AND status != ?", tenantID, checklistID, models.TrelloStatusDeleted).
		Updates(map[string]interface{}{
			"is_checked": true,
			"checked_by": userID,
			"checked_at": gorm.Expr("NOW()"),
		}).Error
}

func (r *checklistRepository) UncheckAllItems(ctx context.Context, tenantID, checklistID uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloChecklistItem{}).
		Where("tenant_id = ? AND checklist_id = ? AND status != ?", tenantID, checklistID, models.TrelloStatusDeleted).
		Updates(map[string]interface{}{
			"is_checked": false,
			"checked_by": nil,
			"checked_at": nil,
		}).Error
}

func (r *checklistRepository) CopyChecklist(ctx context.Context, tenantID, sourceChecklistID, targetCardID, userID uint) (*models.TrelloChecklist, error) {
	// Get the original checklist with its items
	var originalChecklist models.TrelloChecklist
	err := r.db.WithContext(ctx).
		Preload("Items", "status != ?", models.TrelloStatusDeleted).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, sourceChecklistID, models.TrelloStatusDeleted).
		First(&originalChecklist).Error
	if err != nil {
		return nil, err
	}

	// Create new checklist
	newChecklist := &models.TrelloChecklist{
		TenantID:  tenantID,
		CardID:    targetCardID,
		Name:      originalChecklist.Name,
		Position:  originalChecklist.Position,
		CreatedBy: userID,
		Status:    models.TrelloStatusActive,
	}

	// Create the new checklist
	err = r.db.WithContext(ctx).Create(newChecklist).Error
	if err != nil {
		return nil, err
	}

	// Copy all items
	for _, item := range originalChecklist.Items {
		newItem := &models.TrelloChecklistItem{
			TenantID:    tenantID,
			ChecklistID: newChecklist.ID,
			CardID:      targetCardID,
			Name:        item.Name,
			Position:    item.Position,
			IsChecked:   false, // Reset completion status
			CreatedBy:   userID,
			Status:      models.TrelloStatusActive,
		}

		err = r.db.WithContext(ctx).Create(newItem).Error
		if err != nil {
			return nil, err
		}
	}

	return newChecklist, nil
}

func (r *checklistRepository) CountByCard(ctx context.Context, tenantID, cardID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloChecklist{}).
		Where("tenant_id = ? AND card_id = ? AND status != ?", tenantID, cardID, models.TrelloStatusDeleted).
		Count(&count).Error
	return count, err
}

func (r *checklistRepository) CountCheckedItemsByChecklist(ctx context.Context, tenantID, checklistID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloChecklistItem{}).
		Where("tenant_id = ? AND checklist_id = ? AND is_checked = ? AND status != ?",
			tenantID, checklistID, true, models.TrelloStatusDeleted).
		Count(&count).Error
	return count, err
}

// ListByCardWithItems lists checklists by card with items preloaded
func (r *checklistRepository) ListByCardWithItems(ctx context.Context, tenantID, cardID uint) ([]models.TrelloChecklist, error) {
	var checklists []models.TrelloChecklist
	err := r.db.WithContext(ctx).
		Preload("Items", func(db *gorm.DB) *gorm.DB {
			return db.Where("status != ?", models.TrelloStatusDeleted).Order("position ASC")
		}).
		Where("tenant_id = ? AND card_id = ? AND status != ?",
			tenantID, cardID, models.TrelloStatusDeleted).
		Order("position ASC").
		Find(&checklists).Error
	return checklists, err
}

// GetMaxPosition gets the maximum position value for checklists in a card
func (r *checklistRepository) GetMaxPosition(ctx context.Context, tenantID, cardID uint) (float64, error) {
	var maxPosition float64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloChecklist{}).
		Where("tenant_id = ? AND card_id = ? AND status != ?", tenantID, cardID, models.TrelloStatusDeleted).
		Select("COALESCE(MAX(position), 0)").
		Scan(&maxPosition).Error
	return maxPosition, err
}

// UpdateProgress updates the progress of a checklist based on checked items
func (r *checklistRepository) UpdateProgress(ctx context.Context, tenantID, checklistID uint) error {
	var total, checked int64

	// Get total items
	err := r.db.WithContext(ctx).
		Model(&models.TrelloChecklistItem{}).
		Where("tenant_id = ? AND checklist_id = ? AND status != ?",
			tenantID, checklistID, models.TrelloStatusDeleted).
		Count(&total).Error
	if err != nil {
		return err
	}

	// Get checked items
	err = r.db.WithContext(ctx).
		Model(&models.TrelloChecklistItem{}).
		Where("tenant_id = ? AND checklist_id = ? AND is_checked = ? AND status != ?",
			tenantID, checklistID, true, models.TrelloStatusDeleted).
		Count(&checked).Error
	if err != nil {
		return err
	}

	// Update checklist progress percentage
	var progress uint
	if total > 0 {
		progress = uint((checked * 100) / total)
	}

	return r.db.WithContext(ctx).
		Model(&models.TrelloChecklist{}).
		Where("tenant_id = ? AND id = ?", tenantID, checklistID).
		Update("progress", progress).Error
}

// GetProgress gets the progress of a checklist (total items, checked items)
func (r *checklistRepository) GetProgress(ctx context.Context, tenantID, checklistID uint) (uint, uint, error) {
	var total, checked int64

	// Get total items
	err := r.db.WithContext(ctx).
		Model(&models.TrelloChecklistItem{}).
		Where("tenant_id = ? AND checklist_id = ? AND status != ?",
			tenantID, checklistID, models.TrelloStatusDeleted).
		Count(&total).Error
	if err != nil {
		return 0, 0, err
	}

	// Get checked items
	err = r.db.WithContext(ctx).
		Model(&models.TrelloChecklistItem{}).
		Where("tenant_id = ? AND checklist_id = ? AND is_checked = ? AND status != ?",
			tenantID, checklistID, true, models.TrelloStatusDeleted).
		Count(&checked).Error
	if err != nil {
		return 0, 0, err
	}

	return uint(total), uint(checked), nil
}

// GetMaxItemPosition gets the maximum position value for items in a checklist
func (r *checklistRepository) GetMaxItemPosition(ctx context.Context, tenantID, checklistID uint) (float64, error) {
	var maxPosition float64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloChecklistItem{}).
		Where("tenant_id = ? AND checklist_id = ? AND status != ?", tenantID, checklistID, models.TrelloStatusDeleted).
		Select("COALESCE(MAX(position), 0)").
		Scan(&maxPosition).Error
	return maxPosition, err
}

// UpdateItemPosition updates the position of a checklist item
func (r *checklistRepository) UpdateItemPosition(ctx context.Context, tenantID, itemID uint, position float64) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloChecklistItem{}).
		Where("tenant_id = ? AND id = ?", tenantID, itemID).
		Update("position", position).Error
}

// ToggleItem toggles the checked state of a checklist item
func (r *checklistRepository) ToggleItem(ctx context.Context, tenantID, itemID, userID uint) error {
	var item models.TrelloChecklistItem
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, itemID).
		First(&item).Error
	if err != nil {
		return err
	}

	updates := map[string]interface{}{
		"is_checked": !item.IsChecked,
	}

	if !item.IsChecked {
		// Checking the item
		updates["checked_by"] = userID
		updates["checked_at"] = gorm.Expr("NOW()")
	} else {
		// Unchecking the item
		updates["checked_by"] = nil
		updates["checked_at"] = nil
	}

	return r.db.WithContext(ctx).
		Model(&models.TrelloChecklistItem{}).
		Where("tenant_id = ? AND id = ?", tenantID, itemID).
		Updates(updates).Error
}

// CountItemsByChecklist counts the number of items in a checklist
func (r *checklistRepository) CountItemsByChecklist(ctx context.Context, tenantID, checklistID uint) (int64, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloChecklistItem{}).
		Where("tenant_id = ? AND checklist_id = ? AND status != ?",
			tenantID, checklistID, models.TrelloStatusDeleted).
		Count(&count).Error
	return count, err
}
