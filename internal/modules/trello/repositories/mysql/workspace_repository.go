package mysql

import (
	"context"
	"fmt"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/repositories"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"gorm.io/gorm"
)

// workspaceRepository implements the WorkspaceRepository interface
type workspaceRepository struct {
	db *gorm.DB
}

// NewWorkspaceRepository creates a new workspace repository
func NewWorkspaceRepository(db *gorm.DB) repositories.WorkspaceRepository {
	return &workspaceRepository{db: db}
}

// C<PERSON> creates a new workspace
func (r *workspaceRepository) Create(ctx context.Context, workspace *models.TrelloWorkspace) error {
	return r.db.WithContext(ctx).Create(workspace).Error
}

// GetByID retrieves a workspace by ID
func (r *workspaceRepository) GetByID(ctx context.Context, tenantID, id uint) (*models.TrelloWorkspace, error) {
	var workspace models.TrelloWorkspace
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, id, models.TrelloStatusDeleted).
		First(&workspace).Error
	if err != nil {
		return nil, err
	}
	return &workspace, nil
}

// Update updates a workspace
func (r *workspaceRepository) Update(ctx context.Context, tenantID, id uint, workspace *models.TrelloWorkspace) error {
	return r.db.WithContext(ctx).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Updates(workspace).Error
}

// Delete soft deletes a workspace
func (r *workspaceRepository) Delete(ctx context.Context, tenantID, id uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloWorkspace{}).
		Where("tenant_id = ? AND id = ?", tenantID, id).
		Update("status", models.TrelloStatusDeleted).Error
}

// List retrieves workspaces with pagination
func (r *workspaceRepository) List(ctx context.Context, tenantID uint, pag *pagination.CursorPagination) ([]models.TrelloWorkspace, *pagination.CursorResponse, error) {
	var workspaces []models.TrelloWorkspace
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status != ?", tenantID, models.TrelloStatusDeleted).
		Order("created_at DESC")

	// Apply pagination
	if pag.Cursor != "" {
		query = query.Where("id > ?", pag.Cursor)
	}
	query = query.Limit(pag.Limit + 1)

	if err := query.Find(&workspaces).Error; err != nil {
		return nil, nil, err
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(workspaces) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		workspaces = workspaces[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", workspaces[len(workspaces)-1].ID)
	}

	return workspaces, response, nil
}

// ListByStatus retrieves workspaces by status with pagination
func (r *workspaceRepository) ListByStatus(ctx context.Context, tenantID uint, status models.TrelloStatus, pag *pagination.CursorPagination) ([]models.TrelloWorkspace, *pagination.CursorResponse, error) {
	var workspaces []models.TrelloWorkspace
	query := r.db.WithContext(ctx).
		Where("tenant_id = ? AND status = ?", tenantID, status).
		Order("created_at DESC")

	// Apply pagination
	if pag.Cursor != "" {
		query = query.Where("id > ?", pag.Cursor)
	}
	query = query.Limit(pag.Limit + 1)

	if err := query.Find(&workspaces).Error; err != nil {
		return nil, nil, err
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(workspaces) > pag.Limit,
		Limit:   pag.Limit,
	}

	if response.HasNext {
		workspaces = workspaces[:pag.Limit]
		response.NextCursor = fmt.Sprintf("%d", workspaces[len(workspaces)-1].ID)
	}

	return workspaces, response, nil
}

// GetByName retrieves a workspace by name
func (r *workspaceRepository) GetByName(ctx context.Context, tenantID uint, name string) (*models.TrelloWorkspace, error) {
	var workspace models.TrelloWorkspace
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND name = ? AND status != ?", tenantID, name, models.TrelloStatusDeleted).
		First(&workspace).Error
	if err != nil {
		return nil, err
	}
	return &workspace, nil
}

// GetWithBoards retrieves a workspace with its boards
func (r *workspaceRepository) GetWithBoards(ctx context.Context, tenantID, id uint) (*models.TrelloWorkspace, error) {
	var workspace models.TrelloWorkspace
	err := r.db.WithContext(ctx).
		Preload("Boards", "status != ?", models.TrelloStatusDeleted).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, id, models.TrelloStatusDeleted).
		First(&workspace).Error
	if err != nil {
		return nil, err
	}
	return &workspace, nil
}

// GetWithMembers retrieves a workspace with its members
func (r *workspaceRepository) GetWithMembers(ctx context.Context, tenantID, id uint) (*models.TrelloWorkspace, error) {
	var workspace models.TrelloWorkspace
	err := r.db.WithContext(ctx).
		Preload("Members", "status != ?", models.MemberStatusDeleted).
		Where("tenant_id = ? AND id = ? AND status != ?", tenantID, id, models.TrelloStatusDeleted).
		First(&workspace).Error
	if err != nil {
		return nil, err
	}
	return &workspace, nil
}

// AddMember adds a member to a workspace
func (r *workspaceRepository) AddMember(ctx context.Context, member *models.TrelloWorkspaceMember) error {
	return r.db.WithContext(ctx).Create(member).Error
}

// RemoveMember removes a member from a workspace
func (r *workspaceRepository) RemoveMember(ctx context.Context, tenantID, workspaceID, userID uint) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloWorkspaceMember{}).
		Where("tenant_id = ? AND workspace_id = ? AND user_id = ?", tenantID, workspaceID, userID).
		Update("status", models.MemberStatusDeleted).Error
}

// UpdateMember updates a workspace member
func (r *workspaceRepository) UpdateMember(ctx context.Context, tenantID, workspaceID, userID uint, member *models.TrelloWorkspaceMember) error {
	return r.db.WithContext(ctx).
		Model(&models.TrelloWorkspaceMember{}).
		Where("tenant_id = ? AND workspace_id = ? AND user_id = ?", tenantID, workspaceID, userID).
		Updates(member).Error
}

// GetMember retrieves a workspace member
func (r *workspaceRepository) GetMember(ctx context.Context, tenantID, workspaceID, userID uint) (*models.TrelloWorkspaceMember, error) {
	var member models.TrelloWorkspaceMember
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND workspace_id = ? AND user_id = ? AND status != ?",
			tenantID, workspaceID, userID, models.MemberStatusDeleted).
		First(&member).Error
	if err != nil {
		return nil, err
	}
	return &member, nil
}

// ListMembers retrieves all members of a workspace
func (r *workspaceRepository) ListMembers(ctx context.Context, tenantID, workspaceID uint) ([]models.TrelloWorkspaceMember, error) {
	var members []models.TrelloWorkspaceMember
	err := r.db.WithContext(ctx).
		Where("tenant_id = ? AND workspace_id = ? AND status != ?",
			tenantID, workspaceID, models.MemberStatusDeleted).
		Order("joined_at ASC").
		Find(&members).Error
	return members, err
}

// IsMember checks if a user is a member of a workspace
func (r *workspaceRepository) IsMember(ctx context.Context, tenantID, workspaceID, userID uint) (bool, error) {
	var count int64
	err := r.db.WithContext(ctx).
		Model(&models.TrelloWorkspaceMember{}).
		Where("tenant_id = ? AND workspace_id = ? AND user_id = ? AND status = ?",
			tenantID, workspaceID, userID, models.MemberStatusActive).
		Count(&count).Error
	return count > 0, err
}

// GetMemberRole retrieves the role of a workspace member
func (r *workspaceRepository) GetMemberRole(ctx context.Context, tenantID, workspaceID, userID uint) (models.MemberRole, error) {
	var member models.TrelloWorkspaceMember
	err := r.db.WithContext(ctx).
		Select("role").
		Where("tenant_id = ? AND workspace_id = ? AND user_id = ? AND status = ?",
			tenantID, workspaceID, userID, models.MemberStatusActive).
		First(&member).Error
	if err != nil {
		return "", err
	}
	return member.Role, nil
}

// SearchByName searches workspaces by name
func (r *workspaceRepository) SearchByName(ctx context.Context, tenantID uint, query string, pag *pagination.CursorPagination) ([]models.TrelloWorkspace, *pagination.CursorResponse, error) {
	var workspaces []models.TrelloWorkspace
	dbQuery := r.db.WithContext(ctx).
		Where("tenant_id = ? AND name LIKE ? AND status != ?", tenantID, "%"+query+"%", models.TrelloStatusDeleted).
		Order("created_at DESC")

	// Apply pagination
	if pag.Cursor != "" {
		dbQuery = dbQuery.Where("id < ?", pag.Cursor)
	}
	dbQuery = dbQuery.Limit(int(pag.Limit))

	if err := dbQuery.Find(&workspaces).Error; err != nil {
		return nil, nil, err
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(workspaces) == int(pag.Limit),
		Count:   len(workspaces),
		Limit:   int(pag.Limit),
	}
	if len(workspaces) > 0 {
		response.NextCursor = fmt.Sprintf("%d", workspaces[len(workspaces)-1].ID)
	}

	return workspaces, response, nil
}

// GetUserWorkspaces retrieves workspaces where user is a member
func (r *workspaceRepository) GetUserWorkspaces(ctx context.Context, tenantID, userID uint, pag *pagination.CursorPagination) ([]models.TrelloWorkspace, *pagination.CursorResponse, error) {
	var workspaces []models.TrelloWorkspace
	query := r.db.WithContext(ctx).
		Joins("JOIN trello_workspace_members ON trello_workspaces.id = trello_workspace_members.workspace_id").
		Where("trello_workspaces.tenant_id = ? AND trello_workspace_members.user_id = ? AND trello_workspaces.status != ? AND trello_workspace_members.status = ?",
			tenantID, userID, models.TrelloStatusDeleted, models.MemberStatusActive).
		Order("trello_workspace_members.joined_at DESC")

	// Apply pagination
	if pag.Cursor != "" {
		query = query.Where("trello_workspaces.id < ?", pag.Cursor)
	}
	query = query.Limit(int(pag.Limit))

	if err := query.Find(&workspaces).Error; err != nil {
		return nil, nil, err
	}

	// Build cursor response
	response := &pagination.CursorResponse{
		HasNext: len(workspaces) == int(pag.Limit),
		Count:   len(workspaces),
		Limit:   int(pag.Limit),
	}
	if len(workspaces) > 0 {
		response.NextCursor = fmt.Sprintf("%d", workspaces[len(workspaces)-1].ID)
	}

	return workspaces, response, nil
}
