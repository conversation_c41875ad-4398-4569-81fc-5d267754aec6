package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/models"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/pagination"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

type WorkspaceHandler struct {
	service   services.WorkspaceService
	validator validator.Validator
	logger    utils.Logger
}

func NewWorkspaceHandler(service services.WorkspaceService, validator validator.Validator, logger utils.Logger) *WorkspaceHandler {
	return &WorkspaceHandler{
		service:   service,
		validator: validator,
		logger:    logger,
	}
}

func (h *WorkspaceHandler) Create(c *gin.Context) {
	var request models.CreateWorkspaceRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.J<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if err := h.validator.Validate(c.Request.Context(), &request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	workspace, err := h.service.Create(c.Request.Context(), tenantID.(uint), userID.(uint), &request)
	if err != nil {
		h.logger.Error("failed to create workspace", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create workspace"})
		return
	}

	c.JSON(http.StatusCreated, workspace)
}

func (h *WorkspaceHandler) GetByID(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid workspace ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	workspace, err := h.service.GetByID(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id))
	if err != nil {
		if err.Error() == "workspace not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Workspace not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to get workspace", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get workspace"})
		return
	}

	c.JSON(http.StatusOK, workspace)
}

func (h *WorkspaceHandler) Update(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid workspace ID"})
		return
	}

	var request models.UpdateWorkspaceRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if err := h.validator.Validate(c.Request.Context(), &request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	workspace, err := h.service.Update(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id), &request)
	if err != nil {
		if err.Error() == "workspace not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Workspace not found"})
			return
		}
		if err.Error() == "access denied" || err.Error() == "insufficient permissions" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to update workspace", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update workspace"})
		return
	}

	c.JSON(http.StatusOK, workspace)
}

func (h *WorkspaceHandler) Delete(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid workspace ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	err = h.service.Delete(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id))
	if err != nil {
		if err.Error() == "workspace not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Workspace not found"})
			return
		}
		if err.Error() == "access denied" || err.Error() == "only workspace owner can delete workspace" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to delete workspace", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete workspace"})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

func (h *WorkspaceHandler) List(c *gin.Context) {
	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	pag := pagination.NewCursorPaginationFromQuery(c)

	workspaces, cursor, err := h.service.List(c.Request.Context(), tenantID.(uint), userID.(uint), pag)
	if err != nil {
		h.logger.Error("failed to list workspaces", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list workspaces"})
		return
	}

	response := gin.H{
		"data":       workspaces,
		"pagination": cursor,
	}

	c.JSON(http.StatusOK, response)
}

func (h *WorkspaceHandler) GetWithBoards(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid workspace ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	workspace, err := h.service.GetWithBoards(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id))
	if err != nil {
		if err.Error() == "workspace not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Workspace not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to get workspace with boards", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get workspace with boards"})
		return
	}

	c.JSON(http.StatusOK, workspace)
}

func (h *WorkspaceHandler) GetWithMembers(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid workspace ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	workspace, err := h.service.GetWithMembers(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id))
	if err != nil {
		if err.Error() == "workspace not found" {
			c.JSON(http.StatusNotFound, gin.H{"error": "Workspace not found"})
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to get workspace with members", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get workspace with members"})
		return
	}

	c.JSON(http.StatusOK, workspace)
}

func (h *WorkspaceHandler) AddMember(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid workspace ID"})
		return
	}

	var request models.AddWorkspaceMemberRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if err := h.validator.Validate(c.Request.Context(), &request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	member, err := h.service.AddMember(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id), &request)
	if err != nil {
		if err.Error() == "access denied" || err.Error() == "insufficient permissions" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		if err.Error() == "user is already a member" {
			c.JSON(http.StatusConflict, gin.H{"error": "User is already a member"})
			return
		}
		h.logger.Error("failed to add workspace member", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to add workspace member"})
		return
	}

	c.JSON(http.StatusCreated, member)
}

func (h *WorkspaceHandler) RemoveMember(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid workspace ID"})
		return
	}

	memberID, err := strconv.ParseUint(c.Param("memberId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid member ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	err = h.service.RemoveMember(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id), uint(memberID))
	if err != nil {
		if err.Error() == "access denied" || err.Error() == "insufficient permissions" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		if err.Error() == "cannot remove yourself" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Cannot remove yourself"})
			return
		}
		h.logger.Error("failed to remove workspace member", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to remove workspace member"})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

func (h *WorkspaceHandler) UpdateMember(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid workspace ID"})
		return
	}

	memberID, err := strconv.ParseUint(c.Param("memberId"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid member ID"})
		return
	}

	var request models.UpdateWorkspaceMemberRequest
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid request body"})
		return
	}

	if err := h.validator.Validate(c.Request.Context(), &request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	member, err := h.service.UpdateMember(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id), uint(memberID), &request)
	if err != nil {
		if err.Error() == "access denied" || err.Error() == "insufficient permissions" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to update workspace member", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to update workspace member"})
		return
	}

	c.JSON(http.StatusOK, member)
}

func (h *WorkspaceHandler) ListMembers(c *gin.Context) {
	id, err := strconv.ParseUint(c.Param("id"), 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid workspace ID"})
		return
	}

	// Get tenant ID from context
	tenantID, exists := c.Get("tenant_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Tenant ID is required"})
		return
	}

	// Get user ID from context
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusBadRequest, gin.H{"error": "User ID is required"})
		return
	}

	members, err := h.service.ListMembers(c.Request.Context(), tenantID.(uint), userID.(uint), uint(id))
	if err != nil {
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, gin.H{"error": "Access denied"})
			return
		}
		h.logger.Error("failed to list workspace members", utils.WithError(err))
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to list workspace members"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"data": members})
}
