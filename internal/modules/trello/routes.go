package trello

import (
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/handlers"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/repositories/mysql"
	"github.com/tranthanhloi/wn-api-v3/internal/modules/trello/services"
	"github.com/tranthanhloi/wn-api-v3/pkg/utils"
	"github.com/tranthanhloi/wn-api-v3/pkg/validator"
)

// RegisterRoutes registers all Trello module routes
func RegisterRoutes(router *gin.RouterGroup, db *gorm.DB, v validator.Validator, logger utils.Logger) {
	// Initialize repositories
	workspaceRepo := mysql.NewWorkspaceRepository(db)
	boardRepo := mysql.NewBoardRepository(db)
	listRepo := mysql.NewListRepository(db)
	cardRepo := mysql.NewCardRepository(db)
	checklistRepo := mysql.NewChecklistRepository(db)

	// Initialize services
	workspaceService := services.NewWorkspaceService(workspaceRepo, logger)
	boardService := services.NewBoardService(boardRepo, workspaceRepo, logger)
	listService := services.NewListService(listRepo, boardRepo, logger)
	cardService := services.NewCardService(cardRepo, listRepo, boardRepo, logger)
	checklistService := services.NewChecklistService(checklistRepo, cardRepo, boardRepo, logger)

	// Initialize handlers
	workspaceHandler := handlers.NewWorkspaceHandler(workspaceService, v, logger)
	boardHandler := handlers.NewBoardHandler(boardService, v, logger)
	listHandler := handlers.NewListHandler(listService, v, logger)
	cardHandler := handlers.NewCardHandler(cardService, v, logger)
	checklistHandler := handlers.NewChecklistHandler(checklistService, v, logger)

	// Create a route group for Trello endpoints
	trelloGroup := router.Group("/trello")

	// Workspace routes
	workspaceGroup := trelloGroup.Group("/workspaces")
	{
		workspaceGroup.POST("", workspaceHandler.Create)
		workspaceGroup.GET("", workspaceHandler.List)
		workspaceGroup.GET("/:id", workspaceHandler.GetByID)
		workspaceGroup.PUT("/:id", workspaceHandler.Update)
		workspaceGroup.DELETE("/:id", workspaceHandler.Delete)
		workspaceGroup.GET("/:id/boards", workspaceHandler.GetWithBoards)
		workspaceGroup.GET("/:id/members", workspaceHandler.ListMembers)
		workspaceGroup.POST("/:id/members", workspaceHandler.AddMember)
		workspaceGroup.DELETE("/:id/members/:memberId", workspaceHandler.RemoveMember)
		workspaceGroup.PUT("/:id/members/:memberId", workspaceHandler.UpdateMember)
	}

	// Board routes
	boardGroup := trelloGroup.Group("/boards")
	{
		boardGroup.POST("", boardHandler.Create)
		boardGroup.GET("", boardHandler.List)
		boardGroup.GET("/:id", boardHandler.GetByID)
		boardGroup.PUT("/:id", boardHandler.Update)
		boardGroup.DELETE("/:id", boardHandler.Delete)
		boardGroup.GET("/workspace/:workspaceId", boardHandler.ListByWorkspace)
		boardGroup.GET("/:id/lists", boardHandler.GetWithLists)
		boardGroup.GET("/starred", boardHandler.GetStarredBoards)
		boardGroup.POST("/labels", boardHandler.CreateLabel)
		boardGroup.GET("/:id/labels", boardHandler.ListLabels)
	}

	// List routes
	listGroup := trelloGroup.Group("/lists")
	{
		listGroup.POST("", listHandler.Create)
		listGroup.GET("/board/:boardId", listHandler.ListByBoard)
		listGroup.GET("/:id", listHandler.GetByID)
		listGroup.PUT("/:id", listHandler.Update)
		listGroup.DELETE("/:id", listHandler.Delete)
		listGroup.GET("/:id/cards", listHandler.GetWithCards)
		listGroup.PUT("/:id/move", listHandler.Move)
		listGroup.PUT("/:id/archive", listHandler.Archive)
		listGroup.PUT("/:id/unarchive", listHandler.Unarchive)
	}

	// Card routes
	cardGroup := trelloGroup.Group("/cards")
	{
		cardGroup.POST("", cardHandler.Create)
		cardGroup.GET("/list/:listId", cardHandler.ListByList)
		cardGroup.GET("/board/:boardId", cardHandler.ListByBoard)
		cardGroup.GET("/:id", cardHandler.GetByID)
		cardGroup.GET("/:id/detail", cardHandler.GetDetail)
		cardGroup.PUT("/:id", cardHandler.Update)
		cardGroup.DELETE("/:id", cardHandler.Delete)
		cardGroup.PUT("/:id/move", cardHandler.Move)
		cardGroup.POST("/:id/members", cardHandler.AssignMember)
		cardGroup.DELETE("/:id/members/:memberId", cardHandler.UnassignMember)
		cardGroup.POST("/comments", cardHandler.CreateComment)
		cardGroup.GET("/search/board/:boardId", cardHandler.Search)
	}

	// Checklist routes
	checklistGroup := trelloGroup.Group("/checklists")
	{
		checklistGroup.POST("", checklistHandler.Create)
		checklistGroup.GET("/card/:cardId", checklistHandler.ListByCard)
		checklistGroup.GET("/:id", checklistHandler.GetByID)
		checklistGroup.PUT("/:id", checklistHandler.Update)
		checklistGroup.DELETE("/:id", checklistHandler.Delete)
		checklistGroup.POST("/items", checklistHandler.CreateItem)
		checklistGroup.PUT("/items/:itemId", checklistHandler.UpdateItem)
		checklistGroup.DELETE("/items/:itemId", checklistHandler.DeleteItem)
		checklistGroup.PUT("/items/:itemId/check", checklistHandler.CheckItem)
		checklistGroup.PUT("/items/:itemId/uncheck", checklistHandler.UncheckItem)
	}
}
